import 'package:evoapp/feature/profile/profile_screen/card_status/mock_file/mock_card_status_tracking_file_name.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('returns correct JSON file for each CardStatusTrackingMockCase', () {
    /// Display cases
    expect(
      getCardStatusTrackingMock(CardStatusTrackingMockCase.displayNull),
      'get_card_status_display_null.json',
    );
    expect(
      getCardStatusTrackingMock(CardStatusTrackingMockCase.displayFalse),
      'get_card_status_display_false.json',
    );
    expect(
      getCardStatusTrackingMock(CardStatusTrackingMockCase.ctaWidgetDisplayFalse),
      'get_card_status_cta_widget_display_false.json',
    );

    /// Credit UI cases
    expect(
      getCardStatusTrackingMock(CardStatusTrackingMockCase.creditLimitWidgetWaitingForApproval),
      'get_card_status_credit_limit_widget_waiting_for_approval.json',
    );
    expect(
      getCardStatusTrackingMock(CardStatusTrackingMockCase.creditLimitWidgetNotReadyForPayment),
      'get_card_status_credit_limit_widget_not_ready_for_payment.json',
    );
    expect(
      getCardStatusTrackingMock(CardStatusTrackingMockCase.creditLimitWidgetReadyForPayment),
      'get_card_status_credit_limit_widget_ready_for_payment.json',
    );
    expect(
      getCardStatusTrackingMock(CardStatusTrackingMockCase.creditLimitWidgetOutOfSync),
      'get_card_status_credit_limit_widget_out_of_sync.json',
    );

    /// CTA UI cases
    expect(
      getCardStatusTrackingMock(CardStatusTrackingMockCase.ctaWidgetLinkedOnly),
      'get_card_status_cta_widget_linked_only.json',
    );
    expect(
      getCardStatusTrackingMock(CardStatusTrackingMockCase.ctaWidgetActivatedOnly),
      'get_card_status_cta_widget_activated_only.json',
    );
    expect(
      getCardStatusTrackingMock(CardStatusTrackingMockCase.ctaWidgetAllActionDone),
      'get_card_status_cta_widget_all_action_done.json',
    );

    /// Getting error cases
    expect(
      getCardStatusTrackingMock(CardStatusTrackingMockCase.verdictRenovateStatusLimitExceeded),
      'get_card_status_verdict_renovate_status_limit_exceeded.json',
    );
    expect(
      getCardStatusTrackingMock(CardStatusTrackingMockCase.verdictRenovateStatusFailed),
      'get_card_status_verdict_renovate_status_failed.json',
    );
  });
}
