import 'package:evoapp/model/promotion_status_ui_model.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/promotion/status_ui_data_creator_factory/promotion_source_data.dart';
import 'package:evoapp/util/promotion/status_ui_data_creator_factory/promotion_status_ui_creator_factory.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  final DateTime compareDate = DateTime(2023, 02, 23, 22, 22, 22); // 2022, Feb 23, 22:22:22

  setUpAll(() {
    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
  });

  tearDownAll(() async {
    await getIt.reset();
  });

  test('Give validFromDate null, should return PromotionStatusProperty null', () {
    final PromotionStatusUIModel? result = PromotionStatusUICreatorFactory()
        .create(CampaignSourceData())
        .createPromotionStatusUIData();

    expect(result, isNull);
  });

  test('Give validToDate null, should return PromotionStatusProperty null', () {
    final DateTime validFromDate = DateTime(2023, 02, 22, 22, 22, 22); // 2022, Feb 22, 22:22:22
    final PromotionStatusUIModel? result = PromotionStatusUICreatorFactory()
        .create(
          CampaignSourceData(
            validFromDate: validFromDate,
          ),
        )
        .createPromotionStatusUIData();

    expect(result, isNull);
  });

  test('Campaign expired, should display text: "Đã kết thúc", color 0xFF5E5E5E', () {
    final DateTime validFromDate = DateTime(2023, 02, 01, 22, 22, 22); // 2022, Feb 01, 22:22:22
    final DateTime validToDate = DateTime(2023, 02, 22, 22, 22, 22); // 2022, Feb 22, 22:22:22

    final PromotionStatusUIModel? result = PromotionStatusUICreatorFactory()
        .create(
          CampaignSourceData(
            validFromDate: validFromDate,
            validToDate: validToDate,
            compareDate: compareDate,
          ),
        )
        .createPromotionStatusUIData();

    expect(
      result,
      const PromotionStatusUIModel(
        title: 'Đã kết thúc',
        color: Color(0xFF5E5E5E),
        hasOpacityTitle: true,
      ),
    );
  });

  test('Campaign hotTime, should display text "Còn x phút", color 0xFFE54D2E', () {
    final DateTime validFromDate = DateTime(2023, 02, 01, 22, 22, 22); // 2022, Feb 01, 22:22:22
    final DateTime validToDate = DateTime(2023, 02, 23, 22, 23, 22); // 2022, Feb 23, 22:23:22

    final PromotionStatusUIModel? result = PromotionStatusUICreatorFactory()
        .create(
          CampaignSourceData(
            validFromDate: validFromDate,
            validToDate: validToDate,
            compareDate: compareDate,
          ),
        )
        .createPromotionStatusUIData();

    expect(
      result,
      const PromotionStatusUIModel(
        title: 'Còn 1 phút',
        color: Color(0xFFE54D2E),
      ),
    );
  });

  test('Campaign hotTime, should display text "Còn x giờ", color 0xFFE54D2E', () {
    final DateTime validFromDate = DateTime(2023, 02, 01, 22, 22, 22); // 2022, Feb 01, 22:22:22
    final DateTime validToDate = DateTime(2023, 02, 23, 23, 22, 22); // 2022, Feb 23, 22:23:22

    final PromotionStatusUIModel? result = PromotionStatusUICreatorFactory()
        .create(
          CampaignSourceData(
            validFromDate: validFromDate,
            validToDate: validToDate,
            compareDate: compareDate,
          ),
        )
        .createPromotionStatusUIData();

    expect(
      result,
      const PromotionStatusUIModel(
        title: 'Còn 1 giờ',
        color: Color(0xFFE54D2E),
      ),
    );
  });

  test('Campaign runningOut, should display text "Còn x ngày", color 0xFFF5A70B', () {
    final DateTime validFromDate = DateTime(2023, 02, 01, 22, 22, 22); // 2022, Feb 01, 22:22:22
    final DateTime validToDate = DateTime(2023, 02, 24, 22, 22, 22); // 2022, Feb 24, 22:22:22

    final PromotionStatusUIModel? result = PromotionStatusUICreatorFactory()
        .create(
          CampaignSourceData(
            validFromDate: validFromDate,
            validToDate: validToDate,
            compareDate: compareDate,
          ),
        )
        .createPromotionStatusUIData();

    expect(
      result,
      const PromotionStatusUIModel(
        title: 'Còn 1 ngày',
        color: Color(0xFFF5A70B),
      ),
    );
  });

  test('Campaign runningOut, should display text "Còn x ngày, x giờ", color 0xFFF5A70B', () {
    final DateTime validFromDate = DateTime(2023, 02, 01, 22, 22, 22); // 2022, Feb 01, 22:22:22
    final DateTime validToDate = DateTime(2023, 02, 24, 23, 22, 22); // 2022, Feb 24, 22:22:22

    final PromotionStatusUIModel? result = PromotionStatusUICreatorFactory()
        .create(
          CampaignSourceData(
            validFromDate: validFromDate,
            validToDate: validToDate,
            compareDate: compareDate,
          ),
        )
        .createPromotionStatusUIData();

    expect(
      result,
      const PromotionStatusUIModel(
        title: 'Còn 1 ngày, 1 giờ',
        color: Color(0xFFF5A70B),
      ),
    );
  });

  test('Campaign beforeRunningOut, should display text "HSD dd/MM/yyyy", color 0xFFF5A70B', () {
    final DateTime validFromDate = DateTime(2023, 02, 01, 22, 22, 22); // 2022, Feb 01, 22:22:22
    final DateTime validToDate = DateTime(2023, 02, 27, 23, 22, 22); // 2022, Feb 24, 22:22:22

    final PromotionStatusUIModel? result = PromotionStatusUICreatorFactory()
        .create(
          CampaignSourceData(
            validFromDate: validFromDate,
            validToDate: validToDate,
            compareDate: compareDate,
          ),
        )
        .createPromotionStatusUIData();

    expect(
      result,
      const PromotionStatusUIModel(
        title: 'Kết thúc vào 27/02/2023',
        color: Color(0xFFF5A70B),
      ),
    );
  });

  test('Campaign is coming, should display text "Bắt đầu vào dd/MM/yyyy", color 0xFF09B364', () {
    final DateTime validFromDate = DateTime(2023, 02, 23, 23, 22, 22); // 2022, Feb 23, 23:22:22
    final DateTime validToDate = DateTime(2023, 02, 27, 23, 22, 22); // 2022, Feb 24, 22:22:22

    final PromotionStatusUIModel? result = PromotionStatusUICreatorFactory()
        .create(
          CampaignSourceData(
            validFromDate: validFromDate,
            validToDate: validToDate,
            compareDate: compareDate,
          ),
        )
        .createPromotionStatusUIData();

    expect(
      result,
      const PromotionStatusUIModel(
        title: 'Bắt đầu vào 23/02/2023',
        color: Color(0xFF09B364),
      ),
    );
  });
}
