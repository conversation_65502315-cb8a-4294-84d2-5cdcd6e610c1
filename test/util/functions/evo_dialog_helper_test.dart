import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/profile/profile_detail_screen/other_widgets/list_action_bottom_sheet.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/widget/evo_dialog/full_screen_bottom_sheet_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../feature/delete_account/attention_notes/widget/attention_notes_content_test.dart';
import '../flutter_test_config.dart';

void main() {
  late EvoDialogHelper evoDialogHelper;
  late MockCommonUtilFunction mockCommonUtilFunction;

  setUpAll(() {
    getIt.registerLazySingleton<GlobalKeyProvider>(() => GlobalKeyProvider());
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
    getIt.registerLazySingleton<AppState>(() => AppState());
    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();
    mockCommonUtilFunction = MockCommonUtilFunction();
    getIt.registerSingleton<CommonUtilFunction>(mockCommonUtilFunction);
    registerFallbackValue(MockBuildContext());
  });

  setUp(() {
    evoDialogHelper = EvoDialogHelper();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('EvoDialogHelper', () {
    testWidgets('showFullScreenBottomSheet', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext context) {
              return ElevatedButton(
                onPressed: () {
                  evoDialogHelper.showFullScreenBottomSheet(
                    context,
                    content: const Text('Test Content'),
                  );
                },
                child: const Text('Show Bottom Sheet'),
              );
            },
          ),
          navigatorKey: getIt<GlobalKeyProvider>().navigatorKey,
        ),
      );

      await tester.tap(find.text('Show Bottom Sheet'));
      await tester.pumpAndSettle();

      expect(find.byType(FullScreenBottomSheetWidget), findsOneWidget);
      expect(find.text('Test Content'), findsOneWidget);
    });

    testWidgets('showMultiActionBottomSheet', (WidgetTester tester) async {
      final List<ActionBottomSheet> actions = <ActionBottomSheet>[
        ActionBottomSheet(title: 'Action 1', icon: SizedBox()),
        ActionBottomSheet(title: 'Action 2', icon: SizedBox()),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext context) {
              return ElevatedButton(
                onPressed: () {
                  evoDialogHelper.showMultiActionBottomSheet(
                    context,
                    actions,
                    onSelectedItem: (ActionBottomSheet action) {},
                  );
                },
                child: const Text('Show Multi Action Bottom Sheet'),
              );
            },
          ),
          navigatorKey: getIt<GlobalKeyProvider>().navigatorKey,
        ),
      );

      await tester.tap(find.text('Show Multi Action Bottom Sheet'));
      await tester.pumpAndSettle();

      expect(find.byType(ListActionBottomSheet), findsOneWidget);
      expect(find.text('Action 1'), findsOneWidget);
      expect(find.text('Action 2'), findsOneWidget);
    });

    testWidgets('showDialogBottomSheet', (WidgetTester tester) async {
      when(() => mockCommonUtilFunction.showDialogBottomSheet(
            any(),
            textPositive: any(named: 'textPositive'),
            content: any(named: 'content'),
            title: any(named: 'title'),
            textNegative: any(named: 'textNegative'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            header: any(named: 'header'),
            headerPadding: any(named: 'headerPadding'),
            isDismissible: any(named: 'isDismissible'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            titleTextStyle: any(named: 'titleTextStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickClose: any(named: 'onClickClose'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            buttonClose: any(named: 'buttonClose'),
            isEnableLoggingEvent: any(named: 'isEnableLoggingEvent'),
            dialogId: any(named: 'dialogId'),
            eventTrackingScreenId: any(named: 'eventTrackingScreenId'),
            loggingEventOnShowMetaData: any(named: 'loggingEventOnShowMetaData'),
            loggingEventMetaData: any(named: 'loggingEventMetaData'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            titleTextAlign: any(named: 'titleTextAlign'),
            contentTextAlign: any(named: 'contentTextAlign'),
            contentSpacing: any(named: 'contentSpacing'),
          )).thenAnswer((_) => Future<void>.value());
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext context) {
              return ElevatedButton(
                onPressed: () {
                  evoDialogHelper.showDialogBottomSheet(
                    content: '',
                    textPositive: '',
                    dialogId: EvoDialogId.expiredOrderBottomSheet,
                  );
                },
                child: const Text('Show Bottom Sheet'),
              );
            },
          ),
          navigatorKey: getIt<GlobalKeyProvider>().navigatorKey,
        ),
      );

      await tester.tap(find.text('Show Bottom Sheet'));
      await tester.pumpAndSettle();

      verify(() => mockCommonUtilFunction.showDialogBottomSheet(
            any(),
            textPositive: any(named: 'textPositive'),
            content: any(named: 'content'),
            title: any(named: 'title'),
            textNegative: any(named: 'textNegative'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            header: any(named: 'header'),
            headerPadding: any(named: 'headerPadding'),
            isDismissible: any(named: 'isDismissible'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            titleTextStyle: any(named: 'titleTextStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickClose: any(named: 'onClickClose'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            buttonClose: any(named: 'buttonClose'),
            isEnableLoggingEvent: any(named: 'isEnableLoggingEvent'),
            dialogId: any(named: 'dialogId'),
            eventTrackingScreenId: any(named: 'eventTrackingScreenId'),
            loggingEventOnShowMetaData: any(named: 'loggingEventOnShowMetaData'),
            loggingEventMetaData: any(named: 'loggingEventMetaData'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            titleTextAlign: any(named: 'titleTextAlign'),
            contentTextAlign: any(named: 'contentTextAlign'),
            contentSpacing: any(named: 'contentSpacing'),
          )).called(1);
    });

    testWidgets('showDialogBottomSheet with null context', (WidgetTester tester) async {
      final Future<void> result = evoDialogHelper.showDialogBottomSheet(
        content: '',
        textPositive: '',
        dialogId: EvoDialogId.expiredOrderBottomSheet,
      );
      expect(result, isA<Future<void>>());
    });
  });
}
