import 'package:evoapp/util/download_file_handler/download_file_handler_impl.dart';
import 'package:flutter_common_package/util/flutter_downloader/common_flutter_downloader.dart';
import 'package:flutter_common_package/util/flutter_downloader/models/download_request.dart';
import 'package:flutter_common_package/util/flutter_downloader/models/download_result.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCommonFlutterDownloader extends Mock implements CommonFlutterDownloader {}

class FakeDownloadRequest extends Fake implements DownloadRequest {}

void main() {
  final CommonFlutterDownloader commonFlutterDownloader = MockCommonFlutterDownloader();
  late DownloadFileHandlerImpl downloadFileRepoImpl;

  onDownloadCallback(DownloadResult result) async {}

  setUpAll(() {
    registerFallbackValue(FakeDownloadRequest());
  });

  setUp(() {
    downloadFileRepoImpl = DownloadFileHandlerImpl(downloader: commonFlutterDownloader);
  });

  test('Test init', () {
    downloadFileRepoImpl.init(onDownloadCallback: onDownloadCallback);

    verify(() => commonFlutterDownloader.init()).called(1);
    expect(
      verify(() => commonFlutterDownloader.listenDownloadCallback(captureAny())).captured.single,
      onDownloadCallback,
    );
  });

  group('Test startDownload', () {
    const String fakeUrl = 'url';
    const String fakeFileName = 'saveFileName';

    setUpAll(() {
      when(() => commonFlutterDownloader.requestDownloadFile(request: any(named: 'request')))
          .thenAnswer((_) async {
        return Future<bool>.value(true);
      });
    });

    test('startDownload with default params', () {
      downloadFileRepoImpl.startDownload(
        url: fakeUrl,
        saveFileName: fakeFileName,
      );

      expect(
        verify(() =>
                commonFlutterDownloader.requestDownloadFile(request: captureAny(named: 'request')))
            .captured
            .single,
        isA<DownloadRequest>()
            .having(
              (DownloadRequest p0) => p0.url,
              'verify url',
              fakeUrl,
            )
            .having(
              (DownloadRequest p0) => p0.suggestedFilename,
              'verify suggestedFilename',
              fakeFileName,
            )
            .having(
              (DownloadRequest p0) => p0.hasOpenFile,
              'verify suggestedFilename',
              true,
            ),
      );
    });

    test('startDownload with custom params', () {
      downloadFileRepoImpl.startDownload(
        url: fakeUrl,
        saveFileName: fakeFileName,
        hasOpenFile: false,
      );

      expect(
        verify(() =>
                commonFlutterDownloader.requestDownloadFile(request: captureAny(named: 'request')))
            .captured
            .single,
        isA<DownloadRequest>()
            .having(
              (DownloadRequest p0) => p0.url,
              'verify url',
              fakeUrl,
            )
            .having(
              (DownloadRequest p0) => p0.suggestedFilename,
              'verify suggestedFilename',
              fakeFileName,
            )
            .having(
              (DownloadRequest p0) => p0.hasOpenFile,
              'verify suggestedFilename',
              false,
            ),
      );
    });
  });

  test('Test dispose', () {
    downloadFileRepoImpl.dispose();

    verify(() => commonFlutterDownloader.dispose()).called(1);
  });
}
