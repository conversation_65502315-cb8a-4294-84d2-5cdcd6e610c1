import 'package:evoapp/feature/check_force_update/force_update_ui_model.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_screen_id.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_utils/evo_event_tracking_utils.dart';
import 'package:evoapp/feature/logging/evo_logging_event.dart';
import 'package:evoapp/feature/logging/metadata_define/evo_event_metadata.dart';
import 'package:evoapp/feature/term_and_condition/term_and_condition_overlay.dart';
import 'package:evoapp/feature/term_and_condition/term_and_condition_overlay_widget.dart';
import 'package:evoapp/feature/term_and_condition/term_and_condition_utils.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/resources/text_styles.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/widget/evo_overlay/evo_overlay_util_functions.dart';
import 'package:evoapp/widget/force_update/force_update_overlay.dart';
import 'package:evoapp/widget/force_update/force_update_overlay_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_dialog_behavior_type.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/text_styles.dart';
import 'package:flutter_common_package/ui_model/dialog_type.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockNetworkManager extends Mock implements NetworkManager {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockForceUpdateOverlay extends Mock implements ForceUpdateOverlay {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockEvoTextStyles extends Mock implements EvoTextStyles {}

class MockEvoEventTrackingUtils extends Mock implements EvoEventTrackingUtils {}

class MockEventTrackingUtils extends Mock implements EventTrackingUtils {}

class MockEvoOverlayUtilFunctions extends Mock implements EvoOverlayUtilFunctions {}

class MockAppState extends Mock implements AppState {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

void main() {
  late CommonImageProvider evoImageProvider;
  late EvoEventTrackingUtils evoEventTrackingUtils;
  late EvoOverlayUtilFunctions evoOverlayUtilFunction;
  late NetworkManager networkManager;
  late LoggingRepo loggingRepo;
  late EvoLocalStorageHelper evoLocalStorageHelper;
  late AppState appState;
  late FeatureToggle featureToggle;

  late EvoOverlayUtilFunctions mockEvoOverlayUtilFunctions;

  const String fakeTitle = 'title';
  const String fakeContent = 'content';
  const String fakeTextPositive = 'positive';
  const String fakeTextNegative = 'negative';

  final ForceUpdateUIModel uiModel = ForceUpdateUIModel(
    dialogId: EvoDialogId.newAppVersionBottomSheet,
    imageHeader: Container(),
    title: fakeTitle,
    content: fakeContent,
    textPositive: fakeTextPositive,
    textNegative: fakeTextNegative,
    onClickPositive: () {},
    onClickNegative: () {},
  );

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(EvoEventType.consentAgreed);

    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterColor();
    getItRegisterButtonStyle();

    getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());
    featureToggle = getIt<FeatureToggle>();

    getIt.registerLazySingleton<AppState>(() => MockAppState());
    appState = getIt<AppState>();

    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());

    getIt.registerLazySingleton<EvoOverlayUtilFunctions>(() => MockEvoOverlayUtilFunctions());
    mockEvoOverlayUtilFunctions = getIt.get<EvoOverlayUtilFunctions>();

    getIt.registerLazySingleton<EventTrackingUtils>(() => MockEventTrackingUtils());

    when(() => mockEvoOverlayUtilFunctions.hideTermAndConditionOverlay())
        .thenAnswer((_) async => Future<void>.value());

    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    loggingRepo = getIt.get<LoggingRepo>();

    when(() => loggingRepo.logEvent(
          eventType: any(named: 'eventType'),
          data: any(named: 'data'),
        )).thenAnswer((_) async => Future<void>.value());

    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => MockEvoLocalStorageHelper());
    evoLocalStorageHelper = getIt.get<EvoLocalStorageHelper>();
    when(() => evoLocalStorageHelper.setConsentAgreed(any())).thenAnswer(
      (_) async => Future<void>.value(),
    );

    getIt.registerLazySingleton<NetworkManager>(() => MockNetworkManager());
    networkManager = getIt.get<NetworkManager>();

    when(() => networkManager.hasInternet).thenReturn(true);
  });

  setUp(() {
    getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());

    when(() => featureToggle.enableEventTrackingFeature).thenReturn(true);
    when(() => appState.currentScreenId).thenReturn(EvoEventTrackingScreenId.nonUserHomeScreen);

    getIt.registerLazySingleton<EvoEventTrackingUtils>(() => MockEvoEventTrackingUtils());
    evoEventTrackingUtils = getIt.get<EvoEventTrackingUtils>();
    when(
      () => evoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: any(named: 'eventActionId'),
          metaData: any(named: 'metaData'),
          screenId: any(named: 'screenId')),
    ).thenAnswer(
      (_) async => Future<void>.value(),
    );

    evoImageProvider = getIt.get<CommonImageProvider>();
    when(() => evoImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  });

  tearDown(() {
    reset(appState);
    reset(featureToggle);
    reset(evoEventTrackingUtils);
    getIt.unregister<GlobalKeyProvider>();
    getIt.unregister<EvoEventTrackingUtils>();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('EvoOverlayUtilFunctions Tests', () {
    const String overlayText = 'show overlay';

    setUp(() {
      evoOverlayUtilFunction = EvoOverlayUtilFunctions();

      when(
        () => evoEventTrackingUtils.sendEvoUserEvent(
          eventActionId: any(named: 'eventActionId'),
          metaData: any(named: 'metaData'),
        ),
      ).thenAnswer((_) async {
        return Future<void>.value();
      });
    });

    testWidgets('TermAndConditionOverlay with disable event tracking', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        builder: TermAndConditionOverlay().init(),
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () async {
                  await TermAndConditionUtils().showTermAndConditionOverlay();
                },
                child: const Text(overlayText),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text(overlayText));
      await tester.pumpAndSettle(const Duration(seconds: 1));

      final Finder widgetFinder = find.byType(TermAndConditionOverlayWidget);
      expect(widgetFinder, findsOneWidget);

      expect(
        verify(
          () => evoEventTrackingUtils.sendEvoSpecialEvent(
            eventActionId: EventTrackingDialogBehaviorType.show.eventId,
            metaData: captureAny(named: 'metaData'),
            screenId: EvoEventTrackingScreenId.nonUserHomeScreen,
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              EvoDialogId.termAndConditionDialog.id,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              DialogType.dialog.type,
            ),
      );

      when(() => featureToggle.enableEventTrackingFeature).thenAnswer((_) => false);
      // verify log event when touch CTA button
      await tester.tap(find.byType(CommonButton));
      await tester.pumpAndSettle();

      verifyNever(
        () => evoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: EventTrackingDialogBehaviorType.show.eventId,
          metaData: captureAny(named: 'metaData'),
          screenId: EvoEventTrackingScreenId.nonUserHomeScreen,
        ),
      );
    });

    testWidgets('showForceUpdateOverlay', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        builder: ForceUpdateOverlay.instance.init(),
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () async {
                  await evoOverlayUtilFunction.showForceUpdateOverlay(uiModel);
                },
                child: const Text(overlayText),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text(overlayText));
      await tester.pumpAndSettle(const Duration(seconds: 1));

      final Finder widgetFinder = find.byType(ForceUpdateOverlayWidget);
      expect(widgetFinder, findsOneWidget);

      // verify log event when show pop-up
      expect(
        verify(
          () => evoEventTrackingUtils.sendEvoSpecialEvent(
            eventActionId: EventTrackingDialogBehaviorType.show.eventId,
            metaData: captureAny(named: 'metaData'),
            screenId: EvoEventTrackingScreenId.nonUserHomeScreen,
          ),
        ).captured.first,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              EvoDialogId.newAppVersionBottomSheet.id,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              DialogType.bottomSheet.type,
            ),
      );

      // verify log event when touch CTA button
      await tester.tap(find.byType(CommonButton));
      await tester.pumpAndSettle();

      expect(
        verify(
          () => evoEventTrackingUtils.sendEvoSpecialEvent(
            eventActionId: EventTrackingDialogBehaviorType.clickPositive.eventId,
            metaData: captureAny(named: 'metaData'),
            screenId: EvoEventTrackingScreenId.nonUserHomeScreen,
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              EvoDialogId.newAppVersionBottomSheet.id,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              DialogType.bottomSheet.type,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['selected_content'],
              'check selected_content',
              fakeTextPositive,
            ),
      );
    });

    test('verify logEvent() with enableEventTrackingFeature is disable', () {
      when(() => featureToggle.enableEventTrackingFeature).thenAnswer((_) => false);

      evoOverlayUtilFunction.logEvent(
        dialogId: EvoDialogId.newAppVersionBottomSheet,
        dialogBehaviorType: EventTrackingDialogBehaviorType.show,
        dialogType: DialogType.bottomSheet,
        eventData: <String, dynamic>{
          EvoEventMetadataKey.title: fakeTitle,
          EvoEventMetadataKey.content: fakeContent,
        },
      );

      verifyNever(() => evoEventTrackingUtils.sendEvoSpecialEvent(
            eventActionId: EventTrackingDialogBehaviorType.show.eventId,
            metaData: captureAny(named: 'metaData'),
            screenId: EvoEventTrackingScreenId.nonUserHomeScreen,
          ));
    });

    test('verify logEvent() with enableEventTrackingFeature is enable', () {
      when(() => featureToggle.enableEventTrackingFeature).thenAnswer((_) => true);

      evoOverlayUtilFunction.logEvent(
        dialogId: EvoDialogId.newAppVersionBottomSheet,
        dialogBehaviorType: EventTrackingDialogBehaviorType.show,
        dialogType: DialogType.bottomSheet,
        eventData: <String, dynamic>{
          EvoEventMetadataKey.title: fakeTitle,
          EvoEventMetadataKey.content: fakeContent,
        },
      );

      expect(
        verify(
          () => evoEventTrackingUtils.sendEvoSpecialEvent(
            eventActionId: EventTrackingDialogBehaviorType.show.eventId,
            metaData: captureAny(named: 'metaData'),
            screenId: any(named: 'screenId'),
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> metaData) => metaData['dialog_id'],
              'check id',
              EvoDialogId.newAppVersionBottomSheet.id,
            )
            .having(
              (Map<String, dynamic> metaData) => metaData['ui_type'],
              'check ui_type',
              DialogType.bottomSheet.type,
            ),
      );
    });
  });

  group('verify hideTermAndConditionOverlay()', () {
    final EvoOverlayUtilFunctions evoOverlayUtilFunction = EvoOverlayUtilFunctions();

    setUpAll(() {
      TermAndConditionOverlay.setInstanceForTesting(MockTermAndConditionOverlay());
      when(() => TermAndConditionOverlay().dismiss()).thenAnswer((_) async => Future<void>.value());
    });

    test('verify hideTermAndConditionOverlay()', () {
      evoOverlayUtilFunction.hideTermAndConditionOverlay();

      verify(() => TermAndConditionOverlay().dismiss()).called(1);
    });
  });
}
