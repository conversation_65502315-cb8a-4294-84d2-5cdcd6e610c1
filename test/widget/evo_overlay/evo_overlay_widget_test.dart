import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/widget/evo_overlay/evo_overlay_widget.dart';
import 'package:evoapp/widget/evo_overlay/widgets/evo_overlay_entry.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeText = 'fakeText';
  bool isOverlayEntryCallback = false;
  bool hasBuilderWidget = false;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());
  });

  tearDownAll(() {
    getIt.reset();
  });

  testWidgets('test EvoOverlayWidget widget', (WidgetTester tester) async {
    await tester.pumpWidget(MaterialApp(
      navigatorKey: getIt.get<GlobalKeyProvider>().navigator<PERSON>ey,
      scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
      home: Scaffold(
        body: Builder(
          builder: (BuildContext context) {
            return EvoOverlayWidget(
              overlayChild: const Text(fakeText),
              overlayEntryCallback: (EvoOverlayEntry evoOverlayEntry) {
                isOverlayEntryCallback = true;
              },
              builder: (BuildContext context) {
                hasBuilderWidget = true;
                return Container();
              },
            );
          },
        ),
      ),
    ));

    final Finder overlayChildFinder = find.text(fakeText);
    expect(overlayChildFinder, findsOneWidget);

    expect(isOverlayEntryCallback, true);
    expect(hasBuilderWidget, true);
  });
}
