import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/evo_divider_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('EvoDividerWidget', () {
    testWidgets('should render with default color when no color is provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: EvoDividerWidget(),
        ),
      );

      final Finder containerFinder = find.byType(Container).first;
      final Container containerWidget = tester.widget<Container>(containerFinder);
      expect(containerWidget.color, evoColors.secondaryBackground);
    });

    testWidgets('should render with provided color', (WidgetTester tester) async {
      const Color testColor = Colors.red;
      await tester.pumpWidget(
        const MaterialApp(
          home: EvoDividerWidget(color: testColor),
        ),
      );

      final Finder containerFinder = find.byType(Container).first;
      final Container containerWidget = tester.widget<Container>(containerFinder);
      expect(containerWidget.color, testColor);
    });

    testWidgets('should render with provided height', (WidgetTester tester) async {
      const double testHeight = 2.0;
      await tester.pumpWidget(
        const MaterialApp(
          home: EvoDividerWidget(height: testHeight),
        ),
      );

      final Finder containerFinder = find.byType(Container).first;
      final Container containerWidget = tester.widget<Container>(containerFinder);
      expect(containerWidget.constraints?.maxHeight, testHeight);
    });

    testWidgets('should render with provided margin', (WidgetTester tester) async {
      const EdgeInsets testMargin = EdgeInsets.all(10.0);
      await tester.pumpWidget(
        const MaterialApp(
          home: EvoDividerWidget(margin: testMargin),
        ),
      );

      final Finder containerFinder = find.byType(Container).first;
      final Container containerWidget = tester.widget<Container>(containerFinder);
      expect(containerWidget.margin, testMargin);
    });

    testWidgets('should render with default width as infinity', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: EvoDividerWidget(),
        ),
      );

      final Finder containerFinder = find.byType(Container).first;
      final Container containerWidget = tester.widget<Container>(containerFinder);
      expect(containerWidget.constraints?.maxWidth, double.infinity);
    });
  });
}
