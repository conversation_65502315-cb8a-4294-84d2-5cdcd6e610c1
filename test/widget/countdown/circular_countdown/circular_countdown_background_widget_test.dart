import 'package:evoapp/widget/countdown/circular_countdown/circular_countdown_background_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets(
    'CircularCountdownBackgroundWidget displays with default size and colors',
    (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CircularCountdownBackgroundWidget(
              size: null,
              centerColor: null,
              edgeColor: null,
            ),
          ),
        ),
      );

      final Container container = tester.widget<Container>(find.byType(Container));
      final BoxDecoration decoration = container.decoration as BoxDecoration;
      final RadialGradient gradient = decoration.gradient as RadialGradient;

      expect(container.constraints?.maxWidth, 100);
      expect(container.constraints?.maxHeight, 100);
      expect(gradient.colors, <Color>[Colors.white, Colors.white]);
    },
  );

  testWidgets(
    'CircularCountdownBackgroundWidget displays with custom size and colors',
    (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CircularCountdownBackgroundWidget(
              size: 150,
              centerColor: Colors.red,
              edgeColor: Colors.blue,
            ),
          ),
        ),
      );

      final Container container = tester.widget<Container>(find.byType(Container));
      final BoxDecoration decoration = container.decoration as BoxDecoration;
      final RadialGradient gradient = decoration.gradient as RadialGradient;

      expect(container.constraints?.maxWidth, 150);
      expect(container.constraints?.maxHeight, 150);
      expect(gradient.colors, <MaterialColor>[Colors.red, Colors.blue]);
    },
  );
}
