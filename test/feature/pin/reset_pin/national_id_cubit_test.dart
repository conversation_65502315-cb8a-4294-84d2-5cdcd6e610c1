import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:evoapp/feature/pin/reset_pin/national_id/national_id_cubit.dart';
import 'package:evoapp/feature/pin/reset_pin/national_id/national_id_state.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class AuthenticationRepoMock extends Mock implements AuthenticationRepo {}

void main() {
  late AuthenticationRepoMock authenticationRepoMock;

  setUpAll(() {
    authenticationRepoMock = AuthenticationRepoMock();

    getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
  });

  NationalIdCubit getCubit() {
    return NationalIdCubit(authenticationRepoMock);
  }

  group('test checkLengthNationalID function', () {
    blocTest<NationalIdCubit, NationalIdState>(
      'emit valid state when nation id len is min (9 char) ',
      setUp: () {},
      build: () => getCubit(),
      act: (NationalIdCubit cubit) => cubit.checkLengthNationalID('123123123'),
      expect: () => <dynamic>[
        isA<NationalIdValidate>().having(
          (NationalIdValidate state) => state.isValid,
          'validate isValid value must true',
          true,
        ),
      ],
    );

    blocTest<NationalIdCubit, NationalIdState>(
      'emit valid state when nation id len is max (12 char) ',
      setUp: () {},
      build: () => getCubit(),
      act: (NationalIdCubit cubit) => cubit.checkLengthNationalID('123123123123'),
      expect: () => <dynamic>[
        isA<NationalIdValidate>().having(
          (NationalIdValidate state) => state.isValid,
          'validate isValid value must true',
          true,
        ),
      ],
    );

    blocTest<NationalIdCubit, NationalIdState>(
      'emit invalid state when nation id len is not min (9 char) or max (12 char)',
      setUp: () {},
      build: () => getCubit(),
      act: (NationalIdCubit cubit) => cubit.checkLengthNationalID('1231231231231'),
      expect: () => <dynamic>[
        isA<NationalIdValidate>().having(
          (NationalIdValidate state) => state.isValid,
          'validate isValid value must false',
          false,
        ),
      ],
    );
  });

  group('test pushError function', () {
    const String errorMessage = 'errorMessage';

    blocTest<NationalIdCubit, NationalIdState>(
      'emit correct state with error message',
      setUp: () {},
      build: () => getCubit(),
      act: (NationalIdCubit cubit) => cubit.pushError(errorMessage),
      expect: () => <dynamic>[
        isA<NationalIdError>().having(
          (NationalIdError state) => state.errorUIModel.userMessage,
          'validate error message',
          errorMessage,
        ),
      ],
    );
  });

  group('test validateNationalId function', () {
    const String newNationId = '123123123';
    const String sessionToken = 'sessionToken';

    blocTest<NationalIdCubit, NationalIdState>(
      'emit NationalIdError state when api call is fail',
      setUp: () {
        when(() => authenticationRepoMock.requestResetPin(
              ResetPinType.verifyNationalId,
              nationalId: newNationId,
              sessionToken: sessionToken,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return ResetPinEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.UNKNOWN_ERRORS,
            response: <String, dynamic>{'statusCode': CommonHttpClient.BAD_REQUEST},
          ));
        });
      },
      build: () => getCubit(),
      act: (NationalIdCubit cubit) => cubit.validateNationalId(
        newNationId,
        sessionToken: sessionToken,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<NationalIdLoading>(),
        isA<NationalIdError>().having(
          (NationalIdError state) => state.errorUIModel.statusCode,
          'verify error statusCode value is UNKNOWN_ERRORS',
          CommonHttpClient.UNKNOWN_ERRORS,
        ),
      ],
    );

    blocTest<NationalIdCubit, NationalIdState>(
      'emit NationalIdLoaded state when api call is success',
      setUp: () {
        when(() => authenticationRepoMock.requestResetPin(
              ResetPinType.verifyNationalId,
              nationalId: newNationId,
              sessionToken: sessionToken,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return ResetPinEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: <String, dynamic>{'verdict': 'success'},
          ));
        });
      },
      build: () => getCubit(),
      act: (NationalIdCubit cubit) => cubit.validateNationalId(
        newNationId,
        sessionToken: sessionToken,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<NationalIdLoading>(),
        isA<NationalIdLoaded>().having(
          (NationalIdLoaded state) => state.entity?.verdict,
          'verify verdict',
          'success',
        ),
      ],
    );
  });
}
