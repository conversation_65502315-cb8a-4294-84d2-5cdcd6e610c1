import 'package:evoapp/data/response/sign_in_otp_entity.dart';
import 'package:evoapp/feature/pin/pin_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PinState', () {
    test('PinNotFullState should be an instance of PinState', () {
      expect(PinNotFullState(), isA<PinState>());
    });

    test('PinFullState should be an instance of PinState', () {
      expect(PinFullState(), isA<PinState>());
    });

    test('PinLoadingState should be an instance of PinState', () {
      expect(PinLoadingState(), isA<PinState>());
    });

    test('PinValidatedState should be an instance of PinState', () {
      expect(PinValidatedState(), isA<PinState>());
    });

    test('PinLoadedState should be an instance of PinState', () {
      const String pinCode = '1234';
      final SignInOtpEntity entity = SignInOtpEntity();
      final PinLoadedState state = PinLoadedState(pinCode: pinCode, entity: entity);

      expect(state, isA<PinState>());
      expect(state.pinCode, pinCode);
      expect(state.entity, entity);
    });

    test('PinErrorState should be an instance of PinState', () {
      final ErrorUIModel errorUIModel = ErrorUIModel();
      final PinErrorState state = PinErrorState(errorUIModel);

      expect(state, isA<PinState>());
      expect(state.errorUIModel, errorUIModel);
    });

    test('RequestBiometricState should be an instance of PinState', () {
      expect(RequestBiometricState(), isA<PinState>());
    });

    test('ResetPinSuccessState should be an instance of PinState', () {
      expect(ResetPinSuccessState(), isA<PinState>());
    });
  });
}
