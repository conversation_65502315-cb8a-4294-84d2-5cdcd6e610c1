import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/sign_in_otp_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/pin/input_pin/input_pin_cubit.dart';
import 'package:evoapp/feature/pin/input_pin/input_pin_screen.dart';
import 'package:evoapp/feature/pin/pin_state.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';
import '../../../util/test_util.dart';

class AuthenticationRepoMock extends Mock implements AuthenticationRepo {}

class EvoLocalStorageHelperMock extends Mock implements EvoLocalStorageHelper {}

class MockAppState extends Mock implements AppState {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  late AuthenticationRepoMock authenticationRepoMock;
  late EvoLocalStorageHelperMock evoLocalStorageHelperMock;
  late AppState mockAppState;
  late EvoUtilFunction mockEvoUtilFunction;

  setUpAll(() {
    registerFallbackValue(FacialVerificationVersion.version_3);
    TestWidgetsFlutterBinding.ensureInitialized();
    authenticationRepoMock = AuthenticationRepoMock();
    evoLocalStorageHelperMock = EvoLocalStorageHelperMock();

    getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
    getIt.registerLazySingleton<AppState>(() => MockAppState());
    mockAppState = getIt.get<AppState>();
    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    when(() => mockEvoUtilFunction.updateProcessUserStatus(any())).thenAnswer((_) async {
      return Future<void>.value();
    });
  });

  setUp(() {
    when(() => mockEvoUtilFunction.validateMaxLengthPin(any())).thenReturn(true);
    when(() => mockEvoUtilFunction.getFacialVerificationVersion()).thenReturn(
      FacialVerificationVersion.version_3,
    );
  });

  tearDown(() {
    reset(mockEvoUtilFunction);
  });

  InputPinCubit getCubit() {
    return InputPinCubit(
      authenticationRepoMock,
      evoLocalStorageHelperMock,
    );
  }

  group('test validateLengthPin function', () {
    blocTest<InputPinCubit, PinState>(
      'emit PinFullState state pin len is valid',
      build: () => getCubit(),
      act: (InputPinCubit cubit) => cubit.validateLengthPin('123456'),
      expect: () => <dynamic>[isA<PinFullState>()],
    );

    blocTest<InputPinCubit, PinState>(
      'emit BiometricChangedState state when pin len is not valid',
      build: () => getCubit(),
      setUp: () {
        when(() => mockEvoUtilFunction.validateMaxLengthPin(any())).thenReturn(false);
      },
      act: (InputPinCubit cubit) => cubit.validateLengthPin('12345'),
      expect: () => <dynamic>[isA<PinNotFullState>()],
    );
  });

  group('test verify pin function', () {
    const String phoneNumber = '0355089290';
    const String pinCode = '123456';
    const String sessionToken = 'sessionToken';

    blocTest<InputPinCubit, PinState>(
      'test with login success status revoke_deletion',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('input_pin_success_revoke_deletion.json');
        when(() => authenticationRepoMock.login(
              TypeLogin.pin,
              phoneNumber: phoneNumber,
              pin: pinCode,
              sessionToken: sessionToken,
              facialVerificationVersion: any(named: 'facialVerificationVersion'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return SignInOtpEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: responseData,
            ),
          );
        });
      },
      build: () => getCubit(),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (InputPinCubit cubit) => cubit.verifyPin(
        phoneNumber: phoneNumber,
        pinCode: pinCode,
        sessionToken: sessionToken,
      ),
      expect: () => <dynamic>[
        isA<PinLoadingState>(),
        isA<PinLoadedState>().having(
          (PinLoadedState state) => state.pinCode,
          'verify pin code',
          pinCode,
        ),
      ],
      verify: (_) {
        verify(() => mockEvoUtilFunction.updateProcessUserStatus('revoke_deletion')).called(1);
      },
    );

    blocTest<InputPinCubit, PinState>(
      'test with login success status normal',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('input_pin_success_normal.json');
        when(() => authenticationRepoMock.login(
              TypeLogin.pin,
              phoneNumber: phoneNumber,
              pin: pinCode,
              sessionToken: sessionToken,
              facialVerificationVersion: any(named: 'facialVerificationVersion'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return SignInOtpEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: responseData,
            ),
          );
        });
      },
      build: () => getCubit(),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (InputPinCubit cubit) => cubit.verifyPin(
        phoneNumber: phoneNumber,
        pinCode: pinCode,
        sessionToken: sessionToken,
      ),
      expect: () => <dynamic>[
        isA<PinLoadingState>(),
        isA<PinLoadedState>().having(
          (PinLoadedState state) => state.pinCode,
          'verify pin code',
          pinCode,
        ),
      ],
      verify: (_) {
        verify(() => mockEvoUtilFunction.updateProcessUserStatus('normal')).called(1);
      },
    );

    blocTest<InputPinCubit, PinState>(
      'test with login success status unhandled',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('input_pin_success_unhandled.json');
        when(() => authenticationRepoMock.login(
              TypeLogin.pin,
              phoneNumber: phoneNumber,
              pin: pinCode,
              sessionToken: sessionToken,
              facialVerificationVersion: any(named: 'facialVerificationVersion'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return SignInOtpEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: responseData,
            ),
          );
        });
      },
      build: () => getCubit(),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (InputPinCubit cubit) => cubit.verifyPin(
        phoneNumber: phoneNumber,
        pinCode: pinCode,
        sessionToken: sessionToken,
      ),
      expect: () => <dynamic>[
        isA<PinLoadingState>(),
        isA<PinLoadedState>().having(
          (PinLoadedState state) => state.pinCode,
          'verify pin code',
          pinCode,
        ),
      ],
      verify: (_) {
        verify(() => mockEvoUtilFunction.updateProcessUserStatus('unhandled_status')).called(1);
      },
    );

    blocTest<InputPinCubit, PinState>(
      'test with login fail',
      setUp: () {
        when(() => authenticationRepoMock.login(
              TypeLogin.pin,
              phoneNumber: phoneNumber,
              pin: pinCode,
              sessionToken: sessionToken,
              facialVerificationVersion: any(named: 'facialVerificationVersion'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return SignInOtpEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: <String, dynamic>{'statusCode': CommonHttpClient.BAD_REQUEST},
            ),
          );
        });
      },
      build: () => getCubit(),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (InputPinCubit cubit) => cubit.verifyPin(
        phoneNumber: phoneNumber,
        pinCode: pinCode,
        sessionToken: sessionToken,
      ),
      expect: () => <dynamic>[
        isA<PinLoadingState>(),
        isA<PinErrorState>().having(
          (PinErrorState state) => state.errorUIModel.statusCode,
          'verify error code',
          CommonHttpClient.BAD_REQUEST,
        ),
      ],
      verify: (_) {
        verifyNever(() => mockAppState.loginSharedData);
      },
    );

    blocTest<InputPinCubit, PinState>(
      'test with entryPoint = InputPinEntryPoint.fromDOE',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('sign_in_from_dop_success.json');
        when(() => authenticationRepoMock.loginFromDOE(
              type: TypeLogin.pin,
              pin: pinCode,
              sessionToken: sessionToken,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return SignInOtpEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: responseData,
            ),
          );
        });
      },
      build: () => getCubit(),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (InputPinCubit cubit) => cubit.verifyPin(
        phoneNumber: phoneNumber,
        pinCode: pinCode,
        sessionToken: sessionToken,
        entryPoint: InputPinEntryPoint.fromDOE,
      ),
      expect: () => <dynamic>[
        isA<PinLoadingState>(),
        isA<PinLoadedState>().having(
          (PinLoadedState state) => state.pinCode,
          'verify pin code',
          pinCode,
        ),
      ],
      verify: (_) {
        verify(() => authenticationRepoMock.loginFromDOE(
              pin: pinCode,
              type: TypeLogin.pin,
              sessionToken: sessionToken,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );
  });

  group('test loginWithAnotherPhoneNumber function', () {
    test('should delete all local data when login with another phone number', () async {
      when(() => evoLocalStorageHelperMock.clearAllUserData()).thenAnswer((_) async {});

      await getCubit().loginWithAnotherPhoneNumber();

      verify(() => evoLocalStorageHelperMock.clearAllUserData()).called(1);
    });
  });

  test('verify setPhoneNumber', () {
    const String phoneNumber = 'fake_phone';
    when(() => evoLocalStorageHelperMock.setUserPhoneNumber(any())).thenAnswer((_) async {});

    getCubit().setPhoneNumber(phoneNumber);

    verify(() => evoLocalStorageHelperMock.setUserPhoneNumber(phoneNumber)).called(1);
  });
}
