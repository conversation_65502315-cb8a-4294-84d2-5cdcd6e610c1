import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/privacy_policy/cubit/storage_permission_cubit.dart';
import 'package:evoapp/feature/privacy_policy/cubit/storage_permission_state.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../constant.dart';

class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

void main() {
  late StoragePermissionCubit cubit;
  late EvoFlutterWrapper mockEvoFlutterWrapper;
  late CommonUtilFunction mockCommonUtilFunction;

  const String permissionHandlerMethodChannel = 'flutter.baseflow.com/permissions/methods';
  const String requestPermissionsMethod = 'requestPermissions';
  const String checkPermissionsStatusMethod = 'checkPermissionStatus';
  late MethodChannel channel;

  final AndroidDeviceInfo androidDeviceInfo30 = AndroidDeviceInfo.fromMap(<String, dynamic>{
    'version': <String, dynamic>{
      'sdkInt': 30,
      'baseOS': 'baseOS',
      'codename': 'codename',
      'incremental': 'incremental',
      'previewSdkInt': 30,
      'release': 'release',
      'securityPatch': 'securityPatch',
    },
    'board': '.board',
    'bootloader': '.bootloader',
    'brand': '.brand',
    'device': '.device',
    'display': '.display',
    'fingerprint': '.fingerprint',
    'hardware': '.hardware',
    'host': '.host',
    'id': '.id',
    'manufacturer': '.manufacturer',
    'model': '.model',
    'product': '.product',
    'supported32BitAbis': <String>[],
    'supported64BitAbis': <String>[],
    'supportedAbis': <String>[],
    'tags': '.tags',
    'type': '.type',
    'isPhysicalDevice': false,
    'systemFeatures': <String>[],
    'serialNumber': 'serialNumber',
    'isLowRamDevice': false,
  });

  final AndroidDeviceInfo androidDeviceInfo29 = AndroidDeviceInfo.fromMap(<String, dynamic>{
    'version': <String, dynamic>{
      'sdkInt': 29,
      'baseOS': 'baseOS',
      'codename': 'codename',
      'incremental': 'incremental',
      'previewSdkInt': 29,
      'release': 'release',
      'securityPatch': 'securityPatch',
    },
    'board': '.board',
    'bootloader': '.bootloader',
    'brand': '.brand',
    'device': '.device',
    'display': '.display',
    'fingerprint': '.fingerprint',
    'hardware': '.hardware',
    'host': '.host',
    'id': '.id',
    'manufacturer': '.manufacturer',
    'model': '.model',
    'product': '.product',
    'supported32BitAbis': <String>[],
    'supported64BitAbis': <String>[],
    'supportedAbis': <String>[],
    'tags': '.tags',
    'type': '.type',
    'isPhysicalDevice': false,
    'systemFeatures': <String>[],
    'serialNumber': 'serialNumber',
    'isLowRamDevice': false,
  });

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => MockEvoFlutterWrapper());
    mockEvoFlutterWrapper = getIt.get<EvoFlutterWrapper>();

    getIt.registerLazySingleton<CommonUtilFunction>(() => MockCommonUtilFunction());
    mockCommonUtilFunction = getIt.get<CommonUtilFunction>();

    channel = const MethodChannel(permissionHandlerMethodChannel);
  });

  setUp(() {
    cubit = StoragePermissionCubit();
  });

  tearDownAll(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      null,
    );
  });

  test('Default state is StoragePermissionInitialState', () {
    expect(cubit.state is StoragePermissionInitialState, true);
  });

  int convertPermissionStatusToValue(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.denied:
        return 0;
      case PermissionStatus.granted:
        return 1;
      case PermissionStatus.restricted:
        return 2;
      case PermissionStatus.limited:
        return 3;
      case PermissionStatus.permanentlyDenied:
        return 4;
      case PermissionStatus.provisional:
        return 5;
      default:
        throw UnimplementedError('PermissionStatusValue: $status is not supported');
    }
  }

  blocTest<StoragePermissionCubit, StoragePermissionState>(
    'Test platform iOS, request storage permission denied',
    build: () => cubit,
    setUp: () {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(true);

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        if (methodCall.method == requestPermissionsMethod) {
          return <int, int>{
            Permission.storage.value: convertPermissionStatusToValue(PermissionStatus.denied)
          };
        }
        if (methodCall.method == checkPermissionsStatusMethod) {
          return convertPermissionStatusToValue(PermissionStatus.denied);
        }
        return null;
      });
    },
    act: (StoragePermissionCubit cubit) => cubit.requestStoragePermission(),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<StoragePermissionLoadingState>(),
      isA<StoragePermissionDeniedState>(),
    ],
  );

  blocTest<StoragePermissionCubit, StoragePermissionState>(
    'Test platform iOS, request storage permission granted',
    build: () => cubit,
    setUp: () {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(true);

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        if (methodCall.method == requestPermissionsMethod) {
          return <int, int>{
            Permission.storage.value: convertPermissionStatusToValue(PermissionStatus.granted)
          };
        }
        if (methodCall.method == checkPermissionsStatusMethod) {
          return convertPermissionStatusToValue(PermissionStatus.denied);
        }
        return null;
      });
    },
    act: (StoragePermissionCubit cubit) => cubit.requestStoragePermission(),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<StoragePermissionLoadingState>(),
      isA<StoragePermissionGrantedState>(),
    ],
  );

  blocTest<StoragePermissionCubit, StoragePermissionState>(
    'Test platform iOS, request storage permission permanentlyDenied',
    build: () => cubit,
    setUp: () {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(true);

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        if (methodCall.method == requestPermissionsMethod) {
          return <int, int>{
            Permission.storage.value:
                convertPermissionStatusToValue(PermissionStatus.permanentlyDenied)
          };
        }
        if (methodCall.method == checkPermissionsStatusMethod) {
          return convertPermissionStatusToValue(PermissionStatus.denied);
        }
        return null;
      });
    },
    act: (StoragePermissionCubit cubit) => cubit.requestStoragePermission(),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<StoragePermissionLoadingState>(),
      isA<StoragePermissionDeniedState>(),
    ],
  );

  blocTest<StoragePermissionCubit, StoragePermissionState>(
    'Test platform Android, sdk version 30, no need to request permission',
    build: () => cubit,
    setUp: () {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

      when(() => mockCommonUtilFunction.getAndroidInfo()).thenAnswer((_) async {
        return Future<AndroidDeviceInfo>.value(androidDeviceInfo30);
      });
    },
    act: (StoragePermissionCubit cubit) => cubit.requestStoragePermission(),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<StoragePermissionLoadingState>(),
      isA<StoragePermissionGrantedState>(),
    ],
  );

  blocTest<StoragePermissionCubit, StoragePermissionState>(
    'Test platform Android, sdk version 29, request storage permission denied',
    build: () => cubit,
    setUp: () {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

      when(() => mockCommonUtilFunction.getAndroidInfo()).thenAnswer((_) async {
        return Future<AndroidDeviceInfo>.value(androidDeviceInfo29);
      });

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        if (methodCall.method == requestPermissionsMethod) {
          return <int, int>{
            Permission.storage.value: convertPermissionStatusToValue(PermissionStatus.denied)
          };
        }
        if (methodCall.method == checkPermissionsStatusMethod) {
          return convertPermissionStatusToValue(PermissionStatus.denied);
        }
        return null;
      });
    },
    act: (StoragePermissionCubit cubit) => cubit.requestStoragePermission(),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<StoragePermissionLoadingState>(),
      isA<StoragePermissionDeniedState>(),
    ],
  );

  blocTest<StoragePermissionCubit, StoragePermissionState>(
    'Test platform Android, sdk version 29, request storage permission granted',
    build: () => cubit,
    setUp: () {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

      when(() => mockCommonUtilFunction.getAndroidInfo()).thenAnswer((_) async {
        return Future<AndroidDeviceInfo>.value(androidDeviceInfo29);
      });

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        if (methodCall.method == requestPermissionsMethod) {
          return <int, int>{
            Permission.storage.value: convertPermissionStatusToValue(PermissionStatus.granted)
          };
        }
        if (methodCall.method == checkPermissionsStatusMethod) {
          return convertPermissionStatusToValue(PermissionStatus.denied);
        }
        return null;
      });
    },
    act: (StoragePermissionCubit cubit) => cubit.requestStoragePermission(),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<StoragePermissionLoadingState>(),
      isA<StoragePermissionGrantedState>(),
    ],
  );

  blocTest<StoragePermissionCubit, StoragePermissionState>(
    'Test platform Android, sdk version 29, request storage permission permanentlyDenied',
    build: () => cubit,
    setUp: () {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

      when(() => mockCommonUtilFunction.getAndroidInfo()).thenAnswer((_) async {
        return Future<AndroidDeviceInfo>.value(androidDeviceInfo29);
      });

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        if (methodCall.method == requestPermissionsMethod) {
          return <int, int>{
            Permission.storage.value:
                convertPermissionStatusToValue(PermissionStatus.permanentlyDenied)
          };
        }
        if (methodCall.method == checkPermissionsStatusMethod) {
          return convertPermissionStatusToValue(PermissionStatus.denied);
        }
        return null;
      });
    },
    act: (StoragePermissionCubit cubit) => cubit.requestStoragePermission(),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<StoragePermissionLoadingState>(),
      isA<StoragePermissionDeniedState>(),
    ],
  );

  test('Test unknown platform, should throw exception', () {
    when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
    when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);
    expect(
        () => cubit.requestStoragePermission(),
        throwsA(isA<UnsupportedError>().having(
          (UnsupportedError error) => error.message,
          'error message',
          'Unsupported platform',
        )));
  });
}
