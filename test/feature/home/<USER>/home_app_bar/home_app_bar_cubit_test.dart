import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/home_screen/home_widgets/home_app_bar/home_app_bar_cubit.dart';
import 'package:evoapp/feature/home_screen/home_widgets/home_app_bar/home_app_bar_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('test loaded() function', () {
    blocTest<HomeAppBarCubit, HomeAppBarState>(
      'test loaded emit HomeAppBarLoaded state with hasHeroBanner is true',
      build: () => HomeAppBarCubit(),
      act: (HomeAppBarCubit cubit) => cubit.loaded(true),
      expect: () => <dynamic>[
        isA<HomeAppBarLoaded>().having(
            (HomeAppBarLoaded data) => data.hasHeroBanner, 'test hasHeroBanner is true', isTrue),
      ],
    );

    blocTest<HomeAppBarCubit, HomeAppBarState>(
      'test loaded emit HomeAppBarLoaded state with hasHeroBanner is false',
      build: () => HomeAppBarCubit(),
      act: (HomeAppBarCubit cubit) => cubit.loaded(false),
      expect: () => <dynamic>[
        isA<HomeAppBarLoaded>().having(
            (HomeAppBarLoaded data) => data.hasHeroBanner, 'test hasHeroBanner is false', isFalse),
      ],
    );
  });

  group('test loading() function', () {
    blocTest<HomeAppBarCubit, HomeAppBarState>(
      'test loading emit HomeAppBarLoading state with isLoading is true',
      build: () => HomeAppBarCubit(),
      act: (HomeAppBarCubit cubit) => cubit.loading(true),
      expect: () => <dynamic>[
        isA<HomeAppBarLoading>().having(
          (HomeAppBarLoading data) => data.isLoading,
          'test isLoading is true',
          true,
        ),
      ],
    );

    blocTest<HomeAppBarCubit, HomeAppBarState>(
      'test loading emit HomeAppBarInitial state with isLoading is false',
      build: () => HomeAppBarCubit(),
      act: (HomeAppBarCubit cubit) => cubit.loading(false),
      expect: () => <dynamic>[
        isA<HomeAppBarInitial>(),
      ],
    );
  });
}
