import 'package:evoapp/feature/home_screen/non_user/v2/mock/mock_home_page_stories_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('getMockHomePageStoriesFileNameByCase should return correct file names', () {
    // Test for homepageWithStories case
    expect(
      getMockHomePageStoriesFileNameByCase(MockTestHomPageStoriesUseCase.homepageWithStories),
      'home_page_with_story.json',
    );

    // Test for homepageWithoutStories case
    expect(
      getMockHomePageStoriesFileNameByCase(MockTestHomPageStoriesUseCase.homepageWithoutStories),
      'home_page_without_story.json',
    );
  });
}
