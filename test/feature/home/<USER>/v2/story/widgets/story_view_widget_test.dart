import 'package:evoapp/feature/home_screen/non_user/v2/story/model/story_view_model.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/story_config.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/widgets/story_indicator/story_indicator_widget.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/widgets/story_view_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../util/flutter_test_config.dart';

class MockStoryIndicatorController extends Mock implements StoryIndicatorController {}

void main() {
  const MaterialColor expectedProgressBarBackgroundColor = Colors.red;
  const MaterialColor expectedProgressBarActiveColor = Colors.blue;
  final StoryIndicatorController mockStoryIndicatorController = MockStoryIndicatorController();

  final List<StoryViewModel> fakeModels = <StoryViewModel>[
    StoryViewModel(
      contentView: Text('fake_view_1'),
      headerView: Container(),
      footerView: Container(),
    ),
    StoryViewModel(
      contentView: Text('fake_view_2'),
      headerView: Container(),
      footerView: Container(),
      type: StoryViewType.isPlaying,
      durationInMs: 5000,
    ),
  ];

  final StoryViewController storyViewController = StoryViewController();

  setUpAll(() {
    getItRegisterColor();
    getIt.registerLazySingleton<GlobalKeyProvider>(() => GlobalKeyProvider());
    getIt.registerFactory<StoryIndicatorController>(() => mockStoryIndicatorController);
  });

  tearDown(() {
    reset(mockStoryIndicatorController);
  });

  testWidgets('Give storyItems is empty', (WidgetTester tester) async {
    await tester.pumpWidget(
      const MaterialApp(
        home: StoryViewWidget(
          storyItems: <StoryViewModel>[],
        ),
      ),
    );

    // Verify that only show SizedBox.shrink() when storyItems is empty
    expect(find.byType(SizedBox), findsOneWidget);

    expect(find.byType(StoryIndicatorWidget), findsNothing);
    expect(find.byType(PageView), findsNothing);
  });

  testWidgets('Give storyItems is NOT empty', (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(
        MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          home: StoryViewWidget(
            storyItems: fakeModels,
            progressBarBackgroundColor: expectedProgressBarBackgroundColor,
            progressBarActiveColor: expectedProgressBarActiveColor,
            controller: storyViewController,
          ),
        ),
      );

      /// Verify that show StoryIndicatorWidget and PageView when storyItems is NOT empty
      expect(find.byType(PageView), findsOneWidget);
      expect(find.byType(StoryIndicatorWidget), findsOneWidget);

      /// Verify parameters of PageView
      final PageView pageView = tester.widget(find.byType(PageView));
      expect(pageView.controller, isNotNull);
      expect(pageView.physics, isNull);
      expect(pageView.allowImplicitScrolling, isTrue);
      expect(
        pageView.childrenDelegate,
        isA<SliverChildBuilderDelegate>(),
      );

      /// Verify parameters of StoryIndicatorWidget
      final StoryIndicatorWidget storyIndicatorWidget =
          tester.widget(find.byType(StoryIndicatorWidget));
      expect(storyIndicatorWidget.stories, fakeModels);
      expect(storyIndicatorWidget.backgroundColor, expectedProgressBarBackgroundColor);
      expect(storyIndicatorWidget.activeColor, expectedProgressBarActiveColor);
      expect(storyIndicatorWidget.controller, isNotNull);
      expect(storyIndicatorWidget.animateToNextPage, isNotNull);

      /// Verify StoryIndicatorWidget callback action
      final int initialPage = fakeModels.length * 10;
      final PageController? pageController = pageView.controller;
      expect(pageController?.page, initialPage);
      storyIndicatorWidget.animateToNextPage?.call();
      await tester.pumpAndSettle();
      expect(pageController?.page, initialPage + 1);

      /// Verify indicator controller
      // Verify that to the indicator controller calls play function when init StoryViewWidget
      verify(() => mockStoryIndicatorController.play).called(1);

      // Verify user action on story view
      final Finder gestureDetectorFinder = find.byType(GestureDetector);
      expect(gestureDetectorFinder, findsOneWidget);
      final GestureDetector gestureDetector = tester.widget(gestureDetectorFinder);

      // Long press end will resume the story indicator
      gestureDetector.onLongPressEnd?.call(const LongPressEndDetails());
      verify(() => mockStoryIndicatorController.play).called(1);

      // Tap up on left/right side will previous/next the story indicator
      gestureDetector.onTapUp?.call(TapUpDetails(kind: PointerDeviceKind.unknown));
      await tester.pumpAndSettle();
      verify(() => mockStoryIndicatorController.previous).called(1);
      gestureDetector.onTapUp?.call(TapUpDetails(
        kind: PointerDeviceKind.unknown,
        globalPosition: Offset(navigatorContext!.size!.width, 100),
      ));
      verify(() => mockStoryIndicatorController.next).called(1);

      // onTapDown will pause the story indicator
      gestureDetector.onTapDown?.call(TapDownDetails());
      verify(() => mockStoryIndicatorController.pause).called(1);

      // Swipe left/right will previous/next the story indicator
      await tester.fling(find.byType(PageView), const Offset(-300.0, 0.0), 500);
      await tester.pump();
      await tester.pumpAndSettle();
      verify(() => mockStoryIndicatorController.next).called(1);
      await tester.fling(find.byType(PageView), const Offset(300.0, 0.0), 500);
      await tester.pump();
      await tester.pumpAndSettle();
      verify(() => mockStoryIndicatorController.previous).called(1);

      /// Verify StoryViewController
      storyViewController.pause?.call();
      verify(() => mockStoryIndicatorController.pause).called(1);
      storyViewController.replay?.call();
      verify(() => mockStoryIndicatorController.reset).called(1);
      storyViewController.resume?.call();
      verify(() => mockStoryIndicatorController.play).called(1);
    });
  });
}
