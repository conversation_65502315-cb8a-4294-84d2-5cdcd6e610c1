import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/model/story_view_model.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/story_config.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/widgets/story_indicator/story_indicator_cubit.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/widgets/story_indicator/story_indicator_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  final StoryViewModel storyViewModelPlaying = StoryViewModel(
    contentView: Container(),
    headerView: Container(),
    footerView: Container(),
    type: StoryViewType.isPlaying,
  );
  final StoryViewModel storyViewModelNoDisplay = StoryViewModel(
    contentView: Container(),
    headerView: Container(),
    footerView: Container(),
  );
  final StoryViewModel storyViewModelDisplayed = StoryViewModel(
    contentView: Container(),
    headerView: Container(),
    footerView: Container(),
    type: StoryViewType.isShown,
  );
  final List<StoryViewModel> expectedStoryItems = <StoryViewModel>[
    storyViewModelDisplayed,
    storyViewModelPlaying,
    storyViewModelNoDisplay,
  ];

  late StoryIndicatorCubit storyViewCubit;

  setUp(() {
    storyViewCubit = StoryIndicatorCubit();
  });

  test('verify init state', () {
    expect(storyViewCubit.state, isA<StoryIndicatorState>());
    expect(storyViewCubit.storyItems.isEmpty, true);
    expect(storyViewCubit.currentStoryIndex, 0);
  });

  test('verify lastIndexStory', () {
    storyViewCubit.storyItems.addAll(expectedStoryItems);
    expect(storyViewCubit.lastIndexStory, 2);
  });

  group('test nextStoryView() method', () {
    blocTest<StoryIndicatorCubit, StoryIndicatorState>(
      'verify nextStoryView() method with emit [NextStoryViewState] state',
      setUp: () {
        storyViewCubit.initStoryView(expectedStoryItems);
      },
      build: () => storyViewCubit,
      act: (StoryIndicatorCubit cubit) => cubit.nextStoryView(),
      expect: () => <dynamic>[
        isA<IndicatorNextState>()
            .having(
              (IndicatorNextState state) => state.currentStoryIndex,
              'verify currentStoryIndex is 1',
              1,
            )
            .having(
              (IndicatorNextState state) => state.storyItems.first.type,
              'the first story is type [StoryViewType.displayed]',
              StoryViewType.isShown,
            ),
      ],
    );

    blocTest<StoryIndicatorCubit, StoryIndicatorState>(
      'verify nextStoryView() method with emit [StoryViewCompletedState] state',
      setUp: () {
        storyViewCubit.initStoryView(expectedStoryItems);
        storyViewCubit.currentStoryIndex = storyViewCubit.lastIndexStory;
      },
      build: () => storyViewCubit,
      act: (StoryIndicatorCubit cubit) => cubit.nextStoryView(),
      expect: () => <dynamic>[
        isA<IndicatorCompleteState>(),
      ],
      verify: (StoryIndicatorCubit cubit) {
        /// verify currentStoryIndex is 0
        expect(cubit.currentStoryIndex, 0);

        // verify the first story is playing
        expect(cubit.storyItems.first.type, StoryViewType.isPlaying);

        /// verify all story is not showed
        for (int i = 1; i < cubit.storyItems.length - 1; i++) {
          final StoryViewModel storyItem = cubit.storyItems[i];
          expect(storyItem.type, StoryViewType.isHidden);
        }
      },
    );
  });

  group('test previousStoryView() method', () {
    blocTest<StoryIndicatorCubit, StoryIndicatorState>(
      'verify previousStoryView() method with emit [NextStoryViewState] state',
      setUp: () {
        storyViewCubit.initStoryView(expectedStoryItems);
        storyViewCubit.currentStoryIndex = 1;
      },
      build: () => storyViewCubit,
      act: (StoryIndicatorCubit cubit) => cubit.previousStoryView(),
      expect: () => <dynamic>[
        isA<IndicatorNextState>()
            .having(
              (IndicatorNextState state) => state.currentStoryIndex,
              'verify currentStoryIndex is 0',
              0,
            )
            .having(
              (IndicatorNextState state) => state.storyItems.first.type,
              'the first story is type [StoryViewType.isPlaying]',
              StoryViewType.isPlaying,
            ),
      ],
    );

    blocTest<StoryIndicatorCubit, StoryIndicatorState>(
        'verify previousStoryView() method with emit [NextStoryViewState] state when at first story',
        setUp: () {
          storyViewCubit.initStoryView(expectedStoryItems);
        },
        build: () => storyViewCubit,
        act: (StoryIndicatorCubit cubit) => cubit.previousStoryView(),
        expect: () => <dynamic>[
              isA<IndicatorNextState>()
                  .having(
                    (IndicatorNextState state) => state.currentStoryIndex,
                    'verify currentStoryIndex is last index',
                    storyViewCubit.lastIndexStory,
                  )
                  .having(
                    (IndicatorNextState state) => state.storyItems.last.type,
                    'the last story is type [StoryViewType.isPlaying]',
                    StoryViewType.isPlaying,
                  ),
            ],
        verify: (StoryIndicatorCubit cubit) {
          /// verify all stories (except the last) is showed
          for (int i = 0; i < cubit.storyItems.length - 1; i++) {
            final StoryViewModel storyItem = cubit.storyItems[i];
            expect(storyItem.type, StoryViewType.isShown);
          }
        });
  });
}
