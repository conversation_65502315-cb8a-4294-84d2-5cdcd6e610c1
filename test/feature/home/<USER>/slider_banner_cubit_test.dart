import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/campaign_repo.dart';
import 'package:evoapp/data/response/campaign_list_entity.dart';
import 'package:evoapp/feature/home_screen/home_widgets/home_slide_banner_group/home_slide_banner_cubit.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_component/ui_component.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';
import '../../../util/test_util.dart';

class MockCampaignRepo extends Mock implements CampaignRepo {}

void main() {
  late MockCampaignRepo mockCampaignRepo;
  late HomeSlideBannerCubit cubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockCampaignRepo = MockCampaignRepo();
  });

  group('test getHomeSpotlightCampaigns() function', () {
    setUp(() {
      cubit = HomeSlideBannerCubit(campaignRepo: mockCampaignRepo);
    });

    tearDown(() {
      cubit.close();
      reset(mockCampaignRepo);
    });

    blocTest<HomeSlideBannerCubit, UiComponentState>('test getHomeSpotlightCampaigns is success',
        setUp: () async {
          final Map<String, dynamic> responseData =
              await TestUtil.getResponseMock('campaigns_information.json');

          when(() => mockCampaignRepo.getOffers(
                flowType: FlowType.spotlight,
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async {
            return CampaignListEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
          });
        },
        build: () => cubit,
        act: (HomeSlideBannerCubit cubit) => cubit.getHomeSpotlightCampaigns(),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<UiComponentLoading>(),
              isA<UiComponentDataLoaded<CampaignListEntity>>()
                  .having(
                      (UiComponentDataLoaded<CampaignListEntity> entity) => entity.data?.statusCode,
                      'test statusCode success',
                      CommonHttpClient.SUCCESS)
                  .having(
                      (UiComponentDataLoaded<CampaignListEntity> entity) =>
                          entity.data?.campaigns?.isNotEmpty,
                      'test api return success and check has data',
                      isTrue),
            ],
        verify: (HomeSlideBannerCubit cubit) {
          verify(() => mockCampaignRepo.getOffers(
                flowType: FlowType.spotlight,
                mockConfig: any(named: 'mockConfig'),
              )).called(1);
        });

    blocTest<HomeSlideBannerCubit, UiComponentState>('test getHomeSpotlightCampaigns is fail',
        setUp: () async {
          when(() => mockCampaignRepo.getOffers(
                flowType: FlowType.spotlight,
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async {
            return CampaignListEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: <String, dynamic>{
              'status_code': CommonHttpClient.BAD_REQUEST,
            }));
          });
        },
        build: () => cubit,
        act: (HomeSlideBannerCubit cubit) => cubit.getHomeSpotlightCampaigns(),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<UiComponentLoading>(),
              isA<UiComponentFailed>().having(
                  (UiComponentFailed error) => error.errorUIModel.statusCode,
                  'test statusCode error',
                  CommonHttpClient.BAD_REQUEST),
            ],
        verify: (HomeSlideBannerCubit cubit) {
          verify(() => mockCampaignRepo.getOffers(
                flowType: FlowType.spotlight,
                mockConfig: any(named: 'mockConfig'),
              )).called(1);
        });
  });
}
