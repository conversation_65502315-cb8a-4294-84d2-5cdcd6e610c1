import 'package:evoapp/data/repository/announcement_repo.dart';
import 'package:evoapp/data/request/reward_request.dart';
import 'package:evoapp/data/response/announcement_list_entity.dart';
import 'package:evoapp/data/response/announcement_status_entity.dart';
import 'package:evoapp/feature/announcement/announcement_screen.dart';
import 'package:evoapp/feature/announcement/reward/reward_announcement_screen.dart';
import 'package:evoapp/feature/announcement/utils/mock_file/mock_announcement_file_name.dart';
import 'package:evoapp/feature/announcement/widget/announcement_item_widget.dart';
import 'package:evoapp/feature/announcement/widget/no_announcement_widget.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/model/evo_action_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_action_handler.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';
import '../../push_notification/notification_handler_test.dart';

void main() {
  late AnnouncementReloadController controller;
  late CommonNavigatorObserver mockNavigatorObserver;

  setUpAll(() {
    registerFallbackValue(EvoActionModel());
    registerFallbackValue(SnackBarType.neutral);
    registerFallbackValue(AnnouncementRequest(nextCursor: '', limit: null, status: ''));
  });

  setUp(() {
    EvoAuthenticationHelper.setInstanceForTesting(MockEvoAuthenticationHelper());
    EvoActionHandler.setInstanceForTesting(MockEvoActionHandler());
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
    mockNavigatorObserver = getIt.get<CommonNavigatorObserver>();
    when(() => mockNavigatorObserver.topStackIsAPageRoute()).thenAnswer((_) {
      return true;
    });
    when(() => EvoActionHandler().handle(
          any(),
          arg: any(named: 'arg'),
        )).thenAnswer((_) async {
      return true;
    });
    getIt.registerSingleton<AnnouncementRepo>(MockAnnouncementRepo());
    getIt.registerSingleton<FeatureToggle>(FeatureToggle());
    controller = AnnouncementReloadController();
    setupMockImageProvider();
    setUpOneLinkDeepLinkRegExForTest();
    setUpMockSnackBarForTest();

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenAnswer((_) => 10);
  });

  tearDown(() {
    EvoAuthenticationHelper.resetToOriginalInstance();
    EvoActionHandler.resetToOriginalInstance();
    getIt.reset();
  });

  Future<void> mockAnnouncementRepoSuccessResponse(WidgetTester tester) async {
    await tester.runAsync(() async {
      final BaseResponse listEntityResponse =
          await getMockBaseResponse(getAnnouncementsMockFileName(''));
      when(() => getIt<AnnouncementRepo>()
              .getAnnouncements(any(), mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async => AnnouncementListEntity.fromBaseResponse(listEntityResponse));
    });
  }

  Future<void> mockAnnouncementRepoEmptyResponse() async {
    when(() =>
            getIt<AnnouncementRepo>().getAnnouncements(any(), mockConfig: any(named: 'mockConfig')))
        .thenAnswer((_) async => AnnouncementListEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: <String, dynamic>{'data': <String, dynamic>{}})));
  }

  Future<void> mockAnnouncementRepoErrorResponse() async {
    when(() =>
            getIt<AnnouncementRepo>().getAnnouncements(any(), mockConfig: any(named: 'mockConfig')))
        .thenAnswer((_) async => AnnouncementListEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.UNKNOWN_ERRORS,
            response: <String, dynamic>{'data': <String, dynamic>{}})));
  }

  testWidgets('RewardAnnouncementScreen test ui elements when load data success',
      (WidgetTester tester) async {
    await mockAnnouncementRepoSuccessResponse(tester);
    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: RewardAnnouncementScreen(
          controller: controller,
        ),
      ),
    ));
    await tester.pumpAndSettle();

    final RewardAnnouncementScreenState state = tester.state(find.byType(RewardAnnouncementScreen));
    state.refreshController.footerMode?.value = LoadStatus.loading;

    expect(find.byType(AnnouncementItemWidget), findsWidgets);
    await tester.pumpWidget(const SizedBox.shrink());
    await tester.pumpAndSettle();
  });

  testWidgets('RewardAnnouncementScreen test onAnnouncementTapped', (WidgetTester tester) async {
    await mockAnnouncementRepoSuccessResponse(tester);

    // Mock response for updating read status
    when(() => getIt<AnnouncementRepo>().updateAnnouncementStatus(any(), any(),
        mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => AnnouncementStatusEntity());

    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: RewardAnnouncementScreen(controller: controller),
      ),
    ));
    await tester.pumpAndSettle();

    final RewardAnnouncementScreenState state = tester.state(find.byType(RewardAnnouncementScreen));
    state.refreshController.headerMode?.value = RefreshStatus.refreshing;

    // Find and tap the first announcement item
    await tester.tap(find.byType(AnnouncementItemWidget).first);
    await tester.pumpAndSettle();

    // Verify read status update was called
    verify(() => getIt<AnnouncementRepo>()
        .updateAnnouncementStatus(any(), any(), mockConfig: any(named: 'mockConfig'))).called(1);

    await tester.pumpWidget(const SizedBox.shrink());
    await tester.pumpAndSettle();
  });

  testWidgets('RewardAnnouncementScreen test ui element on empty response',
      (WidgetTester tester) async {
    await mockAnnouncementRepoEmptyResponse();

    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: RewardAnnouncementScreen(controller: controller),
      ),
    ));
    await tester.pumpAndSettle();
    expect(find.byType(NoAnnouncementWidget), findsOneWidget);

    await tester.pumpWidget(const SizedBox.shrink());
    await tester.pumpAndSettle();
  });

  testWidgets('RewardAnnouncementScreen test error response', (WidgetTester tester) async {
    await mockAnnouncementRepoErrorResponse();

    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: RewardAnnouncementScreen(controller: controller),
      ),
    ));
    await tester.pumpAndSettle();
    expect(find.byType(NoAnnouncementWidget), findsOneWidget);
    verify(() => getIt<EvoSnackBar>().show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInSec: any(named: 'durationInSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).called(1);

    await tester.pumpWidget(const SizedBox.shrink());
    await tester.pumpAndSettle();
  });
}
