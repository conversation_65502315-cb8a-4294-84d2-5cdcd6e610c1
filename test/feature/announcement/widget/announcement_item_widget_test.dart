import 'package:evoapp/data/response/announcement_entity.dart';
import 'package:evoapp/feature/announcement/widget/announcement_item_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/extension.dart';
import 'package:evoapp/widget/evo_image_provider_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider commonImageProvider;
  late EvoTextStyles evoTextStyles;
  late EvoColors evoColors;

  const String fakeUnreadImageUrl = 'fakeUnreadImageUrl';
  const String fakeReadImageUrl = 'fakeReadImageUrl';

  final AnnouncementEntity announcementUnread = AnnouncementEntity(
      id: 1,
      status: AnnouncementEntity.statusUnread,
      image: fakeUnreadImageUrl,
      sentAt: '2000-01-01T00:00:00.000Z',
      title: 'title',
      description: 'description');

  final AnnouncementEntity announcementRead = AnnouncementEntity(
      id: 2,
      status: AnnouncementEntity.statusRead,
      image: fakeReadImageUrl,
      sentAt: '2000-01-01T00:00:00.000Z',
      title: 'title',
      description: 'description');

  setUpAll(() {
    registerFallbackValue(BoxFit.cover);
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    commonImageProvider = getIt.get<CommonImageProvider>();
    evoTextStyles = getIt.get<EvoTextStyles>();
    evoColors = getIt.get<EvoColors>();

    when(() => commonImageProvider.network(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
          color: any(named: 'color'),
          cornerRadius: any(named: 'cornerRadius'),
          placeholder: any(named: 'placeholder'),
          errorWidget: any(named: 'errorWidget'),
          onLoadError: any(named: 'onLoadError'),
        )).thenAnswer((_) => Container());

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
          color: any(named: 'color'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  });

  tearDownAll(() {
    getIt.reset();
  });

  void verifyEvoNetworkImageProviderWidget({required WidgetTester tester, String? url}) {
    /// verify EvoNetworkImageProviderWidget
    final Finder finderImage = find.byType(EvoNetworkImageProviderWidget);

    expect(finderImage, findsOneWidget);

    final EvoNetworkImageProviderWidget evoNetworkImageProviderWidget = tester.widget(finderImage);

    /// verify image url
    expect(evoNetworkImageProviderWidget.imageUrl, url);
    expect(evoNetworkImageProviderWidget.width, 72);
    expect(evoNetworkImageProviderWidget.height, 72);
  }

  void verifyTitleAndDescriptionWidget({
    required WidgetTester tester,
    required Color colorTitle,
    required Color colorDescription,
  }) {
    final Finder finderTitle = find.text(announcementUnread.title ?? '');
    expect(finderTitle, findsOneWidget);
    final Text textTitle = tester.widget(finderTitle);
    expect(textTitle.style, evoTextStyles.h300(color: colorTitle));

    final Finder finderDescription = find.text(announcementUnread.description ?? '');
    expect(finderDescription, findsOneWidget);
    final Text textDescription = tester.widget(finderDescription);
    expect(textDescription.style, evoTextStyles.bodyMedium(colorDescription).copyWith(height: 1.4));
  }

  void verifyItemDatetime({required WidgetTester tester}) {
    final Finder finderDateTime =
        find.text(announcementUnread.sentAtDateTime?.toNotificationDateTimeFormat() ?? '');
    expect(finderDateTime, findsOneWidget);
    final Text textDateTime = tester.widget(finderDateTime);
    expect(textDateTime.style, evoTextStyles.bodySmall(color: evoColors.textPassive2));
  }

  group('test AnnouncementItemWidget() function', () {
    testWidgets('AnnouncementItemWidget with status = [AnnouncementEntity.statusUnread]',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AnnouncementItemWidget(announcement: announcementUnread),
          ),
        ),
      );

      /// verify EvoNetworkImageProviderWidget
      verifyEvoNetworkImageProviderWidget(tester: tester, url: announcementUnread.image);

      /// verify title and description widget
      verifyTitleAndDescriptionWidget(
        tester: tester,
        colorTitle: evoColors.textActive,
        colorDescription: evoColors.textPassive,
      );

      /// verify item send_at date time
      verifyItemDatetime(tester: tester);

      /// verify item dot red
      final Finder finderDot = find.byType(NotificationDot);
      expect(finderDot, findsOneWidget);
    });
  });

  testWidgets('AnnouncementItemWidget with status = [AnnouncementEntity.statusRead]',
      (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: AnnouncementItemWidget(announcement: announcementRead),
        ),
      ),
    );

    /// verify EvoNetworkImageProviderWidget
    verifyEvoNetworkImageProviderWidget(tester: tester, url: announcementRead.image);

    /// verify title and description widget
    verifyTitleAndDescriptionWidget(
      tester: tester,
      colorTitle: evoColors.textPassive2,
      colorDescription: evoColors.textPassive2,
    );

    /// verify item send_at date time
    verifyItemDatetime(tester: tester);

    /// verify item dot red
    final Finder finderDot = find.byType(NotificationDot);
    expect(finderDot, findsNothing);
  });

  testWidgets(
    'AnnouncementItemWidget with onTap is not null',
    (WidgetTester tester) async {
      bool isHasTap = false;
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AnnouncementItemWidget(
              announcement: announcementUnread,
              onTap: () {
                isHasTap = true;
              },
            ),
          ),
        ),
      );

      final Finder finderInkWell = find.byType(InkWell);
      expect(finderInkWell, findsOneWidget);

      // Verify tap InkWell
      await tester.tap(finderInkWell);
      expect(isHasTap, true);
    },
  );
}
