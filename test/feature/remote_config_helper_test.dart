import 'package:evoapp/data/repository/ts_remote_config.dart';
import 'package:evoapp/data/response/remote_config_biometric_entity.dart';
import 'package:evoapp/data/response/remote_config_common_base_url_entity.dart';
import 'package:evoapp/data/response/remote_config_common_entity.dart';
import 'package:evoapp/data/response/remote_config_feature_toggle_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/remote_config/remote_config_helper.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockRemoteConfigRepo extends Mock implements RemoteConfigRepo {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockCommonHttpClient extends Mock implements CommonHttpClient {}

class MockFlavorConfig extends Mock implements FlavorConfig {}

void main() {
  late RemoteConfigRepo remoteConfigRepo;
  late FeatureToggle featureToggle;
  late CommonHttpClient commonHttpClient;
  late RemoteConfigHelperImpl remoteConfigHelperImpl;
  late FlavorConfig? flavorConfig;

  setUp(() {
    flavorConfig = FlavorConfig(
        flavor: FlavorType.stag.name,
        values: CommonFlavorValues(
          baseUrl: 'https://example.com',
          initializeFirebaseSdk: true,
          oneSignalAppId: null,
        ));

    remoteConfigRepo = MockRemoteConfigRepo();
    featureToggle = MockFeatureToggle();
    commonHttpClient = MockCommonHttpClient();
    getIt.registerSingleton<FeatureToggle>(featureToggle);
    getIt.registerSingleton<CommonHttpClient>(commonHttpClient);
    remoteConfigHelperImpl = RemoteConfigHelperImpl(remoteConfigRepo: remoteConfigRepo);
  });

  tearDown(() {
    getIt.unregister<FeatureToggle>();
    getIt.unregister<CommonHttpClient>();
    flavorConfig = null;
  });

  test('init should call initConfig and fetch on remoteConfigRepo', () async {
    when(() => remoteConfigRepo.initConfig()).thenAnswer((_) async => Future<void>.value());
    when(() => remoteConfigRepo.fetch()).thenAnswer((_) async => Future<void>.value());
    final RemoteConfigFeatureToggleEntity entity = RemoteConfigFeatureToggleEntity();
    when(() => remoteConfigRepo.getFeatureToggle()).thenAnswer((_) async => entity);
    when(() => remoteConfigRepo.getCommonConfig()).thenAnswer((_) async {
      return Future<RemoteConfigCommonEntity>.value(RemoteConfigCommonEntity());
    });

    await remoteConfigHelperImpl.init();

    verify(() => remoteConfigRepo.initConfig()).called(1);
    verify(() => remoteConfigRepo.fetch()).called(1);
  });

  test('updateFeatureToggle should call getFeatureToggle and mapFromRemoteConfigEntity', () async {
    final RemoteConfigFeatureToggleEntity entity = RemoteConfigFeatureToggleEntity();
    when(() => remoteConfigRepo.getFeatureToggle()).thenAnswer((_) async => entity);

    await remoteConfigHelperImpl.updateFeatureToggle();

    verify(() => remoteConfigRepo.getFeatureToggle()).called(1);
    verify(() => featureToggle.mapFromRemoteConfigEntity(entity)).called(1);
  });

  test('updateBaseUrlIfNeed should update baseUrl when shouldChangeBaseUrl is true', () async {
    when(() => remoteConfigRepo.getCommonConfig()).thenAnswer((_) async {
      return Future<RemoteConfigCommonEntity>.value(
        RemoteConfigCommonEntity(
          baseUrl: RemoteConfigCommonBaseUrlEntity(value: 'https://new-example.com'),
        ),
      );
    });
    await remoteConfigHelperImpl.updateBaseUrlIfNeed();

    verify(() => remoteConfigRepo.getCommonConfig()).called(1);
    verify(() => commonHttpClient.setBaseUrl('https://new-example.com')).called(1);
  });

  test(
      'shouldChangeBaseUrl should return true when isStagingEnvironment is true and baseUrl is not empty',
      () {
    final bool result = remoteConfigHelperImpl.shouldChangeBaseUrl('https://new-example.com');
    expect(result, true);
  });

  test('isStagingEnvironment should return true when flavor is stag', () {
    final bool result = remoteConfigHelperImpl.isStagingEnvironment;
    expect(result, true);
  });

  test('baseUrlIsNotEmpty should return true when baseUrl is not empty', () {
    final bool result = remoteConfigHelperImpl.baseUrlIsNotEmpty('https://new-example.com');

    expect(result, true);
  });

  test('updateBaseUrl should update baseUrl in FlavorConfig and CommonHttpClient', () {
    remoteConfigHelperImpl.updateBaseUrl('https://new-example.com');

    expect(flavorConfig?.values.baseUrl, 'https://new-example.com');
    verify(() => commonHttpClient.setBaseUrl('https://new-example.com')).called(1);
  });

  test('getFeatureToggle should call getFeatureToggle on remoteConfigRepo', () async {
    final RemoteConfigFeatureToggleEntity entity = RemoteConfigFeatureToggleEntity();
    when(() => remoteConfigRepo.getFeatureToggle()).thenAnswer((_) async => entity);

    final RemoteConfigFeatureToggleEntity? result = await remoteConfigHelperImpl.getFeatureToggle();

    expect(result, entity);
    verify(() => remoteConfigRepo.getFeatureToggle()).called(1);
  });

  test('getIOSDevicesSupportFinger should call getIOSDevicesSupportFinger on remoteConfigRepo',
      () async {
    final List<String> devices = <String>['iPhone10,1', 'iPhone10,4'];
    when(() => remoteConfigRepo.getBiometricConfigs()).thenAnswer((_) async {
      return RemoteConfigBiometricEntity(
        fingeringSupportedDevice: devices,
      );
    });

    final List<String>? result = await remoteConfigHelperImpl.getIOSDevicesSupportFinger();

    expect(result, devices);
    verify(() => remoteConfigRepo.getBiometricConfigs()).called(1);
  });
}
