import 'package:evoapp/feature/emi_management/widgets/emi_management_empty_widget.dart';
import 'package:evoapp/feature/main_screen/main_screen.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

void main() {
  late CommonImageProvider commonImageProvider;
  late BuildContext mockNavigatorContext;
  late CommonNavigator commonNavigator;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(MockBuildContext());

    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    commonImageProvider = getIt.get<CommonImageProvider>();
    setUtilsMockInstanceForTesting();
    mockNavigatorContext = MockBuildContext();
    commonNavigator = MockCommonNavigator();
    getIt.registerSingleton<CommonNavigator>(commonNavigator);
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    when(() => EvoUiUtils().calculateVerticalSpace(
          heightPercentage: any(named: 'heightPercentage'),
          context: any(named: 'context'),
        )).thenReturn(50.0);
  });

  tearDownAll(() {
    resetUtilMockToOriginalInstance();
  });

  testWidgets('EmiManagementEmptyWidget displays correctly', (WidgetTester tester) async {
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: EmiManagementEmptyWidget(),
        ),
      ),
    );

    // Verify the layout and components
    verify(() => commonImageProvider.asset(EvoImages.imgEmiEmptyState)).called(1);

    expect(find.text(EvoStrings.emiManagementEmptyTitle), findsOneWidget);
    expect(find.text(EvoStrings.emiManagementEmptyDescription), findsOneWidget);
    expect(find.byType(CommonButton), findsOneWidget);
    expect(find.text(EvoStrings.moveToHome), findsOneWidget);

    // Tap the button
    await tester.tap(find.byType(CommonButton));
    await tester.pumpAndSettle();

    // Verify the button press
    expect(
        verify(
          () => mockNavigatorContext.removeUntilAndPushReplacementNamed(
            any(),
            any(),
            extra: captureAny(named: 'extra'),
          ),
        ).captured.single,
        isA<MainScreenArg>().having(
          (MainScreenArg p0) => p0.isLoggedIn,
          'verify isLoggedIn',
          true,
        ));
  });
}
