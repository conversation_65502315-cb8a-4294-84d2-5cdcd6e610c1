import 'package:evoapp/data/response/emi_record_entity.dart';
import 'package:evoapp/feature/emi_management/management_screen/cubit/emi_management_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EmiManagementState', () {
    test('EmiManagementInitialState should be instantiated', () {
      final EmiManagementInitialState state = EmiManagementInitialState();
      expect(state, isA<EmiManagementInitialState>());
    });

    test('EmiManagementLoadingState should be instantiated', () {
      final EmiManagementLoadingState state = EmiManagementLoadingState();
      expect(state, isA<EmiManagementLoadingState>());
    });

    test('EmiManagementSucceedState should be instantiated with correct properties', () {
      final List<EmiRecordEntity> emiRecords = <EmiRecordEntity>[];
      final EmiManagementSucceedState state =
          EmiManagementSucceedState(emiRecords: emiRecords, isLoadMore: true);

      expect(state, isA<EmiManagementSucceedState>());
      expect(state.emiRecords, emiRecords);
      expect(state.isLoadMore, true);
    });

    test('EmiManagementSucceedState should default isLoadMore to false', () {
      final EmiManagementSucceedState state = EmiManagementSucceedState(emiRecords: null);

      expect(state.isLoadMore, false);
    });

    test('EmiManagementFailureState should be instantiated with correct properties', () {
      final ErrorUIModel errorUIModel = ErrorUIModel(userMessage: 'An error occurred');
      final EmiManagementFailureState state =
          EmiManagementFailureState(errorUIModel: errorUIModel, isRefresh: true);

      expect(state, isA<EmiManagementFailureState>());
      expect(state.errorUIModel, errorUIModel);
      expect(state.isRefresh, true);
    });
  });
}
