import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/feature/deep_link/deep_link_constants.dart';
import 'package:evoapp/feature/deep_link/deep_link_handler.dart';
import 'package:evoapp/feature/deep_link/deep_link_utils.dart';
import 'package:evoapp/feature/deep_link/model/deep_link_model.dart';
import 'package:evoapp/feature/deep_link/model/deep_link_shared_data.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class DeepLinkHandlerMock extends Mock implements DeepLinkHandler {}

class MockAppState extends Mock implements AppState {}

void main() {
  late DeepLinkUtils deepLinkUtils;
  late DeepLinkHandler deepLinkHandler;
  late AppState appState;

  setUpAll(() {
    getIt.registerSingleton<DeepLinkHandler>(DeepLinkHandlerMock());
    deepLinkHandler = getIt.get<DeepLinkHandler>();

    getIt.registerSingleton<AppState>(MockAppState());
    appState = getIt.get<AppState>();

    when(() => appState.deepLinkSharedData).thenReturn(DeepLinkSharedData());

    registerFallbackValue(
        DeepLinkModel(screenName: 'dummy_screen_name', params: <String, String>{}));
  });

  setUp(() {
    deepLinkUtils = DeepLinkUtils();
  });

  tearDown(() {
    reset(deepLinkHandler);
  });

  tearDownAll(() {
    getIt.unregister<DeepLinkHandler>();
  });

  group('verify isEvoAppDeepLink', () {
    test('returns true for links starting with the evoApp deep link prefix', () {
      const String link = 'evoappvn://mobile/deeplinking';
      expect(deepLinkUtils.isEvoAppDeepLink(link), isTrue);
    });

    test('returns false for links not starting with the part of evoApp deep link prefix', () {
      const String link = 'evoappvn://mobile';
      expect(deepLinkUtils.isEvoAppDeepLink(link), isFalse);
    });

    test('returns false for links not starting with the evoApp deep link prefix', () {
      const String link = 'http://example.com';
      expect(deepLinkUtils.isEvoAppDeepLink(link), isFalse);
    });

    test('returns false for a null link', () {
      const String? link = null;
      expect(deepLinkUtils.isEvoAppDeepLink(link), isFalse);
    });

    test('returns false for an empty string', () {
      const String link = '';
      expect(deepLinkUtils.isEvoAppDeepLink(link), isFalse);
    });

    test('returns false for a link that contains the evoApp prefix but does not start with it', () {
      const String link = 'http://example.com/evoapp://';
      expect(deepLinkUtils.isEvoAppDeepLink(link), isFalse);
    });
  });

  group('verify extractScreenNameFromUri', () {
    test('extracts screen name from query parameters', () {
      const String screenName = 'testScreen';
      final Uri uri = Uri.parse('https://example.com/path?screen_name=$screenName');

      final String? extractedScreenName = deepLinkUtils.extractScreenNameFromUri(uri);

      expect(extractedScreenName, screenName);
    });

    test('extracts screen name from path segments', () {
      const String screenName = 'dop_completed';
      final Uri uri = Uri.parse('evoappvn://mobile/deeplinking/$screenName');

      final String? extractedScreenName = deepLinkUtils.extractScreenNameFromUri(uri);

      expect(extractedScreenName, screenName);
    });

    test('returns null if screen name is not present', () {
      final Uri uri = Uri.parse('https://example.com/path');

      final String? extractedScreenName = deepLinkUtils.extractScreenNameFromUri(uri);

      expect(extractedScreenName, isNull);
    });

    test('prioritizes query parameters over path segments', () {
      const String screenName = 'testScreen';
      final Uri uri =
          Uri.parse('evoappvn://mobile/deeplinking/not_screen_name?screen_name=$screenName');

      final String? extractedScreenName = deepLinkUtils.extractScreenNameFromUri(uri);

      expect(extractedScreenName, screenName);
    });
  });

  group('verify generateDeepLinkModel()', () {
    test('return model with correct parameters', () {
      /// Arrange
      const String deepLinkValue =
          '${DeepLinkConstants.evoAppDeepLinkPrefix}?screen_name=profile&user_id=123';

      /// Action
      final DeepLinkModel model =
          deepLinkUtils.generateDeepLinkModel(deepLinkValue: deepLinkValue, isDeferred: true);

      /// Verify model  with the correct parameters
      expect(model.screenName, 'profile');
      expect(model.params, <String, String>{'screen_name': 'profile', 'user_id': '123'});
      expect(model.isDeferred, true);
      expect(appState.deepLinkSharedData.originalUrl, deepLinkValue);
      expect(appState.deepLinkSharedData.deepLink, model);
    });

    test('return model with correct screen_name & parameter ', () {
      /// Arrange
      const String deepLinkValue =
          '${DeepLinkConstants.evoAppDeepLinkPrefix}/dop_completed?user_id=123';

      /// Action
      final DeepLinkModel model =
          deepLinkUtils.generateDeepLinkModel(deepLinkValue: deepLinkValue, isDeferred: false);

      /// Verify model null screenName and correct params
      expect(model.screenName, 'dop_completed');
      expect(model.params, <String, String>{'user_id': '123'});
      expect(model.isDeferred, false);

      expect(appState.deepLinkSharedData.originalUrl, deepLinkValue);
      expect(appState.deepLinkSharedData.deepLink, model);
    });

    test('return model with no parameters', () {
      /// Arrange
      const String deepLinkValue = DeepLinkConstants.evoAppDeepLinkPrefix;

      /// Action
      final DeepLinkModel model = deepLinkUtils.generateDeepLinkModel(deepLinkValue: deepLinkValue);

      /// Verify model with null screenName and empty params
      expect(model.screenName, isNull);
      expect(model.params, <String, String>{});
      expect(model.isDeferred, isNull);

      expect(appState.deepLinkSharedData.originalUrl, deepLinkValue);
      expect(appState.deepLinkSharedData.deepLink, model);
    });

    test('return model without parameters when handles invalid deeplink', () {
      /// Arrange
      const String deepLinkValue = 'invalid_link';

      /// Action
      final DeepLinkModel model = deepLinkUtils.generateDeepLinkModel(deepLinkValue: deepLinkValue);

      /// Verify the model the link is invalid
      expect(model.screenName, isNull);
      expect(model.params, <String, String>{});
      expect(model.isDeferred, isNull);

      expect(appState.deepLinkSharedData.originalUrl, deepLinkValue);
      expect(appState.deepLinkSharedData.deepLink, model);
    });
  });

  group('verify extractEvoDeepLinkFromUrl', () {
    const String expectedDeepLinkValue = '${DeepLinkConstants.evoAppDeepLinkPrefix}/some/path';

    test('should extract deep_link_value when present and is a valid EVO deep link', () {
      const String url = 'http://example.com?deep_link_value=$expectedDeepLinkValue';
      final String? extracted = deepLinkUtils.extractEvoDeepLinkFromUrl(url);
      expect(extracted, expectedDeepLinkValue);
    });

    test('should return null when deep_link_value is present but not a valid EVO deep link', () {
      const String url = 'http://example.com?deep_link_value=http://anotherurl.com';
      final String? extracted = deepLinkUtils.extractEvoDeepLinkFromUrl(url);
      expect(extracted, isNull);
    });

    test('should return null when deep_link_value is not present', () {
      const String url = 'http://example.com';
      final String? extracted = deepLinkUtils.extractEvoDeepLinkFromUrl(url);
      expect(extracted, isNull);
    });

    test('should return null when URL is empty', () {
      const String url = '';
      final String? extracted = deepLinkUtils.extractEvoDeepLinkFromUrl(url);
      expect(extracted, isNull);
    });

    test('should return the URL itself if it is a valid EVO deep link', () {
      final String? extracted = deepLinkUtils.extractEvoDeepLinkFromUrl(expectedDeepLinkValue);
      expect(extracted, expectedDeepLinkValue);
    });

    test('should return null if URL is not a valid EVO deep link', () {
      const String url = 'evoappvn://other_domain/other_path';
      final String? extracted = deepLinkUtils.extractEvoDeepLinkFromUrl(url);
      expect(extracted, isNull);
    });
  });

  group('verify getDeepLinkFromAppsflyerOneLink', () {
    test('should return deep_link_value if present', () {
      const String url = 'http://example.com?deep_link_value=evo://some/path';
      final String? deepLink = deepLinkUtils.getDeepLinkFromAppsflyerOneLink(url);
      expect(deepLink, 'evo://some/path');
    });

    test('should return null if deep_link_value is not present', () {
      const String url = 'http://example.com?other_param=some_value';
      final String? deepLink = deepLinkUtils.getDeepLinkFromAppsflyerOneLink(url);
      expect(deepLink, isNull);
    });

    test('should return null if URL has no query parameters', () {
      const String url = 'http://example.com';
      final String? deepLink = deepLinkUtils.getDeepLinkFromAppsflyerOneLink(url);
      expect(deepLink, isNull);
    });

    test('should return null if URL is empty', () {
      const String url = '';
      final String? deepLink = deepLinkUtils.getDeepLinkFromAppsflyerOneLink(url);
      expect(deepLink, isNull);
    });

    test('should handle URLs with multiple query parameters', () {
      const String url =
          'http://example.com?param1=value1&deep_link_value=evo://some/path&param3=value3';
      final String? deepLink = deepLinkUtils.getDeepLinkFromAppsflyerOneLink(url);
      expect(deepLink, 'evo://some/path');
    });

    test('should return the last deep_link_value if multiple are present', () {
      const String url =
          'http://example.com?deep_link_value=evo://path1&deep_link_value=evo://path2';
      final String? deepLink = deepLinkUtils.getDeepLinkFromAppsflyerOneLink(url);
      expect(deepLink, 'evo://path2');
    });
  });

  group('generateDeepLink', () {
    test('returns base deeplink when screenName and params are null', () {
      // Arrange
      final DeepLinkModel model = DeepLinkModel(
        screenName: null,
        params: null,
      );

      // Act
      final String result = deepLinkUtils.generateDeepLink(model);
      final Uri uriResult = Uri.parse(result);

      // Assert
      expect('${uriResult.scheme}://${uriResult.host}${uriResult.path}',
          DeepLinkConstants.evoAppDeepLinkPrefix);
    });

    test('includes screenName in deeplink', () {
      // Arrange
      final DeepLinkModel model = DeepLinkModel(screenName: 'home', params: null);

      // Act
      final String result = deepLinkUtils.generateDeepLink(model);
      final Uri uriResult = Uri.parse(result);

      // Assert
      expect('${uriResult.scheme}://${uriResult.host}${uriResult.path}',
          DeepLinkConstants.evoAppDeepLinkPrefix);
      expect(
        uriResult.queryParameters,
        isA<Map<String, String>>().having(
          (Map<String, String> p) => p['screen_name'],
          'verify screen_name',
          'home',
        ),
      );
    });

    test('includes additional params in deeplink', () {
      // Arrange
      final DeepLinkModel model = DeepLinkModel(
        params: <String, String>{'key1': 'value1', 'key2': 'value2'},
        screenName: null,
      );

      // Act
      final String result = deepLinkUtils.generateDeepLink(model);

      final Uri uriResult = Uri.parse(result);

      // Assert
      expect('${uriResult.scheme}://${uriResult.host}${uriResult.path}',
          DeepLinkConstants.evoAppDeepLinkPrefix);
      expect(
        uriResult.queryParameters,
        isA<Map<String, String>>()
            .having(
              (Map<String, String> p) => p['key1'],
              'verify key 1',
              'value1',
            )
            .having(
              (Map<String, String> p) => p['key2'],
              'verify key 2',
              'value2',
            ),
      );
    });

    test('includes screenName and additional params in deeplink', () {
      // Arrange
      final DeepLinkModel model = DeepLinkModel(
        screenName: 'home',
        params: <String, String>{'key1': 'value1', 'key2': 'value2'},
      );

      // Act
      final String result = deepLinkUtils.generateDeepLink(model);
      final Uri uriResult = Uri.parse(result);

      // Assert
      expect('${uriResult.scheme}://${uriResult.host}${uriResult.path}',
          DeepLinkConstants.evoAppDeepLinkPrefix);
      expect(
        uriResult.queryParameters,
        isA<Map<String, String>>()
            .having(
              (Map<String, String> p) => p['key1'],
              'verify key 1',
              'value1',
            )
            .having(
              (Map<String, String> p) => p['key2'],
              'verify key 2',
              'value2',
            )
            .having(
              (Map<String, String> p) => p['screen_name'],
              'verify key screen name',
              'home',
            ),
      );
    });

    test('test generateDeepLink with encodedUrl', () {
      const String sampleURL = 'https//google.com';
      final String encodedUrl = Uri.encodeComponent(sampleURL);
      final String deeplink = deepLinkUtils.generateDeepLink(
        DeepLinkModel(
          screenName: null,
          params: <String, String>{
            'web_link': encodedUrl,
          },
        ),
      );
      expect(deeplink, '${DeepLinkConstants.evoAppDeepLinkPrefix}?web_link=$encodedUrl');
    });
  });

  group('verify isDOPWebLinkNfcStep', () {
    test('returns false when link is null', () {
      expect(deepLinkUtils.isDOPWebLinkNfcStep(null), false);
    });

    test('returns false when link is not a valid URI', () {
      expect(deepLinkUtils.isDOPWebLinkNfcStep('not-a-valid-uri'), false);
    });

    test('returns false when URI does not have redirect_from_nfc parameter', () {
      expect(deepLinkUtils.isDOPWebLinkNfcStep('https://example.com/path?param=value'), false);
    });

    test('returns false when redirect_from_nfc parameter is not true', () {
      expect(deepLinkUtils.isDOPWebLinkNfcStep('https://example.com/path?redirect_from_nfc=false'),
          false);
    });

    test('returns true when redirect_from_nfc parameter is true', () {
      expect(deepLinkUtils.isDOPWebLinkNfcStep('https://example.com/path?redirect_from_nfc=true'),
          true);
    });

    test('returns true when URI has multiple parameters including redirect_from_nfc=true', () {
      expect(
          deepLinkUtils
              .isDOPWebLinkNfcStep('https://example.com/path?param=value&redirect_from_nfc=true'),
          true);
    });
  });

  group('verify removeEvoDefinedKeysFromMap()', () {
    test('should return null if params is null', () {
      final Map<String, String>? result = deepLinkUtils.removeEvoDeepLinkKeysFromMap(null);
      expect(result, isNull);
    });

    test('should return null if params is empty', () {
      final Map<String, String>? result =
          deepLinkUtils.removeEvoDeepLinkKeysFromMap(<String, String>{});
      expect(result, isNull);
    });

    test('should remove known-defined keys from the map', () {
      final Map<String, String> inputMap = <String, String>{
        EVODeeplinkKey.screenNameKey.value: 'value1',
        EVODeeplinkKey.nextActionLinkKey.value: 'value2',
        'utm_source': 'utm_source_value',
        'utm_campaign': 'utm_source_campaign',
      };

      final Map<String, String>? result = deepLinkUtils.removeEvoDeepLinkKeysFromMap(inputMap);

      expect(result, isNotNull);
      expect(result!.containsKey(EVODeeplinkKey.screenNameKey.value), isFalse);
      expect(result.containsKey(EVODeeplinkKey.nextActionLinkKey.value), isFalse);

      expect(result.length, 2);
      expect(result['utm_source'], 'utm_source_value');
      expect(result['utm_campaign'], 'utm_source_campaign');
    });

    test('should return a map with the same content if none of the keys are present', () {
      final Map<String, String> inputMap = <String, String>{
        'otherKey1': 'value1',
        'otherKey2': 'value2',
      };

      final Map<String, String>? result = deepLinkUtils.removeEvoDeepLinkKeysFromMap(inputMap);

      expect(result, equals(inputMap));
      expect(result, isNot(same(inputMap))); // Ensure it is not the same instance (not mutated).
    });

    test('should not mutate the original map', () {
      final Map<String, String> inputMap = <String, String>{
        'screenName': 'value1',
        'webViewTitle': 'value2',
        'otherKey': 'value3',
      };

      final Map<String, String> originalMap = Map<String, String>.from(inputMap);
      deepLinkUtils.removeEvoDeepLinkKeysFromMap(inputMap);

      expect(inputMap, equals(originalMap));
    });
  });

  group('test getRegExpOfDeeplink', () {
    test('should match URLs for prod environment', () {
      final List<String> validDeeplinkPRODURL = <String>[
        'evoappvn://mobile/deeplinking',
        'evoappvn://mobile/deeplinking/dop_completed',
        'evoappvn://mobile/deeplinking?screen_name=webview&web_link=https://your-web-link.com&title=title',
        'evoappvn://mobile/deeplinking?screen_name=dop_native_introduction_screen&utm_source=evo_hoanghamobile&utm_campaign=offline_hhm_270524',
      ];

      const List<String> invalidDeeplinkPRODUrl = <String>[
        'evoappvn://mobile/deeplinking.com',
        'evoappvn://mobile/deep-linking.com',
        'evoappvn://mobile/deeplinking.com/dop_completed',
        'evoappvn://mobile/deeplinking/dop_completed.com',
        'evoappvn://mobile/deeplinking.com.vn',
        'evo-appvn://mobile/deeplinking',
        'evo.appvn://mobile/deeplinking',
        'appvn://mobile/deeplinking',
      ];

      FlavorConfig(
        flavor: FlavorType.prod.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.prod).getFlavorValue(),
      );

      final RegExp regex = deepLinkUtils.getRegExpOfDeeplink();

      for (final String url in validDeeplinkPRODURL) {
        expect(regex.hasMatch(url), isTrue);
      }

      for (final String url in invalidDeeplinkPRODUrl) {
        expect(regex.hasMatch(url), isFalse);
      }
    });

    test('should match URLs for uat environment', () {
      FlavorConfig(
        flavor: FlavorType.uat.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.uat).getFlavorValue(),
      );

      final RegExp regex = deepLinkUtils.getRegExpOfDeeplink();

      // Correct URLs
      final List<String> validDeeplinkUATUrls = <String>[
        'evoappvn://mobile/deeplinking',
        'evoappvn://mobile/deeplinking/dop_completed',
        'evoappvn://mobile/deeplinking?screen_name=webview&web_link=https://your-web-link.com&title=title',
        'evoappvn://mobile/deeplinking?screen_name=dop_native_introduction_screen&utm_source=evo_hoanghamobile&utm_campaign=offline_hhm_270524',
      ];

      // Incorrect URLs
      const List<String> invalidDeeplinkUATUrl = <String>[
        'evoappvn://mobile/deeplinking.com',
        'evoappvn://mobile/deep-linking.com',
        'evoappvn://mobile/deeplinking.com/dop_completed',
        'evoappvn://mobile/deeplinking/dop_completed.com',
        'evoappvn://mobile/deeplinking.com.vn',
        'evo-appvn://mobile/deeplinking',
        'evo.appvn://mobile/deeplinking',
        'appvn://mobile/deeplinking',
      ];
      for (final String url in validDeeplinkUATUrls) {
        expect(regex.hasMatch(url), isTrue);
      }

      for (final String url in invalidDeeplinkUATUrl) {
        expect(regex.hasMatch(url), isFalse);
      }
    });

    test('should match URLs for staging environments', () {
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );

      final RegExp regex = deepLinkUtils.getRegExpOfDeeplink();

      final List<String> validDeepLinkStagUrls = <String>[
        'evoappvn://mobile/deeplinking',
        'evoappvn://mobile/deeplinking/dop_completed',
        'evoappvn://mobile/deeplinking?screen_name=webview&web_link=https://your-web-link.com&title=title',
        'evoappvn://mobile/deeplinking?screen_name=dop_native_introduction_screen&utm_source=evo_hoanghamobile&utm_campaign=offline_hhm_270524',
      ];

      const List<String> invalidDeepLinkStagUrls = <String>[
        'evoappvn://mobile/deeplinking.com',
        'evoappvn://mobile/deep-linking.com',
        'evoappvn://mobile/deeplinking.com/dop_completed',
        'evoappvn://mobile/deeplinking/dop_completed.com',
        'evoappvn://mobile/deeplinking.com.vn',
        'evo-appvn://mobile/deeplinking',
        'evo.appvn://mobile/deeplinking',
        'appvn://mobile/deeplinking',
      ];

      for (final String url in validDeepLinkStagUrls) {
        expect(regex.hasMatch(url), isTrue);
      }

      for (final String url in invalidDeepLinkStagUrls) {
        expect(regex.hasMatch(url), isFalse);
      }
    });
  });

  group('verify getDOEDeepLinkNonRelaxRule', () {
    test('should generate exact expected deeplink string with provided phone number', () {
      // Arrange
      const String phoneNumber = '0987654321';

      // Act
      final String deepLink = deepLinkUtils.getDOEDeepLinkNonRelaxRule(phoneNumber: phoneNumber);

      // Assert
      const String expected = 'evoappvn://mobile/deeplinking?'
          'screen_name=dop_native_introduction_screen'
          '&phone_number=0987654321'
          '&lead_source=evo_native'
          '&utm_source=evo_log_in'
          '&auto_request_otp=true';

      expect(deepLink, equals(expected));
    });

    test('should generate correct deeplink with provided phone number', () {
      // Arrange
      const String phoneNumber = '0987654321';

      // Act
      final String deepLink = deepLinkUtils.getDOEDeepLinkNonRelaxRule(phoneNumber: phoneNumber);
      final Uri uri = Uri.parse(deepLink);

      // Assert
      expect(uri.scheme, equals('evoappvn'));
      expect(
          '${uri.scheme}://${uri.host}${uri.path}', equals(DeepLinkConstants.evoAppDeepLinkPrefix));

      final Map<String, String> params = uri.queryParameters;
      expect(params[EVODeeplinkKey.screenNameKey.value],
          equals(DeepLinkConstants.dopNativeIntroductionScreenName));
      expect(params[EVODeeplinkKey.phoneNumberKey.value], equals(phoneNumber));
      expect(params[EVODeeplinkKey.leadSourceKey.value], equals(LeadSource.evoNative));
      expect(params[EVODeeplinkKey.utmSourceKey.value],
          equals(DeepLinkConstants.utmSourceFromEvoLogin));
      expect(params[EVODeeplinkKey.autoRequestOTPKey.value], equals('true'));
    });

    test('should generate correct deeplink with null phone number', () {
      // Act
      final String deepLink = deepLinkUtils.getDOEDeepLinkNonRelaxRule();
      final Uri uri = Uri.parse(deepLink);

      // Assert
      expect(uri.scheme, equals('evoappvn'));
      expect(
          '${uri.scheme}://${uri.host}${uri.path}', equals(DeepLinkConstants.evoAppDeepLinkPrefix));

      final Map<String, String> params = uri.queryParameters;
      expect(params[EVODeeplinkKey.screenNameKey.value],
          equals(DeepLinkConstants.dopNativeIntroductionScreenName));
      expect(params[EVODeeplinkKey.phoneNumberKey.value], isEmpty);
      expect(params[EVODeeplinkKey.leadSourceKey.value], equals(LeadSource.evoNative));
      expect(params[EVODeeplinkKey.utmSourceKey.value],
          equals(DeepLinkConstants.utmSourceFromEvoLogin));
      expect(params[EVODeeplinkKey.autoRequestOTPKey.value], equals('true'));
    });

    test('should include all required parameters in the generated deeplink', () {
      // Arrange
      const String phoneNumber = '0123456789';

      // Act
      final String deepLink = deepLinkUtils.getDOEDeepLinkNonRelaxRule(phoneNumber: phoneNumber);

      // Assert
      expect(
          deepLink, contains('screen_name=${DeepLinkConstants.dopNativeIntroductionScreenName}'));
      expect(deepLink, contains('phone_number=$phoneNumber'));
      expect(deepLink, contains('lead_source=${LeadSource.evoNative}'));
      expect(deepLink, contains('utm_source=${DeepLinkConstants.utmSourceFromEvoLogin}'));
      expect(deepLink, contains('auto_request_otp=true'));
    });
  });
}
