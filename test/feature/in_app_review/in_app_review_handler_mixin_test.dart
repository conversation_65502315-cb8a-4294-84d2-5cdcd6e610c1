import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/in_app_review/in_app_review_wrapper.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:evoapp/feature/in_app_review/in_app_review_handler_mixin.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';

import '../../util/flutter_test_config.dart';

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockInAppReviewWrapper extends Mock implements InAppReviewWrapper {}

class MockEvoDialogHelper extends Mock implements EvoDialogHelper {}

class MockNetworkManager extends Mock implements NetworkManager {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockGlobalKeyProvider extends Mock implements GlobalKeyProvider {}

class TestInAppReviewHandler with InAppReviewHandlerMixin {}

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

void main() {
  final MockBuildContext mockBuildContext = MockBuildContext();
  late TestInAppReviewHandler handler;
  late EvoLocalStorageHelper mockLocalStorageHelper;
  late InAppReviewWrapper mockInAppReviewWrapper;
  late NetworkManager mockNetworkManager;
  late EvoSnackBar mockSnackBar;
  late CommonNavigator mockCommonNavigator;
  late FeatureToggle mockFeatureToggle;

  const String fakeTitle = 'Test title';
  const String fakeContent = 'Test content';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(SnackBarType.error);
    registerFallbackValue(mockBuildContext);
    registerFallbackValue(EvoDialogId.requestRatingAndReviewDialog);
    getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());

    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();

    EvoDialogHelper.setInstanceForTesting(MockEvoDialogHelper());
    mockSnackBar = MockEvoSnackBar();

    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => MockEvoLocalStorageHelper());
    mockLocalStorageHelper = getIt.get<EvoLocalStorageHelper>();

    getIt.registerLazySingleton<InAppReviewWrapper>(() => MockInAppReviewWrapper());
    mockInAppReviewWrapper = getIt.get<InAppReviewWrapper>();

    getIt.registerLazySingleton<NetworkManager>(() => MockNetworkManager());
    mockNetworkManager = getIt.get<NetworkManager>();

    getIt.registerLazySingleton<EvoSnackBar>(() => MockEvoSnackBar());
    mockSnackBar = getIt.get<EvoSnackBar>();

    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());
    mockCommonNavigator = getIt.get<CommonNavigator>();

    getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());
    mockFeatureToggle = getIt.get<FeatureToggle>();

    when(() => EvoDialogHelper().showDialogConfirm(
          isDismissible: any(named: 'isDismissible'),
          dialogId: any(named: 'dialogId'),
          title: any(named: 'title'),
          content: any(named: 'content'),
          textPositive: any(named: 'textPositive'),
          textNegative: any(named: 'textNegative'),
          onClickPositive: any(named: 'onClickPositive'),
          onClickNegative: any(named: 'onClickNegative'),
        )).thenAnswer((_) => Future<void>.value());
  });

  setUp(() {
    handler = TestInAppReviewHandler();

    when(() => mockSnackBar.show(any(),
        typeSnackBar: any(named: 'typeSnackBar'),
        durationInSec: any(named: 'durationInSec'))).thenAnswer((_) {
      return Future<bool?>.value(true);
    });

    when(() => mockFeatureToggle.enableRequestReviewRatingFeature).thenAnswer((_) => true);

    when(() => mockInAppReviewWrapper.openStoreListing(
          appStoreId: any(named: 'appStoreId'),
        )).thenAnswer((_) async {});

    when(() => mockLocalStorageHelper.setValueToCheckReviewPopupShown(any()))
        .thenAnswer((_) async {});

    when(() => mockCommonNavigator.pop(
          any(),
          result: any(named: 'result'),
        )).thenAnswer((_) => Future<void>.value());
  });

  tearDown(() {
    reset(mockSnackBar);
    reset(mockCommonNavigator);
    reset(mockLocalStorageHelper);
    reset(mockInAppReviewWrapper);
  });

  void verifyShowPopup({bool isNeverShow = false}) {
    if (isNeverShow) {
      verifyNever(() => EvoDialogHelper().showDialogConfirm(
            isDismissible: any(named: 'isDismissible'),
            dialogId: any(named: 'dialogId'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          ));
      return;
    }

    verify(() => EvoDialogHelper().showDialogConfirm(
          isDismissible: false,
          dialogId: EvoDialogId.requestRatingAndReviewDialog,
          title: fakeTitle,
          content: fakeContent,
          textNegative: EvoStrings.ignore,
          textPositive: EvoStrings.review,
          onClickPositive: any(named: 'onClickPositive'),
          onClickNegative: any(named: 'onClickNegative'),
        )).called(1);
  }

  test('verify constants', () {
    final TestInAppReviewHandler handler = TestInAppReviewHandler();
    expect(handler.evoAppAppleId, '1665449531');
    expect(InAppReviewHandlerMixin.delayRequestRatingPopupInMs, 2000);
    expect(handler.isRequestingShowPopup, false);
  });

  group('verify showRequestRatingDialogIfNeeded()', () {
    test('do not show showRequestRatingDialogIfNeeded()', () async {
      when(() => mockFeatureToggle.enableRequestReviewRatingFeature).thenAnswer((_) => false);

      await handler.showRequestRatingDialogIfNeeded(
        title: 'Test title',
        content: 'Test content',
      );

      verifyNever(() => mockLocalStorageHelper.getValueToCheckReviewPopupShown());

      verifyShowPopup(isNeverShow: true);
    });

    test('showRequestRatingDialogIfNeeded should not show dialog if isRequestingShowPopup is true',
        () async {
      when(() => mockLocalStorageHelper.getValueToCheckReviewPopupShown())
          .thenAnswer((_) async => false);

      handler.isRequestingShowPopup = true;

      await handler.showRequestRatingDialogIfNeeded(
        title: 'Test title',
        content: 'Test content',
      );

      expect(handler.isRequestingShowPopup, false);
      verifyShowPopup(isNeverShow: true);
    });

    test('showRequestRatingDialogIfNeeded should not show dialog if popup was already shown',
        () async {
      when(() => mockLocalStorageHelper.getValueToCheckReviewPopupShown())
          .thenAnswer((_) async => true);

      await handler.showRequestRatingDialogIfNeeded(
        title: 'Test title',
        content: 'Test content',
      );

      when(() => mockLocalStorageHelper.getValueToCheckReviewPopupShown())
          .thenAnswer((_) async => false);

      verifyShowPopup(isNeverShow: true);
    });

    testWidgets('showRequestRatingDialogIfNeeded should show dialog if popup was not shown before',
        (WidgetTester tester) async {
      when(() => mockLocalStorageHelper.getValueToCheckReviewPopupShown())
          .thenAnswer((_) async => false);

      await tester.runAsync(() async {
        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: Scaffold(
            body: GestureDetector(
              onTap: () async {
                await handler.showRequestRatingDialogIfNeeded(
                  title: fakeTitle,
                  content: fakeContent,
                );
              },
              child: Text('Test Popup'),
            ),
          ),
        ));
      });

      await tester.pump();

      await tester.tap(find.text('Test Popup'));
      await tester.pumpAndSettle(
          Duration(milliseconds: InAppReviewHandlerMixin.delayRequestRatingPopupInMs));

      verifyShowPopup();
    });
  });

  testWidgets('verify onClose()', (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: GestureDetector(
            onTap: () async {
              await handler.onClose();
            },
            child: Text('Test'),
          ),
        ),
      ));
    });

    await tester.tap(find.text('Test'));

    verify(() => mockLocalStorageHelper.setValueToCheckReviewPopupShown(true)).called(1);
    verify(() => mockCommonNavigator.pop(any(), result: any(named: 'result'))).called(1);
  });

  group('verify requestRatingReview()', () {
    test('requestRatingReview with not internet connection', () async {
      when(() => mockNetworkManager.hasInternet).thenReturn(false);

      final ReviewStatus result = await handler.requestRatingReview();

      expect(result, ReviewStatus.lostConnection);
      verifyNever(() => mockCommonNavigator.pop(any(), result: any(named: 'result')));
      verifyNever(() => mockLocalStorageHelper.setValueToCheckReviewPopupShown(true));
    });

    test('requestRatingReview isAvailable', () async {
      when(() => mockNetworkManager.hasInternet).thenReturn(true);
      when(() => mockInAppReviewWrapper.isAvailable()).thenAnswer((_) => Future<bool>.value(true));
      when(() => mockInAppReviewWrapper.requestReview()).thenAnswer((_) => Future<void>.value());

      final ReviewStatus result = await handler.requestRatingReview();

      expect(result, ReviewStatus.available);
      verify(() => mockInAppReviewWrapper.requestReview()).called(1);
      verifyNever(
          () => mockInAppReviewWrapper.openStoreListing(appStoreId: any(named: 'appStoreId')));
    });

    test('requestRatingReview isAvailable with open store', () async {
      when(() => mockNetworkManager.hasInternet).thenReturn(true);
      when(() => mockInAppReviewWrapper.isAvailable()).thenAnswer((_) => Future<bool>.value(false));
      when(() => mockInAppReviewWrapper.requestReview()).thenAnswer((_) => Future<void>.value());

      final ReviewStatus result = await handler.requestRatingReview();

      expect(result, ReviewStatus.available);
      verifyNever(() => mockInAppReviewWrapper.requestReview());
      verify(() => mockInAppReviewWrapper.openStoreListing(appStoreId: any(named: 'appStoreId')))
          .called(1);
    });
  });

  group('verify openStoreListing()', () {
    test('openStoreListing should call openStoreListing method', () async {
      await handler.openStoreListing();

      verify(() => mockInAppReviewWrapper.openStoreListing(
            appStoreId: handler.evoAppAppleId,
          )).called(1);
    });
  });

  group('verify checkInternetConnection', () {
    test('checkInternetConnection should return true when internet is available', () async {
      when(() => mockNetworkManager.hasInternet).thenReturn(true);

      final bool result = await handler.checkInternetConnection();

      expect(result, true);
      verifyNever(() => mockSnackBar.show(any(),
          typeSnackBar: any(named: 'typeSnackBar'), durationInSec: any(named: 'durationInSec')));
    });

    test('checkInternetConnection should return false and show snackbar when no internet',
        () async {
      when(() => mockNetworkManager.hasInternet).thenReturn(false);

      final bool result = await handler.checkInternetConnection();

      expect(result, false);
      verify(() => mockSnackBar.show(
            CommonStrings.genericNoInternetErrorMessage,
            typeSnackBar: SnackBarType.error,
            durationInSec: SnackBarDuration.short.value,
          )).called(1);
    });
  });

  test('verify disposeShowRatingPopup()', () {
    expect(handler.isRequestingShowPopup, false);
    handler.disposeShowRatingPopup();
    expect(handler.isRequestingShowPopup, true);
  });
}
