import 'package:evoapp/data/response/credit_limit_widget_config_entity.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/widget/credit_limit_widget/credit_limit_title_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../../../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterTextStyle();
    getItRegisterColor();
  });

  tearDownAll(() {
    getIt.reset();
  });

  testWidgets(
    'creditStatus = null, '
    'should display right title & title color',
    (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: CreditLimitTitleWidget()));
      final Finder titleFinder = find.byType(Text);
      expect(titleFinder, findsOneWidget);
      final Text titleText = tester.widget(titleFinder);
      expect(titleText.data, EvoStrings.cardStatusTitleAwaitingForApproval);
      expect(titleText.style?.color, evoColors.cardStatusTitleNotReadyForPayment);
    },
  );

  testWidgets(
    'creditStatus = waiting_for_approval, '
    'should display right title & title color',
    (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: CreditLimitTitleWidget(
          creditLimitWidgetConfig:
              CreditLimitWidgetConfigEntity(creditStatus: 'waiting_for_approval'),
        ),
      ));

      final Finder titleFinder = find.byType(Text);
      expect(titleFinder, findsOneWidget);
      final Text titleText = tester.widget(titleFinder);
      expect(titleText.data, EvoStrings.cardStatusTitleAwaitingForApproval);
      expect(titleText.style?.color, evoColors.cardStatusTitleNotReadyForPayment);
    },
  );

  testWidgets(
    'creditStatus = not_ready_for_payment, '
    'should display right title & title color',
    (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: CreditLimitTitleWidget(
          creditLimitWidgetConfig:
              CreditLimitWidgetConfigEntity(creditStatus: 'not_ready_for_payment'),
        ),
      ));

      final Finder titleFinder = find.byType(Text);
      expect(titleFinder, findsOneWidget);
      final Text titleText = tester.widget(titleFinder);
      expect(titleText.data, EvoStrings.cardStatusTitleCreditLimit);
      expect(titleText.style?.color, evoColors.cardStatusTitleNotReadyForPayment);
    },
  );

  testWidgets(
    'creditStatus = ready_for_payment, '
    'should display right title & title color',
    (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: CreditLimitTitleWidget(
          creditLimitWidgetConfig: CreditLimitWidgetConfigEntity(creditStatus: 'ready_for_payment'),
        ),
      ));

      final Finder titleFinder = find.byType(Text);
      expect(titleFinder, findsOneWidget);
      final Text titleText = tester.widget(titleFinder);
      expect(titleText.data, EvoStrings.cardStatusTitleCreditLimit);
      expect(titleText.style?.color, evoColors.cardStatusTitleReadyForPaymentOrOutOfSync);
    },
  );

  testWidgets(
    'creditStatus = out_of_sync, '
    'should display right title & title color',
    (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: CreditLimitTitleWidget(
          creditLimitWidgetConfig: CreditLimitWidgetConfigEntity(creditStatus: 'out_of_sync'),
        ),
      ));

      final Finder titleFinder = find.byType(Text);
      expect(titleFinder, findsOneWidget);
      final Text titleText = tester.widget(titleFinder);
      expect(titleText.data, EvoStrings.cardStatusTitleCreditLimit);
      expect(titleText.style?.color, evoColors.cardStatusTitleReadyForPaymentOrOutOfSync);
    },
  );
}
