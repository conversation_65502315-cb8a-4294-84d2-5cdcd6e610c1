import 'dart:async';

import 'package:evoapp/feature/logging/evo_event_tracking_handler.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_utils/evo_event_tracking_utils.dart';
import 'package:evoapp/feature/logging/screen_action_define/special_action_event.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Create mock classes
class MockNetworkManager extends Mock implements NetworkManager {}

class MockEvoEventTrackingUtils extends Mock implements EvoEventTrackingUtils {}

void main() {
  late EvoEventTrackingHandler handler;
  late MockNetworkManager mockNetworkManager;
  late MockEvoEventTrackingUtils mockEvoEventTrackingUtils;

  setUp(() {
    getIt.registerSingleton<EvoEventTrackingHandler>(EvoEventTrackingHandler());
    mockNetworkManager = MockNetworkManager();
    mockEvoEventTrackingUtils = MockEvoEventTrackingUtils();
    handler = evoEventTrackingHandler;

    // Set up the dependency injection
    getIt.registerSingleton<NetworkManager>(mockNetworkManager);
    getIt.registerSingleton<EvoEventTrackingUtils>(mockEvoEventTrackingUtils);
  });

  tearDown(() {
    getIt.reset();
  });

  group('EvoEventTrackingHandler', () {
    test('should log initial session when there is internet', () {
      when(() => mockNetworkManager.hasInternet).thenReturn(true);

      handler.prepareInitialSession();

      verify(
        () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: SpecialActionEvent.initSession,
        ),
      ).called(1);
      expect(handler.isLoggedInitSessionEvent, true);
    });

    test('should not log initial session if already logged', () {
      when(() => mockNetworkManager.hasInternet).thenReturn(true);
      handler.isLoggedInitSessionEvent = true;

      handler.prepareInitialSession();

      verifyNever(
        () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: SpecialActionEvent.initSession,
        ),
      );
    });

    test('should observe network changes and log when internet is back', () async {
      when(() => mockNetworkManager.hasInternet).thenReturn(false);
      final StreamController<bool> streamController = StreamController<bool>();
      when(() => mockNetworkManager.myStreamNetwork).thenAnswer((_) => streamController.stream);

      handler.prepareInitialSession();

      streamController.add(true); // Simulate internet coming back
      await Future<void>.delayed(const Duration(milliseconds: 100));
      verify(
        () => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: SpecialActionEvent.initSession,
        ),
      ).called(1);
      expect(handler.isLoggedInitSessionEvent, true);

      streamController.close();
    });

    test('should not log if internet comes back but already logged', () {
      when(() => mockNetworkManager.hasInternet).thenReturn(false);
      final StreamController<bool> streamController = StreamController<bool>();
      when(() => mockNetworkManager.myStreamNetwork).thenAnswer((_) => streamController.stream);

      handler.isLoggedInitSessionEvent = true;
      handler.prepareInitialSession();

      streamController.add(true); // Simulate internet coming back

      verifyNever(() => mockEvoEventTrackingUtils.sendEvoSpecialEvent(
            eventActionId: SpecialActionEvent.initSession,
          ));

      streamController.close();
    });
  });
}
