import 'package:evoapp/data/response/dop_native/dop_native_register_entity.dart';
import 'package:evoapp/feature/dop_native/dialogs/input_phone_number/cubit/dop_native_input_phone_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeUserMessage = 'fakeUserMessage';

  group('DOPNativeInputPhoneNumberState', () {
    test('DOPNativeInputPhoneNumberInitial should be initialized correctly', () {
      final DOPNativeInputPhoneNumberInitial state = DOPNativeInputPhoneNumberInitial();
      expect(state, isA<DOPNativeInputPhoneNumberState>());
    });

    test('DOPNativeRegisterPhoneNumberSuccess', () {
      const String expectToken = 'expect_token';
      final DOPNativeRegisterEntity expectEntity = DOPNativeRegisterEntity(token: expectToken);

      final DOPNativeRegisterPhoneNumberSuccess state =
          DOPNativeRegisterPhoneNumberSuccess(expectEntity);
      expect(state, isA<DOPNativeRegisterPhoneNumberSuccess>());
      expect(state.entity.token, expectToken);
    });

    test('DOPNativeRegisterPhoneNumberCommonFail', () {
      const String expectErrorMsg = 'expect_error';
      final ErrorUIModel errorUIModel = ErrorUIModel(userMessage: expectErrorMsg);

      final DOPNativeRegisterPhoneNumberCommonFail state =
          DOPNativeRegisterPhoneNumberCommonFail(error: errorUIModel);
      expect(state, isA<DOPNativeRegisterPhoneNumberCommonFail>());
      expect(state.error, errorUIModel);
    });

    test('DOPNativeRegisterPhoneNumberDuplicate', () {
      const String expectErrorMsg = 'expect_error';
      final ErrorUIModel errorUIModel = ErrorUIModel(userMessage: expectErrorMsg);

      final DOPNativeRegisterPhoneNumberDuplicate state =
          DOPNativeRegisterPhoneNumberDuplicate(error: errorUIModel);
      expect(state, isA<DOPNativeRegisterPhoneNumberDuplicate>());
      expect(state.error, errorUIModel);
    });

    test('DOPNativeRegisterExistingRecord', () {
      final DOPNativeRegisterEntity registerEntity = DOPNativeRegisterEntity(
        existingApp: ExistingApp(
          isPriority: false,
          leadSource: 'evo_native',
          platform: ExistingAppPlatform.native,
          uniqueToken: 'abc123',
        ),
        merchantName: 'Merchant name',
      );

      final DOPNativeRegisterExistingRecord state = DOPNativeRegisterExistingRecord(registerEntity);
      expect(state, isA<DOPNativeRegisterExistingRecord>());
      expect(
          state.entity,
          isA<DOPNativeRegisterEntity>()
              .having(
                (DOPNativeRegisterEntity p) => p.merchantName,
                'verify merchant name',
                'Merchant name',
              )
              .having(
                (DOPNativeRegisterEntity p) => p.existingApp,
                'verify existing app',
                isA<ExistingApp>()
                    .having(
                      (ExistingApp p0) => p0.leadSource,
                      'verify lead source',
                      'evo_native',
                    )
                    .having(
                      (ExistingApp p0) => p0.isPriority,
                      'verify isPriority',
                      false,
                    )
                    .having(
                      (ExistingApp p0) => p0.platform,
                      'verify platform',
                      ExistingAppPlatform.native,
                    )
                    .having(
                      (ExistingApp p0) => p0.uniqueToken,
                      'verify uniqueToken',
                      'abc123',
                    ),
              ));
    });

    test('DOPNativeRegisterPhoneNumberLimitExceeded', () {
      const String expectErrorMsg = 'limit_exceeded';
      final ErrorUIModel errorUIModel = ErrorUIModel(userMessage: expectErrorMsg);

      final DOPNativeRegisterPhoneNumberLimitExceeded state =
          DOPNativeRegisterPhoneNumberLimitExceeded(error: errorUIModel);
      expect(state, isA<DOPNativeRegisterPhoneNumberLimitExceeded>());
      expect(state.error, errorUIModel);
    });

    test(
        'DOPNativeInputPhoneNumberCompleted should be an instance of DOPNativeInputPhoneNumberState',
        () {
      const String expectPhoneNumber = '123456789';
      final DOPNativeInputPhoneNumberCompleted state =
          DOPNativeInputPhoneNumberCompleted(expectPhoneNumber);
      expect(state, isA<DOPNativeInputPhoneNumberState>());
      expect(state.phoneNumber, expectPhoneNumber);
    });

    test('DOPNativeInputPhoneNumberFailed should contain the error message', () {
      final DOPNativeInputPhoneNumberFailed state =
          DOPNativeInputPhoneNumberFailed(fakeUserMessage);
      expect(state, isA<DOPNativeInputPhoneNumberState>());
      expect(state.errorMsg, fakeUserMessage);
    });

    test('ScreenLoading should be initialized correctly', () {
      final DOPNativeInputPhoneNumberLoading state = DOPNativeInputPhoneNumberLoading();
      expect(state, isA<DOPNativeInputPhoneNumberState>());
    });

    test('DOPNativeInputPhoneNumberError should contain the error model', () {
      final ErrorUIModel errorModel = ErrorUIModel(userMessage: fakeUserMessage);
      final DOPNativeInputPhoneNumberError state = DOPNativeInputPhoneNumberError(errorModel);
      expect(state, isA<DOPNativeInputPhoneNumberState>());
      expect(state.error, equals(errorModel));
    });

    test('DOPNativeHandleExistingNativeApp should contain the error model', () {
      const String uniqueToken = 'fake_token';
      bool callbackCalled = false;
      callback() {
        callbackCalled = true;
      }

      final DOPNativeHandleExistingNativeApp state = DOPNativeHandleExistingNativeApp(
        uniqueToken: uniqueToken,
        onLogEvent: callback,
      );

      expect(state, isA<DOPNativeInputPhoneNumberState>());
      expect(state.uniqueToken, equals(uniqueToken));
      expect(state.onLogEvent, isNotNull);

      //verify onLogEvent
      state.onLogEvent?.call();
      expect(callbackCalled, isTrue);
    });

    test('DOPNativeRegisterPhoneNumberExpiredPToken', () {
      const String expectErrorMsg = 'expect_error';
      final ErrorUIModel errorUIModel = ErrorUIModel(userMessage: expectErrorMsg);

      final DOPNativeRegisterPhoneNumberExpiredPToken state =
          DOPNativeRegisterPhoneNumberExpiredPToken(error: errorUIModel);
      expect(state, isA<DOPNativeInputPhoneNumberState>());
      expect(state.error, errorUIModel);
    });

    test('DOPNativeRegisterPhoneNumberInvalidPToken', () {
      const String expectErrorMsg = 'expect_error';
      final ErrorUIModel errorUIModel = ErrorUIModel(userMessage: expectErrorMsg);

      final DOPNativeRegisterPhoneNumberInvalidPToken state =
          DOPNativeRegisterPhoneNumberInvalidPToken(error: errorUIModel);
      expect(state, isA<DOPNativeInputPhoneNumberState>());
      expect(state.error, errorUIModel);
    });

    test('DOPNativeRegisterPhoneNumberDuplicateReject', () {
      final DOPNativeRegisterPhoneNumberDuplicateReject state =
          DOPNativeRegisterPhoneNumberDuplicateReject();
      expect(state, isA<DOPNativeInputPhoneNumberState>());
    });
  });
}
