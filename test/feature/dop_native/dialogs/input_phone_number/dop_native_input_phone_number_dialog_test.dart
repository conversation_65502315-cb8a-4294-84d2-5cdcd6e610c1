import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_register_entity.dart';
import 'package:evoapp/feature/dop_native/dialogs/input_phone_number/cubit/dop_native_input_phone_cubit.dart';
import 'package:evoapp/feature/dop_native/dialogs/input_phone_number/dop_native_input_phone_number_dialog.dart';
import 'package:evoapp/feature/dop_native/dialogs/input_phone_number/widgets/dop_native_term_and_condition_widget.dart';
import 'package:evoapp/feature/dop_native/features/status_screen/dop_native_status_screen.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_images.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_resources.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/dop_native/util/dop_native_navigation_utils.dart';
import 'package:evoapp/feature/dop_native/widgets/dop_loading/dop_loading_widget.dart';
import 'package:evoapp/feature/dop_native/widgets/text_field/dop_native_text_field_widget.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockBuildContext extends Mock implements BuildContext {}

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockDopNativeButtonStyles extends Mock implements DopNativeButtonStyles {}

class MockDOPNativeNavigationUtils extends Mock implements DOPNativeNavigationUtils {}

class MockDOPNativeInputPhoneNumberCubit extends MockCubit<DOPNativeInputPhoneNumberState>
    implements DOPNativeInputPhoneNumberCubit {}

class MockEventTrackingUtils extends Mock implements EventTrackingUtils {}

void main() {
  const String fakePhoneNumber = '**********';
  const String fakeCampaignCode = 'fakeCampaignCode';
  const String fakeSource = 'fakeSource';
  const String fakeUniqueToken = 'fakeUniqueToken';

  const ButtonStyle mockPrimaryMediumButtonStyle = ButtonStyle();
  const ButtonStyle mockTertiaryMediumButtonStyle = ButtonStyle();

  final LoggingRepo mockLoggingRepo = MockLoggingRepo();
  final BuildContext mockBuildContext = MockBuildContext();

  late CommonImageProvider evoImageProvider;
  late CommonNavigator commonNavigator;
  late AppState appState;
  late DOPNativeRepo dopNativeRepo;
  late EventTrackingUtils eventTrackingUtils;
  final DOPNativeInputPhoneNumberCubit mockDOPNativeInputPhoneNumberCubit =
      MockDOPNativeInputPhoneNumberCubit();
  late DOPNativeNavigationUtils dopNativeNavigationUtils;

  setUpAll(() {
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterColor();

    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
    getIt.registerLazySingleton<EventTrackingUtils>(() => MockEventTrackingUtils());
    eventTrackingUtils = getIt<EventTrackingUtils>();
    when(
      () => eventTrackingUtils.sendUserActionEvent(
        eventId: any(named: 'eventId'),
        metaData: any(named: 'metaData'),
      ),
    ).thenAnswer((_) => Future<void>.value());

    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());

    getIt.registerLazySingleton<DopNativeButtonStyles>(() => MockDopNativeButtonStyles());
    registerFallbackValue(ButtonSize.medium);
    when(() => dopNativeButtonStyles.primary(ButtonSize.medium))
        .thenReturn(mockPrimaryMediumButtonStyle);
    when(() => dopNativeButtonStyles.tertiary(ButtonSize.medium))
        .thenReturn(mockTertiaryMediumButtonStyle);

    getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());

    getIt.registerLazySingleton<DOPNativeRepo>(() => MockDOPNativeRepo());
    dopNativeRepo = getIt.get<DOPNativeRepo>();

    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());
    commonNavigator = getIt.get<CommonNavigator>();

    getIt.registerLazySingleton<LoggingRepo>(() => mockLoggingRepo);

    getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());

    getIt.registerSingleton<DOPUtilFunctions>(MockDOPUtilFunctions());

    getIt.registerLazySingleton<AppState>(() => AppState());
    appState = getIt.get<AppState>();

    getIt.registerFactory<DOPNativeInputPhoneNumberCubit>(() => mockDOPNativeInputPhoneNumberCubit);

    registerFallbackValue(mockBuildContext);

    when(() => mockLoggingRepo.logEvent(
          eventType: EventType.userAction,
          data: any(named: 'data'),
        )).thenAnswer((_) => Future<void>.value());

    getIt.registerSingleton<DOPNativeNavigationUtils>(MockDOPNativeNavigationUtils());
    dopNativeNavigationUtils = getIt.get<DOPNativeNavigationUtils>();
  });

  setUp(() {
    appState.dopNativeState.clear();
    appState.dopNativeState.campaignCode = fakeCampaignCode;
    appState.dopNativeState.source = fakeSource;

    evoImageProvider = getIt.get<CommonImageProvider>();
    when(() => evoImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    when(() => commonNavigator.pushReplacementNamed(mockBuildContext, any())).thenAnswer((_) {});

    when(() => dopNativeNavigationUtils.navigateToLandingPage()).thenReturn(null);
  });

  tearDown(() {
    reset(evoImageProvider);
    reset(commonNavigator);
    reset(dopNativeNavigationUtils);
  });

  tearDownAll(() {
    getIt.reset();
  });

  Future<void> handleDOPNativeInputPhoneNumberState(
    WidgetTester tester,
    DOPNativeInputPhoneNumberState state,
  ) async {
    final DOPNativeInputPhoneNumberDialogState dopNativeInputPhoneNumberDialogState =
        tester.state(find.byType(DOPNativeInputPhoneNumberDialog));
    dopNativeInputPhoneNumberDialogState.handleDOPNativeInputPhoneNumberState(state);
    await tester.pump();
  }

  void verifyIcon() {
    verify(() => evoImageProvider.asset(DOPNativeImages.icSMSPhone)).called(1);
  }

  void verifyDialogShowing() {
    expect(find.byType(Dialog), findsOneWidget);
    expect(find.byType(DOPNativeInputPhoneNumberDialog), findsOneWidget);
  }

  Future<void> verifyInputPhone(WidgetTester tester, {String? errorMessage}) async {
    final Finder inputPhoneFinder = find.byType(DOPNativeTextField);
    expect(inputPhoneFinder, findsOneWidget);
    final DOPNativeTextField inputPhone = tester.widget(inputPhoneFinder);
    expect(inputPhone.hintText, DOPNativeStrings.dopNativeInputPhoneHint);
    expect(inputPhone.keyboardType, TextInputType.phone);
    expect(inputPhone.errorText, errorMessage);
  }

  void verifyTitle(WidgetTester tester) {
    final Finder titleDialogFinder = find.text(DOPNativeStrings.dopNativeInputPhoneDialogTitle);
    expect(titleDialogFinder, findsOneWidget);
    final Widget titleDialog = tester.widget(titleDialogFinder);
    expect(titleDialog, isA<Text>());
    final Text titleDialogText = titleDialog as Text;
    expect(titleDialogText.style, dopNativeTextStyles.h500(color: dopNativeColors.textPrimary2));
  }

  void verifyTermCondition() {
    final Finder dopNativeTermAndConditionWidget = find.byType(DOPNativeTermAndConditionWidget);
    expect(dopNativeTermAndConditionWidget, findsOneWidget);
  }

  group('Test function show()', () {
    testWidgets('Test function show()', (WidgetTester tester) async {
      when(() => mockDOPNativeInputPhoneNumberCubit.state)
          .thenReturn(DOPNativeInputPhoneNumberInitial());

      await tester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  DOPNativeInputPhoneNumberDialog.show();
                },
                child: const Text('show dialog'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('show dialog'));
      await tester.pumpAndSettle(const Duration(seconds: 1));

      verifyDialogShowing();
    });
  });

  group('Test DOPNativeInputPhoneNumberDialog UI', () {
    setUpAll(() {
      when(() => dopNativeRepo.register(
            phoneNumber: any(named: 'phoneNumber'),
            campaignCode: any(named: 'campaignCode'),
            source: any(named: 'source'),
            collectedData: any(named: 'collectedData'),
            params: any(named: 'params'),
            signature: any(named: 'signature'),
            platform: any(named: 'platform'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async {
        return DOPNativeRegisterEntity.fromBaseResponse(
          BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: <String, dynamic>{'token': fakeUniqueToken},
          ),
        );
      });
    });

    testWidgets('Test state DOPNativeInputPhoneNumberInitial', (WidgetTester tester) async {
      when(() => mockDOPNativeInputPhoneNumberCubit.state)
          .thenReturn(DOPNativeInputPhoneNumberInitial());
      await tester.runAsync(() async {
        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: DOPNativeInputPhoneNumberDialog(),
        ));

        verifyIcon();

        verifyTitle(tester);

        await verifyInputPhone(tester);

        verifyTermCondition();
      });
    });

    testWidgets('Test state DOPNativeInputPhoneNumberCompleted', (WidgetTester tester) async {
      await tester.runAsync(() async {
        when(() => mockDOPNativeInputPhoneNumberCubit.state)
            .thenReturn(DOPNativeInputPhoneNumberCompleted(fakePhoneNumber));

        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: DOPNativeInputPhoneNumberDialog(),
        ));

        verifyIcon();

        verifyTitle(tester);

        await verifyInputPhone(tester);

        verifyTermCondition();
      });
    });

    testWidgets('Test state DOPNativeInputPhoneNumberFailed', (WidgetTester tester) async {
      await tester.runAsync(() async {
        const String fakeErrorMessage = 'fakeErrorMessage';
        when(() => mockDOPNativeInputPhoneNumberCubit.state)
            .thenReturn(DOPNativeInputPhoneNumberFailed(fakeErrorMessage));

        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: DOPNativeInputPhoneNumberDialog(),
        ));

        verifyIcon();

        verifyTitle(tester);

        await verifyInputPhone(tester, errorMessage: fakeErrorMessage);

        verifyTermCondition();
      });
    });

    testWidgets('Test state DOPNativeInputPhoneNumberLoading', (WidgetTester tester) async {
      await tester.runAsync(() async {
        when(() => mockDOPNativeInputPhoneNumberCubit.state)
            .thenReturn(DOPNativeInputPhoneNumberLoading());

        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: DOPNativeInputPhoneNumberDialog(),
        ));

        verifyIcon();

        verifyTitle(tester);

        await verifyInputPhone(tester);

        verifyTermCondition();
      });
    });

    testWidgets('Test state DOPNativeInputPhoneNumberError', (WidgetTester tester) async {
      await tester.runAsync(() async {
        when(() => mockDOPNativeInputPhoneNumberCubit.state)
            .thenReturn(DOPNativeInputPhoneNumberError(ErrorUIModel()));

        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: DOPNativeInputPhoneNumberDialog(),
        ));

        verifyIcon();

        verifyTitle(tester);

        await verifyInputPhone(tester);

        verifyTermCondition();
      });
    });
  });

  group('Test handleDOPNativeInputPhoneNumberState', () {
    setUpAll(() {
      when(() => mockDOPNativeInputPhoneNumberCubit.state)
          .thenReturn(DOPNativeInputPhoneNumberInitial());
    });

    testWidgets('Test DOPNativeInputPhoneNumberLoading', (WidgetTester tester) async {
      await tester.runAsync(() async {
        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: DOPNativeInputPhoneNumberDialog(),
        ));

        await handleDOPNativeInputPhoneNumberState(tester, DOPNativeInputPhoneNumberLoading());

        expect(find.byType(DOPLoadingWidget), findsOneWidget);
      });
    });

    testWidgets('Test DOPNativeInputPhoneNumberError', (WidgetTester tester) async {
      await tester.runAsync(() async {
        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: DOPNativeInputPhoneNumberDialog(),
        ));

        await handleDOPNativeInputPhoneNumberState(
          tester,
          DOPNativeInputPhoneNumberError(ErrorUIModel()),
        );

        expect(find.byType(DOPLoadingWidget), findsNothing);

        expect(
          verify(() => commonNavigator.pushNamed(
                any(),
                Screen.dopNativeStatusScreen.name,
                extra: captureAny(named: 'extra'),
              )).captured,
          <dynamic>[
            isA<DOPNativeStatusScreenArg>()
                .having(
                  (DOPNativeStatusScreenArg arg) => arg.title,
                  'verify title',
                  DOPNativeStrings.dopNativeCommonErrorTitle,
                )
                .having(
                  (DOPNativeStatusScreenArg arg) => arg.description,
                  'verify description',
                  DOPNativeStrings.dopNativeCommonErrorDescription,
                )
                .having(
                  (DOPNativeStatusScreenArg arg) => arg.icon,
                  'verify icon',
                  DOPNativeImages.icOtpCodeError,
                )
          ],
        );
      });
    });

    testWidgets('Test DOPNativeInputPhoneNumberError with status code is SOCKET_ERROR',
        (WidgetTester tester) async {
      await tester.runAsync(() async {
        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: DOPNativeInputPhoneNumberDialog(),
        ));

        await handleDOPNativeInputPhoneNumberState(
          tester,
          DOPNativeInputPhoneNumberError(ErrorUIModel(
            statusCode: CommonHttpClient.SOCKET_ERRORS,
          )),
        );

        expect(find.byType(DOPLoadingWidget), findsNothing);

        expect(
          verify(() => commonNavigator.pushNamed(
                any(),
                Screen.dopNativeStatusScreen.name,
                extra: captureAny(named: 'extra'),
              )).captured,
          <dynamic>[
            isA<DOPNativeStatusScreenArg>().having(
              (DOPNativeStatusScreenArg arg) => arg,
              'verify verdict not null',
              isNotNull,
            )
          ],
        );
      });
    });

    testWidgets('Test DOPNativeInputPhoneNumberError with status code is NO_INTERNET',
        (WidgetTester tester) async {
      await tester.runAsync(() async {
        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: DOPNativeInputPhoneNumberDialog(),
        ));

        await handleDOPNativeInputPhoneNumberState(
          tester,
          DOPNativeInputPhoneNumberError(ErrorUIModel(
            statusCode: CommonHttpClient.NO_INTERNET,
          )),
        );

        expect(find.byType(DOPLoadingWidget), findsNothing);

        expect(
          verify(() => commonNavigator.pushNamed(
                any(),
                Screen.dopNativeStatusScreen.name,
                extra: captureAny(named: 'extra'),
              )).captured,
          <dynamic>[
            isA<DOPNativeStatusScreenArg>().having(
              (DOPNativeStatusScreenArg arg) => arg,
              'verify verdict is not null',
              isNotNull,
            )
          ],
        );
      });
    });

    testWidgets('Test DOPNativeRegisterPhoneNumberLimitExceeded', (WidgetTester tester) async {
      await tester.runAsync(() async {
        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: DOPNativeInputPhoneNumberDialog(),
        ));

        await handleDOPNativeInputPhoneNumberState(
          tester,
          DOPNativeRegisterPhoneNumberLimitExceeded(
            error: ErrorUIModel(userMessage: CommonStrings.otherGenericErrorMessage),
          ),
        );

        expect(find.byType(DOPLoadingWidget), findsNothing);

        verify(() => commonNavigator.pop(any(), result: any(named: 'result'))).called(1);
        expect(
          verify(() => commonNavigator.pushNamed(
                any(),
                Screen.dopNativeStatusScreen.name,
                extra: captureAny(named: 'extra'),
              )).captured,
          <dynamic>[
            isA<DOPNativeStatusScreenArg>()
                .having(
                  (DOPNativeStatusScreenArg arg) => arg.title,
                  'verify title',
                  DOPNativeStrings.dopNativeOTPRateLimitErrorTitle,
                )
                .having(
                  (DOPNativeStatusScreenArg arg) => arg.description,
                  'verify description',
                  DOPNativeStrings.dopNativeOTPRateLimitErrorDescription,
                )
                .having(
                  (DOPNativeStatusScreenArg arg) => arg.icon,
                  'verify icon',
                  DOPNativeImages.icOtpRateLimit,
                )
          ],
        );
      });
    });

    testWidgets('Test DOPNativeRegisterPhoneNumberCommonFail', (WidgetTester tester) async {
      await tester.runAsync(() async {
        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: DOPNativeInputPhoneNumberDialog(),
        ));

        await handleDOPNativeInputPhoneNumberState(
          tester,
          DOPNativeRegisterPhoneNumberCommonFail(
            error: ErrorUIModel(userMessage: CommonStrings.otherGenericErrorMessage),
          ),
        );

        expect(find.byType(DOPLoadingWidget), findsNothing);

        verify(() => commonNavigator.pop(any(), result: any(named: 'result'))).called(1);
        expect(
          verify(() => commonNavigator.pushNamed(
                any(),
                Screen.dopNativeStatusScreen.name,
                extra: captureAny(named: 'extra'),
              )).captured,
          <dynamic>[
            isA<DOPNativeStatusScreenArg>()
                .having(
                  (DOPNativeStatusScreenArg arg) => arg.title,
                  'verify title',
                  DOPNativeStrings.dopNativeCommonErrorTitle,
                )
                .having(
                  (DOPNativeStatusScreenArg arg) => arg.description,
                  'verify description',
                  DOPNativeStrings.dopNativeCommonErrorDescription,
                )
                .having(
                  (DOPNativeStatusScreenArg arg) => arg.icon,
                  'verify icon',
                  DOPNativeImages.icOtpCodeError,
                )
          ],
        );
      });
    });
  });
}
