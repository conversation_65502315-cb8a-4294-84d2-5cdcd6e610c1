import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('verify EvoStrings', () {
    test('should have correct values', () {
      /// Common
      expect(DOPNativeStrings.dopNativeHelpChat, 'Trợ giúp');
      expect(DOPNativeStrings.dopNativeScreenTitle, 'Đăng ký mở thẻ tại cửa hàng');
      expect(DOPNativeStrings.dopOnWebTitle, 'Đăng ký mở thẻ');
      expect(DOPNativeStrings.dopNativeAnd, 'và');
      expect(DOPNativeStrings.dopNativeOr, 'hoặc');
      expect(DOPNativeStrings.dopNativeOur, 'về bảo vệ dữ liệu cá nhân của TPBank');
      expect(DOPNativeStrings.dopNativeNote, 'Lưu ý');
      expect(DOPNativeStrings.dopNativeOpenEVOCard, 'Mở thẻ ngay');
      expect(DOPNativeStrings.dopNativeRetry, 'Th<PERSON> lại');
      expect(DOPNativeStrings.dopNativeSetup, 'Cài đặt');
      expect(DOPNativeStrings.dopNativeIgnore, 'Bỏ qua');
      expect(DOPNativeStrings.dopNativeDoNot, 'Không');
      expect(DOPNativeStrings.dopNativeNext, 'Tiếp tục');
      expect(DOPNativeStrings.dopNativeMinute, 'phút');
      expect(DOPNativeStrings.dopNativeHour, 'giờ');
      expect(DOPNativeStrings.dopNativeConfirm, 'Đồng ý');
      expect(DOPNativeStrings.dopNativeDone, 'Hoàn tất');
      expect(DOPNativeStrings.dopNativeActive, 'Kích hoạt');
      expect(DOPNativeStrings.dopNativeDownload, 'Tải ngay');
      expect(DOPNativeStrings.dopNativeDownloadTPBankMobile, 'Tải TPBank Mobile');
      expect(DOPNativeStrings.dopNativeAgree, 'Xác nhận');
      expect(DOPNativeStrings.dopNativeTPBankAppName, 'TPBank Mobile');
      expect(DOPNativeStrings.dopNativeReceiveVoucher, 'Nhận ưu đãi');
      expect(DOPNativeStrings.dopNativeClose, 'Đóng');
      expect(DOPNativeStrings.dopNativeGoToHome, 'Về trang chủ');
      expect(DOPNativeStrings.dopNativeAutoNavigateAfter, 'Tự động chuyển màn hình sau');

      /// OTP screen
      expect(DOPNativeStrings.dopNativeVerifyOtpTitle, 'Xác thực số điện thoại');
      expect(DOPNativeStrings.dopNativeVerifyOtpDescription,
          'Vui lòng nhập mã xác thực (OTP) đã được gửi đến số điện thoại của bạn.');
      expect(DOPNativeStrings.dopNativeNote1, 'Bạn chỉ được nhập 1 mã OTP tối đa 3 lần.');
      expect(DOPNativeStrings.dopNativeNote2,
          'Phiên làm việc sẽ tạm ngưng 30 phút nếu gửi lại OTP quá 4 lần.');
      expect(DOPNativeStrings.otpGeneralErrorMessage, 'Mã OTP không đúng, vui lòng nhập lại.');

      /// Error Screen
      expect(DOPNativeStrings.dopNativeCommonErrorTitle, 'Có lỗi xảy ra');
      expect(DOPNativeStrings.dopNativeCommonErrorDescription, 'Vui lòng thử lại sau ít phút');
      expect(DOPNativeStrings.dopNativeCommonErrorButtonTitle, 'Thử lại');
      expect(DOPNativeStrings.dopNativeCommonBackToHomeButtonTitle, 'Quay lại');
      expect(DOPNativeStrings.dopNativeOTPCodeErrorTitle, 'Lỗi mã xác thực');
      expect(DOPNativeStrings.dopNativeOTPCodeErrorDescription,
          'Mã xác thực lỗi hoặc đã hết hiệu lực, vui lòng nhấn vào nút bên dưới để nhận lại mã');
      expect(DOPNativeStrings.dopNativeOTPCodeErrorButtonTitle, 'Gửi lại mã xác thực');
      expect(DOPNativeStrings.dopNativeOTPRateLimitErrorTitle, 'Vượt quá số lần nhập\nmã xác thực');
      expect(DOPNativeStrings.dopNativeOTPRateLimitErrorDescription,
          'Bạn đã nhập mã xác thực quá số lần cho phép. Vui lòng quay lại sau 30 phút để tiếp tục');
      expect(DOPNativeStrings.dopNativeOTPTimeoutErrorTitle, 'Hết thời hạn đăng ký');
      expect(DOPNativeStrings.dopNativeOTPTimeoutErrorDescription,
          'Đã quá thời gian đăng ký sản phẩm ưu đãi dành cho quý khách. Vui lòng liên hệ 1900 58 58 85 để được hỗ trợ');
      expect(DOPNativeStrings.dopNativeOTPTimeoutErrorButtonTitle, 'Đăng ký lại');
      expect(DOPNativeStrings.otpDidNotReceive, 'Không nhận được mã OTP?');
      expect(DOPNativeStrings.otpResend, 'Gửi lại OTP');

      expect(DOPNativeStrings.dopNativeRegisterPTokenErrorTitle, 'Mã QR hết hiệu lực');
      expect(DOPNativeStrings.dopNativeRegisterPTokenErrorDescription,
          'Mã QR mở thẻ đã hết thời gian hiệu lực, bạn vui lòng liên hệ nhân viên bán hàng để quét lại');
      expect(DOPNativeStrings.dopNativeRegisterPTokenErrorCTAButton, 'Quét lại mã QR');

      /// Input phone number Dialog
      expect(DOPNativeStrings.dopNativeInputPhoneDialogTitle,
          'Vui lòng nhập số điện thoại để xác thực và đăng ký');
      expect(DOPNativeStrings.dopNativeInputPhoneHint, 'Số điện thoại của bạn');
      expect(DOPNativeStrings.dopNativeInputPhoneNumberDescription,
          'Tôi xác nhận đã đọc, hiểu rõ và đồng ý về');
      expect(DOPNativeStrings.dopNativeInputPhoneNumberTermConditionAppDescription,
          'Điều khoản sử dụng dịch vụ');
      expect(DOPNativeStrings.dopNativeInputPhoneNumberTermConditionPersonalDescription,
          'Điều kiện điều khoản');
      expect(DOPNativeStrings.dopNativeInputPhoneNumberFormatFailed,
          'SĐT sai định dạng, vui lòng nhập lại');
      expect(DOPNativeStrings.dopNativeInputPhoneNumberEmptyFailed, 'Vui lòng nhập số điện thoại');

      /// Rejection screen
      expect(DOPNativeStrings.rejectionContentTitle, 'Đăng ký chưa thành công');
      expect(DOPNativeStrings.rejectionContentDescription1,
          'Hiện tại chúng tôi chưa tìm được sản phẩm thẻ phù hợp với bạn.\nBạn có thể liên hệ');
      expect(DOPNativeStrings.rejectionContentDescription2,
          'để được tư vấn thêm hoặc tham khảo các sản phẩm thẻ tín dụng khác của TPBank.');
      expect(DOPNativeStrings.rejectionButtonTitle, 'Xem các sản phẩm khác');

      /// Sub Introduction Screen
      expect(DOPNativeStrings.dopNativeSubIntroductionFooter,
          'Đăng ký 100% online,\nphê duyệt tức thì');

      /// FaceOTP
      expect(DOPNativeStrings.faceOtpIntroductionTitle, 'Chào mừng bạn đã trở lại');
      expect(DOPNativeStrings.faceOtpIntroductionDescription,
          'Để tiếp tục hành trình mở thẻ, xin vui lòng đăng nhập bằng cách chụp ảnh chân dung (selfie) \ncủa bạn.');
      expect(DOPNativeStrings.faceOtpIntroductionCTA, 'Mở camera');
      expect(DOPNativeStrings.faceOtpSuccessTitle, 'Xác thực khuôn mặt\nthành công!');

      /// Id verification Instruction Screen
      expect(
          DOPNativeStrings.dopNativeIdVerificationInstructionDescTitle, 'Xác thực CCCD\ngắn chip');
      expect(DOPNativeStrings.dopNativeIdVerificationInstructionDesc,
          'Thông tin từ CCCD gắn chip sẽ được hệ thống tự động đọc và điền giúp bạn. Vui lòng kiểm tra lại thông tin sau khi hoàn thành bước chụp.');
      expect(DOPNativeStrings.dopNativeStartCapture, 'Bắt đầu chụp');
      expect(
          DOPNativeStrings.dopNativeIdVerificationInstructionAware1Prefix, 'Chỉnh hướng chụp để');
      expect(DOPNativeStrings.dopNativeIdVerificationInstructionAware1Suffix, 'bị loá sáng');
      expect(DOPNativeStrings.dopNativeIdVerificationInstructionAware2Suffix,
          'dùng giấy tờ mất góc, bị nhòe, mờ');
      expect(DOPNativeStrings.dopNativeIdVerificationInstructionAware3Suffix,
          'dùng bản sao hoặc không chính chủ');
      expect(DOPNativeStrings.dopNativeIdVerificationInstructionGraphicCaption,
          'Chụp ảnh 2 mặt CCCD gắn chip');
      expect(
          DOPNativeStrings.dopNativeIdVerificationOpenSettingDialogTitle, 'Quyền truy cập Camera');
      expect(DOPNativeStrings.dopNativeIdVerificationOpenSettingDialogContent,
          'Kích hoạt Camera trong phần cài đặt để thực hiện tính năng xác thực CCCD gắn chip.');

      /// ekyc_ui_only Processing Screen
      expect(DOPNativeStrings.dopNativeEkycProcessingTitle, 'Vui lòng không đóng ứng dụng');
      expect(DOPNativeStrings.dopNativeEkycProcessingDesc,
          'Hệ thống đang kiểm tra\nthông tin trên CMND/CCCD của bạn.');

      /// ekyc_ui_only Success Screen
      expect(DOPNativeStrings.dopNativeIdCardVerificationSuccessTitle,
          'Xác thực CCCD/CMND\nthành công');
      expect(DOPNativeStrings.dopNativeIdCardVerificationSuccessDesc,
          'Mời bạn tiếp tục xác thực CCCD gắn chip ở bước tiếp theo');

      /// ekyc Error Screen
      expect(DOPNativeStrings.dopNativeEkycInvalidImageCapturedTitle,
          'Rất tiếc, ảnh của bạn chưa\nhợp lệ. Bạn thực hiện lại nhé!');
      expect(DOPNativeStrings.dopNativeEkycExceedCapturingLimitTitle, 'Vượt quá số lần chụp');
      expect(DOPNativeStrings.dopNativeEkycInvalidIDVerificationTitle,
          'Giấy tờ không phải là CCCD gắn chip');
      expect(DOPNativeStrings.dopNativeEkycInvalidIDVerificationDesc,
          'Vui lòng sử dụng CCCD gắn chip để  \nđăng ký mở thẻ online');

      //ekyc Selfie Verification Introduction Screen
      expect(DOPNativeStrings.dopNativeSelfieCaptureIntroductionTitle, 'Xác thực\nkhuôn mặt');
      expect(DOPNativeStrings.dopNativeSelfieCaptureInstructionDesc,
          'Chụp ảnh chân dung (selfie) để xác thực hồ sơ theo quy định của Ngân hàng Nhà nước. Mọi thông tin của bạn được bảo mật an toàn.');
      expect(DOPNativeStrings.dopNativeSelfieCaptureAware1, 'Luôn giữ đầu trong khung hình');
      expect(DOPNativeStrings.dopNativeSelfieCaptureAwareSuffix2,
          'đeo kính râm, nón hoặc các phụ kiện che mặt');
      expect(DOPNativeStrings.dopNativeSelfieCaptureAwarePrefix3, 'Môi trường chụp');
      expect(DOPNativeStrings.dopNativeSelfieCaptureAwareSuffix3, 'quá tối hoặc chói sáng');
      expect(DOPNativeStrings.dopNativeSelfieVerificationTitle,
          'Hệ thống đang kiểm tra ảnh chân dung của bạn.');
      expect(
          DOPNativeStrings.dopNativeSelfieVerificationDescription, 'Vui lòng không đóng ứng dụng');
      expect(DOPNativeStrings.dopNativeSelfieVerificationSuccessTitle,
          'Xác thực khuôn mặt\nthành công!');
      expect(DOPNativeStrings.dopNativeSelfieVerificationTitle,
          'Hệ thống đang kiểm tra ảnh chân dung của bạn.');
      expect(
          DOPNativeStrings.dopNativeSelfieVerificationDescription, 'Vui lòng không đóng ứng dụng');
      expect(DOPNativeStrings.dopNativeSelfieVerificationTitle,
          'Hệ thống đang kiểm tra ảnh chân dung của bạn.');
      expect(
          DOPNativeStrings.dopNativeSelfieVerificationDescription, 'Vui lòng không đóng ứng dụng');
      expect(DOPNativeStrings.dopNativeDefaultSelfieEkycErrorMessage,
          'Ảnh chụp không hợp lệ. Vui lòng chụp ảnh chân dung rõ nét, nhìn thẳng vào camera và khuôn mặt chiếm 70%-80% ảnh, không đội mũ/ đeo kính râm/ khẩu trang/ xoã tóc trước mặt, không bị chói sáng.  \n**Lưu ý: Bạn chỉ có tối đa 5 lần chụp**');
      expect(DOPNativeStrings.dopNativeDefaultEkycErrorMessage,
          'Ảnh chụp không hợp lệ. Vui lòng đặt CMND/CCCD vào khung hình của máy ảnh, hình chụp đầy đủ, rõ ràng, không có vật che, không bị mờ/chói sáng.  \n**Lưu ý: Bạn chỉ có tối đa 5 lần chụp**');
      expect(DOPNativeStrings.dopNativeEkycExceedCapturingLimitDesc,
          'Bạn đã vượt quá số lần chụp hình, vui lòng thử lại sau 30 phút.');
      expect(DOPNativeStrings.dopNativeSelfieVerificationOpenSettingDialogTitle,
          'Quyền truy cập Camera');
      expect(DOPNativeStrings.dopNativeSelfieVerificationOpenSettingDialogContent,
          'Kích hoạt Camera trong phần cài đặt để thực hiện tính năng xác thực khuôn mặt.');

      /// Metadata PopUp
      expect(DOPNativeStrings.dopNativeSearchInputHint, 'Tìm Kiếm...');

      // Confirm close DOP Native flow dialog
      expect(DOPNativeStrings.confirmCloseDOPNativeFlowTitle,
          'Bạn có chắc muốn dừng hành trình mở thẻ không?');
      expect(DOPNativeStrings.confirmCloseDOPNativeFlowContent,
          '“Tiếp tục” hành trình để không bỏ lỡ các ưu đãi hấp dẫn từ thẻ TPBank EVO');

      // Address Dialog
      expect(DOPNativeStrings.dopNativeProvinceDialogTitle, 'Tỉnh/Thành phố');
      expect(DOPNativeStrings.dopNativeDistrictDialogTitle, 'Quận/Huyện');
      expect(DOPNativeStrings.dopNativeWardDialogTitle, 'Phường/Xã');

      // ID Verification Confirm screen
      expect(DOPNativeStrings.dopNativeIdVerificationName, 'Họ và tên');
      expect(DOPNativeStrings.dopNativeIdVerificationIdCard, 'Số CCCD');
      expect(DOPNativeStrings.dopNativeIdVerificationOldIdCard, 'Số CMND cũ');
      expect(DOPNativeStrings.dopNativeIdVerificationDOB, 'Ngày sinh');
      expect(DOPNativeStrings.dopNativeIdVerificationGender, 'Giới tính');
      expect(DOPNativeStrings.dopNativeIdVerificationPlaceOfIssue, 'Nơi cấp');
      expect(
          DOPNativeStrings.dopNativeIdVerificationPermanentResidence, 'Địa chỉ trên sổ\nhộ khẩu');
      expect(DOPNativeStrings.dopNativeIdVerificationOldIdCardInput, 'Vui lòng bổ sung nếu có');
      expect(DOPNativeStrings.dopNativeIdVerificationConfirmIndicatorTitle, 'Xác nhận thông tin');
      expect(DOPNativeStrings.dopNativeIdVerificationConfirmScreenTitle, 'Thông tin trên CCCD');
      expect(DOPNativeStrings.dopNativeIdVerificationConfirmScreenDesc,
          'Vui lòng kiểm tra thông tin từ CCCD và bổ sung một số thông tin cần thiết.');
      expect(DOPNativeStrings.dopNativeIdVerificationPermanentResidenceInputTextTitle,
          'Địa chỉ trên sổ hộ khẩu');
      expect(DOPNativeStrings.dopNativeIdVerificationPermanentResidenceInputTextTitleAddition,
          'cụ thể');
      expect(DOPNativeStrings.dopNativeIdVerificationPermanentResidenceInputTextHint,
          'Chọn địa chỉ khu vực');
      expect(DOPNativeStrings.dopNativeIdVerificationStreetInputTextHint, 'Nhập số nhà, tên đường');
      expect(DOPNativeStrings.dopNativeInputPermanentAddressTooLongFailed,
          'Không được dài hơn {0} kí tự');
      expect(DOPNativeStrings.dopNativeInputPermanentAddressSpecialCharacterFailed,
          'Không được chứa ký tự đặc biệt (VD: !@#\$%^&*())');
      expect(DOPNativeStrings.dopNativePermanentAddressEmptyFailed, 'Vui lòng nhập địa chỉ');
      expect(
          DOPNativeStrings.dopNativeResidenceAddressEmptyFailed, 'Vui lòng chọn địa chỉ khu vực');

      // Employment Dialog
      expect(DOPNativeStrings.dopNativeEmploymentStatusTitle, 'Tình trạng việc làm');
      expect(DOPNativeStrings.dopNativeEmploymentTitle, 'Nghề nghiệp');

      // Appraising verification screen
      expect(DOPNativeStrings.dopNativeAppraisingVerificationTitle, 'TPBank đang xử lý thông tin');
      expect(DOPNativeStrings.dopNativeAppraisingVerificationDesc, 'Vui lòng không đóng ứng dụng');

      // Internet error
      expect(DOPNativeStrings.dopNativeInternetErrorTitle, 'Lỗi kết nối');
      expect(DOPNativeStrings.dopNativeInternetErrorDesc,
          'Vui lòng kiểm tra kết nối mạng của bạn và nhấn nút bên dưới để tiếp tục');

      // Invalid token
      expect(DOPNativeStrings.dopNativeInvalidTokenTitle, 'Phiên làm việc hết hạn');
      expect(DOPNativeStrings.dopNativeInvalidTokenDesc,
          'Phiên làm việc của bạn đã hết hạn, vui lòng nhấn vào nút bên dưới để tiếp tục');
      expect(DOPNativeStrings.dopNativeInvalidTokenButtonTitle, 'Thử lại');

      // Inform success screen
      expect(DOPNativeStrings.dopNativeApproveSuccess, 'Phê duyệt thẻ thành công');
      expect(DOPNativeStrings.dopNativeRegisterSuccessAutoDesc1,
          'Chúc mừng bạn đã được phê duyệt thẻ tín dụng TPBank EVO với hạn mức');
      expect(DOPNativeStrings.dopNativeRegisterSuccessAutoDesc2,
          'Vui lòng bổ sung một số thông tin liên hệ để chúng tôi hoàn thiện hồ sơ và giao thẻ.');
      expect(DOPNativeStrings.dopNativeVietnameseCurrencySymbolFull, 'đồng');
      expect(DOPNativeStrings.dopNativeInformSuccessTitle, 'Bổ sung thông tin');
      expect(DOPNativeStrings.dopNativeInformSuccessDesc,
          'Vui lòng bổ sung thông tin để chúng tôi hoàn thiện hồ sơ');

      // ID confirm additional info screen
      expect(DOPNativeStrings.dopNativeAdditionalInfoScreenTitle, 'Thông tin bổ sung');
      expect(DOPNativeStrings.dopNativePaymentTypeTitle, 'Hình thức nhận lương');
      expect(DOPNativeStrings.dopNativeAddressInputHint, 'Chọn theo khu vực');
      expect(DOPNativeStrings.dopNativeAddressInputLabel, 'Địa chỉ nơi ở hiện tại');
      expect(DOPNativeStrings.dopNativeEmploymentInputHint, 'Chọn');
      expect(DOPNativeStrings.dopNativeEmploymentInputLabel, 'Tình trạng việc làm và Nghề nghiệp');
      expect(DOPNativeStrings.dopNativeEmailInputHint, 'Email');
      expect(DOPNativeStrings.dopNativeEmailInputLabel, 'Nhập email của bạn');
      expect(DOPNativeStrings.dopNativeIncomeInputHint, 'Nhập số tiền');
      expect(
          DOPNativeStrings.dopNativeIncomeInputLabel, 'Thu nhập hàng tháng (Đơn vị: Triệu đồng)');
      expect(
          DOPNativeStrings.dopNativePaymentTypeEmptyErrorMsg, 'Vui lòng chọn hình thức nhận lương');
      expect(DOPNativeStrings.dopNativeEmailEmptyErrorMsg, 'Vui lòng nhập email');
      expect(DOPNativeStrings.dopNativeEmailInvalidErrorMsg, 'Email không đúng định dạng');
      expect(DOPNativeStrings.dopNativeIncomeEmptyErrorMsg, 'Vui lòng nhập thu nhập hàng tháng');
      expect(DOPNativeStrings.dopNativeIncomeTooSmallErrorMsg, 'Thu nhập phải lớn hơn {0}');
      expect(DOPNativeStrings.dopNativeIncomeInvalidErrorMsg, 'Không đúng định dạng');
      expect(
          DOPNativeStrings.dopNativeIncomeExceedCapPrefix, 'Thu nhập kê khai không được vượt quá');
      expect(DOPNativeStrings.dopNativeIncomeExceedCapPostfix, 'triệu');
      expect(DOPNativeStrings.dopNativeEmploymentEmptyErrorMsg,
          'Vui lòng chọn Tình trạng việc làm và Nghề nghiệp');

      /// CIF Confirm screen
      expect(DOPNativeStrings.cifConfirmUpdateInfoAtCounterTitle, 'Cập nhật thông tin tại quầy');
      expect(DOPNativeStrings.cifConfirmOpenCardInfoTitle, 'Xác nhận thông tin mở thẻ');
      expect(DOPNativeStrings.cifConfirmNoBranchTitle, 'Đăng ký chưa thành công');

      // Subtitle
      expect(DOPNativeStrings.cifConfirmExistingCustomerSubtitle,
          'Bạn là khách hàng hiện hữu của TPBank.');
      expect(DOPNativeStrings.cifConfirmNoBranchSubTitle,
          'Thông tin mở thẻ chưa khớp với thông tin định danh (CIF) đã đăng ký tại TPBank. Tạm thời chúng tôi chưa thể mở thẻ cho bạn');

      // Without CIF Info
      expect(DOPNativeStrings.cifConfirmPleaseOpenCardNote,
          'Vui lòng chọn "Mở thẻ" để xác nhận bạn đồng ý mở thẻ tín dụng TPBank EVO với thông tin số CMND/CCCD đã đăng ký dịch vụ tại TPBank.');
      expect(DOPNativeStrings.cifConfirmUpdateIDCardNote,
          'Nếu bạn muốn mở thẻ với số CMND/CCCD hiện tại, vui lòng đến Chi nhánh/ Phòng giao dịch gần nhất của TPBank trong vòng 7 ngày để cập nhật thông tin và quay lại EVO app, chọn "Tôi đã cập nhật thông tin" để tiếp tục mở thẻ.');

      expect(DOPNativeStrings.cifConfirmDifPhoneNote,
          'Số điện thoại bạn đăng ký mở thẻ tín dụng không khớp với số điện thoại bạn đã đăng ký dịch vụ trước đó tại TPBank');
      expect(DOPNativeStrings.cifConfirmDifCifNote,
          'Thông tin giấy tờ tùy thân đang sử dụng hiện chưa khớp đúng với thông tin mà bạn đã đăng ký với TPBank trước đó.');
      expect(DOPNativeStrings.cifConfirmDifInfoNote,
          'Họ và tên, ngày tháng năm sinh đang sử dụng hiện chưa khớp với thông tin mà bạn đã đăng ký với TPBank.');
      expect(DOPNativeStrings.cifConfirmCifReopenNote,
          'Thông tin giấy tờ tuỳ thân của bạn trùng với hồ sơ đã đóng tại TPBank.');

      expect(DOPNativeStrings.cifConfirmUpdateAtCounterNote,
          'Vui lòng đến Chi nhánh/ Phòng giao dịch gần nhất của TPBank trong vòng 7 ngày để cập nhật thông tin và quay lại EVO app, chọn "Tôi đã cập nhật thông tin" để tiếp tục mở thẻ.');

      // With CIF Info
      expect(DOPNativeStrings.cifConfirmDifNationIdNote,
          'Thông tin số CMND/CCCD đang đăng ký khác với thông tin bạn đã đăng ký dịch vụ tại TPBank.');
      expect(DOPNativeStrings.cifConfirmWait24hNote,
          'Nếu bạn đã thực hiện cập nhật thông tin tại Chi nhánh của TPBank, vui lòng đợi hệ thống đồng bộ thông tin trong vòng 24h.');
      expect(DOPNativeStrings.cifConfirmOpenWithRegisteredIdCardNote,
          'Nếu bạn muốn mở thẻ TPBank EVO với số CMND/CCCD đã đăng ký dịch vụ tại TPBank, vui lòng chọn "Mở thẻ".');
      expect(DOPNativeStrings.cifConfirmDifOtherInfoNote,
          'Thông tin bạn đang sử dụng hiện chưa khớp đúng với thông tin mà bạn đã đăng ký với TPBank trước đó.');
      expect(DOPNativeStrings.cifConfirmForSafetyNote,
          'Để bảo đảm an toàn cho giao dịch của bạn, vui lòng đến Chi nhánh/ Phòng giao dịch gần nhất của TPBank trong vòng 7 ngày để cập nhật thông tin và quay lại EVO app, chọn "Tôi đã cập nhật thông tin" để tiếp tục mở thẻ');

      // CTA
      expect(DOPNativeStrings.cifConfirmOpenCardCTA, 'Mở thẻ');
      expect(DOPNativeStrings.cifConfirmNoBranchCTA, 'Đã hiểu');
      expect(DOPNativeStrings.cifConfirmConfirmInfoUpdatedCTA, 'Tôi đã cập nhật thông tin');
      expect(DOPNativeStrings.cifConfirmViewNearestBranchesCTA, 'Xem chi nhánh gần bạn');

      /// Additional Info - Secret Question
      expect(DOPNativeStrings.dopNativeSecretQuestionTitle, 'Câu hỏi bảo mật');
      expect(DOPNativeStrings.dopNativeSecretQuestionDesc,
          'Bạn cần ghi nhớ câu trả lời cho câu hỏi này để dùng khi bị mất thẻ.');
      expect(DOPNativeStrings.dopNativeSecretQuestionLabel, 'Tên trường tiểu học của bạn');
      expect(DOPNativeStrings.dopNativeSecretQuestionHint, 'Nhập tên trường');
      expect(DOPNativeStrings.dopNativeSecretQuestionEmptyErrorMsg, 'Vui lòng nhập câu trả lời');
      expect(DOPNativeStrings.additionalFormTitle, 'Thông tin bổ sung');

      // Additional info - Subscribe channel
      expect(DOPNativeStrings.dopNativeInputZaloPhoneNumberFormatFailed,
          'Số điện thoại không đúng định dạng');
      expect(DOPNativeStrings.dopNativeInputZaloPhoneNumberEmptyFailed,
          'Vui lòng nhập số điện thoại tài khoản Zalo');
      expect(DOPNativeStrings.dopNativeRegisterInfo, 'Đăng ký nhận thông tin');
      expect(DOPNativeStrings.dopNativeFavoriteInfoChannel, 'Kênh nhận thông tin yêu thích');
      expect(DOPNativeStrings.dopNativeYourZaloAccount, 'Tài khoản Zalo của bạn:');
      expect(DOPNativeStrings.dopNativeCurrentPhone, 'Số điện thoại hiện tại');
      expect(DOPNativeStrings.dopNativeOtherPhone, 'Số điện thoại khác');
      expect(DOPNativeStrings.dopNativeZaloInfoReceiveDesc,
          'Thông tin về kết quả đăng ký và các chương trình khuyến mãi sẽ được gửi qua Zalo cho bạn.');

      // Additional info - Address additional info
      expect(DOPNativeStrings.dopNativeCurrentAddressTitle, 'Địa chỉ hiện tại');
      expect(DOPNativeStrings.dopNativeStreetOfCurrentAddressInputTextHint, 'Số nhà, tên đường');
      expect(DOPNativeStrings.dopNativeCompanyAddressTitle, 'Thông tin nơi làm việc');
      expect(DOPNativeStrings.dopNativeCompanyNameInputTextLabel, 'Tên công ty của bạn');
      expect(DOPNativeStrings.dopNativeCompanyNameInputTextHint, 'Nhập tên công ty');
      expect(DOPNativeStrings.dopNativeCompanyAddressInputTextLabel, 'Địa chỉ khu vực');
      expect(DOPNativeStrings.dopNativeCompanyAddressInputTextHint, 'Chọn theo khu vực');
      expect(DOPNativeStrings.dopNativeCompanyStreetOfAddressInputTextLabel, 'Địa chỉ cụ thể');
      expect(
          DOPNativeStrings.dopNativeCompanyStreetOfAddressInputTextHint, 'Nhập số nhà, tên đường');
      expect(DOPNativeStrings.dopNativeCardDeliveryTypeTitle, 'Địa chỉ nhận thẻ');
      expect(DOPNativeStrings.dopNativeCompanyNameEmptyFailed, 'Vui lòng nhập tên cơ quan');
      expect(DOPNativeStrings.dopNativeCompanyNameTooLongFailed,
          'Tên cơ quan không được quá {0} ký tự');
      expect(DOPNativeStrings.dopNativeStreetOfCompanyInputTextHint, 'Nhập số nhà, tên đường');

      /// Additional Form - Emergency Contact
      expect(DOPNativeStrings.dopNativeEmergencyContactTitle, 'Thông tin liên hệ khẩn cấp');
      expect(DOPNativeStrings.dopNativeEmergencyContactDesc,
          'Cung cấp 2 số điện thoại khác nhau của người thân hoặc bạn bè.');
      expect(DOPNativeStrings.dopNativeEmergencyContactNotice,
          '* Không điền số điện thoại khác của bạn.');
      expect(DOPNativeStrings.dopNativeEmergencyContact1Label, 'Số điện thoại người thân 1');
      expect(DOPNativeStrings.dopNativeEmergencyContact2Label, 'Số điện thoại người thân 2');
      expect(DOPNativeStrings.dopNativeEmergencyContactInvalidErrorMsg,
          'Số điện thoại không đúng định dạng');
      expect(DOPNativeStrings.dopNativeEmergencyContactIsIdenticalErrorMsg,
          'Số liên lạc khẩn cấp phải khác nhau');
      expect(DOPNativeStrings.dopNativeEmergencyContact1EmptyErrorMsg,
          'Vui lòng nhập số điện thoại người thân 1');
      expect(DOPNativeStrings.dopNativeEmergencyContact2EmptyErrorMsg,
          'Vui lòng nhập số điện thoại người thân 2');
      expect(DOPNativeStrings.dopNativeEmergencyContactIsUserPhoneErrorMsg,
          'Vui lòng không nhập số điện thoại của chính bạn');

      // EContract, TPB contact & TPB warning widget
      expect(DOPNativeStrings.viewEContract, 'Xem hợp đồng điện tử');
      expect(DOPNativeStrings.tpbContact, 'Tổng đài TPBank');
      expect(DOPNativeStrings.dopNativeTPBWarning,
          'TPBank không liên hệ khách hàng để quảng cáo dịch vụ rút tiền mặt từ thẻ hay yêu cầu cung cấp các thông tin thẻ.');
      expect(DOPNativeStrings.dopNativeTPBWarningRejectMoneyWithdrawInvitation,
          'Hãy từ chối mọi lời mời rút tiền mặt từ thẻ tín dụng TPBank EVO.');
      expect(DOPNativeStrings.dopNativeTPBWarningProtectCVV,
          'Tuyệt đối không cung cấp thông tin thẻ, mã bảo mật (CVV) cho bất kì ai.');
      expect(DOPNativeStrings.dopNativeTPBWarningProtectCVVViewDetail, 'Xem chi tiết');
      expect(DOPNativeStrings.dopNativeTPBWarningProtectCVVViewDetailHere, 'Tại đây');

      // Card status
      expect(DOPNativeStrings.dopNativeCardActivatedSuccessTitle,
          'Đã kích hoạt thẻ tín dụng thành công.');
      expect(DOPNativeStrings.dopNativeCardActivatedSuccessDesc,
          'Thanh toán ngay đơn hàng bằng thẻ TPBank EVO để hưởng trọn ưu đãi hấp dẫn dành riêng cho bạn');
      expect(DOPNativeStrings.dopNativeActiveCardRetryTitle, 'Kích hoạt thẻ\nkhông thành công');
      expect(DOPNativeStrings.dopNativeActiveCardRetryDesc,
          'Thẻ tín dụng TPBank EVO của bạn được kích hoạt không thành công. Vui lòng kích hoạt lại.');
      expect(DOPNativeStrings.dopNativeActiveEvoNow, 'Kích hoạt thẻ tín dụng\nTPBank EVO ngay');
      expect(DOPNativeStrings.dopNativeActiveCardFailTitle, 'Kích hoạt thẻ\nkhông thành công');
      expect(DOPNativeStrings.dopNativeActiveCardFailDesc,
          'Thẻ tín dụng TPBank EVO của bạn được kích hoạt không thành công, vui lòng tải app TPBank để thực hiện kích hoạt lại.');
      expect(DOPNativeStrings.dopNativeDownloadTPBankToActive,
          'Tải ứng dụng TPBank\nMobile để kích hoạt thẻ');
      expect(DOPNativeStrings.dopNativeCountDownBannerTitle, 'Sẵn sàng để\nkích hoạt thẻ sau:');

      expect(DOPNativeStrings.dopNativeCardActivatedRetryPosLimitBannerTitle,
          'Đang cài đặt\nhạn mức giao dịch');
      expect(DOPNativeStrings.dopNativeCardActivatedRetryPosLimitTitle,
          'Vui lòng chờ để cài đặt\nhạn mức');
      expect(DOPNativeStrings.dopNativeCardActivatedRetryPosLimitDesc,
          'Thẻ TPBank EVO của bạn đã được kích hoạt thành công. Hệ thống đang thiết lập hạn mức giao dịch, vui lòng ở lại trang và chờ ít phút.');
      expect(DOPNativeStrings.dopNativeCardActivatedPosFailedBannerTitle,
          'Cài đặt\nhạn mức giao dịch');
      expect(DOPNativeStrings.dopNativeCardActivatedPosFailedTitle,
          'Thẻ chưa cài đặt hạn mức\ngiao dịch.');
      expect(DOPNativeStrings.dopNativeCardActivatedPosFailedDesc,
          'Thẻ tín dụng TPBank EVO của bạn đã được kích hoạt thành công. Vui lòng tải TPBank Mobile để cài đặt hạn mức giao dịch.');

      // ID Verification QR Code
      expect(DOPNativeStrings.dopNativeQRCodeTimeoutDialogTitle,
          'Quá thời gian quét. Vui lòng thực hiện lại');
      expect(DOPNativeStrings.dopNativeQRCodeTimeoutDialogContent,
          'Đưa camera vào vị trí mã QR trên nhé. Canh vừa vào 4 góc và thử dịch chuyển camera (xa-gần) để camera điện thoại lấy nét chuẩn hơn');
      expect(DOPNativeStrings.dopNativeQRCodeRetryCTA, 'Thực hiện lại');

      // E-Sign review
      expect(DOPNativeStrings.dopNativeSignEContract, 'Ký hợp đồng điện tử');
      expect(DOPNativeStrings.dopNativeESignUserProclamation,
          'Bằng cách nhấn vào nút Ký hợp đồng, tôi yêu cầu FPT-CA cung cấp dịch vụ chứng thư số và xác nhận:');
      expect(DOPNativeStrings.dopNativeESignHasReadFPTCA1,
          'Đã đọc, hiểu rõ, đồng ý và cam kết tuân thủ các ');
      expect(DOPNativeStrings.dopNativeESignHasReadFPTCA2, 'điều kiện và điều khoản');
      expect(DOPNativeStrings.dopNativeESignHasReadFPTCA3, ' sử dụng dịch vụ Chứng thư số FPT.CA.');
      expect(DOPNativeStrings.dopNativeESignAgreeFPTCASign,
          'Đồng ý sử dụng chữ ký số FPT.CA để ký điện tử văn bản, hợp đồng điện tử với TPBank với nội dung như trên.');
      expect(DOPNativeStrings.dopNativeESignResponsibility,
          'Chịu trách nhiệm đối với các phát sinh liên quan đến việc sử dụng thẻ tín dụng được cấp theo  điều khoản và điều kiện sử dụng thẻ tín dụng TPBank.');
      expect(DOPNativeStrings.dopNativeUserDoNotHaveAmericaElement,
          'Khách hàng không có yếu tố Hoa Kỳ');
      expect(
          DOPNativeStrings.dopNativeUserAgreeLinkCardToEVO, 'Tôi đồng ý liên kết thẻ vào EVO app');
      expect(DOPNativeStrings.dopNativeSignESign, 'Ký hợp đồng');
      expect(DOPNativeStrings.dopNativeFATCATooltip,
          'Thông tin cho mục đích tuân thủ Đạo luật tuân thủ với tài khoản nước ngoài (FATCA)');
      expect(DOPNativeStrings.dopNativeLinkCardToolTip,
          'Liên kết thẻ tín dụng TPBank EVO vào EVO app để thanh toán dễ dàng với nhiều ưu đãi hấp dẫn.');
      expect(DOPNativeStrings.dopNativeAmericanCitizenTitle,
          'Thông tin cho mục đích tuân thủ Đạo luật tuân thủ thuế với tài khoản nước ngoài (FATCA). Vui lòng chọn nội dung thích hợp bên dưới nếu bạn có yếu tố liên quan đến Hoa Kỳ.');
      expect(DOPNativeStrings.dopNativeYouAreAmericaCitizen,
          'Bạn là công dân Hoa Kỳ hoặc là thường trú nhân hợp pháp tại Hoa Kỳ (có thẻ xanh)');
      expect(DOPNativeStrings.dopNativeBornAmerica, 'Bạn sinh ra tại Hoa Kỳ (Nơi sinh tại Hoa Kỳ)');
      expect(DOPNativeStrings.dopNativeInstructDepositToOrWithdrawFromUSAddress,
          'Bạn có cung cấp hướng dẫn chuyển tiền thường xuyên nào tới một tài khoản được duy trì tại Hoa Kỳ hoặc hướng dẫn nhận tiền thường xuyên từ 1 địa chỉ tại Hoa Kỳ');
      expect(DOPNativeStrings.dopNativeHaveAmericaAddress,
          'Bạn có địa chỉ Hoa Kỳ (bao gồm cả địa chỉ hộp thư P.O.box) hay số điện thoại Hoa Kỳ');
      expect(DOPNativeStrings.dopNativeIsDelegateToUSAddress,
          'Bạn có ủy quyền cho người có địa chỉ tại Hoa Kỳ');
      expect(DOPNativeStrings.dopNativeHaveUniqueUSMailAddress,
          'Bạn có địa chỉ "gửi qua" hoặc "giữ thư" là địa chỉ duy nhất của bạn tại Hoa Kỳ');
      expect(DOPNativeStrings.dopNativeESignNotificationForSMSOTP,
          'Phương thức xác thực mặc định trên ứng dụng eBank của TPBank là SMS OTP. Phí dịch vụ được áp dụng theo biểu phí hiện hành của Ngân hàng. Để thay đổi phương thức xác thực, Quý khách vui lòng thực hiện trên ứng dụng TPBank eBank.');

      // Download eContract
      expect(DOPNativeStrings.dopNativeEContractDownloadTitle, 'Xem và tải hợp đồng điện tử');
      expect(DOPNativeStrings.dopNativeEContractDownloadCTA, 'Tải hợp đồng điện tử');
      expect(DOPNativeStrings.dopNativeEContractBackCTA, 'Quay lại');
      expect(
          DOPNativeStrings.dopNativeEContractDownloadZoomInGuide, 'Nhấn vào đây để xem chi tiết');
      expect(DOPNativeStrings.dopNativeEContractDownloadFileName, 'Hop dong mo the');
      expect(DOPNativeStrings.dopNativeEContractDownloading, 'Đang tải file, bạn đợi chút nhé');
      expect(DOPNativeStrings.dopNativeEContractDownloadFailed, 'Tải file không thành công');
      expect(DOPNativeStrings.dopNativeEContractDownloadSuccess, 'Tải file thành công');

      // E-Sign - link card popup
      expect(DOPNativeStrings.dopNativeLinkCardPopupTitle,
          'Liên kết thẻ tín dụng TPBank EVO vào EVO app để thanh toán dễ dàng với nhiều ưu đãi hấp dẫn.');
      expect(DOPNativeStrings.dopNativeLinkCardPopupDesc,
          'Việc chia sẻ hoàn toàn bảo mật, chỉ phục vụ mục đích thanh toán cho thẻ TPBank EVO');
      expect(DOPNativeStrings.dopNativeAgreeLinkCard, 'Đồng ý liên kết thẻ');
      expect(DOPNativeStrings.dopNativeNotAgreeLinkCard, 'Không đồng ý');

      // E-success
      expect(DOPNativeStrings.dopNativeESuccessTitle, 'Đăng ký thẻ thành công');
      expect(DOPNativeStrings.dopNativePcbDescPrefix,
          'Chúc mừng bạn đã được phê duyệt thẻ với hạn mức ban đầu');
      expect(DOPNativeStrings.dopNativePcbDescSuffix,
          '\nHạn mức chính thức sẽ được thông báo qua email.');
      expect(DOPNativeStrings.dopNativeCicDescPrefix, 'Hạn mức tín dụng của bạn là');
      expect(DOPNativeStrings.dopNativeCicDescSuffix,
          'TPBank sẽ liên hệ để chuyển thẻ vật lý tới bạn trong thời gian sớm nhất.');
      expect(DOPNativeStrings.dopNativeESuccessSemiTitle, 'Hồ sơ đang được xử lý');
      expect(DOPNativeStrings.dopNativeESuccessSemiDesc,
          'Hồ sơ của bạn sẽ được xử lý ngay khi có kết quả tra cứu thông tin lịch sử tín dụng.\nBạn sẽ nhận được thông báo kết quả trong vòng 1 đến 5 ngày làm việc qua email đăng ký.');

      // Pos Limit Dialog
      expect(DOPNativeStrings.dopNativeSetupPosLimitTitle, 'Cài đặt hạn mức giao dịch');
      expect(
          DOPNativeStrings.dopNativeSetupPosLimitDescription, 'Hạn mức thanh toán POS (VNĐ/lần)');
      expect(DOPNativeStrings.dopNativeSetupPosLimitHintText,
          'Hạn mức lần (nhập hạn mức giao dịch lần)');
      expect(DOPNativeStrings.dopNativeSetupPosLimitValidInfoInput,
          'Hạn mức giao dịch POS tối đa có thể thiết lập ban đầu là {0} VNĐ');
      expect(DOPNativeStrings.dopNativeSetupPosLimitInvalidInfoInput1,
          'Hạn mức giao dịch được thiết lập không vượt quá {0} VNĐ. Để thiết lập hạn mức giao dịch cao hơn, vui lòng thực hiện trên ứng dụng ');
      expect(DOPNativeStrings.dopNativeSetupPosLimitNoteTitle, 'Lưu ý');
      expect(DOPNativeStrings.dopNativeSetupPosLimitNoteDescription,
          'Sau khi kích hoạt thẻ, Quý khách vui lòng giữ bảo mật thông tin thẻ để tránh phát sinh các rủi ro. Thẻ cứng sẽ được chuyển tới Quý khách theo đúng quy trình vận hành tại TPBank.');
      expect(DOPNativeStrings.dopNativeESuccessAutoPCBMWGTitle,
          'Đang tra cứu thông tin\nlịch sử tín dụng');
      expect(DOPNativeStrings.dopNativeESuccessAutoPCBMWGPleaseWait, 'Bạn vui lòng chờ tối đa');
      expect(DOPNativeStrings.dopNativeESuccessAutoPCBMWGStatusAutoUpdate,
          'Trạng thái hồ sơ sẽ được tự động cập nhật sau mỗi');
      expect(DOPNativeStrings.dopNativeESuccessMWGAutoPCBWaitingDuration, '30 phút.');
      expect(DOPNativeStrings.dopNativeESuccessMWGAutoPCBPollingInterval, '10 giây.');
      expect(DOPNativeStrings.dopNativeESuccessAutoPCBMWGTemporarilyCredit,
          'Hạn mức tạm tính của bạn là');

      // E-sign - otp
      expect(DOPNativeStrings.dopNativeConfirmSignContractTitle, 'Xác nhận ký hợp đồng điện tử');
      expect(DOPNativeStrings.dopNativeConfirmSignContractDesc,
          'Mã OTP đã được gửi đến số điện thoại của bạn để xác nhận đồng ý nội dung Đơn đề nghị phát hành thẻ tín dụng TPBank kiêm Hợp đồng mở thẻ tín dụng, Hợp đồng mở và sử dụng tài khoản thanh toán.');

      // ESign intro
      expect(DOPNativeStrings.dopNativeESignIntroTitle, 'Hồ sơ đã hoàn tất');
      expect(DOPNativeStrings.dopNativeESignIntroDescription, 'Mời bạn ký hợp đồng điện tử');
      expect(DOPNativeStrings.dopNativeESignIntroCreditLimit, 'Hạn mức tín dụng của bạn là');
      expect(DOPNativeStrings.dopNativeESignIntroGotoSignESign,
          'Tiếp theo mời bạn ký hợp đồng điện tử');

      // pdf viewer
      expect(DOPNativeStrings.dopNativePdfViewError, 'Vui lòng thử lại để xem\nhợp đồng điện tử');

      // Underwriting card status
      expect(DOPNativeStrings.dopNativeUnderwritingCardStatusTitle,
          'Thẻ tín dụng đã được cấp thành công.');
      expect(DOPNativeStrings.dopNativeUnderwritingCardStatusDesc,
          'TPBank sẽ liên hệ để chuyển thẻ vật lý tới bạn trong vòng 5 ngày làm việc.');

      // Underwriting sub flow
      expect(DOPNativeStrings.dopNativeCardCICBlockedTitle, 'Rất tiếc! Thẻ tín dụng bị khoá');
      expect(DOPNativeStrings.dopNativeUnderWritingInProgressTitle, 'Hồ sơ đang được xử lý');
      expect(DOPNativeStrings.dopNativeUnderWritingInProgressDesc,
          'Bạn sẽ nhận được cuộc gọi xác nhận thông tin hoặc TPBank sẽ thông báo kết quả xử lý hồ sơ qua email đã đăng ký trong vòng 24 giờ.');
      expect(DOPNativeStrings.dopNativeCardIssuedActivatedCardTitle,
          'Đã kích hoạt thẻ tín dụng\nthành công.');
      expect(DOPNativeStrings.dopNativeCardIssuedActivatedCardDescription,
          'Thanh toán ngay đơn hàng bằng thẻ TPBank EVO để hưởng trọn ưu đãi hấp dẫn dành riêng cho bạn');
      expect(DOPNativeStrings.dopNativeCardIssuedTitle, 'Thẻ tín dụng đã được cấp thành công.');
      expect(DOPNativeStrings.dopNativeCardIssuedDescription,
          'TPBank sẽ liên hệ để chuyển thẻ vật lý tới bạn trong vòng 5 ngày làm việc.');

      // NFC reader introduction screen
      expect(DOPNativeStrings.dopNativeTvNFCInstructionTitle, 'Quét chip của\nCCCD');
      expect(DOPNativeStrings.dopNativeTvNFCInstructionDesc,
          'Hệ thống tự động đọc thông tin trong chip để đáp ứng quy định của Ngân hàng nhà nước');
      expect(DOPNativeStrings.dopNativeTvNFCInstructionCaption,
          'Áp chip của CCCD vào vị trí được hướng dẫn ở\nmặt sau điện thoại.');
      expect(DOPNativeStrings.dopNativeTvNFCStart, 'Bắt đầu xác thực');
      expect(DOPNativeStrings.dopNativeTvNFCAware1, 'Áp chip\nCCCD vào\nđúng vị trí');
      expect(DOPNativeStrings.dopNativeTvNFCAware2, 'Giữ cố định\ntối thiểu\n3 giây');
      expect(DOPNativeStrings.dopNativeTvNFCAware3, 'Nhận thẻ\nTPBank EVO');

      expect(DOPNativeStrings.dopNativeFptNFCStart, 'Bắt đầu quét');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionTitle, 'Quét chip của CCCD');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionDesc,
          'Lật mặt sau của CCCD gắn chip và bắt đầu quét theo hướng dẫn');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionGuide1, 'Áp chip CCCD vào đúng vị trí');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionGuide2, 'Giữ cố định tối thiểu 3 giây');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionGuideButtonText, 'Xem video hướng dẫn');

      expect(DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroTitle,
          'Video hướng dẫn quét chip của CCCD');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescTitle,
          'Mẹo quét nhanh và dễ dàng');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescAndroidContent1,
          'Gỡ ốp lưng hoặc thẻ từ gắn trên điện thoại');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescAndroidContent2,
          'Giữ nguyên CCCD khi đang quét');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescAndroidContent3,
          'Sử dụng CCCD gắn chip chính chủ');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescAndroidContent4,
          'Vệ sinh và lau khô chip trên CCCD');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescAndroidContent5,
          'Thử tắt và bật lại NFC nếu chưa quét được');

      expect(DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescIOSContent1,
          'Gỡ ốp lưng hoặc thẻ từ gắn trên điện thoại');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescIOSContent2,
          'Giữ nguyên CCCD khi đang quét');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescIOSContent3,
          'Sử dụng CCCD gắn chip chính chủ');
      expect(DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescIOSContent4,
          'Vệ sinh và lau khô chip trên CCCD');

      expect(DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroRetryVideo,
          'Có lỗi xảy ra, nhấn để tải lại');

      expect(DOPNativeStrings.dopNativeFptNfcEnableNfcTitle, 'Bật chức năng NFC');
      expect(DOPNativeStrings.dopNativeFptNfcEnableNfcContent,
          'Chức năng NFC của điện thoại được sử dụng để đọc Chip NFC của CCCD');
      expect(DOPNativeStrings.dopNativeFptNfcEnableNfcButton, 'Bật NFC');

      expect(DOPNativeStrings.dopNativeFptNfcErrorTimeoutTitle, 'Phiên làm việc hết hạn');
      expect(DOPNativeStrings.dopNativeFptNfcErrorTimeoutContent,
          'Vui lòng nhấn vào nút bên dưới để xác thực lại và tiếp tục');
      expect(DOPNativeStrings.dopNativeFptNfcErrorTimeoutButton, 'Xác thực lại');

      expect(
          DOPNativeStrings.dopNativeFptNfcErrorWrongInfoTitle, 'Thông tin CCCD không trùng khớp');
      expect(DOPNativeStrings.dopNativeFptNfcErrorWrongInfoContent, 'Vui lòng dùng CCCD chính chủ');
      expect(DOPNativeStrings.dopNativeFptNfcErrorWrongInfoButton, 'Xác thực lại');

      expect(DOPNativeStrings.dopNativeFptNfcErrorUnknownTitle, 'Có lỗi xảy ra');
      expect(DOPNativeStrings.dopNativeFptNfcErrorUnknownContent,
          'Vui lòng áp sát CCCD và giữ cố định!');
      expect(DOPNativeStrings.dopNativeFptNfcErrorUnknownButton, 'Xác thực lại');

      // retry NFC verification popup
      expect(DOPNativeStrings.retryNFCVerificationPopupTitle, 'Chưa thể tải thông tin');
      expect(DOPNativeStrings.retryNFCVerificationPopupContent,
          'Hiện tại chúng tôi chưa thể xử lý thông tin. Vui lòng thử lại nhé!');

      // NFC verification with invalid token popup
      expect(DOPNativeStrings.nfcVerificationInvalidTokenPopupContent,
          'Vui lòng nhấn vào nút bên dưới để xác thực lại và tiếp tục');
      expect(DOPNativeStrings.nfcInvalidTokenRetry, 'Xác thực lại');
      expect(DOPNativeStrings.nfcInternetErrorDesc,
          'Vui lòng kiểm tra kết nối mạng của bạn và nhấn nút bên dưới để thử lại');

      /// Salesman & Rewards
      expect(DOPNativeStrings.storeInfo, 'Thông tin cửa hàng');
      expect(DOPNativeStrings.salesmanID, 'Mã nhân viên');
      expect(DOPNativeStrings.salesmanIDDesc,
          'Là thông tin của nhân viên bán hàng hỗ trợ bạn đăng ký thẻ tín dụng tại cửa hàng.');
      expect(DOPNativeStrings.pleaseEnterSalesmanID, 'Vui lòng nhập mã nhân viên');
      expect(DOPNativeStrings.confirmSalesmanID, 'Xác nhận mã nhân viên');
      expect(DOPNativeStrings.confirmSalesmanIDDesc,
          'Vui lòng xác nhận mã nhân viên đã hỗ trợ bạn hoàn thành hồ sơ nhé.');
      expect(DOPNativeStrings.viettelStoreSalesmanIDErrorMessage,
          'Mã nhân viên phải bắt đầu là VST, theo sau 6 chữ số');
      expect(DOPNativeStrings.mwgSalesmanIDErrorMessage, 'Mã nhân viên phải gồm 4 đến 6 chữ số');
      expect(DOPNativeStrings.frtSalesmanIDErrorMessage, 'Mã nhân viên phải gồm 1 đến 6 chữ số');

      // Acquisition Reward
      expect(DOPNativeStrings.acquisitionRewardTitle, 'Chọn ưu đãi mở thẻ');
      expect(DOPNativeStrings.acquisitionRewardDescription,
          'Ưu đãi đã sẵn sàng - Chờ bạn chi tiêu\nHãy chọn 1 trong các ưu đãi bên dưới nhé!');
      expect(DOPNativeStrings.acquisitionRewardViewDetailTitle, 'Chi tiết thể lệ chương trình');
      expect(DOPNativeStrings.acquisitionRewardViewDetailBottomSheetTitle, 'Thể lệ chương trình');

      // NFC device unsupported screen
      expect(DOPNativeStrings.nfcDeviceUnsupportedTitle,
          'Thiết bị không hỗ trợ tính năng quét chip CCCD');
      expect(DOPNativeStrings.nfcDeviceUnsupportedDesc,
          'Vui lòng sử dụng thiết bị khác quét QR bên dưới để thử lại');
      expect(DOPNativeStrings.nfcDeviceUnsupportedRequireDevice, 'Yêu cầu về thiết bị');
      expect(DOPNativeStrings.nfcDeviceUnsupportedRequireIOS,
          'iOS: iPhone 8 trở lên và hệ điều hành từ iOS 13 trở lên');
      expect(DOPNativeStrings.nfcDeviceUnsupportedRequireAndroid,
          'Android: Điện thoại sản xuất từ năm 2012 trở đi và hệ điều hành từ Android 5.0 trở lên');
      expect(DOPNativeStrings.nfcDeviceUnsupportedShareToOtherDevice, 'Chia sẻ tới thiết bị khác');
      expect(DOPNativeStrings.nfcDeviceUnsupportedSharedMessage,
          'Tải app EVO theo đường link này {0} để tiếp tục quá trình Quét chip CCCD. Lưu ý hãy sử dụng một thiết bị có hỗ trợ NFC để tiếp tục quá trình.');

      // Switch flow
      expect(DOPNativeStrings.dopNativeExistingRecord,
          'Bạn đang có 1 hồ sơ mở thẻ chưa hoàn thành tại');
      expect(DOPNativeStrings.dopNativeCreateNewRecord, 'Bạn có muốn tạo hồ sơ mới tại');
      expect(DOPNativeStrings.dopNativeCompleteOldRecord, 'Hoàn thành hồ sơ cũ');
      expect(DOPNativeStrings.dopNativeRegisterNew, 'Đăng ký mới');
      expect(DOPNativeStrings.dopNativeAlreadyHaveCard, 'Bạn đã có thẻ');
      expect(DOPNativeStrings.dopNativeAlreadyHaveCardDesc,
          'Bạn đã là khách hàng của TPBank EVO, tiếp tục sử dụng thẻ để nhận thêm ưu đãi');
      expect(DOPNativeStrings.dopNativeUnderstand, 'Đã hiểu');

      // Collect location
      expect(DOPNativeStrings.dopNativeCollectLocationTitle, 'Cho phép truy cập địa chỉ');
      expect(DOPNativeStrings.dopNativeCollectLocationDescription,
          'Chúng tôi cần quyền truy cập vào địa chỉ của bạn để cung cấp các dịch vụ phù hợp');
      expect(DOPNativeStrings.dopNativeCollectLocationAllowButton, 'Cho phép');
      expect(DOPNativeStrings.dopNativeCollectLocationDenyButton, 'Từ chối');

      // Fourth appraising
      expect(DOPNativeStrings.dopNativeFourthAppraisingTitle,
          'Đang tra cứu thông tin\nlịch sử tín dụng');
      expect(DOPNativeStrings.dopNativeFourthAppraisingDesc, 'Bạn vui lòng chờ trong giây lát...');
      expect(DOPNativeStrings.dopNativeAdditionalInfoCall, 'Cuộc gọi bổ sung thông tin');
      expect(DOPNativeStrings.dopNativeWeWillCallYouAfterMinutes,
          'Chúng tôi có thể liên hệ bạn sau\\\nít phút nữa');
      expect(DOPNativeStrings.dopNativeAdditionalInfoCallContent,
          'Cuộc gọi diễn ra từ 3-5 phút\nVui lòng nghe máy để nhận kết quả mở thẻ nhanh chóng');
      expect(DOPNativeStrings.dopNativeAdditionalInfoCallButtonNote,
          'Trong lúc chờ cuộc gọi, bấm tiếp tục để hoàn thành các bước tiếp theo');
    });
  });
}
