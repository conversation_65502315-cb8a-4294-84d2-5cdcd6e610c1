import 'dart:ui';

import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/sign_in_otp_entity.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_complete_onboarding_cubit.dart';
import 'package:evoapp/feature/dop_native/models/dop_native_state.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';
import '../../../../util/flutter_test_config.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockAppState extends Mock implements AppState {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

class MockEvoUtilsFunction extends Mock implements EvoUtilFunction {}

void main() {
  late DOPNativeCompleteOnboardingCubit cubit;
  late AuthenticationRepo mockAuthenticationRepo;
  late AppState mockAppState;
  late DOPUtilFunctions mockDOPUtilFunctions;

  setUpAll(() {
    setUtilsMockInstanceForTesting();

    getIt.registerSingleton<EvoUtilFunction>(MockEvoUtilsFunction());

    getIt.registerSingleton<DOPUtilFunctions>(MockDOPUtilFunctions());
    mockDOPUtilFunctions = getIt.get<DOPUtilFunctions>();

    getIt.registerSingleton<AuthenticationRepo>(MockAuthenticationRepo());
    mockAuthenticationRepo = getIt.get<AuthenticationRepo>();

    getIt.registerSingleton<AppState>(MockAppState());
    mockAppState = getIt.get<AppState>();

    when(() => mockAuthenticationRepo.logout(mockConfig: any(named: 'mockConfig'))).thenAnswer(
      (_) async => BaseEntity.fromBaseResponse(
          BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
        'verdict': 'success',
      })),
    );
    when(() => EvoAuthenticationHelper().clearAllUserData())
        .thenAnswer((_) async => Future<void>.value());
  });

  tearDownAll(() {
    resetUtilMockToOriginalInstance();
  });

  setUp(() {
    cubit = DOPNativeCompleteOnboardingCubit(
        authenticationRepo: mockAuthenticationRepo, appState: mockAppState);
  });

  void verifyLogoutEvoCalled() {
    verify(() => mockAuthenticationRepo.logout(mockConfig: any(named: 'mockConfig'))).called(1);
    verify(() => EvoAuthenticationHelper().clearAllUserData()).called(1);
  }

  group('Initial State', () {
    test('initial state is DOPNativeCompleteOnboardingInitial', () {
      expect(cubit.state, isA<DOPNativeCompleteOnboardingInitial>());
    });
  });

  group('test logoutEvoIfNeeded', () {
    test('calls logout and clearAllUserData if user logged in', () async {
      when(() => mockAppState.isUserLogIn).thenReturn(true);

      await cubit.logoutEvoIfNeeded();

      verifyLogoutEvoCalled();
    });

    test('do not calls logout API if user NOT logged in', () async {
      when(() => mockAppState.isUserLogIn).thenReturn(false);

      cubit.logoutEvoIfNeeded();

      verifyNever(() => mockAuthenticationRepo.logout(mockConfig: any(named: 'mockConfig')));
      verify(() => EvoAuthenticationHelper().clearAllUserData()).called(1);
    });
  });

  group('test onExitDOPFlow', () {
    blocTest<DOPNativeCompleteOnboardingCubit, DOPNativeCompleteOnboardingState>(
      'calls signInToEvo and clearDOPNativeData',
      build: () => cubit,
      setUp: () {
        when(() => mockAuthenticationRepo.loginFromDOE(
              dopAccessToken: any(named: 'dopAccessToken'),
              type: any(named: 'type'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => SignInOtpEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
              'verdict': SignInOtpEntity.verdictAlreadySignedIn,
            })));

        when(() => mockAppState.dopNativeState).thenReturn(DOPNativeState(
          dopNativeAccessToken: 'token',
          phoneNumber: '0909000990',
        ));

        when(() => mockDOPUtilFunctions.clearDOPNativeData())
            .thenAnswer((_) async => Future<void>.value());
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeCompleteOnboardingCubit cubit) => cubit.onExitDOPFlow(),
      verify: (_) {
        verifyInOrder(<VoidCallback>[
          () => mockAuthenticationRepo.loginFromDOE(
                mockConfig: any(named: 'mockConfig'),
                type: any(named: 'type'),
                dopAccessToken: any(named: 'dopAccessToken'),
              ),
          () => mockDOPUtilFunctions.clearDOPNativeData(),
        ]);
      },
    );
  });

  group('test signInToEvo', () {
    const String fakeDOPAccessToken = 'token';
    const String fakeDOPPhoneNumber = '0312345678';

    blocTest<DOPNativeCompleteOnboardingCubit, DOPNativeCompleteOnboardingState>(
      'emits DOPNativeAlreadySignInEvo if response has verdict AlreadySignedIn',
      build: () => cubit,
      setUp: () {
        when(() => mockAuthenticationRepo.loginFromDOE(
              dopAccessToken: any(named: 'dopAccessToken'),
              type: any(named: 'type'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => SignInOtpEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
              'verdict': SignInOtpEntity.verdictAlreadySignedIn,
            })));
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeCompleteOnboardingCubit cubit) => cubit.signInToEvo(
        dopAccessToken: fakeDOPAccessToken,
        dopPhoneNumber: fakeDOPPhoneNumber,
      ),
      expect: () => <TypeMatcher<DOPNativeCompleteOnboardingState>>[
        isA<DOPNativeCompleteOnboardingLoading>(),
        isA<DOPNativeAlreadySignInEvo>(),
      ],
      verify: (_) {
        verify(() => mockAuthenticationRepo.loginFromDOE(
              mockConfig: any(named: 'mockConfig'),
              type: TypeLogin.none,
              dopAccessToken: fakeDOPAccessToken,
            )).called(1);
      },
    );

    blocTest<DOPNativeCompleteOnboardingCubit, DOPNativeCompleteOnboardingState>(
      'emits DOPNativeSignInToEvoFailed if dopPhoneNumber is null or empty',
      build: () => cubit,
      setUp: () {
        when(() => mockAuthenticationRepo.loginFromDOE(
              dopAccessToken: any(named: 'dopAccessToken'),
              type: any(named: 'type'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => SignInOtpEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
              'verdict': SignInOtpEntity.verdictSuccess,
            })));

        when(() => mockAppState.isUserLogIn).thenReturn(true);
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeCompleteOnboardingCubit cubit) => cubit.signInToEvo(
        dopAccessToken: fakeDOPAccessToken,
      ),
      expect: () => <TypeMatcher<DOPNativeCompleteOnboardingState>>[
        isA<DOPNativeCompleteOnboardingLoading>(),
        isA<DOPNativeSignInToEvoFailed>(),
      ],
      verify: (_) {
        verify(() => mockAuthenticationRepo.loginFromDOE(
              mockConfig: any(named: 'mockConfig'),
              type: TypeLogin.none,
              dopAccessToken: fakeDOPAccessToken,
            )).called(1);

        verifyLogoutEvoCalled();
      },
    );

    blocTest<DOPNativeCompleteOnboardingCubit, DOPNativeCompleteOnboardingState>(
      'emits DOPNativeLoginToEVOSuccess if API return success with verdict != verdictAlreadySignedIn, dop phone number is not null',
      build: () => cubit,
      setUp: () {
        when(() => mockAuthenticationRepo.loginFromDOE(
              dopAccessToken: any(named: 'dopAccessToken'),
              type: any(named: 'type'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => SignInOtpEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
              'verdict': SignInOtpEntity.verdictSuccess,
            })));
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeCompleteOnboardingCubit cubit) => cubit.signInToEvo(
        dopAccessToken: fakeDOPAccessToken,
        dopPhoneNumber: fakeDOPPhoneNumber,
      ),
      expect: () => <TypeMatcher<DOPNativeCompleteOnboardingState>>[
        isA<DOPNativeCompleteOnboardingLoading>(),
        isA<DOPNativeSignInToEVOSuccess>()
            .having(
              (DOPNativeSignInToEVOSuccess p0) => p0.phone,
              'verify phone number',
              '0312345678',
            )
            .having(
              (DOPNativeSignInToEVOSuccess p0) => p0.entity,
              'verify sign in entity',
              isA<SignInOtpEntity>(),
            ),
      ],
      verify: (_) {
        verify(() => mockAuthenticationRepo.loginFromDOE(
              mockConfig: any(named: 'mockConfig'),
              type: TypeLogin.none,
              dopAccessToken: fakeDOPAccessToken,
            )).called(1);
      },
    );

    blocTest<DOPNativeCompleteOnboardingCubit, DOPNativeCompleteOnboardingState>(
      'emits DOPNativeCompleteWithAnotherPhone when status code = 400 & verdict = verdictCompleteWithOtherPhone ',
      build: () => cubit,
      setUp: () {
        when(() => mockAuthenticationRepo.loginFromDOE(
              dopAccessToken: any(named: 'dopAccessToken'),
              type: any(named: 'type'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => SignInOtpEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: <String, dynamic>{
              'verdict': SignInOtpEntity.verdictCompleteWithOtherPhone,
            })));

        when(() => mockAppState.dopNativeState).thenReturn(DOPNativeState(
          dopNativeAccessToken: fakeDOPAccessToken,
        ));

        when(() => mockAppState.isUserLogIn).thenReturn(true);
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeCompleteOnboardingCubit cubit) => cubit.signInToEvo(
        dopAccessToken: fakeDOPAccessToken,
      ),
      expect: () => <TypeMatcher<DOPNativeCompleteOnboardingState>>[
        isA<DOPNativeCompleteOnboardingLoading>(),
        isA<DOPNativeCompleteWithAnotherPhone>(),
      ],
      verify: (_) {
        verifyLogoutEvoCalled();
      },
    );

    blocTest<DOPNativeCompleteOnboardingCubit, DOPNativeCompleteOnboardingState>(
      'emits DOPNativeCompleteWithAnotherPhone when status code = 400 & verdict != verdictCompleteWithOtherPhone ',
      build: () => cubit,
      setUp: () {
        when(() => mockAuthenticationRepo.loginFromDOE(
              dopAccessToken: any(named: 'dopAccessToken'),
              type: any(named: 'type'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => SignInOtpEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: <String, dynamic>{
              'verdict': 'other_verdict',
            })));

        when(() => mockAppState.isUserLogIn).thenReturn(true);
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeCompleteOnboardingCubit cubit) => cubit.signInToEvo(
        dopAccessToken: fakeDOPAccessToken,
      ),
      expect: () => <TypeMatcher<DOPNativeCompleteOnboardingState>>[
        isA<DOPNativeCompleteOnboardingLoading>(),
        isA<DOPNativeSignInToEvoFailed>(),
      ],
      verify: (_) {
        verifyLogoutEvoCalled();
      },
    );

    blocTest<DOPNativeCompleteOnboardingCubit, DOPNativeCompleteOnboardingState>(
      'emits DOPNativeLoginToEVOFailed when status code != 200 & status code != 400',
      build: () => cubit,
      setUp: () {
        when(() => mockAuthenticationRepo.loginFromDOE(
                dopAccessToken: any(named: 'dopAccessToken'),
                type: any(named: 'type'),
                mockConfig: any(
                  named: 'mockConfig',
                )))
            .thenAnswer((_) async => SignInOtpEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.UNKNOWN_ERRORS, response: <String, dynamic>{})));

        when(() => mockAppState.isUserLogIn).thenReturn(true);
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeCompleteOnboardingCubit cubit) =>
          cubit.signInToEvo(dopAccessToken: fakeDOPAccessToken),
      expect: () => <TypeMatcher<DOPNativeCompleteOnboardingState>>[
        isA<DOPNativeCompleteOnboardingLoading>(),
        isA<DOPNativeSignInToEvoFailed>(),
      ],
      verify: (_) {
        verifyLogoutEvoCalled();
      },
    );
  });
}
