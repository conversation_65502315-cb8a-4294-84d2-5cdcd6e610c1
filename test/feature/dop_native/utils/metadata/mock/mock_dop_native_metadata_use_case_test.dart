import 'package:evoapp/feature/dop_native/util/metadata/mock/mock_dop_native_metadata_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('getMockDOPNativeEkycFileNameByCase', () {
    test('should return correct file name for each MockTestDOPNativeEkycUseCase', () {
      expect(
        getMockDOPNativeMetadataFileNameByCase(
            MockTestDOPNativeMetadataUseCase.getHCMDistrictSuccess),
        'dop_native_get_district_of_hcm_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(
            MockTestDOPNativeMetadataUseCase.getProvincesSuccess),
        'dop_native_get_provinces_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(MockTestDOPNativeMetadataUseCase.getWardOneSuccess),
        'dop_native_get_ward_of_one_district_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(
            MockTestDOPNativeMetadataUseCase.getEmploymentStatusSuccess),
        'dop_native_get_employment_status_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(
            MockTestDOPNativeMetadataUseCase.getEmploymentsSuccess),
        'dop_native_get_employments_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(
            MockTestDOPNativeMetadataUseCase.getSpecificWardSuccess),
        'dop_native_get_specific_ben_thanh_ward_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(
            MockTestDOPNativeMetadataUseCase.getIdIssuePlaceSuccess),
        'dop_native_get_id_issue_place_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(
            MockTestDOPNativeMetadataUseCase.getDistrictOneSuccess),
        'dop_native_get_district_one_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(
            MockTestDOPNativeMetadataUseCase.getHcmProvinceSuccess),
        'dop_native_get_hcm_province_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(
            MockTestDOPNativeMetadataUseCase.getPaymentTypeSuccess),
        'dop_native_get_payment_type_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(
            MockTestDOPNativeMetadataUseCase.getSubscribeChannelSuccess),
        'dop_native_get_subscribe_channel_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(
            MockTestDOPNativeMetadataUseCase.getCardDeliveryTypeSuccess),
        'dop_native_get_card_delivery_type_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(MockTestDOPNativeMetadataUseCase.getCompanySuccess),
        'dop_native_get_company_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(
          MockTestDOPNativeMetadataUseCase.getAcquisitionRewardSuccess,
        ),
        'dop_native_get_acquisition_reward_success.json',
      );

      expect(
        getMockDOPNativeMetadataFileNameByCase(
          MockTestDOPNativeMetadataUseCase.getAcquisitionRewardTermAndConditionSuccess,
        ),
        'dop_native_get_acquisition_reward_tc_success.json',
      );
    });
  });
}
