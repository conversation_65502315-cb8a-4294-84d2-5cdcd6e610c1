import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_contact_info_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_metadata_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_metadata_item_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_metadata_suggestion_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_ocr_data_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_personal_info_entity.dart';
import 'package:evoapp/feature/dop_native/util/metadata/dop_native_metadata_utils_impl.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/test_util.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

void main() {
  late DOPNativeMetadataUtilsImpl metadataUtils;
  late DOPNativeRepo mockDOPNativeRepo;

  const String fakeAddress = '123 Main St';
  const String fakeDistId = 'dist1';
  const String fakeProvId = 'prov1';
  const String fakeWardId = 'ward1';
  const String fakeBirthday = '1989-01-01';
  const String fakeFullName = 'PHAN THỊ AN';
  const String fakeIdIssuePlaceId = '4';

  final DOPNativeOCRDataEntity fakeOCRDataEntity = DOPNativeOCRDataEntity(
    contactInfo: const DOPNativeContactInfoEntity(
      familyAddress: fakeAddress,
      familyBookAddressDistId: fakeDistId,
      familyBookAddressProvinceId: fakeProvId,
      familyBookAddressWardId: fakeWardId,
    ),
    personalInfo: const DOPNativePersonalInfoEntity(
      birthday: fakeBirthday,
      fullName: fakeFullName,
      idIssuePlaceId: fakeIdIssuePlaceId,
    ),
  );

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    registerFallbackValue(MetadataType.PROVINCE);
    registerFallbackValue(MetadataType.DISTRICT);
    registerFallbackValue(MetadataType.WARD);
    registerFallbackValue(MetadataType.EMPLOYMENT);
    registerFallbackValue(MetadataType.PAYMENT_TYPE);
    registerFallbackValue(MetadataSuggestionType.COMPANY);

    mockDOPNativeRepo = MockDOPNativeRepo();
    metadataUtils = DOPNativeMetadataUtilsImpl(dopNativeRepo: mockDOPNativeRepo);
  });

  tearDown(() {
    reset(mockDOPNativeRepo);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('verify getProvinces()', () {
    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_provinces_success.json'),
          )));
    });

    test('getProvinces() is success', () async {
      final DOPNativeMetadataEntity entity = await metadataUtils.getProvinces();

      expect(entity.metadata, isNotNull);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PROVINCE,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getProvinces() is fail', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataEntity entity = await metadataUtils.getProvinces();

      expect(entity.metadata, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PROVINCE,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getDistricts()', () {
    const DOPNativeMetadataItemEntity fakeProvince = DOPNativeMetadataItemEntity(
      code: '1',
      name: 'Province 1',
    );

    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            parentType: any(named: 'parentType'),
            parentCode: any(named: 'parentCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_district_of_hcm_success.json'),
          )));
    });

    test('getDistricts() is success', () async {
      final DOPNativeMetadataEntity entity = await metadataUtils.getDistricts(
        province: fakeProvince,
      );

      expect(entity.metadata, isNotNull);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            parentCode: fakeProvince.code,
            parentType: MetadataType.PROVINCE,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getDistricts() is fail', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            parentType: any(named: 'parentType'),
            parentCode: any(named: 'parentCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataEntity entity = await metadataUtils.getDistricts(
        province: fakeProvince,
      );

      expect(entity.metadata, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            parentCode: fakeProvince.code,
            parentType: MetadataType.PROVINCE,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getWards()', () {
    const DOPNativeMetadataItemEntity fakeDistrict = DOPNativeMetadataItemEntity(
      code: '1',
      name: 'District 1',
    );

    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            parentType: any(named: 'parentType'),
            parentCode: any(named: 'parentCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response:
                await TestUtil.getResponseMock('dop_native_get_ward_of_one_district_success.json'),
          )));
    });

    test('getWards() is success', () async {
      final DOPNativeMetadataEntity entity = await metadataUtils.getWards(
        district: fakeDistrict,
      );

      expect(entity.metadata, isNotNull);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.WARD,
            parentCode: fakeDistrict.code,
            parentType: MetadataType.DISTRICT,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getWards() is fail', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            parentType: any(named: 'parentType'),
            parentCode: any(named: 'parentCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataEntity entity = await metadataUtils.getWards(
        district: fakeDistrict,
      );

      expect(entity.metadata, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.WARD,
            parentCode: fakeDistrict.code,
            parentType: MetadataType.DISTRICT,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getSpecificProvince()', () {
    const String fakeProvinceCode = '79';

    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            metadataCode: any(named: 'metadataCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_hcm_province_success.json'),
          )));
    });

    test('getSpecificProvince() is success', () async {
      final DOPNativeMetadataItemEntity? entity = await metadataUtils.getSpecificProvince(
        metadataCode: fakeProvinceCode,
      );

      expect(entity?.name, 'Thành phố Hồ Chí Minh');

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PROVINCE,
            metadataCode: fakeProvinceCode,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getSpecificProvince() is fail', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            metadataCode: any(named: 'metadataCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataItemEntity? entity = await metadataUtils.getSpecificProvince(
        metadataCode: fakeProvinceCode,
      );

      expect(entity, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PROVINCE,
            metadataCode: fakeProvinceCode,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getSpecificDistricts()', () {
    const String fakeDistrictCode = '760';

    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            metadataCode: any(named: 'metadataCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_district_one_success.json'),
          )));
    });

    test('getSpecificDistricts() is success', () async {
      final DOPNativeMetadataItemEntity? entity = await metadataUtils.getSpecificDistricts(
        metadataCode: fakeDistrictCode,
      );

      expect(entity?.name, 'Quận 1');

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeDistrictCode,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getSpecificDistricts() is fail', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            metadataCode: any(named: 'metadataCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataItemEntity? entity = await metadataUtils.getSpecificDistricts(
        metadataCode: fakeDistrictCode,
      );

      expect(entity, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeDistrictCode,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getSpecificWards()', () {
    const String fakeWardCode = '760';

    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            metadataCode: any(named: 'metadataCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(
                'dop_native_get_specific_ben_thanh_ward_success.json'),
          )));
    });

    test('getSpecificWards() is success', () async {
      final DOPNativeMetadataItemEntity? entity = await metadataUtils.getSpecificWards(
        metadataCode: fakeWardCode,
      );

      expect(entity?.name, 'Phường Bến Thành');

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.WARD,
            metadataCode: fakeWardCode,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getSpecificWards() is fail', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            metadataCode: any(named: 'metadataCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataItemEntity? entity = await metadataUtils.getSpecificWards(
        metadataCode: fakeWardCode,
      );

      expect(entity, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.WARD,
            metadataCode: fakeWardCode,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getPaymentTypes()', () {
    const String fakeEmploymentCode = '1';

    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            parentType: any(named: 'parentType'),
            parentCode: any(named: 'parentCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_payment_type_success.json'),
          )));
    });

    test('getEmployments() is success', () async {
      final DOPNativeMetadataEntity entity = await metadataUtils.getPaymentTypes(
        employmentCode: fakeEmploymentCode,
      );

      expect(entity.metadata, isNotNull);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PAYMENT_TYPE,
            parentType: MetadataType.EMPLOYMENT,
            parentCode: fakeEmploymentCode,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getEmployments() is fail', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            parentType: any(named: 'parentType'),
            parentCode: any(named: 'parentCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataEntity entity = await metadataUtils.getPaymentTypes(
        employmentCode: fakeEmploymentCode,
      );

      expect(entity.metadata, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PAYMENT_TYPE,
            parentType: MetadataType.EMPLOYMENT,
            parentCode: fakeEmploymentCode,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getSubscribeChannels()', () {
    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response:
                await TestUtil.getResponseMock('dop_native_get_subscribe_channel_success.json'),
          )));
    });

    test('getEmployments() is success', () async {
      final DOPNativeMetadataEntity entity = await metadataUtils.getSubscribeChannels();

      expect(entity.metadata, isNotNull);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.SUBSCRIBE_CHANNEL,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getEmployments() is fail', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataEntity entity = await metadataUtils.getSubscribeChannels();

      expect(entity.metadata, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.SUBSCRIBE_CHANNEL,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getEmployments()', () {
    const DOPNativeMetadataItemEntity employmentStatus = DOPNativeMetadataItemEntity(
      code: '1',
      name: 'Employed',
    );

    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_employments_success.json'),
          )));
    });

    test('getEmployments() is success', () async {
      final DOPNativeMetadataEntity entity = await metadataUtils.getEmployments(
        employmentStatus: employmentStatus,
      );

      expect(entity.metadata, isNotNull);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.EMPLOYMENT,
            parentCode: employmentStatus.code,
            parentType: MetadataType.EMPLOYMENT_STATUS,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getEmployments() is fail', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataEntity entity = await metadataUtils.getEmployments(
        employmentStatus: employmentStatus,
      );

      expect(entity.metadata, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.EMPLOYMENT,
            parentCode: employmentStatus.code,
            parentType: MetadataType.EMPLOYMENT_STATUS,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getEmploymentStatus()', () {
    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response:
                await TestUtil.getResponseMock('dop_native_get_employment_status_success.json'),
          )));
    });

    test('getEmploymentStatus() is success', () async {
      final DOPNativeMetadataEntity entity = await metadataUtils.getEmploymentStatus();

      expect(entity.metadata, isNotNull);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.EMPLOYMENT_STATUS,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getEmploymentStatus() is fail', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataEntity entity = await metadataUtils.getEmploymentStatus();

      expect(entity.metadata, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.EMPLOYMENT_STATUS,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getCardDeliveryType()', () {
    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response:
                await TestUtil.getResponseMock('dop_native_get_card_delivery_type_success.json'),
          )));
    });

    test('getCardDeliveryType() is success', () async {
      final DOPNativeMetadataEntity entity = await metadataUtils.getCardDeliveryType();

      expect(entity.metadata, isNotNull);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.CARD_DELIVERY_TYPE,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getCardDeliveryType() is fail', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataEntity entity = await metadataUtils.getCardDeliveryType();

      expect(entity.metadata, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.CARD_DELIVERY_TYPE,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getIdIssuePlaceName()', () {
    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            metadataCode: any(named: 'metadataCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_id_issue_place_success.json'),
          )));
    });

    test('getIdIssuePlaceName() is success', () async {
      final DOPNativeMetadataItemEntity? entity = await metadataUtils.getIdIssuePlaceName(
          idIssuePlaceCode: fakeOCRDataEntity.personalInfo?.idIssuePlaceId);

      expect(entity?.name, 'Công An HCM');

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.CARD_ISSUE_PLACE,
            metadataCode: fakeIdIssuePlaceId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getIdIssuePlaceName() with empty address', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            metadataCode: any(named: 'metadataCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataItemEntity? entity = await metadataUtils.getIdIssuePlaceName(
          idIssuePlaceCode: fakeOCRDataEntity.personalInfo?.idIssuePlaceId);

      expect(entity?.name, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.CARD_ISSUE_PLACE,
            metadataCode: fakeIdIssuePlaceId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getCompanySuggestions()', () {
    const String fakeSearchPrefix = 'searchPrefix';
    const int fakePageSize = 10;

    setUp(() {
      when(() => mockDOPNativeRepo.getMetadataSuggestion(
                type: any(named: 'type'),
                searchPrefix: any(named: 'searchPrefix'),
                pageSize: any(named: 'pageSize'),
                mockConfig: any(named: 'mockConfig'),
              ))
          .thenAnswer((_) async => DOPNativeMetadataSuggestionEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: await TestUtil.getResponseMock('dop_native_get_company_success.json'),
              )));
    });

    test('getCompanySuggestions() has value', () async {
      final DOPNativeMetadataSuggestionEntity item = await metadataUtils.getCompanySuggestions(
        searchPrefix: fakeSearchPrefix,
        pageSize: fakePageSize,
      );

      expect(item, isNotNull);

      verify(() => mockDOPNativeRepo.getMetadataSuggestion(
            type: MetadataSuggestionType.COMPANY,
            searchPrefix: fakeSearchPrefix,
            pageSize: fakePageSize,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getSpecificMetadata() with API failed', () async {
      when(() => mockDOPNativeRepo.getMetadataSuggestion(
                type: any(named: 'type'),
                searchPrefix: any(named: 'searchPrefix'),
                pageSize: any(named: 'pageSize'),
                mockConfig: any(named: 'mockConfig'),
              ))
          .thenAnswer((_) async => DOPNativeMetadataSuggestionEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.BAD_REQUEST,
                response: null,
              )));

      final DOPNativeMetadataSuggestionEntity item = await metadataUtils.getCompanySuggestions(
        searchPrefix: fakeSearchPrefix,
        pageSize: fakePageSize,
      );

      expect(item.items, null);

      verify(() => mockDOPNativeRepo.getMetadataSuggestion(
            type: MetadataSuggestionType.COMPANY,
            searchPrefix: fakeSearchPrefix,
            pageSize: fakePageSize,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getSpecificMetadata()', () {
    const String fakeCode = 'fake_code';

    setUp(() {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            metadataCode: any(named: 'metadataCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(
                'dop_native_get_specific_ben_thanh_ward_success.json'),
          )));
    });

    test('getSpecificMetadata() has value', () async {
      final DOPNativeMetadataItemEntity? item = await metadataUtils.getSpecificMetadata(
        type: MetadataType.DISTRICT,
        metadataCode: fakeCode,
      );

      expect(item, isNotNull);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeCode,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getSpecificMetadata() with API failed', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            metadataCode: any(named: 'metadataCode'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeMetadataItemEntity? item = await metadataUtils.getSpecificMetadata(
        type: MetadataType.DISTRICT,
        metadataCode: fakeCode,
      );

      expect(item, null);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeCode,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getSpecificMetadata() with metadataCode is null', () async {
      final DOPNativeMetadataItemEntity? item = await metadataUtils.getSpecificMetadata(
        type: MetadataType.DISTRICT,
        metadataCode: null,
      );

      expect(item, null);

      verifyNever(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeCode,
            mockConfig: any(named: 'mockConfig'),
          ));
    });
  });

  group('getPermanentResidenceAddressName', () {
    test('should return only the province name when district and ward are null', () {
      final String address = metadataUtils.getPermanentResidenceAddressName(
        provinceName: 'Province',
        districtName: null,
        wardName: null,
      );

      expect(address, '');
    });

    test('should return district and province name when ward is null', () {
      final String address = metadataUtils.getPermanentResidenceAddressName(
        provinceName: 'Province',
        districtName: 'District',
        wardName: null,
      );

      expect(address, '');
    });

    test('should return ward, district, and province names', () {
      final String address = metadataUtils.getPermanentResidenceAddressName(
        provinceName: 'Province',
        districtName: 'District',
        wardName: 'Ward',
      );

      expect(address, 'Ward, District, Province');
    });

    test('should handle empty strings for any part of the address', () {
      final String address = metadataUtils.getPermanentResidenceAddressName(
        provinceName: 'Province',
        districtName: '',
        wardName: 'Ward',
      );

      expect(address, '');
    });

    test('should return an empty string when all parameters are null or empty', () {
      final String address = metadataUtils.getPermanentResidenceAddressName(
        provinceName: null,
        districtName: null,
        wardName: null,
      );

      expect(address, '');
    });
  });

  group('getPermanentResidenceAddressName()', () {
    const String fakeDistId = 'dist1';
    const String fakeProvId = 'prov1';
    const String fakeWardId = 'ward1';

    test('getPermanentResidenceAddressName has value', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.WARD,
            metadataCode: fakeWardId,
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(
                'dop_native_get_specific_ben_thanh_ward_success.json'),
          )));

      when(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PROVINCE,
            metadataCode: fakeProvId,
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_hcm_province_success.json'),
          )));

      when(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeDistId,
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_district_one_success.json'),
          )));

      final String address = await metadataUtils.getResidenceAddressName(
        provinceCode: fakeProvId,
        districtCode: fakeDistId,
        wardCode: fakeWardId,
      );

      expect(address, 'Phường Bến Thành, Quận 1, Thành phố Hồ Chí Minh');

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.WARD,
            metadataCode: fakeWardId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PROVINCE,
            metadataCode: fakeProvId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeDistId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getPermanentResidenceAddressName has not province value', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.WARD,
            metadataCode: fakeWardId,
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(
                'dop_native_get_specific_ben_thanh_ward_success.json'),
          )));

      when(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PROVINCE,
            metadataCode: fakeProvId,
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      when(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeDistId,
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_district_one_success.json'),
          )));

      final String address = await metadataUtils.getResidenceAddressName(
        provinceCode: fakeProvId,
        districtCode: fakeDistId,
        wardCode: fakeWardId,
      );

      expect(address, '');

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.WARD,
            metadataCode: fakeWardId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PROVINCE,
            metadataCode: fakeProvId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeDistId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getPermanentResidenceAddressName has not district value', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.WARD,
            metadataCode: fakeWardId,
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(
                'dop_native_get_specific_ben_thanh_ward_success.json'),
          )));

      when(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PROVINCE,
            metadataCode: fakeProvId,
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_hcm_province_success.json'),
          )));

      when(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeDistId,
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final String address = await metadataUtils.getResidenceAddressName(
        provinceCode: fakeProvId,
        districtCode: fakeDistId,
        wardCode: fakeWardId,
      );

      expect(address, '');

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.WARD,
            metadataCode: fakeWardId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PROVINCE,
            metadataCode: fakeProvId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeDistId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('getPermanentResidenceAddressName has not ward value', () async {
      when(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.WARD,
            metadataCode: fakeWardId,
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      when(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PROVINCE,
            metadataCode: fakeProvId,
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_hcm_province_success.json'),
          )));

      when(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeDistId,
            parentCode: any(named: 'parentCode'),
            parentType: any(named: 'parentType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_get_district_one_success.json'),
          )));

      final String address = await metadataUtils.getResidenceAddressName(
        provinceCode: fakeProvId,
        districtCode: fakeDistId,
        wardCode: fakeWardId,
      );

      expect(address, '');

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.WARD,
            metadataCode: fakeWardId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.PROVINCE,
            metadataCode: fakeProvId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);

      verify(() => mockDOPNativeRepo.getMetadata(
            type: MetadataType.DISTRICT,
            metadataCode: fakeDistId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify getAcquisitionRewards()', () {
    const String leadSource = 'test-lead-source';
    setUp(
      () {
        when(
          () => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            mockConfig: any(named: 'mockConfig'),
            parentCode: any(named: 'parentCode'),
          ),
        ).thenAnswer(
          (_) async => DOPNativeMetadataEntity.fromJson(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: await TestUtil.getResponseMock(
                'dop_native_get_acquisition_reward_success.json',
              ),
            ),
          ),
        );
      },
    );

    test('getAcquisitionRewards() is success', () async {
      final DOPNativeMetadataEntity entity = await metadataUtils.getAcquisitionRewards(
        leadSource: leadSource,
      );
      expect(entity.metadata, isNotNull);
      verify(
        () => mockDOPNativeRepo.getMetadata(
          type: MetadataType.ACQUISITION_REWARD,
          mockConfig: any(named: 'mockConfig'),
          parentCode: leadSource,
        ),
      ).called(1);
    });

    test('getAcquisitionRewards() is fail', () async {
      when(
        () => mockDOPNativeRepo.getMetadata(
          type: any(named: 'type'),
          mockConfig: any(named: 'mockConfig'),
          parentCode: leadSource,
        ),
      ).thenAnswer(
        (_) async => DOPNativeMetadataEntity.fromJson(
          BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          ),
        ),
      );
      final DOPNativeMetadataEntity entity = await metadataUtils.getAcquisitionRewards(
        leadSource: leadSource,
      );
      expect(entity.metadata, null);
      verify(
        () => mockDOPNativeRepo.getMetadata(
          type: MetadataType.ACQUISITION_REWARD,
          mockConfig: any(named: 'mockConfig'),
          parentCode: leadSource,
        ),
      ).called(1);
    });
  });

  group('verify getAcquisitionRewardTermAndCondition()', () {
    const String leadSource = 'test-lead-source';
    setUp(
      () {
        when(
          () => mockDOPNativeRepo.getMetadata(
            type: any(named: 'type'),
            mockConfig: any(named: 'mockConfig'),
            parentCode: any(named: 'parentCode'),
          ),
        ).thenAnswer(
          (_) async => DOPNativeMetadataEntity.fromJson(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: await TestUtil.getResponseMock(
                'dop_native_get_acquisition_reward_tc_success.json',
              ),
            ),
          ),
        );
      },
    );

    test('getAcquisitionRewards() is success', () async {
      final DOPNativeMetadataEntity entity =
          await metadataUtils.getAcquisitionRewardTermAndCondition(
        leadSource: leadSource,
      );
      expect(entity.metadata, isNotNull);
      verify(
        () => mockDOPNativeRepo.getMetadata(
          type: MetadataType.ACQUISITION_REWARD_TC,
          mockConfig: any(named: 'mockConfig'),
          parentCode: leadSource,
        ),
      ).called(1);
    });

    test('getAcquisitionRewardTermAndCondition() is fail', () async {
      when(
        () => mockDOPNativeRepo.getMetadata(
          type: any(named: 'type'),
          mockConfig: any(named: 'mockConfig'),
          parentCode: leadSource,
        ),
      ).thenAnswer(
        (_) async => DOPNativeMetadataEntity.fromJson(
          BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          ),
        ),
      );
      final DOPNativeMetadataEntity entity =
          await metadataUtils.getAcquisitionRewardTermAndCondition(
        leadSource: leadSource,
      );
      expect(entity.metadata, null);
      verify(
        () => mockDOPNativeRepo.getMetadata(
          type: MetadataType.ACQUISITION_REWARD_TC,
          mockConfig: any(named: 'mockConfig'),
          parentCode: leadSource,
        ),
      ).called(1);
    });
  });
}
