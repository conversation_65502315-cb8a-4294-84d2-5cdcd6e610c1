import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_card_status_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_pos_limit_entity.dart';
import 'package:evoapp/feature/dop_native/util/card_status/cubit/dop_native_card_status_cubit.dart';
import 'package:evoapp/feature/dop_native/util/card_status/cubit/dop_native_card_status_state.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../constant.dart';
import '../../../../../util/test_util.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

void main() {
  late DOPNativeCardStatusCubit cubit;
  late DOPNativeRepo mockRepo;
  late AppState appState;
  const DOPNativePosLimitEntity posLimitEntity = DOPNativePosLimitEntity(
    posLimitActivationStatus: DOPNativePOSLimitActivationStatus.success,
    maxPosLimitAllow: 90000,
    posLimitUserSetting: 90000,
  );

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    mockRepo = MockDOPNativeRepo();
    appState = AppState();
    cubit = DOPNativeCardStatusCubit(
      dopNativeRepo: mockRepo,
      cardStatusUseCase: CardStatusUseCase.underwritingInProgress,
      appState: appState,
    );
    appState.dopNativeState.posLimitNumber = null;

    registerFallbackValue(DOPNativeCardStatusEntity());
  });

  test('Initial state should be DOPNativeUnderwritingInProgressInitial', () {
    expect(cubit.state, isA<GetCardStatusInitial>());
  });

  blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>('getCardStatus failure case',
      wait: TestConstant.blocEmitStateDelayDuration,
      build: () => cubit,
      setUp: () {
        when(() => mockRepo.getCardStatus(mockConfig: any(named: 'mockConfig'))).thenAnswer(
          (_) async => DOPNativeCardStatusEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: <String, dynamic>{},
            ),
          ),
        );
      },
      act: (DOPNativeCardStatusCubit cubit) => cubit.getCardStatus(),
      expect: () => <TypeMatcher<DOPNativeCardStatusState>>[
            isA<GetCardStatusLoading>(),
            isA<GetCardStatusFailure>().having(
              (GetCardStatusFailure state) => state.error.statusCode,
              'verify statusCode should matched',
              CommonHttpClient.BAD_REQUEST,
            ),
          ]);

  blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>('getCardStatus with needShowLoading = true',
      wait: TestConstant.blocEmitStateDelayDuration,
      build: () => cubit,
      setUp: () {
        when(() => mockRepo.getCardStatus(mockConfig: any(named: 'mockConfig'))).thenAnswer(
              (_) async => DOPNativeCardStatusEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: <String, dynamic>{},
            ),
          ),
        );
      },
      act: (DOPNativeCardStatusCubit cubit) => cubit.getCardStatus(needShowLoading: false),
      expect: () => <TypeMatcher<DOPNativeCardStatusState>>[
        isA<GetCardStatusFailure>().having(
              (GetCardStatusFailure state) => state.error.statusCode,
          'verify statusCode should matched',
          CommonHttpClient.BAD_REQUEST,
        ),
      ]);

  group('handleSuccessCase', () {
    const String mockBlockedResponse = 'dop_native_get_card_status_blocked.json';

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit GetCardStatusBlocked when activation_status is blocked state',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        setUp: () {
          when(() => mockRepo.getCardStatus(mockConfig: any(named: 'mockConfig')))
              .thenAnswer((_) async => DOPNativeCardStatusEntity.fromBaseResponse(BaseResponse(
                    statusCode: CommonHttpClient.SUCCESS,
                    response: await TestUtil.getResponseMock(mockBlockedResponse),
                  )));
        },
        act: (DOPNativeCardStatusCubit cubit) => cubit.getCardStatus(),
        expect: () => <TypeMatcher<DOPNativeCardStatusState>>[
              isA<GetCardStatusLoading>(),
              isA<GetCardStatusBlocked>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
      'calls handleUnderwritingInProgress for underwritingInProgress case',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeCardStatusCubit cubit) => cubit.handleSuccessCase(
        cardStatus: DOPNativeCardStatusEntity(
          activationStatus: DOPNativeCardActivationStatus.empty,
          canActivateCard: false,
          consentLinkCard: false,
          isOfflineMerchant: false,
          posLimit: posLimitEntity,
        ),
        useCase: CardStatusUseCase.underwritingInProgress,
      ),
      expect: () => <dynamic>[
        isA<UnderwritingNoneOfflineMerchantOrNoneLinkCard>(),
      ],
    );

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
      'calls handleUnderwritingCardIssued for underwritingCardIssued case',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeCardStatusCubit cubit) => cubit.handleSuccessCase(
        cardStatus: DOPNativeCardStatusEntity(
          activationStatus: DOPNativeCardActivationStatus.empty,
          canActivateCard: false,
          consentLinkCard: false,
          isOfflineMerchant: false,
          posLimit: posLimitEntity,
        ),
        useCase: CardStatusUseCase.underwritingCardIssued,
      ),
      expect: () => <dynamic>[
        isA<UnderwritingNoneOfflineMerchantOrNoneLinkCard>(),
      ],
    );

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
      'calls handleCardStatusInformation for cardInformation case',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeCardStatusCubit cubit) => cubit.handleSuccessCase(
        cardStatus: DOPNativeCardStatusEntity(
          activationStatus: DOPNativeCardActivationStatus.activated,
          canActivateCard: false,
          posLimit: const DOPNativePosLimitEntity(
            posLimitActivationStatus: DOPNativePOSLimitActivationStatus.success,
          ),
        ),
        useCase: CardStatusUseCase.cardInformation,
      ),
      expect: () => <dynamic>[
        isA<CardStatusInformationActivated>(),
      ],
    );

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
      'calls handleUnderwritingCardStatus for cardActivated case',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeCardStatusCubit cubit) => cubit.handleSuccessCase(
        cardStatus: DOPNativeCardStatusEntity(
          activationStatus: DOPNativeCardActivationStatus.activated,
          canActivateCard: false,
        ),
        useCase: CardStatusUseCase.underwritingCardStatus,
      ),
      expect: () => <dynamic>[
        isA<UnderwritingCardStatusCardActivated>(),
      ],
    );
  });

  group('validateUnderwritingRequiredField', () {
    test('should return false if required field is null', () {
      final DOPNativeCardStatusEntity cardStatus = DOPNativeCardStatusEntity();
      expect(cubit.validateUnderwritingRequiredField(cardStatus), isFalse);
    });

    test('should return false if posLimit field null', () {
      expect(
        cubit.validateUnderwritingRequiredField(DOPNativeCardStatusEntity(
          activationStatus: DOPNativeCardActivationStatus.unknown,
          canActivateCard: false,
          consentLinkCard: true,
          isOfflineMerchant: true,
        )),
        isFalse,
      );
    });

    test('should return false if posLimit.maxPosLimitAllow is null', () {
      expect(
        cubit.validateUnderwritingRequiredField(DOPNativeCardStatusEntity(
          activationStatus: DOPNativeCardActivationStatus.unknown,
          canActivateCard: false,
          consentLinkCard: true,
          isOfflineMerchant: true,
          posLimit: const DOPNativePosLimitEntity(
            posLimitActivationStatus: DOPNativePOSLimitActivationStatus.success,
          ),
        )),
        isFalse,
      );
    });

    test('should return true if required field non null', () {
      expect(
        cubit.validateUnderwritingRequiredField(DOPNativeCardStatusEntity(
          activationStatus: DOPNativeCardActivationStatus.unknown,
          canActivateCard: false,
          consentLinkCard: true,
          posLimit: const DOPNativePosLimitEntity(
            posLimitActivationStatus: DOPNativePOSLimitActivationStatus.success,
            maxPosLimitAllow: 1234,
            posLimitUserSetting: 1234,
          ),
          isOfflineMerchant: true,
        )),
        isTrue,
      );
    });
  });

  group('handleUnderwritingInProgress', () {
    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit GetCardStatusFailure if not having required field',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) =>
            cubit.handleUnderwritingInProgress(DOPNativeCardStatusEntity()),
        expect: () => <TypeMatcher<GetCardStatusFailure>>[
              isA<GetCardStatusFailure>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit GetCardStatusFailure if isOfflineMerchant = consentLinkCard = canActivateCard = true',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) =>
            cubit.handleUnderwritingInProgress(DOPNativeCardStatusEntity(
              activationStatus: DOPNativeCardActivationStatus.unknown,
              canActivateCard: true,
              consentLinkCard: true,
              posLimit: posLimitEntity,
              isOfflineMerchant: true,
            )),
        expect: () => <TypeMatcher<GetCardStatusFailure>>[
              isA<GetCardStatusFailure>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit UnderwritingOfflineMerchantAndLinkCard if cardStatus.isOfflineMerchant == true && cardStatus.consentLinkCard == true',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) =>
            cubit.handleUnderwritingInProgress(DOPNativeCardStatusEntity(
              activationStatus: DOPNativeCardActivationStatus.unknown,
              canActivateCard: false,
              consentLinkCard: true,
              posLimit: posLimitEntity,
              isOfflineMerchant: true,
            )),
        expect: () => <TypeMatcher<UnderwritingOfflineMerchantAndLinkCard>>[
              isA<UnderwritingOfflineMerchantAndLinkCard>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit UnderwritingOfflineMerchantAndLinkCard if cardStatus.isOfflineMerchant == false',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) =>
            cubit.handleUnderwritingInProgress(DOPNativeCardStatusEntity(
              activationStatus: DOPNativeCardActivationStatus.unknown,
              canActivateCard: false,
              consentLinkCard: true,
              posLimit: posLimitEntity,
              isOfflineMerchant: false,
            )),
        expect: () => <TypeMatcher<UnderwritingNoneOfflineMerchantOrNoneLinkCard>>[
              isA<UnderwritingNoneOfflineMerchantOrNoneLinkCard>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit UnderwritingOfflineMerchantAndLinkCard if cardStatus.consentLinkCard == false',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) =>
            cubit.handleUnderwritingInProgress(DOPNativeCardStatusEntity(
              activationStatus: DOPNativeCardActivationStatus.unknown,
              canActivateCard: false,
              consentLinkCard: false,
              posLimit: posLimitEntity,
              isOfflineMerchant: true,
            )),
        expect: () => <TypeMatcher<UnderwritingNoneOfflineMerchantOrNoneLinkCard>>[
              isA<UnderwritingNoneOfflineMerchantOrNoneLinkCard>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit GetCardStatusBlocked if activationStatus is lock',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleUnderwritingInProgress(
            DOPNativeCardStatusEntity(
                activationStatus: DOPNativeCardActivationStatus.permanentBlocked)),
        expect: () => <TypeMatcher<GetCardStatusBlocked>>[
              isA<GetCardStatusBlocked>(),
            ]);
  });

  group('handleUnderwritingCardIssued ', () {
    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit GetCardStatusFailure if not having required field',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) =>
            cubit.handleUnderwritingCardIssued(DOPNativeCardStatusEntity()),
        expect: () => <TypeMatcher<GetCardStatusFailure>>[
              isA<GetCardStatusFailure>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit UnderwritingCardIssuedCardActivated if isOfflineMerchant = consentLinkCard = true and activationStatus = activated',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) =>
            cubit.handleUnderwritingCardIssued(DOPNativeCardStatusEntity(
              isOfflineMerchant: true,
              consentLinkCard: true,
              posLimit: posLimitEntity,
              canActivateCard: false,
              activationStatus: DOPNativeCardActivationStatus.activated,
            )),
        expect: () => <TypeMatcher<UnderwritingCardIssuedCardActivated>>[
              isA<UnderwritingCardIssuedCardActivated>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit UnderwritingCardIssuedCanActivateCard if isOfflineMerchant = consentLinkCard = true and activationStatus != activated and canActivateCard = true',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) =>
            cubit.handleUnderwritingCardIssued(DOPNativeCardStatusEntity(
              isOfflineMerchant: true,
              consentLinkCard: true,
              posLimit: posLimitEntity,
              canActivateCard: true,
              activationStatus: DOPNativeCardActivationStatus.unknown,
            )),
        expect: () => <TypeMatcher<UnderwritingCardIssuedCanActivateCard>>[
              isA<UnderwritingCardIssuedCanActivateCard>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit UnderwritingCardIssuedCannotActivateCard if isOfflineMerchant = consentLinkCard = true and activationStatus != activated and canActivateCard = false',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) =>
            cubit.handleUnderwritingCardIssued(DOPNativeCardStatusEntity(
              isOfflineMerchant: true,
              consentLinkCard: true,
              posLimit: posLimitEntity,
              canActivateCard: false,
              activationStatus: DOPNativeCardActivationStatus.unknown,
            )),
        expect: () => <TypeMatcher<UnderwritingCardIssuedCannotActivateCard>>[
              isA<UnderwritingCardIssuedCannotActivateCard>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit UnderwritingNoneOfflineMerchantOrNoneLinkCard if isOfflineMerchant = false or consentLinkCard = false',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) =>
            cubit.handleUnderwritingCardIssued(DOPNativeCardStatusEntity(
              isOfflineMerchant: false,
              consentLinkCard: true,
              posLimit: posLimitEntity,
              canActivateCard: false,
              activationStatus: DOPNativeCardActivationStatus.unknown,
            )),
        expect: () => <TypeMatcher<UnderwritingNoneOfflineMerchantOrNoneLinkCard>>[
              isA<UnderwritingNoneOfflineMerchantOrNoneLinkCard>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit GetCardStatusBlocked if activationStatus is lock',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleUnderwritingCardIssued(
            DOPNativeCardStatusEntity(
                activationStatus: DOPNativeCardActivationStatus.permanentBlocked)),
        expect: () => <TypeMatcher<GetCardStatusBlocked>>[
              isA<GetCardStatusBlocked>(),
            ]);
  });

  group('savePosLimit', () {
    const int posLimitAllow = 1234;
    const int initialPostLimit = 999;
    const DOPNativePosLimitEntity posLimitEntity = DOPNativePosLimitEntity(
      maxPosLimitAllow: posLimitAllow,
      posLimitActivationStatus: DOPNativePOSLimitActivationStatus.success,
      posLimitUserSetting: 1234,
    );

    test('savePosLimit ignore when DOPNativePosLimitEntity null', () {
      appState.dopNativeState.posLimitNumber = initialPostLimit;
      cubit.savePosLimit(null);
      expect(appState.dopNativeState.posLimitNumber, initialPostLimit);
    });

    test('savePosLimit ignore DOPNativePosLimitEntity.maxPosLimitAllow null', () {
      appState.dopNativeState.posLimitNumber = initialPostLimit;
      cubit.savePosLimit(const DOPNativePosLimitEntity());
      expect(appState.dopNativeState.posLimitNumber, initialPostLimit);
    });

    test('savePosLimit should update nonnull value', () {
      appState.dopNativeState.posLimitNumber = initialPostLimit;
      cubit.savePosLimit(posLimitEntity);
      expect(appState.dopNativeState.posLimitNumber, posLimitEntity.maxPosLimitAllow);
    });

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'getCardStatus success savePosLimit should matched ',
        wait: TestConstant.blocEmitStateDelayDuration,
        build: () => cubit,
        setUp: () {
          when(() => mockRepo.getCardStatus(mockConfig: any(named: 'mockConfig'))).thenAnswer(
              (_) async => DOPNativeCardStatusEntity.fromBaseResponse(BaseResponse(
                      statusCode: CommonHttpClient.SUCCESS,
                      response: <String, dynamic>{
                        'data': <String, dynamic>{
                          'pos_limit': posLimitEntity.toJson(),
                        }
                      })));
        },
        act: (DOPNativeCardStatusCubit cubit) => cubit.getCardStatus(),
        verify: (DOPNativeCardStatusCubit cubit) {
          expect(appState.dopNativeState.posLimitNumber, posLimitEntity.maxPosLimitAllow);
        });

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'getCardStatus failure savePosLimit == null',
        wait: TestConstant.blocEmitStateDelayDuration,
        build: () => cubit,
        setUp: () {
          when(() => mockRepo.getCardStatus(mockConfig: any(named: 'mockConfig'))).thenAnswer(
            (_) async => DOPNativeCardStatusEntity.fromBaseResponse(
              BaseResponse(
                statusCode: CommonHttpClient.BAD_REQUEST,
                response: <String, dynamic>{},
              ),
            ),
          );
        },
        act: (DOPNativeCardStatusCubit cubit) => cubit.getCardStatus(),
        verify: (DOPNativeCardStatusCubit cubit) {
          expect(appState.dopNativeState.posLimitNumber, isNull);
        });
  });

  group('handleCardStatusInformation ', () {
    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit GetCardStatusFailure if activationStatus is null or unknown',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleCardStatusInformation(
              activationStatus: null,
              canActivateCard: true,
            ),
        expect: () => <TypeMatcher<GetCardStatusFailure>>[
              isA<GetCardStatusFailure>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit CardStatusInformationActivated if activationStatus is activated && posLimitActivationStatus is success',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleCardStatusInformation(
              activationStatus: DOPNativeCardActivationStatus.activated,
              posLimitActivationStatus: DOPNativePOSLimitActivationStatus.success,
              canActivateCard: true,
            ),
        expect: () => <TypeMatcher<CardStatusInformationActivated>>[
              isA<CardStatusInformationActivated>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit CardStatusRetryPOSLimit if activationStatus is activated && posLimitActivationStatus is empty',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleCardStatusInformation(
              activationStatus: DOPNativeCardActivationStatus.activated,
              posLimitActivationStatus: DOPNativePOSLimitActivationStatus.empty,
              canActivateCard: true,
            ),
        expect: () => <TypeMatcher<DOPNativeCardStatusState>>[
              isA<CardStatusInformationActivated>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit CardStatusRetryPOSLimit if activationStatus is activated && posLimitActivationStatus is pending',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleCardStatusInformation(
              activationStatus: DOPNativeCardActivationStatus.activated,
              posLimitActivationStatus: DOPNativePOSLimitActivationStatus.pending,
              canActivateCard: true,
            ),
        expect: () => <TypeMatcher<DOPNativeCardStatusState>>[
              isA<CardStatusInformationActivatedRetryPosLimit>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit CardStatusPOSLimitFail if activationStatus is activated && posLimitActivationStatus is timeout',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleCardStatusInformation(
              activationStatus: DOPNativeCardActivationStatus.activated,
              posLimitActivationStatus: DOPNativePOSLimitActivationStatus.timeout,
              canActivateCard: true,
            ),
        expect: () => <TypeMatcher<DOPNativeCardStatusState>>[
              isA<CardStatusInformationActivatedPosFailed>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit CardStatusPOSLimitFail if activationStatus is activated && posLimitActivationStatus is timeout',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleCardStatusInformation(
              activationStatus: DOPNativeCardActivationStatus.activated,
              posLimitActivationStatus: DOPNativePOSLimitActivationStatus.failure,
              canActivateCard: true,
            ),
        expect: () => <TypeMatcher<DOPNativeCardStatusState>>[
              isA<CardStatusInformationActivatedPosFailed>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit GetCardStatusFailure if activationStatus is activated && posLimitActivationStatus is null',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleCardStatusInformation(
              activationStatus: DOPNativeCardActivationStatus.activated,
              canActivateCard: true,
            ),
        expect: () => <TypeMatcher<DOPNativeCardStatusState>>[
              isA<GetCardStatusFailure>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit CardStatusInformationRetry if activationStatus is not active and canActivateCard is true',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleCardStatusInformation(
              activationStatus: DOPNativeCardActivationStatus.lost,
              canActivateCard: true,
            ),
        expect: () => <TypeMatcher<CardStatusInformationRetry>>[
              isA<CardStatusInformationRetry>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit CardStatusInformationFail if activationStatus is not active and canActivateCard is false',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleCardStatusInformation(
              activationStatus: DOPNativeCardActivationStatus.lost,
              canActivateCard: false,
            ),
        expect: () => <TypeMatcher<CardStatusInformationFail>>[
              isA<CardStatusInformationFail>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit GetCardStatusFailure if activationStatus is not active and canActivateCard is null',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleCardStatusInformation(
              activationStatus: DOPNativeCardActivationStatus.lost,
              canActivateCard: null,
            ),
        expect: () => <TypeMatcher<GetCardStatusFailure>>[
              isA<GetCardStatusFailure>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit CardStatusInformationActivated if activationStatus is active and canActivateCard is null && posLimitActivationStatus is success',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleCardStatusInformation(
              activationStatus: DOPNativeCardActivationStatus.activated,
              canActivateCard: null,
              posLimitActivationStatus: DOPNativePOSLimitActivationStatus.success,
            ),
        expect: () => <TypeMatcher<CardStatusInformationActivated>>[
              isA<CardStatusInformationActivated>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit GetCardStatusBlocked if activationStatus is locked',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleCardStatusInformation(
              activationStatus: DOPNativeCardActivationStatus.lockCard,
              canActivateCard: null,
            ),
        expect: () => <TypeMatcher<GetCardStatusBlocked>>[
              isA<GetCardStatusBlocked>(),
            ]);
  });

  group('test isLockCard', () {
    test('returns true if activation status is permanentBlocked', () {
      expect(cubit.isLockCard(DOPNativeCardActivationStatus.permanentBlocked), isTrue);
    });

    test('returns true if activation status is temporaryBlocked', () {
      expect(cubit.isLockCard(DOPNativeCardActivationStatus.temporaryBlocked), isTrue);
    });

    test('returns true if activation status is', () {
      expect(cubit.isLockCard(DOPNativeCardActivationStatus.lockCard), isTrue);
    });

    test('returns false if activation status not in lock', () {
      expect(cubit.isLockCard(DOPNativeCardActivationStatus.activated), isFalse);
    });

    test('returns false if activation status is null', () {
      expect(cubit.isLockCard(null), isFalse);
    });
  });

  group('test handle handleUnderwritingCardStatus', () {
    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit GetCardStatusBlocked if activation_status is in `permanent_blocked, temporary_blocked, lock_card` ',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) => cubit.handleUnderwritingCardStatus(
            DOPNativeCardStatusEntity(
                activationStatus: DOPNativeCardActivationStatus.temporaryBlocked)),
        expect: () => <TypeMatcher<GetCardStatusBlocked>>[
              isA<GetCardStatusBlocked>(),
            ]);

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
        'should emit UnderwritingCardStatusCardActivated if activation_status != `permanent_blocked, temporary_blocked, lock_card` ',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeCardStatusCubit cubit) =>
            cubit.handleUnderwritingCardStatus(DOPNativeCardStatusEntity(
              activationStatus: DOPNativeCardActivationStatus.activated,
            )),
        expect: () => <TypeMatcher<UnderwritingCardStatusCardActivated>>[
              isA<UnderwritingCardStatusCardActivated>(),
            ]);
  });

  group('test handlePOSLimitStatus', () {
    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
      'emits [CardStatusInformationActivated] when status is success',
      build: () => cubit,
      act: (_) {
        cubit.handlePOSLimitStatus(DOPNativePOSLimitActivationStatus.success);
      },
      expect: () => <TypeMatcher<CardStatusInformationActivated>>[
        isA<CardStatusInformationActivated>(),
      ],
    );

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
      'emits [CardStatusInformationActivated] when status is empty',
      build: () => cubit,
      act: (_) {
        cubit.handlePOSLimitStatus(DOPNativePOSLimitActivationStatus.empty);
      },
      expect: () => <TypeMatcher<CardStatusInformationActivated>>[
        isA<CardStatusInformationActivated>(),
      ],
    );

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
      'emits [CardStatusRetryPOSLimit] when status is pending',
      build: () => cubit,
      act: (_) {
        cubit.handlePOSLimitStatus(DOPNativePOSLimitActivationStatus.pending);
      },
      expect: () => <TypeMatcher<CardStatusInformationActivatedRetryPosLimit>>[
        isA<CardStatusInformationActivatedRetryPosLimit>(),
      ],
    );

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
      'emits [CardStatusPOSLimitFail] when status is timeout',
      build: () => cubit,
      act: (_) {
        cubit.handlePOSLimitStatus(DOPNativePOSLimitActivationStatus.timeout);
      },
      expect: () => <TypeMatcher<CardStatusInformationActivatedPosFailed>>[
        isA<CardStatusInformationActivatedPosFailed>(),
      ],
    );

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
      'emits [CardStatusPOSLimitFail] when status is failure',
      build: () => cubit,
      act: (_) {
        cubit.handlePOSLimitStatus(DOPNativePOSLimitActivationStatus.failure);
      },
      expect: () => <TypeMatcher<CardStatusInformationActivatedPosFailed>>[
        isA<CardStatusInformationActivatedPosFailed>(),
      ],
    );

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
      'emits [GetCardStatusFailure] when status is null',
      build: () => cubit,
      act: (_) {
        cubit.handlePOSLimitStatus(null);
      },
      expect: () => <TypeMatcher<GetCardStatusFailure>>[
        isA<GetCardStatusFailure>(),
      ],
    );

    blocTest<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
      'emits [GetCardStatusFailure] when status is other unknown',
      build: () => cubit,
      act: (_) {
        cubit.handlePOSLimitStatus(DOPNativePOSLimitActivationStatus.unknown);
      },
      expect: () => <TypeMatcher<GetCardStatusFailure>>[
        isA<GetCardStatusFailure>(),
      ],
    );
  });
}
