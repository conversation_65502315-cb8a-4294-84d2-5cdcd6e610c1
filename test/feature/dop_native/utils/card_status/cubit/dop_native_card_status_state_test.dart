import 'package:evoapp/data/response/dop_native/dop_native_card_status_entity.dart';
import 'package:evoapp/feature/dop_native/util/card_status/cubit/dop_native_card_status_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeCardStatusState', () {
    test('Initial state should be GetCardStatusInitial', () {
      expect(GetCardStatusInitial(), isA<DOPNativeCardStatusState>());
    });

    test('GetCardStatusLoading state should be DOPNativeUnderwritingInProgressState', () {
      expect(GetCardStatusLoading(), isA<DOPNativeCardStatusState>());
    });

    test('GetCardStatusFailure should contain error', () {
      final ErrorUIModel error = ErrorUIModel();
      final GetCardStatusFailure failureState = GetCardStatusFailure(error);

      expect(failureState, isA<DOPNativeCardStatusState>());
      expect(failureState.error, equals(error));
    });

    test('GetCardStatusBlocked state should be DOPNativeUnderwritingInProgressState', () {
      expect(GetCardStatusBlocked(), isA<DOPNativeCardStatusState>());
    });

    test('UnderwritingOfflineMerchantAndLinkCard should contain cardStatus', () {
      final DOPNativeCardStatusEntity cardStatus = DOPNativeCardStatusEntity();
      final UnderwritingOfflineMerchantAndLinkCard successState =
          UnderwritingOfflineMerchantAndLinkCard(cardStatus);

      expect(successState, isA<DOPNativeCardStatusState>());
      expect(successState.cardStatus, equals(cardStatus));
    });

    test(
        'UnderwritingNoneOfflineMerchantOrNoneLinkCard state should be DOPNativeUnderwritingInProgressState',
        () {
      expect(UnderwritingNoneOfflineMerchantOrNoneLinkCard(), isA<DOPNativeCardStatusState>());
    });

    test('UnderwritingCardIssuedCanActivateCard state should be DOPNativeCardStatusState', () {
      expect(UnderwritingCardIssuedCanActivateCard(), isA<DOPNativeCardStatusState>());
    });

    test('UnderwritingCardIssuedCannotActivateCard state should be DOPNativeCardStatusState', () {
      expect(UnderwritingCardIssuedCannotActivateCard(), isA<DOPNativeCardStatusState>());
    });

    test('UnderwritingCardIssuedCardActivated state should be DOPNativeCardStatusState', () {
      expect(UnderwritingCardIssuedCardActivated(), isA<DOPNativeCardStatusState>());
    });

    test('CardStatusInformationActivated state should be DOPNativeCardStatusState', () {
      expect(CardStatusInformationActivated(), isA<DOPNativeCardStatusState>());
    });

    test(
        'CardStatusInformationActivated state should be CardStatusInformationActivatedRetryPosLimit',
        () {
      expect(CardStatusInformationActivatedRetryPosLimit(), isA<DOPNativeCardStatusState>());
    });

    test('CardStatusInformationActivated state should be CardStatusInformationActivatedPosFailed',
        () {
      expect(CardStatusInformationActivatedPosFailed(), isA<DOPNativeCardStatusState>());
    });

    test('CardStatusInformationRetry state should be DOPNativeCardStatusState', () {
      expect(CardStatusInformationRetry(), isA<DOPNativeCardStatusState>());
    });

    test('CardStatusInformationFail state should be DOPNativeCardStatusState', () {
      expect(CardStatusInformationFail(), isA<DOPNativeCardStatusState>());
    });

    test('UnderwritingCardStatusCardActivated state should be DOPNativeCardStatusState', () {
      expect(UnderwritingCardStatusCardActivated(), isA<DOPNativeCardStatusState>());
    });

    test('CardStatusInformationActivatedRetryPosLimit state should be DOPNativeCardStatusState',
        () {
      expect(CardStatusInformationActivatedRetryPosLimit(), isA<DOPNativeCardStatusState>());
    });

    test('CardStatusInformationActivatedPosFailed state should be DOPNativeCardStatusState', () {
      expect(CardStatusInformationActivatedPosFailed(), isA<DOPNativeCardStatusState>());
    });
  });
}
