import 'package:evoapp/feature/dop_native/util/mock_file/mock_dop_native_collect_location_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('enum value MockDOPNativeESignStateUseCase', () {
    expect(
      MockDOPNativeCollectLocationUseCase.collectLocationSuccess.value,
      'dop_native_collect_location_success.json',
    );
  });

  test('test getMockDOPNativeCollectLocation returns correct value for CollectLocationSuccess', () {
    final String result =
        getMockDOPNativeCollectLocation(MockDOPNativeCollectLocationUseCase.collectLocationSuccess);
    expect(result, 'dop_native_collect_location_success.json');
  });
}
