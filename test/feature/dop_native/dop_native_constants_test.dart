import 'package:evoapp/feature/dop_native/dop_native_constants.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Test constant values', () {
    expect(DOPNativeConstants.resendOTPDisplayDelayInSeconds, 15);
    expect(DOPNativeConstants.statusIconHeightPercentage, 131 / 812);
    expect(DOPNativeConstants.figmaScreenHeight, 812);
    expect(DOPNativeConstants.figmaScreenWidth, 375);
    expect(DOPNativeConstants.dopNativeAppBarHeight, 64);
    expect(DOPNativeConstants.dopNativeLoadingAnimationSize, 34);
    expect(DOPNativeConstants.dopNativeMaxAddressLength, 70);
    expect(DOPNativeConstants.dopNativeMaxCompanyNameLength, 200);
    expect(DOPNativeConstants.incomeThreshold, 0);
    expect(DOPNativeConstants.maxIncomeInBillions, 999);
    expect(DOPNativeConstants.incomeDecimalSeparator, ',');
    expect(DOPNativeConstants.delayCallGetCardStatusInSeconds, 15);
    expect(DOPNativeConstants.defaultPollingIntervalTimeInMs, 1000);
    expect(DOPNativeConstants.cardStatusCountdownAllProgressInSecs, 30);
    expect(DOPNativeConstants.cardStatusCountdownToPoolingInSecs, 10);
    expect(DOPNativeConstants.ekycInstructionIconHeightPercentage, 107.39 / 812);
    expect(DOPNativeConstants.delayToGetAppStateCardActivatedInSecs, 3);
    expect(DOPNativeConstants.dopDateFormat, 'yyyy-MM-dd');
    expect(DOPNativeConstants.fptNfcSdkDateFormat, 'yyMMdd');
    expect(DOPNativeConstants.tvNfcSdkDateFormat, 'dd/MM/yyyy');
    expect(DOPNativeConstants.receiveTimeoutInSecond, 30);
    expect(DOPNativeConstants.sendTimeoutInSecond, 30);
    expect(DOPNativeConstants.fptNfcVideoIntroductionRatio, 16 / 9);
    expect(DOPNativeConstants.fptNfcIntroductionAnimationHeightPercentage, 108 / 812);
    expect(DOPNativeConstants.nfcErrorPopupPositiveButtonDelayInSeconds, 2);
    expect(DOPNativeConstants.defaultMWGAutoPCBPollingInterval, 10);
    expect(DOPNativeConstants.appraisingPrefix, 'appraising');
  });

  test('DopDevicePlatformConstant', () {
    expect(DopDevicePlatformConstant.android, 'android');
    expect(DopDevicePlatformConstant.ios, 'ios');
  });
}
