import 'package:evoapp/feature/dop_native/features/verify_otp/widgets/dop_native_resend_otp.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_resources.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../../util/flutter_test_config.dart';

void main() {
  group('Test DOPNativeOtpCountdown', () {
    late bool onResendCalled;
    late TextStyle expectedTextStyle;

    onResend() {
      onResendCalled = true;
    }

    setUpAll(() {
      getItRegisterColor();
      getItRegisterTextStyle();
      getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
      getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());

      expectedTextStyle = dopNativeTextStyles.bodyMedium(
        dopNativeColors.otpCountdown,
      );
    });

    setUp(() {
      onResendCalled = false;
    });

    void verifyTitle() {
      expect(
        find.byWidgetPredicate((Widget widget) {
          return widget is Text &&
              widget.data == DOPNativeStrings.otpDidNotReceive &&
              widget.style == dopNativeTextStyles.bodyMedium(dopNativeColors.textActive);
        }),
        findsOneWidget,
      );
    }

    testWidgets('Give remainSeconds > 0, should show remain time text',
        (WidgetTester tester) async {
      const int fakeRemainSeconds = 5;
      const String expectedRemainingTime = '(00:05)';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DOPNativeResendOTP(
              onResend: onResend,
              otpExpiredRemainSeconds: fakeRemainSeconds,
            ),
          ),
        ),
      );

      verifyTitle();

      // Verify action
      await tester.tap(find.byType(InkWell));
      expect(onResendCalled, true);

      // Show countdown time
      expect(
        find.byWidgetPredicate((Widget widget) {
          return widget is Text &&
              widget.style == expectedTextStyle &&
              widget.data == expectedRemainingTime;
        }),
        findsOneWidget,
      );
    });

    testWidgets('Give remainSeconds = 0, should hide remain time text',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: DOPNativeResendOTP(otpExpiredRemainSeconds: 0))),
      );

      verifyTitle();

      expect(
        find.byWidgetPredicate((Widget widget) {
          return widget is Text && widget.style == expectedTextStyle;
        }),
        findsNothing,
      );
    });

    testWidgets('NOT give remainSeconds, should hide remain time text',
        (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: Scaffold(body: DOPNativeResendOTP())));

      verifyTitle();

      expect(
        find.byWidgetPredicate((Widget widget) {
          return widget is Text && widget.style == expectedTextStyle;
        }),
        findsNothing,
      );
    });
  });
}
