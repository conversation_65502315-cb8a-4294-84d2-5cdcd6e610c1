import 'dart:async';

import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_appraising_status_entity.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state_cubit.dart';
import 'package:evoapp/feature/dop_native/features/appraising_verification/cubit/dop_native_appraising_verification_cubit.dart';
import 'package:evoapp/feature/dop_native/features/appraising_verification/cubit/dop_native_appraising_verification_state.dart';
import 'package:evoapp/feature/dop_native/features/appraising_verification/dop_native_fourth_appraising_screen.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/util/dop_native_submit_status_polling/dop_native_submit_status_polling_impl.dart';
import 'package:evoapp/feature/dop_native/widgets/appbar/dop_native_appbar_widget.dart';
import 'package:evoapp/feature/dop_native/widgets/dop_native_status_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';
import '../../../../util/flutter_test_config.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {
  @override
  Future<DOPNativeAppraisingStatusEntity> getAppraisingStatus({
    MockConfig? mockConfig,
  }) async {
    return DOPNativeAppraisingStatusEntity.unserializable();
  }
}

class MockDOPNativeApplicationStateCubit extends Mock implements DOPNativeApplicationStateCubit {
  @override
  Future<void> close() => Future<void>.value();
}

class MockDOPNativeAppraisingVerificationCubit extends Mock
    implements DOPNativeAppraisingVerificationCubit {
  @override
  Future<void> close() => Future<void>.value();
}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  late MockDOPNativeRepo mockDOPNativeRepo;
  late MockDOPNativeApplicationStateCubit mockApplicationStateCubit;
  late MockDOPNativeAppraisingVerificationCubit mockAppraisingVerificationCubit;

  late StreamController<DOPNativeAppraisingVerificationState>
      appraisingVerificationStreamController;
  late StreamController<DOPNativeApplicationState> applicationStateStreamController;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    initConfigEvoPageStateBase();
    setUtilsMockInstanceForTesting();

    mockDOPNativeRepo = MockDOPNativeRepo();
    mockApplicationStateCubit = MockDOPNativeApplicationStateCubit();
    mockAppraisingVerificationCubit = MockDOPNativeAppraisingVerificationCubit();

    getIt.registerLazySingleton<AuthenticationRepo>(() => MockAuthenticationRepo());
    getIt.registerLazySingleton<DOPNativeRepo>(() => MockDOPNativeRepo());

    getIt.registerLazySingleton<DopNativeButtonStyles>(() => DopNativeButtonStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());

    final CommonImageProvider mockCommonImageProvider = getIt.get<CommonImageProvider>();

    when(
      () => mockCommonImageProvider.asset(
        any(),
        width: any(named: 'width'),
        height: any(named: 'height'),
        color: any(named: 'color'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'),
        cacheWidth: any(named: 'cacheWidth'),
        cacheHeight: any(named: 'cacheHeight'),
        package: any(named: 'package'),
      ),
    ).thenAnswer((_) => Container());

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenAnswer((_) => 10);
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    appraisingVerificationStreamController =
        StreamController<DOPNativeAppraisingVerificationState>.broadcast();
    applicationStateStreamController = StreamController<DOPNativeApplicationState>.broadcast();

    when(() => mockAppraisingVerificationCubit.stream)
        .thenAnswer((_) => appraisingVerificationStreamController.stream);
    when(() => mockAppraisingVerificationCubit.state)
        .thenReturn(AppraisingVerificationInitialState());
    when(() => mockAppraisingVerificationCubit.getAppraisingStatus())
        .thenAnswer((_) => Future<void>.value());

    when(() => mockApplicationStateCubit.stream).thenAnswer(
      (_) => applicationStateStreamController.stream,
    );
    when(() => mockApplicationStateCubit.state).thenReturn(
      DOPNativeApplicationStateInitial(),
    );
    when(() => mockApplicationStateCubit.getApplicationState())
        .thenAnswer((_) => Future<void>.value());
  });

  tearDown(() {
    appraisingVerificationStreamController.close();
    applicationStateStreamController.close();

    reset(mockDOPNativeRepo);
    reset(mockAppraisingVerificationCubit);
    reset(mockApplicationStateCubit);
    reset(mockNavigatorContext);
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  group('DOPNativeFourthApprisingScreen', () {
    testWidgets('should display status widget with correct data', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: DOPNativeFourthApprisingScreen(
            appraisingCubit: mockAppraisingVerificationCubit,
            applicationStateCubit: mockApplicationStateCubit,
          ),
        ),
      );

      // Act
      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(DOPNativeAppBar), findsOneWidget);
      expect(find.byType(DOPNativeStatusWidget), findsOneWidget);
      expect(find.text(DOPNativeStrings.dopNativeFourthAppraisingTitle), findsOneWidget);
      expect(find.text(DOPNativeStrings.dopNativeFourthAppraisingDesc), findsOneWidget);
    });

    testWidgets('should call getAppraisingStatus on init state', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: DOPNativeFourthApprisingScreen(
            appraisingCubit: mockAppraisingVerificationCubit,
            applicationStateCubit: mockApplicationStateCubit,
          ),
        ),
      );

      // Act
      await tester.pumpAndSettle();

      // Assert
      verify(() => mockAppraisingVerificationCubit.getAppraisingStatus()).called(1);
    });

    testWidgets('should handle successful appraising verification state',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: DOPNativeFourthApprisingScreen(
            appraisingCubit: mockAppraisingVerificationCubit,
            applicationStateCubit: mockApplicationStateCubit,
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Act
      appraisingVerificationStreamController.add(AppraisingVerificationSuccessState());
      await tester.pumpAndSettle();

      // Assert
      verify(() => mockApplicationStateCubit.getApplicationState()).called(1);
    });

    testWidgets('should handle failed appraising verification state', (WidgetTester tester) async {
      // Arrange
      final ErrorUIModel errorUiModel = ErrorUIModel(statusCode: CommonHttpClient.BAD_REQUEST);

      await tester.pumpWidget(
        MaterialApp(
          home: DOPNativeFourthApprisingScreen(
            appraisingCubit: mockAppraisingVerificationCubit,
            applicationStateCubit: mockApplicationStateCubit,
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Act
      appraisingVerificationStreamController.add(AppraisingVerificationFailedState(errorUiModel));
      await tester.pumpAndSettle();

      // Assert
      verify(
        () => mockNavigatorContext.pushNamed(
          Screen.dopNativeStatusScreenName,
          extra: any(named: 'extra'),
        ),
      ).called(1);
    });

    testWidgets('should cancel polling when disposed', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: DOPNativeFourthApprisingScreen(
            appraisingCubit: mockAppraisingVerificationCubit,
            applicationStateCubit: mockApplicationStateCubit,
          ),
        ),
      );

      // Act
      await tester.pumpAndSettle();
      await tester.pumpWidget(const MaterialApp(home: SizedBox()));

      // Assert
      verify(() => mockAppraisingVerificationCubit.cancelPollingAppraisingStatus()).called(1);
    });

    testWidgets('should setup correct appraisingCubit', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: DOPNativeFourthApprisingScreen(),
        ),
      );

      // Act
      await tester.pumpAndSettle();
      final DOPNativeFourthApprisingScreenState state =
          tester.state(find.byType(DOPNativeFourthApprisingScreen));

      // Assert
      final DOPNativeSubmitStatusPollingImpl polling =
          state.appraisingCubit.dopNativeSubmitStatusPolling as DOPNativeSubmitStatusPollingImpl;
      expect(
        polling.intervalDuration.inMilliseconds,
        DOPNativeFourthApprisingScreenState.pollingIntervalInMilliseconds,
      );
    });

    test('pushReplacementNamed replaces with the correct route', () {
      DOPNativeFourthApprisingScreen.pushReplacementNamed();

      verify(
        () => mockNavigatorContext.pushReplacementNamed(
          Screen.dopNativeFourthAppraisingScreenName,
          extra: any(named: 'extra'),
        ),
      ).called(1);
    });
  });
}
