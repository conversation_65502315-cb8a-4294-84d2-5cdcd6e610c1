import 'package:evoapp/feature/dop_native/features/introduction/sub_introduction/cubit/dop_native_sub_introduction_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('DOPNativeSubIntroductionInitialState is DOPNativeSubIntroductionState', () {
    final DOPNativeSubIntroductionInitialState state = DOPNativeSubIntroductionInitialState();
    expect(state, isA<DOPNativeSubIntroductionState>());
  });
}
