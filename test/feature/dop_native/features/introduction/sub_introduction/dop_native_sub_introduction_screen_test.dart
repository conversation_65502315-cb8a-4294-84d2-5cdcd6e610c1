import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/feature/dop_native/features/introduction/sub_introduction/dop_native_sub_introduction_screen.dart';
import 'package:evoapp/feature/dop_native/features/introduction/sub_introduction/widgets/dop_native_sub_introduction_footer.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_images.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_resources.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/widgets/appbar/dop_native_appbar_widget.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/user_journey/user_journey_handler.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/feature/webview/common_webview_controller.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/feature/webview/webview_utils.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/flutter_downloader/common_flutter_downloader.dart';
import 'package:flutter_inappwebview_android/flutter_inappwebview_android.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../base/evo_page_state_base_test_config.dart';
import '../../../../../util/flutter_test_config.dart';
import '../../../../home/<USER>/v2/story/widgets/story_web_view_test.dart';

class MockCommonFlutterDownloader extends Mock implements CommonFlutterDownloader {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockDopNativeButtonStyles extends Mock implements DopNativeButtonStyles {}

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockUserJourneyHandler extends Mock implements UserJourneyHandler {}

void main() {
  const String fakeUrl = 'fake-url';
  const MethodChannel channel = MethodChannel('flutter/platform_views');

  late DevicePlatform mockDevicePlatform;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    AndroidInAppWebViewPlatform.registerWith();
    initConfigEvoPageStateBase();
    setUpOneLinkDeepLinkRegExForTest();

    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());

    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());

    getIt.registerLazySingleton<DopNativeButtonStyles>(() => MockDopNativeButtonStyles());
    registerFallbackValue(ButtonSize.medium);
    setUtilsMockInstanceForTesting();
    getIt.registerLazySingleton<CommonFlutterDownloader>(() => MockCommonFlutterDownloader());
    getIt.registerLazySingleton<DevicePlatform>(() => MockDevicePlatform());
    getIt.registerLazySingleton<DOPNativeRepo>(() => MockDOPNativeRepo());
    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => MockEvoLocalStorageHelper());
    getIt.registerLazySingleton<AuthenticationRepo>(() => MockAuthenticationRepo());
    getIt.registerLazySingleton<CommonWebViewUtils>(() => MockCommonWebViewUtils());
    when(() => getIt.get<CommonWebViewUtils>().useHybridComposition).thenAnswer((_) => false);
    when(() => evoImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenReturn(const SizedBox());

    mockDevicePlatform = getIt.get<DevicePlatform>();
    when(() => mockDevicePlatform.isAndroid()).thenReturn(true);

    when(() => dopNativeButtonStyles.primary(any())).thenReturn(const ButtonStyle());

    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      (MethodCall methodCall) async {
        return <dynamic, dynamic>{};
      },
    );

    FlavorConfig(
      flavor: FlavorType.stag.name,
      values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
    );

    final MockUserJourneyHandler mockUserJourneyHandler = MockUserJourneyHandler();
    UserJourneyHandler.instanceForTesting = mockUserJourneyHandler;
    when(() => mockUserJourneyHandler.clearInfoDOPWebJourneyIfSaved())
        .thenAnswer((_) => Future<void>.value());
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
  });

  tearDownAll(() {
    UserJourneyHandler.resetToOriginalInstance();
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  CommonWebView verifyWebView(WidgetTester tester) {
    // find webView
    final Finder webViewFinder = find.byType(CommonWebView);
    expect(webViewFinder, findsOneWidget);
    // verify webView param
    final CommonWebView webView = tester.widget(webViewFinder);
    expect(webView.arg.url, fakeUrl);
    expect(
        webView.arg.appBar,
        isA<DOPNativeAppBar>()
            .having(
              (DOPNativeAppBar p0) => p0.title,
              'verify WebView title',
              '',
            )
            .having(
              (DOPNativeAppBar p0) => p0.leading,
              'verify WebView leading',
              isNotNull,
            ));
    verify(() => evoImageProvider.asset(
          DOPNativeImages.icClose,
          width: 25,
          height: 25,
        )).called(1);

    return webView;
  }

  void verifyFooter(WidgetTester tester) {
    // find footer
    final Finder footerFinder = find.byType(DOPNativeSubIntroductionFooter);
    expect(footerFinder, findsOneWidget);
  }

  group('DOPNativeWelcomeBackScreen navigation tests', () {
    final String screenName = Screen.dopNativeSubIntroductionScreen.name;
    bool fakeCanGoBack = true;
    late bool isCanGoBackCalled;
    late bool isGoBackCalled;

    Future<bool> canGoBack() async {
      isCanGoBackCalled = true;
      return fakeCanGoBack;
    }

    Future<void> goBack() async {
      isGoBackCalled = true;
    }

    setUpAll(() {
      when(() => mockNavigatorContext.maybePop()).thenReturn(true);
    });

    setUp(() {
      isCanGoBackCalled = false;
      isGoBackCalled = false;
    });

    test('pushNamed pushes the correct route', () {
      DOPNativeSubIntroductionScreen.pushNamed(url: fakeUrl, showFooter: false);

      expect(
        verify(() => mockNavigatorContext.pushNamed(
              screenName,
              extra: captureAny(named: 'extra'),
            )).captured,
        <dynamic>[
          isA<DOPNativeSubIntroductionScreenArg>()
              .having(
                (DOPNativeSubIntroductionScreenArg p0) => p0.url,
                'verify url',
                fakeUrl,
              )
              .having(
                (DOPNativeSubIntroductionScreenArg p0) => p0.showFooter,
                'verify showFooter',
                false,
              ),
        ],
      );
    });

    testWidgets('[Android only] If there is a previous URL, go back to that URL',
        (WidgetTester tester) async {
      await tester.runAsync(() async {
        await tester.pumpWidget(const MaterialApp(
          home: DOPNativeSubIntroductionScreen(url: fakeUrl),
        ));

        await tester.pumpAndSettle();

        final CommonWebView webView = verifyWebView(tester);

        // verify webView controller
        expect(webView.arg.controller, isNotNull);
        final CommonWebViewController controller = webView.arg.controller!;
        // set up webView controller
        controller.canGoBack = canGoBack;
        controller.goBack = goBack;

        // fake press back button
        final dynamic widgetsAppState = tester.state(find.byType(WidgetsApp));
        await widgetsAppState.didPopRoute();
        await tester.pump();
        expect(isCanGoBackCalled, true);
        expect(isGoBackCalled, true);
        verifyNever(() => mockNavigatorContext.maybePop());
      });
    });

    testWidgets('[Android only] If there is not a previous URL, go back to previous screen',
        (WidgetTester tester) async {
      await tester.runAsync(() async {
        await tester.pumpWidget(const MaterialApp(
          home: DOPNativeSubIntroductionScreen(url: fakeUrl),
        ));

        await tester.pumpAndSettle();

        final CommonWebView webView = verifyWebView(tester);

        // verify webView controller
        expect(webView.arg.controller, isNotNull);
        final CommonWebViewController controller = webView.arg.controller!;
        // set up webView controller
        controller.canGoBack = canGoBack;
        controller.goBack = goBack;
        fakeCanGoBack = false;

        // fake press back button
        final dynamic widgetsAppState = tester.state(find.byType(WidgetsApp));
        await widgetsAppState.didPopRoute();
        await tester.pump();
        expect(isCanGoBackCalled, true);
        expect(isGoBackCalled, false);
        verify(() => mockNavigatorContext.maybePop()).called(1);
      });
    });
  });

  group('Verify screen UI', () {
    testWidgets('Give default showFooter, show show footer widget', (WidgetTester tester) async {
      await tester.runAsync(() async {
        await tester.pumpWidget(const MaterialApp(
          home: DOPNativeSubIntroductionScreen(url: fakeUrl),
        ));

        await tester.pumpAndSettle();

        verifyWebView(tester);
        verifyFooter(tester);
      });
    });

    testWidgets('Give showFooter = false, show NOT show footer widget',
        (WidgetTester tester) async {
      await tester.runAsync(() async {
        await tester.pumpWidget(const MaterialApp(
          home: DOPNativeSubIntroductionScreen(
            url: fakeUrl,
            showFooter: false,
          ),
        ));

        await tester.pumpAndSettle();

        verifyWebView(tester);
        expect(find.byType(DOPNativeSubIntroductionFooter), findsNothing);
      });
    });
  });
}
