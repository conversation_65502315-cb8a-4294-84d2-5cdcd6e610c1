import 'package:evoapp/feature/dop_native/features/cif_confirm/mock_file/mock_dop_native_application_form_data_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('getMockDOPNativeApplicationFormData', () {
    expect(
      getMockDOPNativeApplicationFormData(MockDOPNativeApplicationFormData.successWithUseNewCif),
      'dop_native_application_form_data_with_use_new_cif.json',
    );

    expect(
      getMockDOPNativeApplicationFormData(MockDOPNativeApplicationFormData.successWithoutUseNewCif),
      'dop_native_application_form_data_without_use_new_cif.json',
    );

    expect(
      getMockDOPNativeApplicationFormData(MockDOPNativeApplicationFormData.error),
      'dop_native_application_form_data_error.json',
    );

    expect(
      getMockDOPNativeApplicationFormData(MockDOPNativeApplicationFormData.getPaymentTypeSuccess),
      'dop_native_get_payment_type_success.json',
    );

    expect(
      getMockDOPNativeApplicationFormData(
          MockDOPNativeApplicationFormData.getApplicationFormDataSuccess),
      'dop_native_get_application_form_data_success.json',
    );

    expect(
      getMockDOPNativeApplicationFormData(
          MockDOPNativeApplicationFormData.getApplicationFormDataNFCInvalidData),
      'dop_native_get_application_form_data_nfc_invalid_data.json',
    );
  });
}
