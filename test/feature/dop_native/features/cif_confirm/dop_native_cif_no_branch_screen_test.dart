import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/feature/dop_native/features/cif_confirm/dop_native_cif_no_branch_screen.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_images.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/util/dop_native_navigation_utils.dart';
import 'package:evoapp/feature/dop_native/widgets/appbar/dop_native_appbar_widget.dart';
import 'package:evoapp/feature/dop_native/widgets/dop_native_status_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';
import '../../../../util/flutter_test_config.dart';
import '../../../../util/interceptor/unauthorized_interceptor_test.dart';
import '../../base/cubit/dop_native_application_state_cubit_test.dart';
import '../../base/dop_native_api_error_handler_mixin_test.dart';

void main() async {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() {
    registerFallbackValue(BoxFit.cover);
    EvoAuthenticationHelper.setInstanceForTesting(MockEvoAuthenticationHelper());
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
    getIt.registerLazySingleton<DOPNativeRepo>(() => MockDOPNativeRepo());
    getIt.registerLazySingleton<AuthenticationRepo>(() => MockAuthenticationRepo());
    getIt.registerLazySingleton<DOPNativeNavigationUtils>(() => MockDOPNativeNavigationUtils());
    getIt.registerLazySingleton<DopNativeButtonStyles>(() => DopNativeButtonStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());
    setupMockImageProvider();
    when(() => getIt<CommonNavigatorObserver>().topStackIsAPageRoute()).thenAnswer((_) {
      return true;
    });
    when(() => getIt<DOPNativeNavigationUtils>().navigateToLandingPage()).thenAnswer((_) {});

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenAnswer((_) => 10);
  });

  testWidgets('verify DOPNativeCIFNoBranchScreen CTA call back', (WidgetTester tester) async {
    await tester.pumpWidget(MaterialApp(
      home: DOPNativeCIFNoBranchScreen(),
    ));

    await tester.tap(find.byType(CommonButton));
    verify(() => getIt<DOPNativeNavigationUtils>().navigateToLandingPage()).called(1);
  });

  testWidgets('verify DOPNativeCIFNoBranchScreen UI elements', (WidgetTester tester) async {
    await tester.pumpWidget(MaterialApp(
      home: DOPNativeCIFNoBranchScreen(),
    ));
    final DOPNativeStatusWidget statusWidget =
        tester.widget<DOPNativeStatusWidget>(find.byType(DOPNativeStatusWidget));
    expect(statusWidget.title, DOPNativeStrings.cifConfirmNoBranchTitle);
    expect(statusWidget.description, DOPNativeStrings.cifConfirmNoBranchSubTitle);
    expect(statusWidget.icon, DOPNativeImages.icDOPRejectUserRegister);

    final CommonButton ctaButton = tester.widget<CommonButton>(find.byType(CommonButton));
    final Text ctaText = ctaButton.child as Text;
    expect(ctaText.data, DOPNativeStrings.cifConfirmNoBranchCTA);

    final DOPNativeAppBar appBar = tester.widget<DOPNativeAppBar>(find.byType(DOPNativeAppBar));
    expect(appBar.title, null);
    expect(appBar.enableLeading, false);
    expect(appBar.enableCloseButton, false);
  });
}
