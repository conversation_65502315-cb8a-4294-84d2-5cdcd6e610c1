import 'dart:async';

import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state_cubit.dart';
import 'package:evoapp/feature/dop_native/features/cif_confirm/cubit/dop_native_cif_confirm_cubit.dart';
import 'package:evoapp/feature/dop_native/features/cif_confirm/dop_native_cif_confirm_case.dart';
import 'package:evoapp/feature/dop_native/features/cif_confirm/dop_native_cif_confirm_screen.dart';
import 'package:evoapp/feature/dop_native/features/cif_confirm/widgets/dop_native_cif_confirm_ui_args.dart';
import 'package:evoapp/feature/dop_native/features/cif_confirm/widgets/dop_native_cif_confirm_ui_args_creator.dart';
import 'package:evoapp/feature/dop_native/features/cif_confirm/widgets/dop_native_cif_confirm_widget.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/dop_native/widgets/appbar/dop_native_appbar_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/url_launcher.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';
import '../../../../util/flutter_test_config.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {
  @override
  double getPaddingBottom(BuildContext context) => 0.0;
}

class MockDOPNativeApplicationStateCubit extends Mock implements DOPNativeApplicationStateCubit {
  @override
  Future<void> close() => Future<void>.value();
}

class MockDOPNativeCifConfirmCubit extends Mock implements DOPNativeCifConfirmCubit {
  @override
  Future<void> close() => Future<void>.value();
}

void main() {
  late MockDOPNativeRepo mockDOPNativeRepo;
  late MockDOPUtilFunctions mockDOPUtilFunctions;
  late MockDOPNativeApplicationStateCubit mockApplicationStateCubit;
  late MockDOPNativeCifConfirmCubit mockCifConfirmCubit;
  late DOPNativeCifConfirmScreen cifConfirmScreen;

  late StreamController<DOPNativeCifConfirmState> cifConfirmStreamController;
  late StreamController<DOPNativeApplicationState> applicationStateStreamController;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    initConfigEvoPageStateBase();
    setUtilsMockInstanceForTesting();

    // Register fallback values for mocktail
    registerFallbackValue(Uri.parse('https://example.com'));
    registerFallbackValue(LaunchMode.platformDefault);

    mockDOPNativeRepo = MockDOPNativeRepo();
    mockDOPUtilFunctions = MockDOPUtilFunctions();
    mockApplicationStateCubit = MockDOPNativeApplicationStateCubit();
    mockCifConfirmCubit = MockDOPNativeCifConfirmCubit();

    getIt.registerLazySingleton<AuthenticationRepo>(() => MockAuthenticationRepo());
    getIt.registerLazySingleton<DOPNativeRepo>(() => MockDOPNativeRepo());
    getIt.registerSingleton<DOPUtilFunctions>(mockDOPUtilFunctions);

    getIt.registerLazySingleton<DopNativeButtonStyles>(() => DopNativeButtonStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());

    final CommonImageProvider mockCommonImageProvider = getIt.get<CommonImageProvider>();

    when(
      () => mockCommonImageProvider.asset(
        any(),
        width: any(named: 'width'),
        height: any(named: 'height'),
        color: any(named: 'color'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'),
        cacheWidth: any(named: 'cacheWidth'),
        cacheHeight: any(named: 'cacheHeight'),
        package: any(named: 'package'),
      ),
    ).thenAnswer((_) => Container());

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenAnswer((_) => 10);

    when(() => dopUtilFunction.launchUrl(
          any(),
          mode: any(named: 'mode'),
        )).thenAnswer((_) async => true);
  });

  setUp(() {
    cifConfirmStreamController = StreamController<DOPNativeCifConfirmState>.broadcast();
    applicationStateStreamController = StreamController<DOPNativeApplicationState>.broadcast();

    when(() => mockCifConfirmCubit.stream).thenAnswer((_) => cifConfirmStreamController.stream);
    when(() => mockCifConfirmCubit.state).thenReturn(DOPNativeCifConfirmInitial());

    when(() => mockCifConfirmCubit.getCifInfo()).thenAnswer((_) => Future<void>.value());
    when(() => mockCifConfirmCubit.confirmCif(useNewCif: any(named: 'useNewCif')))
        .thenAnswer((_) => Future<void>.value());

    when(() => mockApplicationStateCubit.stream).thenAnswer(
      (_) => applicationStateStreamController.stream,
    );
    when(() => mockApplicationStateCubit.state).thenReturn(
      DOPNativeApplicationStateInitial(),
    );
    when(() => mockApplicationStateCubit.getApplicationState())
        .thenAnswer((_) => Future<void>.value());

    setUpMockConfigEvoPageStateBase();
  });

  tearDown(() {
    cifConfirmStreamController.close();
    applicationStateStreamController.close();

    reset(mockDOPNativeRepo);
    reset(mockDOPUtilFunctions);
    reset(mockCifConfirmCubit);
    reset(mockApplicationStateCubit);
    reset(mockNavigatorContext);
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  Widget buildCifConfirmScreen() {
    cifConfirmScreen = DOPNativeCifConfirmScreen(
      applicationStateCubit: mockApplicationStateCubit,
      cifConfirmCubit: mockCifConfirmCubit,
    );

    return MaterialApp(home: cifConfirmScreen);
  }

  group('DOPNativeCifConfirmScreen navigation methods', () {
    test('pushNamed should call the correct navigation method', () {
      // Call the method
      DOPNativeCifConfirmScreen.pushNamed();

      // Verify the correct navigation method was called
      verify(() => mockNavigatorContext.pushNamed(
            Screen.dopNativeCifConfirmScreen.name,
          )).called(1);
    });
  });

  group('DOPNativeCifConfirmScreen initState tests', () {
    testWidgets('should call getCifInfo in initState', (WidgetTester tester) async {
      // Arrange - Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());

      // Assert - Verify getCifInfo is called
      verify(() => mockCifConfirmCubit.getCifInfo()).called(1);
    });
  });

  group('DOPNativeCifConfirmScreen UI tests', () {
    testWidgets('should show appbar when state is DOPNativeCifConfirmLoaded with difCif case',
        (WidgetTester tester) async {
      // Arrange - Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());
      await tester.pumpAndSettle();

      // Emit the state
      cifConfirmStreamController.add(DOPNativeCifConfirmLoaded(CifCase.difCif));
      await tester.pumpAndSettle();

      // Assert - Verify the UI elements
      expect(find.byType(DOPNativeAppBar), findsOneWidget);
      expect(find.byType(DOPNativeCifConfirmWidget), findsOneWidget);
      expect(find.text(DOPNativeStrings.cifConfirmUpdateInfoAtCounterTitle), findsOneWidget);
      expect(find.text(DOPNativeStrings.cifConfirmExistingCustomerSubtitle), findsOneWidget);
      expect(find.text(DOPNativeStrings.cifConfirmDifCifNote), findsOneWidget);
    });

    testWidgets(
        'should show appbar when state is DOPNativeCifConfirmLoaded with difNationIdWithCifInfo case',
        (WidgetTester tester) async {
      // Arrange - Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());
      await tester.pumpAndSettle();

      // Emit the state
      cifConfirmStreamController.add(DOPNativeCifConfirmLoaded(CifCase.difNationIdWithCifInfo));
      await tester.pumpAndSettle();

      // Assert - Verify the UI elements
      expect(find.byType(DOPNativeAppBar), findsOneWidget);
      expect(find.byType(DOPNativeCifConfirmWidget), findsOneWidget);
      expect(find.text(DOPNativeStrings.cifConfirmUpdateInfoAtCounterTitle), findsOneWidget);
      expect(find.text(DOPNativeStrings.cifConfirmDifNationIdNote), findsOneWidget);
    });

    testWidgets(
        'should show empty widget when state is DOPNativeCifConfirmLoaded with unknown case',
        (WidgetTester tester) async {
      // Arrange - Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());
      await tester.pumpAndSettle();

      // Emit the state
      cifConfirmStreamController.add(DOPNativeCifConfirmLoaded(CifCase.unknown));
      await tester.pumpAndSettle();

      // Assert - Verify empty widget is shown
      expect(find.byType(SizedBox), findsWidgets);
      expect(find.byType(DOPNativeCifConfirmWidget), findsNothing);
    });

    testWidgets('should show empty widget when state is not DOPNativeCifConfirmLoaded',
        (WidgetTester tester) async {
      // Arrange - Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());
      await tester.pumpAndSettle();

      // Emit the state
      cifConfirmStreamController.add(DOPNativeCifConfirmInitial());
      await tester.pumpAndSettle();

      // Assert - Verify empty widget is shown
      expect(find.byType(SizedBox), findsWidgets);
      expect(find.byType(DOPNativeCifConfirmWidget), findsNothing);
    });
  });

  group('DOPNativeCifConfirmScreen action tests', () {
    testWidgets('should call confirmCif with useNewCif=true when Open Card button is pressed',
        (WidgetTester tester) async {
      // Arrange - Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());
      await tester.pumpAndSettle();

      // Emit the state
      cifConfirmStreamController.add(DOPNativeCifConfirmLoaded(CifCase.difNationIdWithCifInfo));
      await tester.pumpAndSettle();

      // Act - Find and tap the Open Card button
      final Finder openCardButtonFinder = find.text(DOPNativeStrings.cifConfirmOpenCardCTA);
      await tester.tap(openCardButtonFinder);
      await tester.pumpAndSettle();

      // Assert - Verify confirmCif is called with useNewCif=true
      verify(() => mockCifConfirmCubit.confirmCif(useNewCif: true)).called(1);
    });

    testWidgets(
        'should call confirmCif with useNewCif=false when Confirm Info Updated button is pressed',
        (WidgetTester tester) async {
      // Arrange - Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());
      await tester.pumpAndSettle();

      // Emit the state
      cifConfirmStreamController.add(DOPNativeCifConfirmLoaded(CifCase.difCif));
      await tester.pumpAndSettle();

      // Act - Find and tap the Confirm Info Updated button
      final Finder confirmInfoUpdatedButtonFinder =
          find.text(DOPNativeStrings.cifConfirmConfirmInfoUpdatedCTA);
      await tester.tap(confirmInfoUpdatedButtonFinder);
      await tester.pumpAndSettle();

      // Assert - Verify confirmCif is called with useNewCif=false
      verify(() => mockCifConfirmCubit.confirmCif(useNewCif: false)).called(1);
    });

    testWidgets('should call openNearestBranches when View Nearest Branches button is pressed',
        (WidgetTester tester) async {
      // Arrange - Setup mocks
      when(() => dopUtilFunction.launchUrl(
            any(),
            mode: any(named: 'mode'),
          )).thenAnswer((_) async => true);

      // Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());
      await tester.pumpAndSettle();

      // Emit the state
      cifConfirmStreamController.add(DOPNativeCifConfirmLoaded(CifCase.difCif));
      await tester.pumpAndSettle();

      // Act - Find and tap the View Nearest Branches button
      final Finder viewNearestBranchesButtonFinder =
          find.text(DOPNativeStrings.cifConfirmViewNearestBranchesCTA);
      await tester.tap(viewNearestBranchesButtonFinder);
      await tester.pumpAndSettle();

      // Assert - Verify launchUrl is called with the correct URL
      verify(() => dopUtilFunction.launchUrl(
            any(),
            mode: LaunchMode.externalApplication,
          )).called(1);
    });
  });

  group('DOPNativeCifConfirmScreen state handling tests', () {
    setUp(() {
      // mock navigatorContext null to avoid get status-bar height error
      when(() => globalKeyProvider.navigatorContext).thenReturn(null);
    });

    tearDown(() {
      when(() => globalKeyProvider.navigatorContext).thenReturn(mockNavigatorContext);
    });

    testWidgets('should show loading when state is DOPNativeCifConfirmLoading',
        (WidgetTester tester) async {
      // Arrange - Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());
      await tester.pumpAndSettle();

      // Act - Emit the loading state
      cifConfirmStreamController.add(DOPNativeCifConfirmLoading());
      await tester.pump();

      // Assert - Verify loading is shown
      final DOPNativeCifConfirmPageState state = tester.state(
        find.byType(DOPNativeCifConfirmScreen),
      );

      expect(state.loadingNotifier.value, isTrue);
    });

    testWidgets('should hide loading when state is not DOPNativeCifConfirmLoading',
        (WidgetTester tester) async {
      // Arrange - Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());
      await tester.pumpAndSettle();

      // Act - Emit a non-loading state
      cifConfirmStreamController.add(DOPNativeCifConfirmLoaded(CifCase.difCif));
      await tester.pump();

      // Assert - Verify loading is hidden
      final DOPNativeCifConfirmPageState state = tester.state(
        find.byType(DOPNativeCifConfirmScreen),
      );

      expect(state.loadingNotifier.value, isFalse);
    });

    testWidgets('should call getApplicationState when state is DOPNativeCifConfirmSubmitted',
        (WidgetTester tester) async {
      // Arrange - Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());
      await tester.pumpAndSettle();

      // Act - Emit the submitted state
      cifConfirmStreamController.add(DOPNativeCifConfirmSubmitted());
      await tester.pump();

      // Assert - Verify getApplicationState is called
      verify(() => mockApplicationStateCubit.getApplicationState()).called(1);
    });

    testWidgets('should call hideHudLoading when state is DOPNativeCifConfirmError',
        (WidgetTester tester) async {
      // Arrange - Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());
      await tester.pumpAndSettle();

      // Act - Emit the error state
      final ErrorUIModel errorModel = ErrorUIModel(verdict: 'Test error');
      cifConfirmStreamController.add(DOPNativeCifConfirmError(errorModel));
      await tester.pump();

      // Assert - Verify error handling by checking that hideHudLoading is called
      final DOPNativeCifConfirmPageState state = tester.state(
        find.byType(DOPNativeCifConfirmScreen),
      );

      expect(state.loadingNotifier.value, isFalse);
    });

    testWidgets(
        'should call hideHudLoading when state is DOPNativeCifConfirmLoaded with unknown cifCase',
        (WidgetTester tester) async {
      // Arrange - Build the widget
      await tester.pumpWidget(buildCifConfirmScreen());
      await tester.pumpAndSettle();

      // Act - Emit the loaded state with unknown cifCase
      cifConfirmStreamController.add(DOPNativeCifConfirmLoaded(CifCase.unknown));
      await tester.pump();

      // Assert - Verify error handling by checking that hideHudLoading is called
      final DOPNativeCifConfirmPageState state = tester.state(
        find.byType(DOPNativeCifConfirmScreen),
      );

      expect(state.loadingNotifier.value, isFalse);
    });
  });

  group('DOPNativeCifConfirmScreen helper methods tests', () {
    test('DOPNativeCifConfirmUIArgsCreator should return correct UI args for difCif case', () {
      // Create a UI args creator instance
      final DOPNativeCifConfirmUIArgsCreator argsCreator = DOPNativeCifConfirmUIArgsCreator();

      // Call the method
      final DOPNativeCifConfirmUIArgs? args = argsCreator.create(CifCase.difCif);

      // Verify the UI args
      expect(args, isNotNull);
      expect(args!.title, DOPNativeStrings.cifConfirmUpdateInfoAtCounterTitle);
      expect(args.subtitle, DOPNativeStrings.cifConfirmExistingCustomerSubtitle);
      expect(args.notes, contains(DOPNativeStrings.cifConfirmDifCifNote));
      expect(args.showOpenCardCTA, false);
      expect(args.showConfirmInfoUpdated, true);
      expect(args.showViewNearestBranches, true);
    });

    test(
        'DOPNativeCifConfirmUIArgsCreator should return correct UI args for difNationIdWithCifInfo case',
        () {
      // Create a UI args creator instance
      final DOPNativeCifConfirmUIArgsCreator argsCreator = DOPNativeCifConfirmUIArgsCreator();

      // Call the method
      final DOPNativeCifConfirmUIArgs? args = argsCreator.create(CifCase.difNationIdWithCifInfo);

      // Verify the UI args
      expect(args, isNotNull);
      expect(args!.title, DOPNativeStrings.cifConfirmUpdateInfoAtCounterTitle);
      expect(args.subtitle, isNull);
      expect(args.notes, contains(DOPNativeStrings.cifConfirmDifNationIdNote));
      expect(args.showOpenCardCTA, true);
      expect(args.showConfirmInfoUpdated, true);
      expect(args.showViewNearestBranches, true);
    });

    test('DOPNativeCifConfirmUIArgsCreator should return null for unknown case', () {
      // Create a UI args creator instance
      final DOPNativeCifConfirmUIArgsCreator argsCreator = DOPNativeCifConfirmUIArgsCreator();

      // Call the method
      final DOPNativeCifConfirmUIArgs? args = argsCreator.create(CifCase.unknown);

      // Verify the UI args
      expect(args, isNull);
    });
  });
}
