import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/feature/dop_native/features/card_activation_status/card_activated_retry_pos_limit/dop_native_card_activated_retry_pos_limit_screen.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_images.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_resources.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/dop_native/widgets/appbar/dop_native_appbar_widget.dart';
import 'package:evoapp/feature/dop_native/widgets/dop_native_card_acquisition_related_info_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../base/evo_page_state_base_test_config.dart';
import '../../../../../util/flutter_test_config.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

void main() {
  const double fakeBottomPadding = 29.9;

  late Widget dopNativeCardActivatedRetryPosLimitScreen;
  late CommonImageProvider mockCommonImageProvider;
  late DOPUtilFunctions mockDOPUtilFunctions;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    initConfigEvoPageStateBase();

    getIt.registerLazySingleton<DopNativeButtonStyles>(() => DopNativeButtonStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());
    getIt.registerLazySingleton<DOPNativeRepo>(() => MockDOPNativeRepo());
    getIt.registerLazySingleton<DOPUtilFunctions>(() => MockDOPUtilFunctions());
    getIt.registerLazySingleton<AuthenticationRepo>(() => MockAuthenticationRepo());
    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => MockEvoLocalStorageHelper());
    setUtilsMockInstanceForTesting();
    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    mockDOPUtilFunctions = getIt.get<DOPUtilFunctions>();

    when(
      () => mockCommonImageProvider.asset(
        DOPNativeImages.icCardVerify,
        height: 56,
      ),
    ).thenReturn(
      const SizedBox(
        height: 56,
      ),
    );

    when(
      () => mockCommonImageProvider.asset(
        DOPNativeImages.icClock,
        width: 17,
        height: 17,
      ),
    ).thenReturn(
      const SizedBox(
        width: 17,
        height: 17,
      ),
    );

    when(
      () => mockCommonImageProvider.asset(
        DOPNativeImages.icRejectWithdrawMoneyInvitation,
        height: 20,
        width: 20,
      ),
    ).thenReturn(
      const SizedBox(
        height: 20,
        width: 20,
      ),
    );

    when(
      () => mockCommonImageProvider.asset(
        DOPNativeImages.icProtectCVV,
        height: 20,
        width: 20,
      ),
    ).thenReturn(
      const SizedBox(
        height: 20,
        width: 20,
      ),
    );

    when(() => mockDOPUtilFunctions.getPaddingBottom(any())).thenAnswer((_) => fakeBottomPadding);
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    dopNativeCardActivatedRetryPosLimitScreen = const MaterialApp(
      home: DOPNativeCardActivatedRetryPosLimitScreen(),
    );
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  group('DOPNativeCardActivatedScreen navigation tests', () {
    final String screenName = Screen.dopNativeCardActivatedRetryPosLimitScreen.name;

    test('pushNamed pushes the correct route', () {
      DOPNativeCardActivatedRetryPosLimitScreen.pushNamed();
      verify(() => mockNavigatorContext.pushNamed(screenName)).called(1);
    });

    test('pushReplacementNamed replaces with the correct route', () {
      DOPNativeCardActivatedRetryPosLimitScreen.pushReplacementNamed();
      verify(() => mockNavigatorContext.pushReplacementNamed(screenName)).called(1);
    });
  });

  testWidgets('verify init widget', (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedRetryPosLimitScreen);
    });

    final State state = tester.state(find.byType(DOPNativeCardActivatedRetryPosLimitScreen));
    expect(state is DOPNativeCardActivatedRetryPosLimitScreenState, true);

    // Verify the appbar is present
    expect(find.byType(DOPNativeAppBar), findsOneWidget);

    // Verify the background color
    final Scaffold scaffold = tester.widget(find.byType(Scaffold));
    expect(scaffold.backgroundColor, dopNativeColors.screenBackground);

    // Verify the DOPNativeCardStatusWidget
    // - title
    expect(find.text(DOPNativeStrings.dopNativeCardActivatedRetryPosLimitTitle), findsOneWidget);
    // - description
    expect(find.text(DOPNativeStrings.dopNativeCardActivatedRetryPosLimitDesc), findsOneWidget);
    // - banner title
    expect(
      find.text(DOPNativeStrings.dopNativeCardActivatedRetryPosLimitBannerTitle),
      findsOneWidget,
    );

    // Verify the DOPNativeCardAcquisitionRelatedInfoWidget
    expect(find.byType(DOPNativeCardAcquisitionRelatedInfoWidget), findsOneWidget);

    // Verify the padding at the bottom
    expect(
      find.byWidgetPredicate(
          (Widget widget) => widget is SizedBox && widget.height == fakeBottomPadding),
      findsOneWidget,
    );
  });
}
