import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/data/response/sign_in_otp_entity.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state.dart';
import 'package:evoapp/feature/dop_native/features/card_activation_status/card_activated/dop_native_card_activated_screen.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_images.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_resources.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/dop_native/util/dop_native_navigation_utils.dart';
import 'package:evoapp/feature/dop_native/util/dop_native_submit_status_polling/dop_native_submit_status_polling.dart';
import 'package:evoapp/feature/dop_native/widgets/appbar/dop_native_appbar_widget.dart';
import 'package:evoapp/feature/dop_native/widgets/dop_native_card_acquisition_related_info_widget.dart';
import 'package:evoapp/feature/dop_native/widgets/dop_native_status_card_icon_widget.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/feature/in_app_review/in_app_review_wrapper.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../base/evo_page_state_base_test_config.dart';
import '../../../../../util/flutter_test_config.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {
  @override
  Future<SignInOtpEntity> loginFromDOE({
    TypeLogin? type,
    String? pin,
    String? dopAccessToken,
    String? sessionToken,
    MockConfig? mockConfig,
  }) async {
    return SignInOtpEntity();
  }
}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockInAppReviewWrapper extends Mock implements InAppReviewWrapper {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockDOPNativeSubmitStatusPolling extends Mock implements DOPNativeSubmitStatusPolling {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

class MockDOPNativeNavigationUtils extends Mock implements DOPNativeNavigationUtils {}

class MockBuildContext extends Mock implements BuildContext {}

class TestDOPNativeCardActivatedScreen extends DOPNativeCardActivatedScreen {
  final DOPNativeSubmitStatusPolling dopNativeSubmitStatusPolling;
  final DOPNativeSubmitStatusPolling dopNativeDelayToCallAppState;

  const TestDOPNativeCardActivatedScreen({
    required this.dopNativeSubmitStatusPolling,
    required this.dopNativeDelayToCallAppState,
    super.key,
  });

  @override
  TestDOPNativeCardActivatedScreenState createState() =>
      // ignore: no_logic_in_create_state
      TestDOPNativeCardActivatedScreenState(
        dopNativeSubmitStatusPolling: dopNativeSubmitStatusPolling,
        dopNativeDelayToCallAppState: dopNativeDelayToCallAppState,
      );
}

class TestDOPNativeCardActivatedScreenState extends DOPNativeCardActivatedScreenState {
  @override
  // ignore: overridden_fields
  final DOPNativeSubmitStatusPolling dopNativeSubmitStatusPolling;
  @override
  // ignore: overridden_fields
  final DOPNativeSubmitStatusPolling dopNativeDelayToCallAppState;

  TestDOPNativeCardActivatedScreenState({
    required this.dopNativeSubmitStatusPolling,
    required this.dopNativeDelayToCallAppState,
  });
}

void main() {
  late Widget dopNativeCardActivatedScreen;
  late CommonImageProvider mockCommonImageProvider;
  const double expectPercentHeightOfImage = 333 / 812;
  late DOPNativeRepo mockDOPNativeRepo;
  late FeatureToggle mockFeatureToggle;
  late InAppReviewWrapper mockInAppReviewWrapper;
  late EvoLocalStorageHelper mockEvoLocalStorageHelper;
  late DOPUtilFunctions mockDOPUtilFunctions;
  late DOPNativeNavigationUtils mockDOPNativeNavigationUtils;
  late DOPNativeSubmitStatusPolling mockDOPNativeSubmitStatusPolling;
  late DOPNativeSubmitStatusPolling mockDOPNativeDelayToCallAppState;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    setUtilsMockInstanceForTesting();
    initConfigEvoPageStateBase();

    getIt.registerLazySingleton<DopNativeButtonStyles>(() => DopNativeButtonStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());
    getIt.registerLazySingleton<DOPNativeRepo>(() => MockDOPNativeRepo());
    getIt.registerLazySingleton<DOPUtilFunctions>(() => MockDOPUtilFunctions());
    getIt.registerLazySingleton<AuthenticationRepo>(() => MockAuthenticationRepo());
    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => MockEvoLocalStorageHelper());
    getIt.registerLazySingleton<DOPNativeNavigationUtils>(() => MockDOPNativeNavigationUtils());

    mockDOPNativeRepo = getIt.get<DOPNativeRepo>();
    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    mockDOPUtilFunctions = getIt.get<DOPUtilFunctions>();
    mockDOPNativeNavigationUtils = getIt.get<DOPNativeNavigationUtils>();
    mockDOPNativeSubmitStatusPolling = MockDOPNativeSubmitStatusPolling();
    mockDOPNativeDelayToCallAppState = MockDOPNativeSubmitStatusPolling();

    getIt.registerLazySingleton<InAppReviewWrapper>(() => MockInAppReviewWrapper());
    mockInAppReviewWrapper = getIt.get<InAppReviewWrapper>();

    getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());
    mockFeatureToggle = getIt.get<FeatureToggle>();

    mockEvoLocalStorageHelper = getIt.get<EvoLocalStorageHelper>();

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(expectPercentHeightOfImage);

    when(() => mockCommonImageProvider.asset(
          DOPNativeImages.imgEvoCards,
          height: any(named: 'height'),
          fit: BoxFit.fitHeight,
        )).thenReturn(const SizedBox(height: expectPercentHeightOfImage));

    when(() => mockCommonImageProvider.asset(
          DOPNativeImages.icRejectWithdrawMoneyInvitation,
          height: 20,
          width: 20,
        )).thenReturn(const SizedBox(
      height: 20,
      width: 20,
    ));

    when(() => mockCommonImageProvider.asset(
          DOPNativeImages.icProtectCVV,
          height: 20,
          width: 20,
        )).thenReturn(const SizedBox(
      height: 20,
      width: 20,
    ));

    when(() => mockDOPNativeRepo.getApplicationState(
          token: any(named: 'token'),
          flowSelectedAt: any(named: 'flowSelectedAt'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer(
      (_) async => DOPNativeApplicationStateEntity(),
    );

    when(() => mockFeatureToggle.enableRequestReviewRatingFeature).thenReturn(false);
    when(() => mockEvoLocalStorageHelper.getValueToCheckReviewPopupShown())
        .thenAnswer((_) async => false);
    when(() => mockEvoLocalStorageHelper.setValueToCheckReviewPopupShown(any()))
        .thenAnswer((_) async => true);

    when(() => mockInAppReviewWrapper.isAvailable()).thenAnswer((_) async => true);
    when(() => mockInAppReviewWrapper.requestReview()).thenAnswer((_) async => {});
    when(() => mockInAppReviewWrapper.openStoreListing(appStoreId: any(named: 'appStoreId')))
        .thenAnswer((_) async => {});

    when(() => mockDOPUtilFunctions.getPaddingBottom(any())).thenReturn(20.0);
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    when(() => mockDOPNativeNavigationUtils.navigateToLandingPage()).thenAnswer((_) async => {});

    dopNativeCardActivatedScreen = MaterialApp(
      home: TestDOPNativeCardActivatedScreen(
        dopNativeSubmitStatusPolling: mockDOPNativeSubmitStatusPolling,
        dopNativeDelayToCallAppState: mockDOPNativeDelayToCallAppState,
      ),
    );

    when(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
          onDoPolling: any(named: 'onDoPolling'),
        )).thenReturn(null);
    when(() => mockDOPNativeSubmitStatusPolling.cancel()).thenReturn(null);
  });

  tearDown(() {
    reset(mockDOPNativeSubmitStatusPolling);
    reset(mockDOPNativeDelayToCallAppState);
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  group('DOPNativeCardActivatedScreen navigation tests', () {
    final String screenName = Screen.dopNativeCardActivatedScreen.name;

    test('pushNamed pushes the correct route', () {
      DOPNativeCardActivatedScreen.pushNamed();

      verify(() => mockNavigatorContext.pushNamed(screenName)).called(1);
    });

    test('pushReplacementNamed replaces with the correct route', () {
      DOPNativeCardActivatedScreen.pushReplacementNamed();

      verify(() => mockNavigatorContext.pushReplacementNamed(screenName)).called(1);
    });
  });

  testWidgets('verify init widget', (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    final State state = tester.state(find.byType(TestDOPNativeCardActivatedScreen));

    expect(state is DOPNativeCardActivatedScreenState, true);

    // verify Scaffold
    final Finder scaffoldFinder = find.byType(Scaffold);
    expect(scaffoldFinder, findsOneWidget);
    final Scaffold scaffold = tester.widget(scaffoldFinder);
    expect(scaffold.backgroundColor, dopNativeColors.screenBackground);

    // verify AppBar
    final Finder appBarFinder = find.byType(DOPNativeAppBar);
    expect(appBarFinder, findsOneWidget);

    // verify has SingleChildScrollView Widget
    final Finder singleChildScrollViewFinder = find.byType(SingleChildScrollView);
    expect(singleChildScrollViewFinder, findsOneWidget);

    // verify has padding Widget
    final SingleChildScrollView scrollViewWidget = tester.widget(singleChildScrollViewFinder);
    expect(scrollViewWidget.child, isA<Padding>());

    final Padding paddingWidget = scrollViewWidget.child as Padding;
    expect(paddingWidget.padding, const EdgeInsets.symmetric(horizontal: 20));
    expect(paddingWidget.child, isA<Column>());

    final Column colWidget = paddingWidget.child as Column;
    expect(colWidget.children, isNotEmpty);
    final List<Widget> colChildren = colWidget.children;
    expect(colChildren[0], isA<DOPNativeStatusCardIconWidget>());
    expect(colChildren[1], isA<SizedBox>());
    expect(colChildren[2], isA<DOPNativeCardAcquisitionRelatedInfoWidget>());

    // Verify title and description
    expect(find.text(DOPNativeStrings.dopNativeCardActivatedSuccessTitle), findsOneWidget);
    expect(find.text(DOPNativeStrings.dopNativeCardActivatedSuccessDesc), findsOneWidget);

    // Verify CTA button
    final Finder buttonFinder = find.byType(CommonButton);
    expect(buttonFinder, findsOneWidget);
    expect(find.text(DOPNativeStrings.dopNativeReceiveVoucher), findsOneWidget);
  });

  testWidgets('verify DOPNativeCardAcquisitionRelatedInfoWidget is displayed',
      (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    // Verify the DOPNativeCardAcquisitionRelatedInfoWidget is displayed
    expect(find.byType(DOPNativeCardAcquisitionRelatedInfoWidget), findsOneWidget);

    // Verify the enableViewEContractCTA parameter is false
    final DOPNativeCardAcquisitionRelatedInfoWidget widget = tester.widget(
      find.byType(DOPNativeCardAcquisitionRelatedInfoWidget),
    );
    expect(widget.enableViewEContractCTA, isFalse);
  });

  testWidgets('verify button has correct text', (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    // Find the CTA button and verify the text
    expect(find.widgetWithText(CommonButton, DOPNativeStrings.dopNativeReceiveVoucher),
        findsOneWidget);
  });

  testWidgets(
      'verify DOPNativeStatusCardIconWidget is displayed with correct title and description',
      (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    expect(find.byType(DOPNativeStatusCardIconWidget), findsOneWidget);

    expect(find.text(DOPNativeStrings.dopNativeCardActivatedSuccessTitle), findsOneWidget);
    expect(find.text(DOPNativeStrings.dopNativeCardActivatedSuccessDesc), findsOneWidget);
  });

  testWidgets('verify button is displayed', (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    // Find the CommonButton
    final Finder buttonFinder = find.byType(CommonButton);
    expect(buttonFinder, findsOneWidget);
  });

  testWidgets('verify required widgets are displayed', (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    // Verify specific widgets are displayed
    expect(find.byType(DOPNativeStatusCardIconWidget), findsOneWidget);
    expect(find.byType(DOPNativeCardAcquisitionRelatedInfoWidget), findsOneWidget);
    expect(find.byType(SingleChildScrollView), findsOneWidget);
  });

  testWidgets('verify AppBar is displayed', (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    // Verify the AppBar is displayed
    expect(find.byType(DOPNativeAppBar), findsOneWidget);

    // Verify the AppBar has the correct properties
    final DOPNativeAppBar appBar = tester.widget(find.byType(DOPNativeAppBar));
    expect(appBar.onExitDOP, isNotNull);
  });

  testWidgets('verify buildAppBar returns DOPNativeAppBar with correct properties',
      (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    // Verify the AppBar is displayed
    expect(find.byType(DOPNativeAppBar), findsOneWidget);

    // Verify the AppBar has the correct properties
    final DOPNativeAppBar appBar = tester.widget(find.byType(DOPNativeAppBar));
    expect(appBar.onExitDOP, isNotNull);
  });

  testWidgets(
      'verify buildStatusCardIconWidget returns DOPNativeStatusCardIconWidget with correct properties',
      (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    // Verify the DOPNativeStatusCardIconWidget is displayed
    expect(find.byType(DOPNativeStatusCardIconWidget), findsOneWidget);

    // Verify the widget has the correct properties
    final DOPNativeStatusCardIconWidget widget =
        tester.widget(find.byType(DOPNativeStatusCardIconWidget));
    expect(widget.title, DOPNativeStrings.dopNativeCardActivatedSuccessTitle);
    expect(widget.description, DOPNativeStrings.dopNativeCardActivatedSuccessDesc);
  });

  testWidgets(
      'verify buildCardAcquisitionRelatedInfoWidget returns DOPNativeCardAcquisitionRelatedInfoWidget with correct properties',
      (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    // Verify the DOPNativeCardAcquisitionRelatedInfoWidget is displayed
    expect(find.byType(DOPNativeCardAcquisitionRelatedInfoWidget), findsOneWidget);

    // Verify the widget has the correct properties
    final DOPNativeCardAcquisitionRelatedInfoWidget widget = tester.widget(
      find.byType(DOPNativeCardAcquisitionRelatedInfoWidget),
    );
    expect(widget.enableViewEContractCTA, isFalse);
  });

  testWidgets('verify handleDOPNativeApplicationStateChanged with DOPNativeApplicationStateLoading',
      (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    final State state = tester.state(find.byType(TestDOPNativeCardActivatedScreen));
    expect(state is DOPNativeCardActivatedScreenState, true);

    final DOPNativeCardActivatedScreenState screenState =
        state as DOPNativeCardActivatedScreenState;

    // Test with DOPNativeApplicationStateLoading
    screenState.handleDOPNativeApplicationStateChanged(DOPNativeApplicationStateLoading());

    // Verify cancel was not called
    verifyNever(() => mockDOPNativeSubmitStatusPolling.cancel());
  });

  testWidgets('verify handleDOPNativeApplicationStateChanged with cardStatusInformation step',
      (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    final State state = tester.state(find.byType(TestDOPNativeCardActivatedScreen));
    expect(state is DOPNativeCardActivatedScreenState, true);

    final DOPNativeCardActivatedScreenState screenState =
        state as DOPNativeCardActivatedScreenState;

    // Test with DOPNativeApplicationStateLoaded with cardStatusInformation step
    final DOPNativeApplicationStateEntity entity = DOPNativeApplicationStateEntity(
      currentStep: DOPNativeNavigationStep.cardStatusInformation.value,
    );

    screenState.handleDOPNativeApplicationStateChanged(DOPNativeApplicationStateLoaded(entity));

    // Verify delayToPolling was called
    verify(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
          onDoPolling: any(named: 'onDoPolling'),
        )).called(1);
  });

  testWidgets('verify didPopNext', (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    final State state = tester.state(find.byType(TestDOPNativeCardActivatedScreen));
    expect(state is DOPNativeCardActivatedScreenState, true);

    final DOPNativeCardActivatedScreenState screenState =
        state as DOPNativeCardActivatedScreenState;

    expect(screenState.isRequestingShowPopup, false);

    screenState.didPopNext();

    expect(screenState.isRequestingShowPopup, true);
  });

  testWidgets('verify didPop', (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    final State state = tester.state(find.byType(TestDOPNativeCardActivatedScreen));
    expect(state is DOPNativeCardActivatedScreenState, true);

    final DOPNativeCardActivatedScreenState screenState =
        state as DOPNativeCardActivatedScreenState;

    screenState.didPop();

    // Verify cancel was called on both polling instances
    verify(() => mockDOPNativeSubmitStatusPolling.cancel()).called(1);
    verify(() => mockDOPNativeDelayToCallAppState.cancel()).called(1);
  });

  testWidgets('verify dispose cancels polling', (WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(dopNativeCardActivatedScreen);
    });

    final State state = tester.state(find.byType(TestDOPNativeCardActivatedScreen));
    expect(state is DOPNativeCardActivatedScreenState, true);

    final DOPNativeCardActivatedScreenState screenState =
        state as DOPNativeCardActivatedScreenState;

    // Reset the mock to clear any previous calls
    reset(mockDOPNativeSubmitStatusPolling);

    // Dispose the widget
    await tester.pumpWidget(Container());

    // Verify cancel was called on both polling instances
    verify(() => mockDOPNativeSubmitStatusPolling.cancel()).called(1);
    verify(() => mockDOPNativeDelayToCallAppState.cancel()).called(1);

    expect(screenState.isRequestingShowPopup, true);
  });
}
