import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/feature/dop_native/features/inform_success/base/cubit/dop_native_inform_success_cubit.dart';
import 'package:evoapp/feature/dop_native/features/inform_success/base/cubit/dop_native_inform_success_state.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../constant.dart';
import '../../../../../../util/test_util.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

void main() {
  late DOPNativeInformSuccessCubit cubit;

  final DOPNativeRepo dopNativeRepo = MockDOPNativeRepo();

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    cubit = DOPNativeInformSuccessCubit(dopNativeRepo);
  });

  test('Default state', () {
    expect(cubit.state, isA<DOPNativeInformSuccessInitial>());
  });

  group('Test getApplicationNextState', () {
    const String filename = 'dop_native_application_next_state_success.json';
    setUp(() => when(() => dopNativeRepo.getApplicationNextState(
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => BaseEntity.fromBaseResponse(
          BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(filename),
          ),
        )));

    blocTest<DOPNativeInformSuccessCubit, DOPNativeInformSuccessState>(
      'Request application next state success',
      build: () => cubit,
      act: (DOPNativeInformSuccessCubit cubit) => cubit.getApplicationNextState(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeApplicationNextStateLoading>(),
        isA<DOPNativeApplicationNextStateLoaded>(),
      ],
      verify: (_) {
        verify(() => dopNativeRepo.getApplicationNextState(
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<DOPNativeInformSuccessCubit, DOPNativeInformSuccessState>(
      'Request application next state fail',
      build: () => cubit,
      setUp: () {
        when(() => dopNativeRepo.getApplicationNextState(
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => BaseEntity.fromBaseResponse(
              BaseResponse(
                statusCode: CommonHttpClient.BAD_REQUEST,
                response: null,
              ),
            ));
      },
      act: (DOPNativeInformSuccessCubit cubit) => cubit.getApplicationNextState(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeApplicationNextStateLoading>(),
        isA<DOPNativeApplicationNextStateError>(),
      ],
      verify: (_) {
        verify(() => dopNativeRepo.getApplicationNextState(
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );
  });
}
