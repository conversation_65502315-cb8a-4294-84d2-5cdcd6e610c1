import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_upload_face_id_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_verify_face_id_entity.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/face_otp/cubit/dop_native_face_otp_cubit.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/ekyc_ui_only_bridge.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/liveness_mode.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/tv_image_wrapper.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/ui_model/ekyc_error_ui_model.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/ekyc/model/ekyc_result_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../constant.dart';
import '../../../../../../util/test_util.dart';

class MockEkycUiOnlyBridge extends Mock implements EkycUiOnlyBridge {}

class MockDopNativeEkycUIOnlyRepo extends Mock implements DopNativeEkycUIOnlyRepo {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

void main() {
  late AppState mockAppState;
  late DopNativeEkycUIOnlyRepo mockNativeEkycUIOnlyRepo;
  late EkycUiOnlyBridge mockEkycUiOnlyBridge;
  late DOPUtilFunctions mockDOPUtilFunctions;
  late DopNativeFaceOtpCubit cubit;

  const String fakeBase64Image = 'fakeBase64Image';
  const String fakeSignature = 'fakeSignature';
  const String fakeUniqueToken = 'fakeUniqueToken';
  const String fakeAccessToken = 'fake_access_token';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(LivenessModeWrapper.passive);

    getIt.registerSingleton<AppState>(AppState());
    mockAppState = getIt.get<AppState>();

    getIt.registerSingleton<DopNativeEkycUIOnlyRepo>(MockDopNativeEkycUIOnlyRepo());
    mockNativeEkycUIOnlyRepo = getIt<DopNativeEkycUIOnlyRepo>();

    getIt.registerSingleton<EkycUiOnlyBridge>(MockEkycUiOnlyBridge());
    mockEkycUiOnlyBridge = getIt<EkycUiOnlyBridge>();

    getIt.registerSingleton<DOPUtilFunctions>(MockDOPUtilFunctions());
    mockDOPUtilFunctions = getIt<DOPUtilFunctions>();
  });

  setUp(() {
    cubit = DopNativeFaceOtpCubit(
      ekycUiOnlyBridge: mockEkycUiOnlyBridge,
      ekycUiOnlyRepo: mockNativeEkycUIOnlyRepo,
    );
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('init cubit', () {
    expect(cubit.state, isA<DopNativeFaceOtpInitial>());
    expect(cubit.labelFaceId, 'face_id');
    expect(cubit.isInitialEKYCSDK, false);
  });

  group('verify initEkyc', () {
    setUp(() {
      when(() => mockEkycUiOnlyBridge.initEkyc(
            jsonConfigurationByServer: any(named: 'jsonConfigurationByServer'),
          )).thenAnswer((_) async => TVSDKResult.succeed());
    });

    tearDown(() {
      reset(mockEkycUiOnlyBridge);
    });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>('start eKYC sdk is success',
        build: () => cubit,
        act: (_) => cubit.initEkyc(),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpLoading>(),
              isA<DopNativeFaceOtpInitSDKSuccess>(),
            ],
        verify: (_) {
          verify(() => mockEkycUiOnlyBridge.initEkyc(
                jsonConfigurationByServer: '{}',
              )).called(1);

          expect(cubit.isInitialEKYCSDK, true);
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>('start eKYC sdk is failure',
        build: () => cubit,
        setUp: () {
          when(() => mockEkycUiOnlyBridge.initEkyc(
                jsonConfigurationByServer: any(named: 'jsonConfigurationByServer'),
              )).thenAnswer((_) async => TVSDKResult.failed(reason: TVSDKFailReason.userCancelled));
        },
        act: (_) => cubit.initEkyc(),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpLoading>(),
              isA<DopNativeFaceOtpInitSDKFailure>(),
            ],
        verify: (_) {
          verify(() => mockEkycUiOnlyBridge.initEkyc(
                jsonConfigurationByServer: '{}',
              )).called(1);

          expect(cubit.isInitialEKYCSDK, false);
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>('eKYC was initiated earlier',
        build: () => cubit,
        setUp: () {
          cubit.isInitialEKYCSDK = true;
        },
        act: (_) => cubit.initEkyc(),
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpInitSDKSuccess>(),
            ],
        verify: (_) {
          verifyNever(() => mockEkycUiOnlyBridge.initEkyc(jsonConfigurationByServer: '{}'));

          expect(cubit.isInitialEKYCSDK, true);
        });
  });

  group('verify startCaptureSelfie()', () {
    setUp(() {
      when(() => mockEkycUiOnlyBridge.startSelfieCapturing(
            livenessMode: any(named: 'livenessMode'),
            skipConfirmScreen: any(named: 'skipConfirmScreen'),
            onNewFrameBatchListener: any(named: 'onNewFrameBatchListener'),
          )).thenAnswer((_) async => TVSDKResult.succeed());

      when(() => mockEkycUiOnlyBridge.getSelfieImages())
          .thenReturn(<TVImageWrapper>[const TVImageWrapper(rawImageBase64: fakeBase64Image)]);
    });

    tearDown(() {
      reset(mockEkycUiOnlyBridge);
    });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>(
        'emits DopNativeFaceOtpCaptureSuccess when selfie is successful',
        build: () => cubit,
        act: (_) => cubit.startCaptureSelfie(),
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpCaptureSuccess>().having(
                (DopNativeFaceOtpCaptureSuccess state) => state.selfieImage.rawImageBase64,
                'verify image',
                fakeBase64Image,
              ),
            ],
        verify: (_) {
          verify(() => mockEkycUiOnlyBridge.startSelfieCapturing(
                livenessMode: LivenessModeWrapper.passive,
                skipConfirmScreen: true,
                onNewFrameBatchListener: any(named: 'onNewFrameBatchListener'),
              )).called(1);

          verify(() => mockEkycUiOnlyBridge.getSelfieImages()).called(1);
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>(
        'emits DopNativeFaceOtpCaptureSuccess when selfie is successful but images is empty',
        build: () => cubit,
        setUp: () {
          when(() => mockEkycUiOnlyBridge.getSelfieImages()).thenReturn(<TVImageWrapper>[]);
        },
        act: (_) => cubit.startCaptureSelfie(),
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpCaptureFailure>(),
            ],
        verify: (_) {
          verify(() => mockEkycUiOnlyBridge.startSelfieCapturing(
                livenessMode: LivenessModeWrapper.passive,
                skipConfirmScreen: true,
                onNewFrameBatchListener: any(named: 'onNewFrameBatchListener'),
              )).called(1);

          verify(() => mockEkycUiOnlyBridge.getSelfieImages()).called(1);
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>(
        'emits DopNativeFaceOtpCaptureSuccess when selfie is successful but images is null',
        build: () => cubit,
        setUp: () {
          when(() => mockEkycUiOnlyBridge.getSelfieImages()).thenReturn(null);
        },
        act: (_) => cubit.startCaptureSelfie(),
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpCaptureFailure>(),
            ],
        verify: (_) {
          verify(() => mockEkycUiOnlyBridge.startSelfieCapturing(
                livenessMode: LivenessModeWrapper.passive,
                skipConfirmScreen: true,
                onNewFrameBatchListener: any(named: 'onNewFrameBatchListener'),
              )).called(1);

          verify(() => mockEkycUiOnlyBridge.getSelfieImages()).called(1);
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>(
        'emits DopNativeFaceOtpCaptureSuccess when selfie is failure',
        build: () => cubit,
        setUp: () {
          when(() => mockEkycUiOnlyBridge.startSelfieCapturing(
                livenessMode: any(named: 'livenessMode'),
                skipConfirmScreen: any(named: 'skipConfirmScreen'),
                onNewFrameBatchListener: any(named: 'onNewFrameBatchListener'),
              )).thenAnswer((_) async => TVSDKResult.failed(reason: TVSDKFailReason.userCancelled));
        },
        act: (_) => cubit.startCaptureSelfie(),
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpCaptureFailure>().having(
                (DopNativeFaceOtpCaptureFailure state) => state.tvSDKResult.failReason,
                'verify reason failure',
                TVSDKFailReason.userCancelled,
              ),
            ],
        verify: (_) {
          verify(() => mockEkycUiOnlyBridge.startSelfieCapturing(
                livenessMode: LivenessModeWrapper.passive,
                skipConfirmScreen: true,
                onNewFrameBatchListener: any(named: 'onNewFrameBatchListener'),
              )).called(1);
        });
  });

  group('verify checkUniqueToken()', () {
    setUp(() {
      mockAppState.dopNativeState.uniqueToken = null;
    });

    tearDown(() {
      mockAppState.dopNativeState.uniqueToken = null;
    });

    test('verify with UniqueToken is not null', () {
      mockAppState.dopNativeState.uniqueToken = fakeUniqueToken;

      final String? uniqueToken = cubit.checkUniqueToken();
      expect(uniqueToken, fakeUniqueToken);
    });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>(
      'verify with UniqueToken is null',
      build: () => cubit,
      setUp: () {
        mockAppState.dopNativeState.uniqueToken = null;
      },
      act: (_) => cubit.checkUniqueToken(),
      expect: () => <dynamic>[
        isA<DopNativeFaceOtpCommonFailure>()
            .having(
              (DopNativeFaceOtpCommonFailure state) => state.error,
              'verify error',
              isA<ErrorUIModel>(),
            )
            .having(
              (DopNativeFaceOtpCommonFailure state) => state.error.verdict,
              'verify local verdict',
              'FaceOTP checkUniqueToken: unique token is null',
            ),
      ],
    );
  });

  group('verify uploadFaceId()', () {
    late DOPNativeUploadFaceOtpIdEntity fakeEntity;

    setUpAll(() async {
      fakeEntity = DOPNativeUploadFaceOtpIdEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: await TestUtil.getResponseMock('dop_native_upload_face_id_success.json'),
      ));
    });

    setUp(() {
      when(() => mockNativeEkycUIOnlyRepo.uploadFaceId(
            label: any(named: 'label'),
            token: any(named: 'token'),
            file: any(named: 'file'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => fakeEntity);

      mockAppState.dopNativeState.uniqueToken = fakeUniqueToken;
    });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>('uploadFaceId with uniqueToken is null',
        build: () => cubit,
        setUp: () {
          mockAppState.dopNativeState.uniqueToken = null;
        },
        act: (_) => cubit.uploadFaceId(base64Image: fakeBase64Image),
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpCommonFailure>()
                  .having(
                    (DopNativeFaceOtpCommonFailure state) => state.error,
                    'upload error',
                    isA<ErrorUIModel>(),
                  )
                  .having(
                    (DopNativeFaceOtpCommonFailure state) => state.error.verdict,
                    'verify local verdict',
                    'FaceOTP checkUniqueToken: unique token is null',
                  ),
            ],
        verify: (_) {
          verifyNever(() => mockNativeEkycUIOnlyRepo.uploadFaceId(
                label: any(named: 'label'),
                token: any(named: 'token'),
                file: any(named: 'file'),
                mockConfig: any(named: 'mockConfig'),
              ));
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>('uploadFaceId with base64Image is null',
        build: () => cubit,
        act: (_) => cubit.uploadFaceId(base64Image: null),
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpCommonFailure>()
                  .having(
                    (DopNativeFaceOtpCommonFailure state) => state.error,
                    'verify error',
                    isA<ErrorUIModel>(),
                  )
                  .having(
                    (DopNativeFaceOtpCommonFailure state) => state.error.verdict,
                    'verify local verdict',
                    'FaceOTP uploadFaceId: base64Image is null',
                  ),
            ],
        verify: (_) {
          verifyNever(() => mockNativeEkycUIOnlyRepo.uploadFaceId(
                label: any(named: 'label'),
                token: any(named: 'token'),
                file: any(named: 'file'),
                mockConfig: any(named: 'mockConfig'),
              ));
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>(
        'uploadFaceId with status code is success',
        build: () => cubit,
        act: (_) => cubit.uploadFaceId(base64Image: fakeBase64Image),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpLoading>(),
              isA<DopNativeFaceOtpUploadSuccess>().having(
                (DopNativeFaceOtpUploadSuccess state) => state.entity,
                'verify entity',
                fakeEntity,
              ),
            ],
        verify: (_) {
          verify(() => mockNativeEkycUIOnlyRepo.uploadFaceId(
                label: cubit.labelFaceId,
                token: fakeUniqueToken,
                file: fakeBase64Image,
                mockConfig: any(named: 'mockConfig'),
              )).called(1);
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>(
        'uploadFaceId with status code is failure',
        build: () => cubit,
        setUp: () {
          when(() => mockNativeEkycUIOnlyRepo.uploadFaceId(
                    label: any(named: 'label'),
                    token: any(named: 'token'),
                    file: any(named: 'file'),
                    mockConfig: any(named: 'mockConfig'),
                  ))
              .thenAnswer((_) async => DOPNativeUploadFaceOtpIdEntity.fromBaseResponse(BaseResponse(
                    statusCode: CommonHttpClient.UNKNOWN_ERRORS,
                    response: null,
                  )));
        },
        act: (_) => cubit.uploadFaceId(base64Image: fakeBase64Image),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpLoading>(),
              isA<DopNativeFaceOtpUploadFailure>().having(
                (DopNativeFaceOtpUploadFailure state) => state.error.code,
                'verify code',
                EkycErrorCode.commonError,
              ),
            ],
        verify: (_) {
          verify(() => mockNativeEkycUIOnlyRepo.uploadFaceId(
                label: cubit.labelFaceId,
                token: fakeUniqueToken,
                file: fakeBase64Image,
                mockConfig: any(named: 'mockConfig'),
              )).called(1);
        });
  });

  group('verify verifyFaceId()', () {
    const String fakeUserMsg = 'Face does not match';
    late DOPNativeVerifyFaceOtpIdEntity fakeEntity;

    setUpAll(() async {
      fakeEntity = DOPNativeVerifyFaceOtpIdEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: await TestUtil.getResponseMock('dop_native_verify_face_id_success.json'),
      ));
    });

    setUp(() {
      when(() => mockNativeEkycUIOnlyRepo.verifyFaceId(
            imageId: any(named: 'imageId'),
            signature: any(named: 'signature'),
            token: any(named: 'token'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => fakeEntity);

      mockAppState.dopNativeState.uniqueToken = fakeUniqueToken;

      when(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(any()))
          .thenAnswer((_) => Future<void>.value());
    });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>('verifyFaceId with uniqueToken is null',
        build: () => cubit,
        setUp: () {
          mockAppState.dopNativeState.uniqueToken = null;
        },
        act: (_) => cubit.verifyFaceId(imageId: fakeBase64Image, signature: fakeSignature),
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpCommonFailure>().having(
                (DopNativeFaceOtpCommonFailure state) => state.error,
                'verify error',
                isA<ErrorUIModel>(),
              ),
            ],
        verify: (_) {
          verifyNever(() => mockNativeEkycUIOnlyRepo.verifyFaceId(
                imageId: any(named: 'imageId'),
                signature: any(named: 'signature'),
                token: any(named: 'token'),
                mockConfig: any(named: 'mockConfig'),
              ));

          verifyNever(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(any()));
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>('verifyFaceId with imageId is null',
        build: () => cubit,
        act: (_) => cubit.verifyFaceId(imageId: null, signature: fakeSignature),
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpCommonFailure>().having(
                (DopNativeFaceOtpCommonFailure state) => state.error,
                'verify error',
                isA<ErrorUIModel>(),
              ),
            ],
        verify: (_) {
          verifyNever(() => mockNativeEkycUIOnlyRepo.verifyFaceId(
                imageId: any(named: 'imageId'),
                signature: any(named: 'signature'),
                token: any(named: 'token'),
                mockConfig: any(named: 'mockConfig'),
              ));

          verifyNever(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(any()));
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>('verifyFaceId with signature is null',
        build: () => cubit,
        act: (_) => cubit.verifyFaceId(imageId: fakeBase64Image, signature: null),
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpCommonFailure>().having(
                (DopNativeFaceOtpCommonFailure state) => state.error,
                'verify error',
                isA<ErrorUIModel>(),
              ),
            ],
        verify: (_) {
          verifyNever(() => mockNativeEkycUIOnlyRepo.verifyFaceId(
                imageId: any(named: 'imageId'),
                signature: any(named: 'signature'),
                token: any(named: 'token'),
                mockConfig: any(named: 'mockConfig'),
              ));

          verifyNever(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(any()));
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>(
        'verifyFaceId with status code is success',
        build: () => cubit,
        act: (_) => cubit.verifyFaceId(imageId: fakeBase64Image, signature: fakeSignature),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpLoading>(),
              isA<DopNativeVerifyFaceOtpSuccess>().having(
                (DopNativeVerifyFaceOtpSuccess state) => state.entity,
                'verify entity',
                fakeEntity,
              ),
            ],
        verify: (_) {
          verify(() => mockNativeEkycUIOnlyRepo.verifyFaceId(
                imageId: fakeBase64Image,
                signature: fakeSignature,
                token: fakeUniqueToken,
                mockConfig: any(named: 'mockConfig'),
              )).called(1);

          verify(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(fakeAccessToken))
              .called(1);
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>(
        'verifyFaceId with status code is failed',
        build: () => cubit,
        setUp: () {
          when(() => mockNativeEkycUIOnlyRepo.verifyFaceId(
                    imageId: any(named: 'imageId'),
                    signature: any(named: 'signature'),
                    token: any(named: 'token'),
                    mockConfig: any(named: 'mockConfig'),
                  ))
              .thenAnswer((_) async => DOPNativeVerifyFaceOtpIdEntity.fromBaseResponse(BaseResponse(
                    statusCode: CommonHttpClient.UNKNOWN_ERRORS,
                    response: null,
                  )));
        },
        act: (_) => cubit.verifyFaceId(imageId: fakeBase64Image, signature: fakeSignature),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpLoading>(),
              isA<DopNativeVerifyFaceOtpFailure>().having(
                (DopNativeVerifyFaceOtpFailure state) => state.error.code,
                'verify error',
                EkycErrorCode.commonError,
              ),
            ],
        verify: (_) {
          verify(() => mockNativeEkycUIOnlyRepo.verifyFaceId(
                imageId: fakeBase64Image,
                signature: fakeSignature,
                token: fakeUniqueToken,
                mockConfig: any(named: 'mockConfig'),
              )).called(1);

          verifyNever(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(any()));
        });

    blocTest<DopNativeFaceOtpCubit, DopNativeFaceOtpState>('verifyFaceId with unmatched face',
        build: () => cubit,
        setUp: () {
          when(() => mockNativeEkycUIOnlyRepo.verifyFaceId(
                imageId: any(named: 'imageId'),
                signature: any(named: 'signature'),
                token: any(named: 'token'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer(
            (_) async => DOPNativeVerifyFaceOtpIdEntity.fromBaseResponse(
              BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: <String, dynamic>{
                  'verdict': DOPNativeVerifyFaceOtpIdEntity.verdictFaceUnmatched,
                  'data': <String, dynamic>{
                    'user_message': fakeUserMsg,
                  }
                },
              ),
            ),
          );
        },
        act: (_) => cubit.verifyFaceId(imageId: fakeBase64Image, signature: fakeSignature),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<DopNativeFaceOtpLoading>(),
              isA<DopNativeVerifyFaceOtpFailure>().having(
                (DopNativeVerifyFaceOtpFailure state) => state.error.message,
                'verify user message',
                fakeUserMsg,
              ),
            ],
        verify: (_) {
          verify(() => mockNativeEkycUIOnlyRepo.verifyFaceId(
                imageId: fakeBase64Image,
                signature: fakeSignature,
                token: fakeUniqueToken,
                mockConfig: any(named: 'mockConfig'),
              )).called(1);

          verifyNever(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(fakeAccessToken));
        });
  });

  group('verify setAuthToHeaderDOE()', () {
    test('verify setAuthToHeaderDOE with accessToken is null', () {
      cubit.setAuthToHeaderDOE(null);

      expect(mockAppState.dopNativeState.dopNativeAccessToken, null);
      verify(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(null)).called(1);
    });

    test('verify setAuthToHeaderDOE with accessToken is not null', () {
      cubit.setAuthToHeaderDOE(fakeAccessToken);

      expect(mockAppState.dopNativeState.dopNativeAccessToken, fakeAccessToken);
      verify(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(fakeAccessToken)).called(1);
    });
  });
}
