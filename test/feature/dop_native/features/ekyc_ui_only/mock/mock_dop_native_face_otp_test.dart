import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/mock/mock_dop_native_face_otp.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('getMockDOPNativeFaceOtpFileName', () {
    test('returns correct filename for uploadFaceOtp', () {
      expect(
        getMockDOPNativeFaceOtpFileName(MockDOPNativeFaceOtpUseCase.uploadFaceOtp),
        'dop_native_upload_face_id_success.json',
      );
    });

    test('returns correct filename for verifyFaceOtp', () {
      expect(
        getMockDOPNativeFaceOtpFileName(MockDOPNativeFaceOtpUseCase.verifyFaceOtp),
        'dop_native_verify_face_id_success.json',
      );
    });

    test('returns correct filename for verifyFaceOtpFailure', () {
      expect(
        getMockDOPNativeFaceOtpFileName(MockDOPNativeFaceOtpUseCase.verifyFaceOtpFailure),
        'dop_native_verify_face_id_failure.json',
      );
    });
  });
}
