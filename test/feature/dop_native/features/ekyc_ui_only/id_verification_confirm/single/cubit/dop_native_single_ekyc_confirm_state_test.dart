import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/id_verification_confirm/models/dop_native_ocr_data_model.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/id_verification_confirm/single/cubit/dop_native_single_ekyc_confirm_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeSingleEKYCConfirmState', () {
    test('DOPNativeSingleEKYCConfirmInitial can be instantiated', () {
      final DOPNativeSingleEKYCConfirmInitial ekycInitial = DOPNativeSingleEKYCConfirmInitial();

      expect(ekycInitial, isA<DOPNativeSingleEKYCConfirmState>());
    });

    test('DOPNativeSingleEKYCConfirmLoading can be instantiated', () {
      final DOPNativeSingleEKYCConfirmLoading ekycInitial = DOPNativeSingleEKYCConfirmLoading();

      expect(ekycInitial, isA<DOPNativeSingleEKYCConfirmState>());
    });

    test('DOPNativeGetOCRDataSucceed holds correct entity', () {
      const String fakeAddress = '123 Main St';
      const String fakeDistId = 'dist1';
      const String fakeProvId = 'prov1';
      const String fakeWardId = 'ward1';
      const String fakeBirthday = '1989-01-01';
      const String fakeFullName = 'PHAN THỊ AN';

      final DOPNativeOCRDataModel fakeOCRData = DOPNativeOCRDataModel(
        familyAddress: fakeAddress,
        familyBookAddressDistId: fakeDistId,
        familyBookAddressProvinceId: fakeProvId,
        familyBookAddressWardId: fakeWardId,
        birthday: fakeBirthday,
        fullName: fakeFullName,
      );

      final DOPNativeGetOCRDataSucceed state = DOPNativeGetOCRDataSucceed(ocrData: fakeOCRData);

      expect(state, isA<DOPNativeSingleEKYCConfirmState>());
      expect(state.ocrData?.familyAddress, fakeAddress);
      expect(state.ocrData?.familyBookAddressDistId, fakeDistId);
      expect(state.ocrData?.familyBookAddressProvinceId, fakeProvId);
      expect(state.ocrData?.familyBookAddressWardId, fakeWardId);
      expect(state.ocrData?.birthday, fakeBirthday);
      expect(state.ocrData?.fullName, fakeFullName);
    });

    test('DOPNativeGetOCRDataFailed holds correct ErrorUIModel', () {
      const String fakeMessage = 'error_message';

      final ErrorUIModel errorUIModel = ErrorUIModel(
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
        userMessage: fakeMessage,
      );

      final DOPNativeGetOCRDataFailed state = DOPNativeGetOCRDataFailed(error: errorUIModel);

      expect(state, isA<DOPNativeSingleEKYCConfirmState>());
      expect(state.error, errorUIModel);
    });
  });

  test('DOPNativeSubmitOCRSucceed can be instantiated', () {
    final DOPNativeSubmitOCRSucceed state = DOPNativeSubmitOCRSucceed();

    expect(state, isA<DOPNativeSingleEKYCConfirmState>());
  });

  test('DOPNativeSubmitOCRFailure holds correct ErrorUIModel', () {
    const String fakeMessage = 'error_message';

    final ErrorUIModel error = ErrorUIModel(
      statusCode: CommonHttpClient.UNKNOWN_ERRORS,
      userMessage: fakeMessage,
    );
    final DOPNativeSubmitOCRFailure state = DOPNativeSubmitOCRFailure(error: error);

    expect(state, isA<DOPNativeSingleEKYCConfirmState>());
    expect(state.error, error);
  });
}
