import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/ekyc_ui_only_bridge_impl.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/liveness_mode.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/tv_image_wrapper.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/ekyc/model/ekyc_result_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:trust_vision_plugin/enums.dart';
import 'package:trust_vision_plugin/result/tv_card_type.dart';
import 'package:trust_vision_plugin/result/tv_detection_result.dart';
import 'package:trust_vision_plugin/result/tv_frame_batch.dart';
import 'package:trust_vision_plugin/result/tv_frame_class.dart';
import 'package:trust_vision_plugin/result/tv_nfc_info_result.dart';
import 'package:trust_vision_plugin/trust_vision_plugin.dart';

// Mock classes
class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockTrustVisionPlugin extends Mock implements TrustVisionPlugin {}

class MockMethodChannel extends Mock implements MethodChannel {}

class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

void main() {
  late MockLoggingRepo mockLoggingRepo;
  late MockTrustVisionPlugin mockTrustVisionPlugin;
  late EkycUiOnlyBridgeImpl ekycUiOnlyBridgeImpl;
  late EvoFlutterWrapper mockEvoFlutterWrapper;

  setUpAll(() {
    getIt.registerSingleton<EvoFlutterWrapper>(MockEvoFlutterWrapper());
    mockEvoFlutterWrapper = getIt<EvoFlutterWrapper>();
  });

  setUp(() {
    registerFallbackValue(const EventType('fake_type'));

    mockLoggingRepo = MockLoggingRepo();
    mockTrustVisionPlugin = MockTrustVisionPlugin();
    ekycUiOnlyBridgeImpl = EkycUiOnlyBridgeImpl(
      mockTrustVisionPlugin,
      mockLoggingRepo,
    );

    when(() =>
            mockLoggingRepo.logEvent(eventType: any(named: 'eventType'), data: any(named: 'data')))
        .thenAnswer((_) async {});
  });

  test('test static values', () {
    expect(EkycUiOnlyBridgeImpl.stepInitEkycSdk, 'init_ekyc_sdk');
    expect(EkycUiOnlyBridgeImpl.stepResetFlow, 'reset_flow');
    expect(EkycUiOnlyBridgeImpl.stepIdCapturing, 'id_capturing');
    expect(EkycUiOnlyBridgeImpl.stepSelfieCapturing, 'selfie_capturing');
    expect(EkycUiOnlyBridgeImpl.stepReadNFC, 'read_nfc');
    expect(EkycUiOnlyBridgeImpl.stepCheckNFCSupport, 'check_nfc_support');
    expect(EkycUiOnlyBridgeImpl.platformException, 'platform_exception');
    expect(EkycUiOnlyBridgeImpl.otherException, 'other_exception');
  });

  verifyQRScanConfig(dynamic compareObj, bool skipConfirmScreen) {
    expect(
      compareObj,
      <String, dynamic>{
        'cardType': <String, dynamic>{
          'id': 'vn.national_id',
          'name': 'CMND cũ / CMND mới / CCCD / Hộ chiếu',
          'orientation': 'horizontal',
          'hasBackSide': true,
          'frontQr': <String, dynamic>{
            'exist': true,
            'widthHeightRatio': 1.0,
            'type': 'qrCode',
          },
        },
        'cardSide': CardSide.front.name,
        'skipConfirmScreen': skipConfirmScreen,
        'isEnableUploadFrames': false,
        'isEnableUploadImages': false,
      },
    );
  }

  group('before init sdk', () {
    test('getIdFrontSideBase64Image returns correct image', () {
      expect(ekycUiOnlyBridgeImpl.getIdFrontSideBase64Image(), isNull);
    });

    test('getIdQrFrontSideBase64Image returns correct image', () {
      expect(ekycUiOnlyBridgeImpl.getQrFrontSideBase64Image(), isNull);
    });

    test('getIdBackSideBase64Image returns correct image', () {
      expect(ekycUiOnlyBridgeImpl.getIdBackSideBase64Image(), isNull);
    });

    test('isInitialized returns correct value', () {
      expect(ekycUiOnlyBridgeImpl.isInitialized(), isFalse);
    });
  });

  group('initEkyc', () {
    test('initEkyc should handle success correctly', () async {
      // Arrange
      when(() => mockTrustVisionPlugin.initialize(
          jsonConfigurationByServer: any(named: 'jsonConfigurationByServer'),
          languageCode: any(named: 'languageCode'))).thenAnswer((_) async {
        return null;
      });

      // Act
      final TVSDKResult result =
          await ekycUiOnlyBridgeImpl.initEkyc(jsonConfigurationByServer: '{}');

      // Assert
      expect(result, isA<TVSDKResult>());
      verify(() => mockTrustVisionPlugin.initialize(jsonConfigurationByServer: '{}')).called(1);

      expect(ekycUiOnlyBridgeImpl.isInitialized(), isTrue);
    });

    test('initEkyc should handle PlatformException correctly', () async {
      // Arrange
      when(() => mockTrustVisionPlugin.initialize(
              jsonConfigurationByServer: any(named: 'jsonConfigurationByServer'),
              languageCode: any(named: 'languageCode')))
          .thenThrow(PlatformException(code: 'error_code'));

      // Act
      final TVSDKResult result =
          await ekycUiOnlyBridgeImpl.initEkyc(jsonConfigurationByServer: '{}');

      // Assert
      expect(result, isA<TVSDKResult>());
      expect(result.failReason, TVSDKFailReason.unknown);
      verify(() => mockTrustVisionPlugin.initialize(jsonConfigurationByServer: '{}')).called(1);
    });
  });

  group('Selfie capturing', () {
    test('returns correct selfie configuration map', () {
      final Map<String, dynamic> configMap = ekycUiOnlyBridgeImpl.getSelfieConfigMap(
        livenessModeWrapper: LivenessModeWrapper.passive,
        skipConfirmScreen: true,
      );

      expect(configMap, isA<Map<String, dynamic>>());
      expect(configMap['cameraOption'], 'front');
      expect(configMap['isEnableSound'], isTrue);
      expect(configMap['skipConfirmScreen'], isTrue);
      expect(configMap['livenessMode'], 'passive');
    });

    test('startSelfieCapturing calls captureSelfie with correct parameters and succeeds', () async {
      when(() => mockTrustVisionPlugin.captureSelfie(any(),
              onNewFrameBatch: any(named: 'onNewFrameBatch')))
          .thenAnswer((_) async => TVDetectionResult.fromMap(<String, dynamic>{}));

      final TVSDKResult result = await ekycUiOnlyBridgeImpl.startSelfieCapturing(
        livenessMode: LivenessModeWrapper.passive,
        onNewFrameBatchListener: (Map<String, dynamic> params) {},
      );

      expect(result.isSuccess, isTrue);
    });

    test('handleNewFrameBatch calls onNewFrameBatchListener with correct params', () async {
      Map<String, dynamic>? receivedParams;
      // Define the listener
      listener(Map<String, dynamic> params) {
        receivedParams = params;
      }

      // Assuming TvFrameBatch takes a list of TvFrameClass and metadata
      final TvFrameBatch fakeFrameBatch = TvFrameBatch(
        frames: <TvFrameClass>[TvFrameClass()],
        // Replace TvFrameClass with actual frame data if available
        metadata: <String, dynamic>{'some': 'metadata'},
      );

      // Call the method with the listener
      ekycUiOnlyBridgeImpl.handleNewFrameBatch(fakeFrameBatch, listener);

      // Check that the listener was called and the params are as expected
      expect(receivedParams, isNotNull);
      expect(receivedParams!['frames'], equals(fakeFrameBatch.frames));
      expect(receivedParams!['metadata'], equals(fakeFrameBatch.metadata));
      expect(receivedParams!['label'], 'video');
    });

    test('handleSelfieResults with flash selfie, processes results correctly', () {
      // Create a map that represents the expected structure of the detection result
      final Map<String, dynamic> detectionResultMap = <String, dynamic>{
        'selfieImages': <Map<String, Map<String, String>>>[
          <String, Map<String, String>>{
            'frontal_image': <String, String>{'raw_image_base64': 'frontalBase64_1'},
          },
          <String, Map<String, String>>{
            'frontal_image': <String, String>{'raw_image_base64': 'frontalBase64_2'},
          },
        ],
      };

      // Create an instance of TVDetectionResult from the map
      final TVDetectionResult detectionResult = TVDetectionResult.fromMap(detectionResultMap);

      // Assuming handleSelfieResults is a public method for the purpose of this test
      ekycUiOnlyBridgeImpl.handleSelfieResults(
          selfieCapturingResult: detectionResult, livenessMode: LivenessModeWrapper.flash_16);

      final List<TVImageWrapper>? selfieImages = ekycUiOnlyBridgeImpl.getSelfieImages();
      expect(selfieImages, isNotNull);
      expect(selfieImages!.length, 2);

      expect(selfieImages.first.rawImageBase64, 'frontalBase64_1');
      expect(selfieImages.first.direction, Direction.closeFace);

      expect(selfieImages[1].rawImageBase64, 'frontalBase64_2');
      expect(selfieImages[1].direction, Direction.farFace);
    });

    test('handleSelfieResults with active selfie, processes results correctly', () {
      // Create a map that represents the expected structure of the detection result
      final Map<String, dynamic> detectionResultMap = <String, dynamic>{
        'selfieImages': <Map<String, dynamic>>[
          <String, dynamic>{
            'frontal_image': <String, String>{
              'raw_image_base64': 'frontalBase64_1',
            },
            'gesture_image': <String, String>{
              'raw_image_base64': 'gestureBase64_1',
            },
            'gesture_type': 'right',
          },
          <String, dynamic>{
            'frontal_image': <String, String>{
              'raw_image_base64': 'frontalBase64_2',
            },
            'gesture_image': <String, String>{
              'raw_image_base64': 'gestureBase64_2',
            },
            'gesture_type': 'left',
          },
          <String, dynamic>{
            'frontal_image': <String, String>{
              'raw_image_base64': 'frontalBase64_3',
            },
            'gesture_image': <String, String>{
              'raw_image_base64': 'gestureBase64_3',
            },
            'gesture_type': 'down',
          },
          <String, dynamic>{
            'frontal_image': <String, String>{
              'raw_image_base64': 'frontalBase64_4',
            },
            'gesture_type': 'frontal',
          },
        ],
      };

      // Create an instance of TVDetectionResult from the map
      final TVDetectionResult detectionResult = TVDetectionResult.fromMap(detectionResultMap);

      // Assuming handleSelfieResults is a public method for the purpose of this test
      ekycUiOnlyBridgeImpl.handleSelfieResults(
          selfieCapturingResult: detectionResult, livenessMode: LivenessModeWrapper.active);

      final List<TVImageWrapper>? selfieImages = ekycUiOnlyBridgeImpl.getSelfieImages();
      expect(selfieImages, isNotNull);
      expect(selfieImages!.length, 7);

      expect(selfieImages[0].rawImageBase64, 'frontalBase64_1');
      expect(selfieImages[0].direction, Direction.frontal);

      expect(selfieImages[1].rawImageBase64, 'gestureBase64_1');
      expect(selfieImages[1].direction, Direction.right);

      expect(selfieImages[2].rawImageBase64, 'frontalBase64_2');
      expect(selfieImages[2].direction, Direction.frontal);

      expect(selfieImages[3].rawImageBase64, 'gestureBase64_2');
      expect(selfieImages[3].direction, Direction.left);

      expect(selfieImages[4].rawImageBase64, 'frontalBase64_3');
      expect(selfieImages[4].direction, Direction.frontal);

      expect(selfieImages[5].rawImageBase64, 'gestureBase64_3');
      expect(selfieImages[5].direction, Direction.down);

      expect(selfieImages[6].rawImageBase64, 'frontalBase64_4');
      expect(selfieImages[6].direction, Direction.frontalMain);
    });

    test('startSelfieCapturing handles PlatformException', () async {
      when(() => mockTrustVisionPlugin.captureSelfie(any(),
              onNewFrameBatch: any(named: 'onNewFrameBatch')))
          .thenThrow(PlatformException(code: 'error'));

      final TVSDKResult result = await ekycUiOnlyBridgeImpl.startSelfieCapturing(
        livenessMode: LivenessModeWrapper.passive,
        onNewFrameBatchListener: (Map<String, dynamic> params) {},
      );

      expect(result.isSuccess, isFalse);
      expect(result.failReason, TVSDKFailReason.unknown);
    });

    test('getSelfieImages returns correct images', () {
      expect(ekycUiOnlyBridgeImpl.getSelfieImages(), isNotNull);
    });
  });

  group('ID card capturing', () {
    test('returns correct ID configuration map', () {
      final Map<String, dynamic> configMap = ekycUiOnlyBridgeImpl.getIdConfigMap(
        skipConfirmScreen: true,
        isReadBothSide: true,
      );

      expect(configMap, isA<Map<String, dynamic>>());
      expect(configMap['isEnableSound'], isTrue);
      expect(configMap['isReadBothSide'], isTrue);
      expect(configMap['skipConfirmScreen'], isTrue);
      expect(configMap['cardSide'], 'front');
      expect(configMap['isEnableScanQr'], false);
      expect(configMap['isEnableScanNfc'], isFalse);
    });

    test('startIdCapturing calls captureIdCard with correct parameters and succeeds', () async {
      when(() => mockTrustVisionPlugin.captureIdCard(any()))
          .thenAnswer((_) async => TVDetectionResult.fromMap(<String, dynamic>{}));

      final TVSDKResult result = await ekycUiOnlyBridgeImpl.startIdCapturing(
        skipConfirmScreen: true,
        isReadBothSide: false,
      );

      expect(result.isSuccess, isTrue);
    });

    test('startIdCapturing handles PlatformException', () async {
      when(() => mockTrustVisionPlugin.captureIdCard(any()))
          .thenThrow(PlatformException(code: 'error'));

      final TVSDKResult result = await ekycUiOnlyBridgeImpl.startIdCapturing(
        skipConfirmScreen: true,
        isReadBothSide: false,
      );

      expect(result.isSuccess, isFalse);
      expect(result.failReason, TVSDKFailReason.unknown);
    });
  });

  group('Id card capturing - clearPreviousIdCapturingResults', () {
    setUp(() {
      mockTrustVisionPlugin = MockTrustVisionPlugin();
      mockLoggingRepo = MockLoggingRepo();
      ekycUiOnlyBridgeImpl = EkycUiOnlyBridgeImpl(mockTrustVisionPlugin, mockLoggingRepo);

      // Set initial values for these properties to simulate previous capturing results.
      ekycUiOnlyBridgeImpl.idFrontBase64Image = 'frontImage';
      ekycUiOnlyBridgeImpl.qrFrontSideBase64Image = 'frontQrImage';
      ekycUiOnlyBridgeImpl.idBackSideBase64Image = 'backImage';
    });

    test('should clear all ID images when isReadBothSide is true', () async {
      // Act
      await ekycUiOnlyBridgeImpl.clearPreviousIdCapturingResults(
          isReadBothSide: true, isBackSide: false);

      // Assert
      expect(ekycUiOnlyBridgeImpl.idFrontBase64Image, isNull);
      expect(ekycUiOnlyBridgeImpl.qrFrontSideBase64Image, isNull);
      expect(ekycUiOnlyBridgeImpl.idBackSideBase64Image, isNull);
    });

    test('should clear only back ID image when isReadBothSide is false and isBackSide is true',
        () async {
      // Act
      await ekycUiOnlyBridgeImpl.clearPreviousIdCapturingResults(
          isReadBothSide: false, isBackSide: true);

      // Assert
      expect(ekycUiOnlyBridgeImpl.idFrontBase64Image, isNotNull);
      expect(ekycUiOnlyBridgeImpl.qrFrontSideBase64Image, isNotNull);
      expect(ekycUiOnlyBridgeImpl.idBackSideBase64Image, isNull);
    });

    test(
        'should clear only front ID image and QR when isReadBothSide is false and isBackSide is false',
        () async {
      // Act
      await ekycUiOnlyBridgeImpl.clearPreviousIdCapturingResults(
          isReadBothSide: false, isBackSide: false);

      // Assert
      expect(ekycUiOnlyBridgeImpl.idFrontBase64Image, isNull);
      expect(ekycUiOnlyBridgeImpl.qrFrontSideBase64Image, isNull);
      expect(ekycUiOnlyBridgeImpl.idBackSideBase64Image, isNotNull);
    });
  });

  group('Handle after capturing', () {
    test('Capture Id front side - getCardTypeId should return the correct card type ID', () async {
      // Arrange
      const String expectedCardTypeId = 'vn.national_id';
      final TVCardType fakeCardType = TVCardType(
        id: expectedCardTypeId,
        name: 'Fake Card Type',
        hasBackSide: true,
        orientation: TVCardOrientation.HORIZONTAL,
      );
      final TVDetectionResult fakeResult = TVDetectionResult.fromMap(<String, dynamic>{
        'cardType': fakeCardType.toMap(),
      });
      when(() => mockTrustVisionPlugin.captureIdCard(any())).thenAnswer((_) async => fakeResult);

      // Act by starting the ID capturing - front side, which will set the cardTypeId
      await ekycUiOnlyBridgeImpl.startIdCapturing(
        skipConfirmScreen: true,
        isReadBothSide: false,
      );
      final String? cardTypeId = ekycUiOnlyBridgeImpl.getCardTypeId();

      // Assert
      expect(cardTypeId, expectedCardTypeId);
    });

    test('Capture Id both sides - getCardTypeId should return the correct card type ID', () async {
      // Arrange
      const String expectedCardTypeId = 'vn.national_id';
      final TVCardType fakeCardType = TVCardType(
        id: expectedCardTypeId,
        name: 'Fake Card Type',
        hasBackSide: true,
        orientation: TVCardOrientation.HORIZONTAL,
      );
      final TVDetectionResult fakeResult = TVDetectionResult.fromMap(<String, dynamic>{
        'cardType': fakeCardType.toMap(),
      });
      when(() => mockTrustVisionPlugin.captureIdCard(any())).thenAnswer((_) async => fakeResult);

      // Act by starting the ID capturing - back side, which will set the cardTypeId
      await ekycUiOnlyBridgeImpl.startIdCapturing(
        skipConfirmScreen: true,
        isReadBothSide: true,
      );
      final String? cardTypeId = ekycUiOnlyBridgeImpl.getCardTypeId();

      // Assert that the cardTypeId is set correctly
      expect(cardTypeId, expectedCardTypeId);
    });

    test('resetFlow clears all data', () {
      // Set non-null values to the properties
      ekycUiOnlyBridgeImpl.idFrontBase64Image = 'frontImageBase64';
      ekycUiOnlyBridgeImpl.qrFrontSideBase64Image = 'frontIdQrBase64';
      ekycUiOnlyBridgeImpl.idBackSideBase64Image = 'backImageBase64';
      ekycUiOnlyBridgeImpl.selfieImages
          .add(const TVImageWrapper(rawImageBase64: 'frontalImageBase64'));

      ekycUiOnlyBridgeImpl.resetFlow();

      expect(ekycUiOnlyBridgeImpl.getIdFrontSideBase64Image(), isNull);
      expect(ekycUiOnlyBridgeImpl.getQrFrontSideBase64Image(), isNull);
      expect(ekycUiOnlyBridgeImpl.getIdBackSideBase64Image(), isNull);
      expect(ekycUiOnlyBridgeImpl.getSelfieImages()?.isEmpty, isTrue);
      expect(ekycUiOnlyBridgeImpl.cardTypeId, isNull);
    });

    test('handleSucceed logs success and returns succeed result', () {
      final TVSDKResult result = ekycUiOnlyBridgeImpl.handleSucceed('test_step');

      verify(() => mockLoggingRepo.logEvent(
            eventType: any(named: 'eventType'),
            data: any(named: 'data'),
          )).called(1);

      expect(result.isSuccess, isTrue);
    });

    test('handleFailed logs failure and returns failed result', () {
      final TVSDKResult result = ekycUiOnlyBridgeImpl.handleFailed(
        step: 'test_step',
        reason: TVSDKFailReason.userCancelled,
        errorDetail: 'error_detail',
      );

      verify(() => mockLoggingRepo.logEvent(
            eventType: any(named: 'eventType'),
            data: any(named: 'data'),
          )).called(1);

      expect(result.isSuccess, isFalse);
      expect(result.failReason, TVSDKFailReason.userCancelled);
    });
  });

  group('verify getDirectionOfSelfieImageByIndex()', () {
    test('should return correct direction for index 0', () {
      expect(ekycUiOnlyBridgeImpl.getDirectionOfSelfieImageByIndex(0), Direction.closeFace);
    });

    test('should return correct direction for index 1', () {
      expect(ekycUiOnlyBridgeImpl.getDirectionOfSelfieImageByIndex(1), Direction.farFace);
    });

    test('should return null for index 2', () {
      expect(ekycUiOnlyBridgeImpl.getDirectionOfSelfieImageByIndex(2), isNull);
    });
  });

  group('getGestureDirectionOfActiveSelfieImage', () {
    test('returns Direction.left for input "left"', () {
      expect(ekycUiOnlyBridgeImpl.getGestureDirectionOfActiveSelfieImage('left'), Direction.left);
    });

    test('returns Direction.right for input "right"', () {
      expect(ekycUiOnlyBridgeImpl.getGestureDirectionOfActiveSelfieImage('right'), Direction.right);
    });

    test('returns Direction.up for input "up"', () {
      expect(ekycUiOnlyBridgeImpl.getGestureDirectionOfActiveSelfieImage('up'), Direction.up);
    });

    test('returns Direction.down for input "down"', () {
      expect(ekycUiOnlyBridgeImpl.getGestureDirectionOfActiveSelfieImage('down'), Direction.down);
    });

    test('returns null for invalid input', () {
      expect(ekycUiOnlyBridgeImpl.getGestureDirectionOfActiveSelfieImage('invalid'), isNull);
    });

    test('returns null for null input', () {
      expect(ekycUiOnlyBridgeImpl.getGestureDirectionOfActiveSelfieImage(null), isNull);
    });
  });

  group('verify getTVNfcInfoResult()', () {
    test('should return null if selfieFrontalImages is empty ', () {
      expect(ekycUiOnlyBridgeImpl.getTVNfcInfoResult(), isNull);
    });

    test('should return first correct image', () {
      const String fakeSod = 'fakeSod';
      ekycUiOnlyBridgeImpl.tvNfcInfoResult = TVNfcInfoResult(sod: fakeSod);

      expect(ekycUiOnlyBridgeImpl.getTVNfcInfoResult(), isNotNull);
      expect(ekycUiOnlyBridgeImpl.getTVNfcInfoResult()?.sod, fakeSod);
    });
  });

  group('Test startQRCodeCapturing', () {
    test('startQRCodeCapturing called with correct parameters and succeeds', () async {
      const bool skipConfirmScreen = true;
      when(() => mockTrustVisionPlugin.scanQrCode(any())).thenAnswer(
        (_) async => TVDetectionResult.fromMap(<String, dynamic>{}),
      );

      final TVSDKResult result = await ekycUiOnlyBridgeImpl.startQRCodeCapturing(
        skipConfirmScreen: skipConfirmScreen,
      );

      expect(result.isSuccess, true);
      expect(result.failReason, isNull);

      verifyQRScanConfig(
        verify(() => mockTrustVisionPlugin.scanQrCode(captureAny())).captured.single,
        skipConfirmScreen,
      );
    });

    test('startQRCodeCapturing called with correct parameters and fails', () async {
      const bool skipConfirmScreen = false;
      when(() => mockTrustVisionPlugin.scanQrCode(any())).thenThrow(
        PlatformException(code: 'error'),
      );

      final TVSDKResult result = await ekycUiOnlyBridgeImpl.startQRCodeCapturing(
        skipConfirmScreen: skipConfirmScreen,
      );

      expect(result.isSuccess, false);
      expect(result.failReason, TVSDKFailReason.unknown);

      verifyQRScanConfig(
        verify(() => mockTrustVisionPlugin.scanQrCode(captureAny())).captured.single,
        skipConfirmScreen,
      );
    });

    test('startQRCodeCapturing called with correct parameters and fails with qrTimeout', () async {
      const bool skipConfirmScreen = true;

      when(() => mockTrustVisionPlugin.scanQrCode(any())).thenThrow(
        PlatformException(code: 'qrTimeout'),
      );

      final TVSDKResult result = await ekycUiOnlyBridgeImpl.startQRCodeCapturing(
        skipConfirmScreen: skipConfirmScreen,
      );

      expect(result.isSuccess, false);
      expect(result.failReason, TVSDKFailReason.qrTimeout);

      verifyQRScanConfig(
        verify(() => mockTrustVisionPlugin.scanQrCode(captureAny())).captured.single,
        skipConfirmScreen,
      );
    });

    test('startQRCodeCapturing called with correct parameters and fails with qrSkip', () async {
      const bool skipConfirmScreen = true;

      when(() => mockTrustVisionPlugin.scanQrCode(any())).thenThrow(
        PlatformException(code: 'qrSkip'),
      );

      final TVSDKResult result = await ekycUiOnlyBridgeImpl.startQRCodeCapturing(
        skipConfirmScreen: skipConfirmScreen,
      );

      expect(result.isSuccess, false);
      expect(result.failReason, TVSDKFailReason.qrSkip);

      verifyQRScanConfig(
        verify(() => mockTrustVisionPlugin.scanQrCode(captureAny())).captured.single,
        skipConfirmScreen,
      );
    });
  });

  test('Test getQRScanConfigMap', () {
    verifyQRScanConfig(ekycUiOnlyBridgeImpl.getQRScanConfigMap(true), true);
    verifyQRScanConfig(ekycUiOnlyBridgeImpl.getQRScanConfigMap(false), false);
  });

  group('Test readNfc()', () {
    const String fakeIdCardNumber = 'fakeIdCardNumber';
    const String fakeDateOfBirth = '01/01/2000';
    const String fakeDateOfExpiry = '01/01/2023';
    late DOPUtilFunctions mockDOPUtilFunctions;

    setUpAll(() {
      getIt.registerSingleton<DOPUtilFunctions>(MockDOPUtilFunctions());
      mockDOPUtilFunctions = getIt<DOPUtilFunctions>();
    });

    setUp(() {
      when(() => mockTrustVisionPlugin.readNfc(any())).thenAnswer(
        (_) async => TVDetectionResult.fromMap(<String, dynamic>{
          'nfcInfoResult': <String, dynamic>{},
        }),
      );

      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);

      when(() => mockDOPUtilFunctions.isCorrectDateEKYCFormat(
          dateInputted: any(named: 'dateInputted'))).thenReturn(true);
    });

    tearDown(() {
      reset(mockEvoFlutterWrapper);
      reset(mockDOPUtilFunctions);
    });

    test('readNfc called with correct parameters and succeeds on Android', () async {
      final TVSDKResult result = await ekycUiOnlyBridgeImpl.readNfc(
        idCardNumber: fakeIdCardNumber,
        dateOfBirth: fakeDateOfBirth,
        dateOfExpiry: fakeDateOfExpiry,
      );

      expect(result.isSuccess, true);
      expect(result.failReason, isNull);
      expect(ekycUiOnlyBridgeImpl.isOpenedScanNFCScreen, false);

      expect(ekycUiOnlyBridgeImpl.tvNfcInfoResult, isNotNull);

      expect(
        verify(() => mockTrustVisionPlugin.readNfc(captureAny())).captured.single,
        <String, dynamic>{
          'nfcCode': fakeIdCardNumber,
          'isRequestReadImageNfc': true,
          'isRequestCloneDetectionNfc': true,
          'isRequestIntegrityCheckNfc': true,
          'isEnableCheckNfcData ': true,
          'isEnableVerifyNfc ': true,
          'nfcMaxRetries': double.maxFinite.toInt(),
        },
      );
    });

    test('readNfc called with correct parameters and succeeds on iOS', () async {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(true);

      final TVSDKResult result = await ekycUiOnlyBridgeImpl.readNfc(
        idCardNumber: fakeIdCardNumber,
        dateOfBirth: fakeDateOfBirth,
        dateOfExpiry: fakeDateOfExpiry,
      );

      expect(result.isSuccess, true);
      expect(result.failReason, isNull);
      expect(ekycUiOnlyBridgeImpl.isOpenedScanNFCScreen, false);

      expect(ekycUiOnlyBridgeImpl.tvNfcInfoResult, isNotNull);

      verify(() => mockDOPUtilFunctions.isCorrectDateEKYCFormat(dateInputted: fakeDateOfBirth))
          .called(1);
      verify(() => mockDOPUtilFunctions.isCorrectDateEKYCFormat(dateInputted: fakeDateOfExpiry))
          .called(1);

      expect(
        verify(() => mockTrustVisionPlugin.readNfc(captureAny())).captured.single,
        <String, dynamic>{
          'nfcCode': fakeIdCardNumber,
          'isRequestReadImageNfc': true,
          'isRequestCloneDetectionNfc': true,
          'isRequestIntegrityCheckNfc': true,
          'nfcMaxRetries': double.maxFinite.toInt(),
          'isEnableCheckNfcData ': true,
          'isEnableVerifyNfc ': true,
          'dateOfBirth': fakeDateOfBirth,
          'dateOfExpiry': fakeDateOfExpiry,
        },
      );
    });

    test('readNfc called with dateOfBirth & dateOfExpiry is null on iOS', () async {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(true);
      when(() => mockDOPUtilFunctions.isCorrectDateEKYCFormat(
          dateInputted: any(named: 'dateInputted'))).thenReturn(false);

      final TVSDKResult result = await ekycUiOnlyBridgeImpl.readNfc(
        idCardNumber: fakeIdCardNumber,
      );

      expect(result.isSuccess, false);
      expect(result.failReason, TVSDKFailReason.unknown);
      expect(ekycUiOnlyBridgeImpl.isOpenedScanNFCScreen, false);

      expect(ekycUiOnlyBridgeImpl.tvNfcInfoResult, null);

      verify(() => mockDOPUtilFunctions.isCorrectDateEKYCFormat(dateInputted: null)).called(1);

      verifyNever(() => mockTrustVisionPlugin.readNfc(any()));
    });

    test('readNfc called with correct parameters and fails', () async {
      when(() => mockTrustVisionPlugin.readNfc(any())).thenThrow(
        PlatformException(code: 'error'),
      );

      final TVSDKResult result = await ekycUiOnlyBridgeImpl.readNfc(
        idCardNumber: fakeIdCardNumber,
      );

      expect(ekycUiOnlyBridgeImpl.tvNfcInfoResult, null);

      expect(result.isSuccess, false);
      expect(result.failReason, TVSDKFailReason.unknown);
      expect(ekycUiOnlyBridgeImpl.isOpenedScanNFCScreen, false);

      expect(
        verify(() => mockTrustVisionPlugin.readNfc(captureAny())).captured.single,
        <String, dynamic>{
          'nfcCode': fakeIdCardNumber,
          'isRequestReadImageNfc': true,
          'isRequestCloneDetectionNfc': true,
          'isRequestIntegrityCheckNfc': true,
          'isEnableCheckNfcData ': true,
          'isEnableVerifyNfc ': true,
          'nfcMaxRetries': double.maxFinite.toInt(),
        },
      );
    });
  });

  group('verify checkNfcSupport', () {
    test('return true when TVSDK return true', () async {
      when(() => mockTrustVisionPlugin.checkNfcSupport()).thenAnswer(
        (_) async => true,
      );

      final bool result = await ekycUiOnlyBridgeImpl.checkNfcSupport();
      expect(result, isTrue);
      verify(() => mockTrustVisionPlugin.checkNfcSupport()).called(1);
    });

    test('return false when TVSDK return null', () async {
      when(() => mockTrustVisionPlugin.checkNfcSupport()).thenAnswer(
        (_) async => null,
      );

      final bool result = await ekycUiOnlyBridgeImpl.checkNfcSupport();
      expect(result, isFalse);
      verify(() => mockTrustVisionPlugin.checkNfcSupport()).called(1);
    });

    test('return false when throw PlatformException', () async {
      when(() => mockTrustVisionPlugin.checkNfcSupport()).thenThrow(
        PlatformException(code: 'any-code'),
      );

      final bool result = await ekycUiOnlyBridgeImpl.checkNfcSupport();
      expect(result, isFalse);
      verify(() => mockTrustVisionPlugin.checkNfcSupport()).called(1);
    });

    test('return false when throw Unknown Exception', () async {
      when(() => mockTrustVisionPlugin.checkNfcSupport()).thenThrow(
        Exception(),
      );

      final bool result = await ekycUiOnlyBridgeImpl.checkNfcSupport();
      expect(result, isFalse);
      verify(() => mockTrustVisionPlugin.checkNfcSupport()).called(1);
    });
  });

  group('verify closeScanNFCScreenOfSDK', () {
    setUp(() {
      when(() => mockTrustVisionPlugin.closeScreenOfSDK()).thenAnswer((_) async {
        return Future<Map<String, dynamic>>.value(<String, dynamic>{});
      });
    });

    test('should call closeScanNFCScreenOfSDK', () async {
      expect(ekycUiOnlyBridgeImpl.isOpenedScanNFCScreen, null);
      ekycUiOnlyBridgeImpl.isOpenedScanNFCScreen = true;

      ekycUiOnlyBridgeImpl.closeScanNFCScreenOfSDK();

      verify(() => mockTrustVisionPlugin.closeScreenOfSDK()).called(1);
    });

    test('should not call closeScanNFCScreenOfSDK when isOpenedScanNFCScreen != true', () async {
      expect(ekycUiOnlyBridgeImpl.isOpenedScanNFCScreen, null);

      ekycUiOnlyBridgeImpl.closeScanNFCScreenOfSDK();

      verifyNever(() => mockTrustVisionPlugin.closeScreenOfSDK());
    });
  });

  test('verify clearPreviousSelfieCapturingResults()', () {
    expect(ekycUiOnlyBridgeImpl.selfieImages.isEmpty, true);

    final List<TVImageWrapper> fakeSelfieImages = <TVImageWrapper>[
      const TVImageWrapper(rawImageBase64: 'fakeImageBase64'),
    ];
    ekycUiOnlyBridgeImpl.selfieImages.addAll(fakeSelfieImages);
    expect(ekycUiOnlyBridgeImpl.selfieImages.isEmpty, false);

    ekycUiOnlyBridgeImpl.clearPreviousSelfieCapturingResults();
    expect(ekycUiOnlyBridgeImpl.selfieImages.isEmpty, true);
  });
}
