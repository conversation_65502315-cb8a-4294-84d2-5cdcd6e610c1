import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/dop_native/features/collect_location/cubit/dop_native_collect_location_cubit.dart';
import 'package:evoapp/feature/dop_native/features/collect_location/handler/dop_native_collect_location_handler.dart';
import 'package:evoapp/util/device_location/data/device_location_permission_status.dart';
import 'package:evoapp/util/device_location/device_location_util.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockDeviceLocationUtil extends Mock implements DeviceLocationUtil {}

class MockDOPNativeCollectLocationHandler extends Mock implements DOPNativeCollectLocationHandler {}

void main() {
  group('DOPNativeCollectLocationCubit', () {
    late DOPNativeCollectLocationCubit cubit;
    late MockDeviceLocationUtil mockDeviceLocationUtil;
    late MockDOPNativeCollectLocationHandler mockLocationHandler;

    setUp(() {
      mockDeviceLocationUtil = MockDeviceLocationUtil();
      mockLocationHandler = MockDOPNativeCollectLocationHandler();
      cubit = DOPNativeCollectLocationCubit();

      DeviceLocationUtil.instanceForTesting = mockDeviceLocationUtil;
      DOPNativeCollectLocationHandler.instanceForTesting = mockLocationHandler;
    });

    tearDown(() {
      DeviceLocationUtil.resetToOriginalInstance();
      DOPNativeCollectLocationHandler.resetToOriginalInstance();
      cubit.close();
    });

    blocTest<DOPNativeCollectLocationCubit, DOPNativeCollectLocationState>(
      'emits DOPNativeCollectLocationFinished when permission is denied',
      build: () {
        when(() => mockDeviceLocationUtil.requestLocationPermission()).thenAnswer(
          (_) async => DeviceLocationPermissionStatus.denied,
        );
        return cubit;
      },
      act: (DOPNativeCollectLocationCubit cubit) => cubit.handleAllowButton(),
      expect: () => <TypeMatcher<DOPNativeCollectLocationFinished>>[
        isA<DOPNativeCollectLocationFinished>(),
      ],
    );

    blocTest<DOPNativeCollectLocationCubit, DOPNativeCollectLocationState>(
      'emits DOPNativeCollectLocationFinished when permission is deniedForever',
      build: () {
        when(() => mockDeviceLocationUtil.requestLocationPermission()).thenAnswer(
          (_) async => DeviceLocationPermissionStatus.deniedForever,
        );
        return cubit;
      },
      act: (DOPNativeCollectLocationCubit cubit) => cubit.handleAllowButton(),
      expect: () => <TypeMatcher<DOPNativeCollectLocationFinished>>[
        isA<DOPNativeCollectLocationFinished>(),
      ],
    );

    blocTest<DOPNativeCollectLocationCubit, DOPNativeCollectLocationState>(
      'emits DOPNativeCollectLocationFinished when permission is unableToDetermine',
      build: () {
        when(() => mockDeviceLocationUtil.requestLocationPermission()).thenAnswer(
          (_) async => DeviceLocationPermissionStatus.unableToDetermine,
        );
        return cubit;
      },
      act: (DOPNativeCollectLocationCubit cubit) => cubit.handleAllowButton(),
      expect: () => <TypeMatcher<DOPNativeCollectLocationFinished>>[
        isA<DOPNativeCollectLocationFinished>(),
      ],
    );

    blocTest<DOPNativeCollectLocationCubit, DOPNativeCollectLocationState>(
      'calls collectAndSubmitLocation and emits DOPNativeCollectLocationFinished when permission is granted',
      build: () {
        when(() => mockDeviceLocationUtil.requestLocationPermission()).thenAnswer(
          (_) async => DeviceLocationPermissionStatus.granted,
        );
        when(() => mockLocationHandler.collectAndSubmitLocation()).thenAnswer(
          (_) => Future<void>.value(),
        );
        return cubit;
      },
      act: (DOPNativeCollectLocationCubit cubit) => cubit.handleAllowButton(),
      expect: () => <TypeMatcher<DOPNativeCollectLocationFinished>>[
        isA<DOPNativeCollectLocationFinished>(),
      ],
      verify: (_) {
        verify(() => mockLocationHandler.collectAndSubmitLocation()).called(1);
      },
    );

    blocTest<DOPNativeCollectLocationCubit, DOPNativeCollectLocationState>(
      'emits DOPNativeCollectLocationFinished when handleDenyButton is called',
      build: () => cubit,
      act: (DOPNativeCollectLocationCubit cubit) => cubit.handleDenyButton(),
      expect: () => <TypeMatcher<DOPNativeCollectLocationFinished>>[
        isA<DOPNativeCollectLocationFinished>(),
      ],
    );
  });
}
