import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/request/dop_native/dop_native_application_submit_form_request.dart';
import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_contact_info_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_metadata_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_metadata_item_entity.dart';
import 'package:evoapp/feature/dop_native/features/additional_form/models/additional_form_data_model.dart';
import 'package:evoapp/feature/dop_native/features/additional_form/models/additional_form_step_model.dart';
import 'package:evoapp/feature/dop_native/features/additional_form/widgets/subscribe_channel/cubit/dop_native_subscribe_channel_cubit.dart';
import 'package:evoapp/feature/dop_native/features/additional_form/widgets/subscribe_channel/utils/subscribe_channel_constants.dart';
import 'package:evoapp/feature/dop_native/models/dop_native_state.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/dop_native/util/metadata/dop_native_metadata_utils.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../../constant.dart';
import '../../../../../../../util/test_util.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockAppState extends Mock implements AppState {}

class MockDOPNativeMetadataUtils extends Mock implements DOPNativeMetadataUtils {}

void main() {
  final DOPNativeRepo mockDopNativeRepo = MockDOPNativeRepo();
  final AppState mockAppState = MockAppState();
  late DOPNativeSubscribeChannelCubit cubit;
  late DOPNativeMetadataUtils mockMetadataUtils;

  final List<DOPNativeMetadataItemEntity> testChannelsHaveZalo = <DOPNativeMetadataItemEntity>[
    DOPNativeMetadataItemEntity(code: SubscribeChannelConstants.zalo.value),
    DOPNativeMetadataItemEntity(code: SubscribeChannelConstants.sms.value),
    DOPNativeMetadataItemEntity(code: SubscribeChannelConstants.phone.value),
    DOPNativeMetadataItemEntity(code: SubscribeChannelConstants.email.value),
  ];

  final List<DOPNativeMetadataItemEntity> testChannelsNotHaveZalo = <DOPNativeMetadataItemEntity>[
    DOPNativeMetadataItemEntity(code: SubscribeChannelConstants.sms.value),
    DOPNativeMetadataItemEntity(code: SubscribeChannelConstants.phone.value),
    DOPNativeMetadataItemEntity(code: SubscribeChannelConstants.email.value),
  ];

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<DOPUtilFunctions>(() => DOPUtilFunctions());
    registerFallbackValue(DOPNativeApplicationSubmitFormRequest());

    mockMetadataUtils = MockDOPNativeMetadataUtils();
  });

  setUp(() {
    cubit = DOPNativeSubscribeChannelCubit(
      dopNativeRepo: mockDopNativeRepo,
      appState: mockAppState,
      metadataUtils: mockMetadataUtils,
    );
  });

  tearDown(() {
    reset(mockDopNativeRepo);
    reset(mockMetadataUtils);
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('Default state', () {
    expect(cubit.state, isA<DOPNativeSubscribeChannelInitial>());
  });

  test('Verify contactInfo', () {
    expect(cubit.contactInfo, isA<DOPNativeContactInfoEntity>());
  });

  group('Verify request get subscribe channel', () {
    blocTest<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
      'Fetch subscribe channel success',
      build: () => cubit,
      setUp: () {
        when(() => mockMetadataUtils.getSubscribeChannels()).thenAnswer(
          (_) async {
            return DOPNativeMetadataEntity.fromJson(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response:
                  await TestUtil.getResponseMock('dop_native_get_subscribe_channel_success.json'),
            ));
          },
        );
      },
      act: (DOPNativeSubscribeChannelCubit cubit) => cubit.getSubscribeChannels(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<FetchSubscribeChannelLoading>(),
        isA<FetchSubscribeChannelSuccess>().having(
          (FetchSubscribeChannelSuccess state) => state.subscribeChannels,
          'verify subscribe channel',
          isA<List<DOPNativeMetadataItemEntity>>(),
        ),
      ],
      verify: (_) {
        verify(() => mockMetadataUtils.getSubscribeChannels()).called(1);
      },
    );

    blocTest<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
      'Fetch subscribe channel fail',
      build: () => cubit,
      setUp: () {
        when(() => mockMetadataUtils.getSubscribeChannels())
            .thenAnswer((_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
                  statusCode: CommonHttpClient.BAD_REQUEST,
                  response: null,
                )));
      },
      act: (DOPNativeSubscribeChannelCubit cubit) => cubit.getSubscribeChannels(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<FetchSubscribeChannelLoading>(),
        isA<FetchSubscribeChannelError>().having(
          (FetchSubscribeChannelError state) => state.error,
          'verify error',
          isA<ErrorUIModel>(),
        ),
      ],
      verify: (_) {
        verify(() => mockMetadataUtils.getSubscribeChannels()).called(1);
      },
    );
  });

  group(
    'Verify submit subscribe channel',
    () {
      const String filenameResponse = 'dop_native_submit_subscribe_channel_success.json';
      const DOPNativeAdditionalFormStepModel step = DOPNativeAdditionalFormStepModel(
        currentStep: DOPNativeFormStep.subscribeChannel,
        stepIndex: 4,
        nextStep: null,
        previousStep: null,
      );
      final DOPNativeAdditionalFormDataModel model = DOPNativeAdditionalFormDataModel(
        formStepModel: step,
        formDataEntity: null,
      );
      setUp(() {
        when(() => mockAppState.dopNativeState).thenReturn(DOPNativeState()
          ..dopApplicationState = DOPNativeApplicationStateEntity(currentStep: 'fakeStep'));
      });

      blocTest<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
        'submit subscribe channel success',
        build: () => cubit,
        setUp: () {
          when(() => mockDopNativeRepo.submitApplicationForm(
                mockConfig: any(named: 'mockConfig'),
                form: any(named: 'form'),
              )).thenAnswer((_) async => BaseEntity.fromBaseResponse(
                BaseResponse(
                    statusCode: CommonHttpClient.SUCCESS,
                    response: await TestUtil.getResponseMock(filenameResponse)),
              ));
        },
        act: (DOPNativeSubscribeChannelCubit cubit) => cubit.submitSubscribeChannels(model),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
          isA<SubmitSubscribeChannelLoading>(),
          isA<SubmitSubscribeChannelSuccess>(),
        ],
        verify: (_) {
          verify(() => mockDopNativeRepo.submitApplicationForm(
                mockConfig: any(named: 'mockConfig'),
                form: any(named: 'form'),
              )).called(1);
        },
      );

      blocTest<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
        'submit subscribe channel fail',
        build: () => cubit,
        setUp: () {
          when(() => mockDopNativeRepo.submitApplicationForm(
                mockConfig: any(named: 'mockConfig'),
                form: any(named: 'form'),
              )).thenAnswer((_) async => BaseEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.BAD_REQUEST,
                response: null,
              )));
        },
        act: (DOPNativeSubscribeChannelCubit cubit) => cubit.submitSubscribeChannels(model),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
          isA<SubmitSubscribeChannelLoading>(),
          isA<SubmitSubscribeChannelError>().having(
            (SubmitSubscribeChannelError state) => state.error,
            'verify error',
            isA<ErrorUIModel>(),
          ),
        ],
        verify: (_) {
          verify(() => mockDopNativeRepo.submitApplicationForm(
                mockConfig: any(named: 'mockConfig'),
                form: any(named: 'form'),
              )).called(1);
        },
      );
    },
  );

  group('test saveSelectedChannel', () {
    test('test saveSelectedChannel with param DOPNativeMetadataItemEntity is empty', () {
      cubit.saveSelectedChannel(<DOPNativeMetadataItemEntity>[]);
      expect(cubit.selectedChannels, <DOPNativeMetadataItemEntity>[]);
    });

    test('test saveSelectedChannel with param DOPNativeMetadataItemEntity have one item', () {
      final List<DOPNativeMetadataItemEntity> zaloList = <DOPNativeMetadataItemEntity>[
        DOPNativeMetadataItemEntity(code: SubscribeChannelConstants.zalo.value)
      ];

      cubit.saveSelectedChannel(zaloList);

      expect(cubit.selectedChannels, zaloList);
    });

    test('test saveSelectedChannel with param DOPNativeMetadataItemEntity is multi item', () {
      cubit.saveSelectedChannel(testChannelsHaveZalo);

      expect(cubit.selectedChannels, testChannelsHaveZalo);
    });
  });

  group('isZaloChannelSelected test', () {
    test('returns false if zalo not present in selected list', () {
      cubit.saveSelectedChannel(testChannelsNotHaveZalo);
      final bool result = cubit.isZaloChannelSelected();

      expect(result, false);
    });
    test('returns false if zalo  present in selected list', () {
      cubit.saveSelectedChannel(testChannelsHaveZalo);
      final bool result = cubit.isZaloChannelSelected();

      expect(result, true);
    });
  });

  group('Test emit zalo selected ', () {
    blocTest<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
      'Emit zalo selected state if zalo is select',
      build: () => cubit,
      setUp: () {
        cubit.saveSelectedChannel(testChannelsHaveZalo);
      },
      act: (DOPNativeSubscribeChannelCubit cubit) {
        cubit.emitZaloSelectedState();
      },
      expect: () => <dynamic>[isA<ZaloChannelSelectedState>()],
    );
    blocTest<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
      'Emit zalo selected state if zalo not select',
      build: () => cubit,
      setUp: () {
        cubit.saveSelectedChannel(testChannelsNotHaveZalo);
      },
      act: (DOPNativeSubscribeChannelCubit cubit) {
        cubit.emitZaloSelectedState();
      },
      expect: () => <dynamic>[isA<ZaloChannelUnSelectedState>()],
    );
  });

  group('test getSelectedChannelName', () {
    test('test select channel have non-value', () {
      cubit.saveSelectedChannel(<DOPNativeMetadataItemEntity>[]);
      final String result = cubit.getSelectedChannelName();
      expect(result, isEmpty);
    });

    test('test select channel have one value', () {
      cubit.saveSelectedChannel(<DOPNativeMetadataItemEntity>[
        const DOPNativeMetadataItemEntity(
          code: 'ZALO',
          name: 'zalo name',
        )
      ]);
      final String result = cubit.getSelectedChannelName();
      expect(result, equals('ZALO'));
    });

    test('test select channel have multi value', () {
      cubit.saveSelectedChannel(<DOPNativeMetadataItemEntity>[
        const DOPNativeMetadataItemEntity(
          code: 'ZALO',
          name: 'zalo name',
        ),
        const DOPNativeMetadataItemEntity(
          code: 'SMS',
          name: 'sms name',
        )
      ]);
      final String result = cubit.getSelectedChannelName();
      expect(result, equals('ZALO,SMS'));
    });
  });

  group('test onChannelSelected', () {
    blocTest<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
      'Emit zalo selected state if zalo is select',
      build: () => cubit,
      act: (DOPNativeSubscribeChannelCubit cubit) {
        cubit.onChannelSelected(testChannelsHaveZalo);
      },
      expect: () => <dynamic>[isA<ZaloChannelSelectedState>()],
    );
    blocTest<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
      'Emit zalo selected state if zalo not select',
      build: () => cubit,
      act: (DOPNativeSubscribeChannelCubit cubit) {
        cubit.onChannelSelected(testChannelsNotHaveZalo);
      },
      expect: () => <dynamic>[isA<ZaloChannelUnSelectedState>()],
    );
  });

  group('test onPhoneValueChanged', () {
    blocTest<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
      'Emit SubmitButtonEnable if phone is valid',
      build: () => cubit,
      act: (DOPNativeSubscribeChannelCubit cubit) {
        cubit.onPhoneValueChanged(phone: '0934567898', isValidPhone: true);
      },
      expect: () => <dynamic>[isA<SubmitButtonEnable>()],
    );

    blocTest<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
      'Emit SubmitButtonDisable if phone is invalid',
      build: () => cubit,
      act: (DOPNativeSubscribeChannelCubit cubit) {
        cubit.onPhoneValueChanged(phone: '0934567898');
      },
      expect: () => <dynamic>[isA<SubmitButtonDisable>()],
    );
  });

  group('test onSelectZaloTypeChanged', () {
    blocTest<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
      'Emit SubmitButtonEnable if selectedType is current',
      build: () => cubit,
      act: (DOPNativeSubscribeChannelCubit cubit) {
        cubit.onSelectZaloTypeChanged(ZaloPhoneSelectType.current);
      },
      expect: () => <dynamic>[isA<SubmitButtonEnable>()],
    );

    blocTest<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
      'Emit SubmitButtonDisable if selectedType is not current',
      build: () => cubit,
      act: (DOPNativeSubscribeChannelCubit cubit) {
        cubit.onSelectZaloTypeChanged(ZaloPhoneSelectType.other);
      },
      expect: () => <dynamic>[isA<SubmitButtonDisable>()],
    );
  });

  group('test cacheZaloTypeSelected', () {
    test('should set zaloPhoneNumber to null if selectType is current', () {
      cubit.contactInfo = cubit.contactInfo.copyWith(zaloPhoneNumber: '123456789');

      cubit.cacheZaloTypeSelected(ZaloPhoneSelectType.current);

      expect(cubit.contactInfo.zaloPhoneNumber, isEmpty);
      expect(cubit.contactInfo.isZaloCurrentPhoneNumber, isTrue);
    });

    test('should not change zaloPhoneNumber if selectType is not current', () {
      cubit.contactInfo = cubit.contactInfo.copyWith(zaloPhoneNumber: '123456789');

      cubit.cacheZaloTypeSelected(ZaloPhoneSelectType.other);

      expect(cubit.contactInfo.zaloPhoneNumber, equals('123456789'));
      expect(cubit.contactInfo.isZaloCurrentPhoneNumber, isFalse);
    });
  });

  group('test isOtherZaloPhoneValid', () {
    test('should return false if zaloPhoneNumber is null', () {
      const DOPNativeContactInfoEntity contactInfo = DOPNativeContactInfoEntity();
      cubit.contactInfo = contactInfo;

      final bool result = cubit.isOtherZaloPhoneValid();

      expect(result, isFalse);
    });

    test('should return false if zaloPhoneNumber is empty', () {
      const DOPNativeContactInfoEntity contactInfo =
          DOPNativeContactInfoEntity(zaloPhoneNumber: '');
      cubit.contactInfo = contactInfo;

      final bool result = cubit.isOtherZaloPhoneValid();

      expect(result, isFalse);
    });

    test('should return false if zaloPhoneNumber is invalid', () {
      const DOPNativeContactInfoEntity contactInfo =
          DOPNativeContactInfoEntity(zaloPhoneNumber: 'invalid_phone');
      cubit.contactInfo = contactInfo;

      final bool result = cubit.isOtherZaloPhoneValid();

      expect(result, isFalse);
    });

    test('should return true if zaloPhoneNumber is valid', () {
      const DOPNativeContactInfoEntity contactInfo =
          DOPNativeContactInfoEntity(zaloPhoneNumber: '0923456789');
      cubit.contactInfo = contactInfo;

      final bool result = cubit.isOtherZaloPhoneValid();

      expect(result, isTrue);
    });
  });

  group('test shouldEnableButton', () {
    test('shouldEnableButton disables button when selected channels are empty', () {
      cubit.saveSelectedChannel(<DOPNativeMetadataItemEntity>[]);
      cubit.shouldEnableButton();

      expect(cubit.state, isA<SubmitButtonDisable>());
    });

    test('shouldEnableButton enables button when Zalo channel not selected but selected have value',
        () {
      cubit.saveSelectedChannel(testChannelsNotHaveZalo);
      cubit.shouldEnableButton();

      expect(cubit.state, isA<SubmitButtonEnable>());
    });

    test('shouldEnableButton enables button when isZaloCurrentPhoneNumber = true', () {
      cubit.saveSelectedChannel(testChannelsHaveZalo);

      cubit.contactInfo = cubit.contactInfo.copyWith(
        isZaloCurrentPhoneNumber: true,
      );

      cubit.shouldEnableButton();

      expect(cubit.state, isA<SubmitButtonEnable>());
    });

    test(
        'shouldEnableButton enables button when isZaloCurrentPhoneNumber = false && zaloPhoneNumber valid',
        () {
      cubit.saveSelectedChannel(testChannelsHaveZalo);

      cubit.contactInfo = cubit.contactInfo.copyWith(
        isZaloCurrentPhoneNumber: false,
        zaloPhoneNumber: '0934567898',
      );

      cubit.shouldEnableButton();

      expect(cubit.state, isA<SubmitButtonEnable>());
    });
    test(
        'shouldEnableButton enables button when isZaloCurrentPhoneNumber = false && zaloPhoneNumber invalid',
        () {
      cubit.saveSelectedChannel(testChannelsHaveZalo);

      cubit.contactInfo = cubit.contactInfo.copyWith(
        isZaloCurrentPhoneNumber: false,
        zaloPhoneNumber: '12934567898',
      );

      cubit.shouldEnableButton();

      expect(cubit.state, isA<SubmitButtonDisable>());
    });
  });
}
