import 'package:evoapp/data/response/dop_native/dop_native_metadata_item_entity.dart';
import 'package:evoapp/feature/dop_native/features/additional_form/widgets/subscribe_channel/cubit/dop_native_subscribe_channel_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeErrorMessage = 'fakeErrorMessage';
  const List<DOPNativeMetadataItemEntity> fakeSubscribeChannel = <DOPNativeMetadataItemEntity>[];

  group('DOPNative Fetch subscribe channel state test', () {
    test('DOPNativeSubscribeChannelInitial should be an instance of DOPNativeSubscribeChannelState',
        () {
      const DOPNativeSubscribeChannelInitial state = DOPNativeSubscribeChannelInitial();
      expect(state, isA<DOPNativeSubscribeChannelState>());
    });

    test('FetchSubscribeChannelLoading should be an instance of DOPNativeSubscribeChannelState',
        () {
      const FetchSubscribeChannelLoading state = FetchSubscribeChannelLoading();
      expect(state, isA<DOPNativeSubscribeChannelState>());
    });

    test('FetchSubscribeChannelError should be an instance of DOPNativeSubscribeChannelState', () {
      final ErrorUIModel errorModel = ErrorUIModel(userMessage: fakeErrorMessage);
      final FetchSubscribeChannelError state = FetchSubscribeChannelError(errorModel);
      expect(state, isA<DOPNativeSubscribeChannelState>());
      expect(state.error, equals(errorModel));
    });

    test('FetchSubscribeChannelSuccess should be an instance of DOPNativeSubscribeChannelState',
        () {
      const FetchSubscribeChannelSuccess state = FetchSubscribeChannelSuccess(fakeSubscribeChannel);
      expect(state, isA<DOPNativeSubscribeChannelState>());
      expect(state.subscribeChannels, isA<List<DOPNativeMetadataItemEntity>>());
    });
  });

  group('DOPNative Zalo account selected state test', () {
    test('ZaloChannelSelectedState should be an instance of DOPNativeSubscribeChannelState', () {
      const ZaloChannelSelectedState state = ZaloChannelSelectedState();
      expect(state, isA<DOPNativeSubscribeChannelState>());
    });

    test('ZaloChannelUnSelectedState should be an instance of DOPNativeSubscribeChannelState', () {
      const ZaloChannelUnSelectedState state = ZaloChannelUnSelectedState();
      expect(state, isA<DOPNativeSubscribeChannelState>());
    });
  });

  group('DOPNative submit button state test', () {
    test('SubmitButtonDisable should be an instance of DOPNativeSubscribeChannelState', () {
      const SubmitButtonDisable state = SubmitButtonDisable();
      expect(state, isA<DOPNativeSubscribeChannelState>());
    });

    test('SubmitButtonEnable should be an instance of DOPNativeSubscribeChannelState', () {
      const SubmitButtonEnable state = SubmitButtonEnable();
      expect(state, isA<DOPNativeSubscribeChannelState>());
    });
  });

  group('DOPNative submit subscribe channel state test', () {
    test('SubmitSubscribeChannelLoading should be an instance of DOPNativeSubscribeChannelState',
        () {
      const SubmitSubscribeChannelLoading state = SubmitSubscribeChannelLoading();
      expect(state, isA<DOPNativeSubscribeChannelState>());
    });

    test('SubmitSubscribeChannelError should be an instance of DOPNativeSubscribeChannelState', () {
      final ErrorUIModel errorModel = ErrorUIModel(userMessage: fakeErrorMessage);
      final SubmitSubscribeChannelError state = SubmitSubscribeChannelError(errorModel);
      expect(state, isA<DOPNativeSubscribeChannelState>());
      expect(state.error, equals(errorModel));
    });

    test('SubmitSubscribeChannelSuccess should be an instance of DOPNativeSubscribeChannelState',
        () {
      const SubmitSubscribeChannelSuccess state = SubmitSubscribeChannelSuccess();
      expect(state, isA<DOPNativeSubscribeChannelState>());
    });
  });
}
