import 'package:evoapp/feature/transaction_history_screen/transaction_history_empty_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:evoapp/widget/empty_data_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEvoImageProvider extends Mock implements CommonImageProvider {}

void main() {
  late Widget widget;
  late CommonImageProvider commonImageProvider;
  late TransactionHistoryEmptyWidget widgetNeedTest;

  setUpAll(() {
    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    commonImageProvider = getIt.get<CommonImageProvider>();

    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  });

  setUp(() {
    widgetNeedTest = const TransactionHistoryEmptyWidget();
    widget = MaterialApp(
      home: Scaffold(
        body: widgetNeedTest,
      ),
    );
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('verify TransactionHistoryEmptyWidget', () {
    testWidgets('renders empty data container with correct text', (WidgetTester tester) async {
      await tester.pumpWidget(widget);

      final Finder emptyDataContainerFinder = find.byType(EmptyDataContainer);
      expect(emptyDataContainerFinder, findsOneWidget);

      final EmptyDataContainer emptyDataContainerWidget = tester.widget(emptyDataContainerFinder);
      expect(emptyDataContainerWidget.assetName, EvoImages.bgTransactionHistoryEmpty);
      expect(emptyDataContainerWidget.text, EvoStrings.transactionHistoryEmptyTittle);

      // Test bottomWidgets
      final List<Widget>? bottomWidgets = emptyDataContainerWidget.bottomWidgets;
      expect(bottomWidgets?.length, 2);

      // Test text widget of bottomWidgets
      final Finder descTextFinder = find.text(EvoStrings.transactionHistoryEmptyDesc);
      expect(descTextFinder, findsOneWidget);
      final Text descTextWidget = tester.widget(descTextFinder);
      final Text bottomTextWidget = bottomWidgets?.last as Text;
      expect(bottomTextWidget, descTextWidget);
      expect(descTextWidget.style, evoTextStyles.bodyMedium(evoColors.textPassive));
      expect(descTextWidget.textAlign, TextAlign.center);

      // Test paddingTop of EmptyDataContainer
      final double expectedPaddingTop = EvoUiUtils().calculateVerticalSpace(
        context: tester.element(find.byType(TransactionHistoryEmptyWidget)),
        heightPercentage: widgetNeedTest.paddingTopPercentage,
      );
      expect(emptyDataContainerWidget.paddingTop, expectedPaddingTop);
    });
  });
}
