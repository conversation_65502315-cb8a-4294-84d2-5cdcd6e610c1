import 'package:evoapp/data/response/payment_method_entity.dart';
import 'package:evoapp/data/response/payment_result_transaction_entity.dart';
import 'package:evoapp/data/response/store_info_entity.dart';
import 'package:evoapp/feature/transaction_history_screen/transaction_list/widget/transaction_history_emi_label_widget.dart';
import 'package:evoapp/feature/transaction_history_screen/transaction_list/widget/transaction_history_payment_item.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../transaction_history_util.dart';

void main() {
  const String fakeThumbnail = 'fake_thumbnail';
  const String fakeAddress = 'fake_address';
  const String fakeStatus = 'processing';
  const String fakeSourceName = 'fake_source_name';
  const String fakeCreateAt = '2023-03-31T16:52:02+07:00';
  const String paymentService = 'emi';
  const String fakeFormatCurrency = 'fake_format_currency';
  const String currencySymbol = 'đ';
  const int fakeAmount = 1000;

  late EvoUtilFunction evoUtilFunction;

  setUpAll(() {
    TransactionHistoryUtil.instance.initSetUpAll();
    evoUtilFunction = getIt.get<EvoUtilFunction>();

    when(() => evoUtilFunction.evoFormatCurrency(
          any(),
          currencySymbol: any(named: 'currencySymbol'),
        )).thenAnswer((_) => fakeFormatCurrency);
  });

  Widget buildMaterialApp(PaymentResultTransactionEntity transaction) {
    return MaterialApp(
      home: Scaffold(
        body: TransactionHistoryPaymentItem(transaction: transaction),
      ),
    );
  }

  testWidgets('verify UI with transaction has emi', (WidgetTester widgetTester) async {
    final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity(
      status: fakeStatus,
      storeInfo: StoreInfoEntity(
        thumbnail: fakeThumbnail,
        address: fakeAddress,
      ),
      paymentMethod: PaymentMethodEntity(
        sourceName: fakeSourceName,
      ),
      userChargeAmount: fakeAmount,
      paymentService: paymentService,
      createdAt: fakeCreateAt,
    );

    await widgetTester.pumpWidget(buildMaterialApp(transaction));

    final Finder finderItemRightContent = find.byType(TransactionHistoryPaymentItem);
    expect(finderItemRightContent, findsOneWidget);

    verify(() => evoUtilFunction.evoFormatCurrency(
          fakeAmount,
          currencySymbol: currencySymbol,
        )).called(1);

    final Finder finderTextAmount = find.text(fakeFormatCurrency);
    expect(finderTextAmount, findsOneWidget);
    final Text textAmount = widgetTester.widget(finderTextAmount);
    expect(textAmount.style, evoTextStyles.h200());

    final Finder finderTextSourceName = find.text(fakeSourceName);
    expect(finderTextSourceName, findsOneWidget);

    final Text textSourceName = widgetTester.widget(finderTextSourceName);
    expect(textSourceName.style, evoTextStyles.bodySmall(color: evoColors.textPassive2));
    expect(textSourceName.textAlign, TextAlign.end);

    final Finder finderTextEmiLabel = find.byType(TransactionHistoryEmiLabelWidget);
    expect(finderTextEmiLabel, findsOneWidget);
  });

  testWidgets('verify UI with transaction has not emi', (WidgetTester widgetTester) async {
    final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity(
      status: fakeStatus,
      storeInfo: StoreInfoEntity(
        thumbnail: fakeThumbnail,
        address: fakeAddress,
      ),
      paymentMethod: PaymentMethodEntity(
        sourceName: fakeSourceName,
      ),
      userChargeAmount: fakeAmount,
      createdAt: fakeCreateAt,
    );

    await widgetTester.pumpWidget(buildMaterialApp(transaction));

    final Finder finderItemRightContent = find.byType(TransactionHistoryPaymentItem);
    expect(finderItemRightContent, findsOneWidget);

    verify(() => evoUtilFunction.evoFormatCurrency(
          fakeAmount,
          currencySymbol: currencySymbol,
        )).called(1);

    final Finder finderTextAmount = find.text(fakeFormatCurrency);
    expect(finderTextAmount, findsOneWidget);
    final Text textAmount = widgetTester.widget(finderTextAmount);
    expect(textAmount.style, evoTextStyles.h200());

    final Finder finderTextSourceName = find.text(fakeSourceName);
    expect(finderTextSourceName, findsOneWidget);

    final Text textSourceName = widgetTester.widget(finderTextSourceName);
    expect(textSourceName.style, evoTextStyles.bodySmall(color: evoColors.textPassive2));
    expect(textSourceName.textAlign, TextAlign.end);

    final Finder finderTextEmiLabel = find.byType(TransactionHistoryEmiLabelWidget);
    expect(finderTextEmiLabel, findsNothing);
  });
}
