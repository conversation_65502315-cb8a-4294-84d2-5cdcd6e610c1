import 'package:evoapp/feature/transaction_history_screen/transaction_list/widget/transaction_history_emi_label_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../transaction_history_util.dart';

void main() {
  setUpAll(() {
    TransactionHistoryUtil.instance.initSetUpAll();
  });

  testWidgets('test UI TransactionHistoryEmiLabelWidget with default value',
      (WidgetTester widgetTester) async {
    await widgetTester.pumpWidget(
      const MaterialApp(
        home: TransactionHistoryEmiLabelWidget(),
      ),
    );

    expect(find.byWidgetPredicate((Widget widget) {
      if (widget is Container) {
        return widget.decoration ==
                BoxDecoration(
                  borderRadius: BorderRadius.circular(100),
                  color: evoColors.transactionListHistoryBgEmiLabel,
                ) &&
            widget.padding == const EdgeInsets.symmetric(horizontal: 8, vertical: 4);
      }
      return false;
    }), findsOneWidget);

    final Finder finderText = find.text(EvoStrings.transactionHistoryEmiLabel);
    expect(finderText, findsOneWidget);

    final Text text = widgetTester.widget(finderText);
    expect(
        text.style,
        evoTextStyles.h100(
          color: evoColors.transactionListHistoryEmiLabel,
        ));
  });

  testWidgets('test UI TransactionHistoryEmiLabelWidget with new UI', (WidgetTester tester) async {
    await tester.pumpWidget(
      const MaterialApp(
        home: TransactionHistoryEmiLabelWidget(isOldUI: false),
      ),
    );

    expect(find.byWidgetPredicate((Widget widget) {
      if (widget is Container) {
        return widget.decoration ==
                BoxDecoration(
                  borderRadius: BorderRadius.circular(100),
                  color: evoColors.transactionListHistoryBgEmiNewLabel,
                ) &&
            widget.padding == const EdgeInsets.symmetric(horizontal: 8, vertical: 4);
      }
      return false;
    }), findsOneWidget);

    final Finder finderText = find.text(EvoStrings.transactionHistoryEmiLabel);
    expect(finderText, findsOneWidget);

    final Text text = tester.widget(finderText);
    expect(
        text.style,
        evoTextStyles.bodySmall(
          color: evoColors.transactionListHistoryEmiNewLabel,
        ));
  });
}
