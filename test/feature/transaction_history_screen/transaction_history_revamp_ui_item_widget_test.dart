import 'package:evoapp/data/response/payment_result_transaction_entity.dart';
import 'package:evoapp/data/response/store_info_entity.dart';
import 'package:evoapp/feature/transaction_history_screen/transaction_list/transaction_history_revamp_ui_item_widget.dart';
import 'package:evoapp/feature/transaction_history_screen/transaction_list/widget/transaction_history_cashback_info_item.dart';
import 'package:evoapp/feature/transaction_history_screen/transaction_list/widget/transaction_history_cashback_record_item.dart';
import 'package:evoapp/feature/transaction_history_screen/transaction_list/widget/transaction_history_revamp_ui_payment_item.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());

    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
          color: any(named: 'color'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  });

  testWidgets('TransactionHistoryRevampUIItemWidget displays correctly',
      (WidgetTester tester) async {
    final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity();

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TransactionHistoryRevampUIItemWidget(
            transaction: transaction,
            onTap: () {},
          ),
        ),
      ),
    );

    expect(find.byType(TransactionHistoryCashbackInfoItem), findsOneWidget);
    expect(find.byType(TransactionHistoryRevampUIPaymentItem), findsOneWidget);

    // Verify padding
    final Padding padding = tester.widget<Padding>(find.byType(Padding).first);
    expect(padding.padding, const EdgeInsets.symmetric(vertical: 12.0));

    // Verify row alignment
    final Row row = tester.widget<Row>(find.byType(Row).first);
    expect(row.crossAxisAlignment, CrossAxisAlignment.start);
  });

  testWidgets('TransactionHistoryRevampUIItemWidget onTap callback is called',
      (WidgetTester tester) async {
    bool onTapCalled = false;

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TransactionHistoryRevampUIItemWidget(
            transaction: null,
            onTap: () {
              onTapCalled = true;
            },
          ),
        ),
      ),
    );

    await tester.tap(find.byType(InkWell));
    await tester.pumpAndSettle();

    expect(onTapCalled, true);
  });

  testWidgets('TransactionHistoryRevampUIItemWidget with cashback transaction type',
      (WidgetTester tester) async {
    final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity(
      type: TransactionType.cashback,
      cashbackAmount: 10000,
      createdAt: '2023-01-01T10:00:00Z',
    );

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TransactionHistoryRevampUIItemWidget(
            transaction: transaction,
            onTap: () {},
          ),
        ),
      ),
    );

    final InkWell inkWell = tester.widget<InkWell>(find.byType(InkWell));
    expect(inkWell.onTap, isNull);

    expect(find.byType(TransactionHistoryCashbackRecordItem), findsOneWidget);
    expect(find.byType(TransactionHistoryCashbackInfoItem), findsNothing);

    expect(find.byType(TransactionHistoryRevampUIPaymentItem), findsOneWidget);

    final TransactionHistoryCashbackRecordItem recordItem =
        tester.widget<TransactionHistoryCashbackRecordItem>(
            find.byType(TransactionHistoryCashbackRecordItem));
    expect(recordItem.onTap, isNotNull);
  });

  testWidgets('TransactionHistoryRevampUIItemWidget with purchase transaction type',
      (WidgetTester tester) async {
    final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity(
      type: TransactionType.purchase,
      orderAmount: 50000,
      createdAt: '2023-01-01T10:00:00Z',
      storeInfo: StoreInfoEntity(
        name: 'Test Store',
      ),
    );

    bool onTapCalled = false;

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TransactionHistoryRevampUIItemWidget(
            transaction: transaction,
            onTap: () {
              onTapCalled = true;
            },
          ),
        ),
      ),
    );

    expect(find.byType(TransactionHistoryCashbackInfoItem), findsOneWidget);
    expect(find.byType(TransactionHistoryCashbackRecordItem), findsNothing);

    expect(find.byType(TransactionHistoryRevampUIPaymentItem), findsOneWidget);

    await tester.tap(find.byType(InkWell));
    await tester.pumpAndSettle();
    expect(onTapCalled, true);
  });

  testWidgets('TransactionHistoryRevampUIItemWidget with null transaction',
      (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TransactionHistoryRevampUIItemWidget(
            transaction: null,
            onTap: () {},
          ),
        ),
      ),
    );

    expect(find.byType(TransactionHistoryCashbackInfoItem), findsOneWidget);
    expect(find.byType(TransactionHistoryRevampUIPaymentItem), findsOneWidget);
    expect(find.byType(TransactionHistoryCashbackRecordItem), findsNothing);

    expect(find.byWidgetPredicate((Widget widget) {
      return widget is SizedBox && widget.width == 20;
    }), findsOneWidget);
  });

  testWidgets('TransactionHistoryRevampUIItemWidget passes transaction to child widgets',
      (WidgetTester tester) async {
    final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity(
      type: TransactionType.purchase,
      orderAmount: 50000,
      userChargeAmount: 45000,
      createdAt: '2023-01-01T10:00:00Z',
    );

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TransactionHistoryRevampUIItemWidget(
            transaction: transaction,
            onTap: () {},
          ),
        ),
      ),
    );

    final TransactionHistoryCashbackInfoItem infoItem =
        tester.widget<TransactionHistoryCashbackInfoItem>(
            find.byType(TransactionHistoryCashbackInfoItem));
    expect(infoItem.transaction, transaction);

    final TransactionHistoryRevampUIPaymentItem paymentItem =
        tester.widget<TransactionHistoryRevampUIPaymentItem>(
            find.byType(TransactionHistoryRevampUIPaymentItem));
    expect(paymentItem.transaction, transaction);
  });
}
