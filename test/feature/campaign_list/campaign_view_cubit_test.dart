import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/campaign_repo.dart';
import 'package:evoapp/data/response/campaign_list_entity.dart';
import 'package:evoapp/feature/campaign_list/campaign_view/campaign_view_cubit.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../constant.dart';
import '../../util/test_util.dart';

class MockCampaignRepo extends Mock implements CampaignRepo {}

void main() {
  late MockCampaignRepo mockCampaignRepo;
  late CampaignViewCubit cubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockCampaignRepo = MockCampaignRepo();
  });

  tearDown(() {
    reset(mockCampaignRepo);
  });

  group('test constructor()', () {
    test('create new instance CampaignViewCubit with CampaignViewLoading state', () {
      cubit = CampaignViewCubit(mockCampaignRepo);

      expect(cubit.state is CampaignViewLoading, isTrue);
    });
  });

  group('test getCampaigns() function', () {
    setUp(() {
      cubit = CampaignViewCubit(mockCampaignRepo);
    });

    blocTest<CampaignViewCubit, CampaignViewState>(
      'test getCampaigns is success',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('campaigns_information.json');

        when(() => mockCampaignRepo.getOffers(
            flowType: FlowType.offers, mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
          return CampaignListEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
        });
      },
      build: () => cubit,
      act: (CampaignViewCubit cubit) async => await cubit.getCampaigns(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CampaignViewLoading>(),
        isA<CampaignViewInfoSuccess>().having(
            (CampaignViewInfoSuccess dataLoaded) =>
                dataLoaded.campaignListEntity?.campaigns?.isNotEmpty,
            'test getCampaigns with campaigns is not empty',
            isTrue),
      ],
      verify: (_) {
        expect(
            verify(() => mockCampaignRepo.getOffers(
                flowType: FlowType.offers,
                mockConfig: captureAny(named: 'mockConfig'))).captured.single,
            isA<MockConfig>()
                .having((MockConfig p0) => p0.enable, 'mockConfig with enable = false', false));
      },
    );

    blocTest<CampaignViewCubit, CampaignViewState>(
      'test getCampaigns is fail',
      setUp: () async {
        when(() => mockCampaignRepo.getOffers(
            flowType: FlowType.offers, mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
          return CampaignListEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: <String, dynamic>{
            'status_code': CommonHttpClient.BAD_REQUEST,
          }));
        });
      },
      build: () => cubit,
      act: (CampaignViewCubit cubit) async => await cubit.getCampaigns(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CampaignViewLoading>(),
        isA<CampaignViewFail>().having((CampaignViewFail error) => error.error?.statusCode,
            'verify statusCode is CommonHttpClient.BAD_REQUEST', CommonHttpClient.BAD_REQUEST),
      ],
      verify: (_) {
        expect(
            verify(() => mockCampaignRepo.getOffers(
                flowType: FlowType.offers,
                mockConfig: captureAny(named: 'mockConfig'))).captured.single,
            isA<MockConfig>()
                .having((MockConfig p0) => p0.enable, 'mockConfig with enable = false', false));
      },
    );
  });
}
