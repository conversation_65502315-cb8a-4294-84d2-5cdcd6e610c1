import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:evoapp/feature/payment/promotion/payment_promotion_empty_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:evoapp/widget/empty_data_container.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../util/flutter_test_config.dart';

class MockEvoUiUtils extends Mock implements EvoUiUtils {}

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

void main() {
  late MockEvoUiUtils mockEvoUiUtils;
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    registerFallbackValue(MockBuildContext());

    mockEvoUiUtils = MockEvoUiUtils();
    EvoUiUtils.setInstanceForTesting(mockEvoUiUtils);

    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();

    getIt.registerLazySingleton<CommonImageProvider>(() => MockCommonImageProvider());
    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    when(
      () => mockCommonImageProvider.asset(
        captureAny(),
        width: captureAny<double?>(named: 'width'),
        height: captureAny<double?>(named: 'height'),
        color: any(named: 'color'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'),
        cacheWidth: any(named: 'cacheWidth'),
        cacheHeight: any(named: 'cacheHeight'),
        package: any(named: 'package'),
      ),
    ).thenAnswer((_) => Container());
  });

  setUp(() {
    when(() => mockEvoUiUtils.calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(20.0);
  });

  tearDown(() {
    reset(mockEvoUiUtils);
  });

  Widget buildTestableWidget(Widget widget) {
    return MaterialApp(
      home: Scaffold(body: widget),
    );
  }

  group('PaymentPromotionEmptyWidget', () {
    testWidgets('renders correctly with all elements', (WidgetTester tester) async {
      await tester.pumpWidget(buildTestableWidget(const PaymentPromotionEmptyWidget()));

      expect(find.byType(EmptyDataContainer), findsOneWidget);

      expect(find.text(EvoStrings.paymentPromotionEmptyTitle), findsOneWidget);
      expect(find.text(EvoStrings.paymentPromotionEmptyDesc), findsOneWidget);
      expect(find.text(EvoStrings.close), findsOneWidget);
    });

    testWidgets('passes correct parameters to EmptyDataContainer', (WidgetTester tester) async {
      await tester.pumpWidget(buildTestableWidget(const PaymentPromotionEmptyWidget()));

      final EmptyDataContainer emptyDataContainer =
          tester.widget<EmptyDataContainer>(find.byType(EmptyDataContainer));

      expect(emptyDataContainer.assetName, equals(EvoImages.imgNoPromotion));
      expect(emptyDataContainer.text, equals(EvoStrings.paymentPromotionEmptyTitle));
      expect(emptyDataContainer.bottomWidgets?.length, equals(2));
    });

    testWidgets('close button triggers onClose callback when pressed', (WidgetTester tester) async {
      bool closeButtonPressed = false;

      await tester.pumpWidget(buildTestableWidget(PaymentPromotionEmptyWidget(
        onClose: () => closeButtonPressed = true,
      )));

      await tester.tap(find.byType(CommonButton));
      await tester.pump();

      expect(closeButtonPressed, true);
    });

    testWidgets('close button does nothing when onClose is null', (WidgetTester tester) async {
      final bool closeButtonPressed = false;

      await tester.pumpWidget(buildTestableWidget(PaymentPromotionEmptyWidget()));

      await tester.tap(find.byType(CommonButton));
      await tester.pump();

      expect(closeButtonPressed, false);
    });

    testWidgets('button has correct style', (WidgetTester tester) async {
      await tester.pumpWidget(buildTestableWidget(const PaymentPromotionEmptyWidget()));

      final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
      expect(button.isWrapContent, isFalse);
      expect(button.child, isA<Text>());
    });
  });
}
