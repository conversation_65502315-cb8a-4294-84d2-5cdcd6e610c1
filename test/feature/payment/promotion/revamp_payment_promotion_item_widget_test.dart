import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/payment/promotion/model/payment_promotion_model.dart';
import 'package:evoapp/feature/payment/promotion/revamp_payment_promotion_item_widget.dart';
import 'package:evoapp/model/promotion_status_ui_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:evoapp/resources/resources.dart';

import '../../../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterButtonStyle();
    getIt.registerLazySingleton(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<CommonImageProvider>(() => CommonImageProviderImpl());
  });

  testWidgets('renders correctly when promotion is selected', (WidgetTester tester) async {
    final PaymentPromotionModel mockItem = PaymentPromotionModel(
      voucher: VoucherEntity(title: 'Selected Promotion', isQualified: true),
      isSelected: true,
    );

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: RevampPaymentPromotionItemWidget(
            item: mockItem,
            onUnSelected: () {},
          ),
        ),
      ),
    );

    // Assert
    expect(find.text('Selected Promotion'), findsOneWidget);
    expect(find.text(EvoStrings.paymentUnSelectPromotionButton), findsOneWidget);
  });

  testWidgets('renders correctly when promotion is not qualified', (WidgetTester tester) async {
    final PaymentPromotionModel mockItem = PaymentPromotionModel(
      voucher: VoucherEntity(
        title: 'Unqualified Promotion',
        isQualified: false,
      ),
    );

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: RevampPaymentPromotionItemWidget(
            item: mockItem,
          ),
        ),
      ),
    );

    // Assert
    expect(find.text('Unqualified Promotion'), findsOneWidget);
    expect(find.text(EvoStrings.paymentPromotionNotQualified), findsOneWidget);
  });

  testWidgets('renders correctly when promotion cannot be selected', (WidgetTester tester) async {
    final PaymentPromotionModel mockItem = PaymentPromotionModel(
      voucher: VoucherEntity(title: 'Selectable Promotion', isQualified: false),
      isSelected: true,
    );

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: RevampPaymentPromotionItemWidget(
            item: mockItem,
            onSelected: () {},
          ),
        ),
      ),
    );

    expect(find.text('Selectable Promotion'), findsOneWidget);
    expect(find.text(EvoStrings.paymentUnSelectPromotionButton), findsOneWidget);

    final Container container = tester.widget<Container>(find.byType(Container).first);
    final BoxDecoration decoration = container.decoration as BoxDecoration;
    expect(decoration.border?.bottom.color, evoColors.promotionItemUnqualifiedBorder);
  });

  testWidgets('renders correctly when promotion can be selected', (WidgetTester tester) async {
    final PaymentPromotionModel mockItem = PaymentPromotionModel(
      voucher: VoucherEntity(title: 'Selectable Promotion', isQualified: true),
      isCanSelected: true,
    );

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: RevampPaymentPromotionItemWidget(
            item: mockItem,
            onSelected: () {},
          ),
        ),
      ),
    );

    // Assert
    expect(find.text('Selectable Promotion'), findsOneWidget);
    expect(find.text(EvoStrings.paymentSelectPromotionButton), findsOneWidget);
  });

  testWidgets('calls onViewDetail when tapped', (WidgetTester tester) async {
    bool tapped = false;
    final PaymentPromotionModel mockItem = PaymentPromotionModel(
      voucher: VoucherEntity(title: 'Selectable Promotion'),
    );

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: RevampPaymentPromotionItemWidget(
            item: mockItem,
            onViewDetail: () {
              tapped = true;
            },
          ),
        ),
      ),
    );

    await tester.tap(find.text('Selectable Promotion'));
    await tester.pumpAndSettle();

    expect(tapped, isTrue);
  });

  testWidgets('buildExpiredTimeWidget()', (WidgetTester tester) async {
    bool tapped = false;
    final PaymentPromotionModel mockItem = PaymentPromotionModel(
      voucher: VoucherEntity(title: 'Selectable Promotion'),
    );
    final PromotionStatusUIModel promotionStatusUIModel = PromotionStatusUIModel(
      color: Colors.red,
      title: 'fake_title',
      hasOpacityTitle: true,
    );

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: RevampPaymentPromotionItemWidget(
            item: mockItem,
            onViewDetail: () {
              tapped = true;
            },
            promotionStatusUIModel: promotionStatusUIModel,
          ),
        ),
      ),
    );
    await tester.pump();
    expect(find.text('fake_title'), findsOneWidget);

    await tester.tap(find.text('Selectable Promotion'));
    await tester.pumpAndSettle();

    expect(tapped, true);
  });

  testWidgets('buildCanNotSelectWidget()', (WidgetTester tester) async {
    final PaymentPromotionModel mockItem = PaymentPromotionModel(
      voucher: VoucherEntity(
        title: 'Selectable Promotion',
        isQualified: true,
      ),
    );
    final PromotionStatusUIModel promotionStatusUIModel = PromotionStatusUIModel(
      color: Colors.red,
      title: 'fake_title',
      hasOpacityTitle: true,
    );

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: RevampPaymentPromotionItemWidget(
            item: mockItem,
            promotionStatusUIModel: promotionStatusUIModel,
          ),
        ),
      ),
    );
    await tester.pump();

    expect(find.text(EvoStrings.paymentPromotionCanNotSelected), findsOneWidget);
  });
}
