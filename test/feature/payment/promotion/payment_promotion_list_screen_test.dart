import 'dart:async';

import 'package:evoapp/data/repository/campaign_repo.dart';
import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/emi_tenor_offer_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/data/response/update_order_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/appsflyer/one_link_utils.dart';
import 'package:evoapp/feature/deep_link/deep_link_utils.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/payment/base_page_payment/cubit/update_order_cubit.dart';
import 'package:evoapp/feature/payment/promotion/bloc/payment_promotion_cubit.dart';
import 'package:evoapp/feature/payment/promotion/model/payment_promotion_model.dart';
import 'package:evoapp/feature/payment/promotion/payment_promotion_empty_widget.dart';
import 'package:evoapp/feature/payment/promotion/payment_promotion_item_widget.dart';
import 'package:evoapp/feature/payment/promotion/payment_promotion_list_screen.dart';
import 'package:evoapp/feature/payment/promotion/revamp_payment_promotion_item_widget.dart';
import 'package:evoapp/model/evo_action_model.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_action_handler.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_common_package/widget/refreshable_view.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockBuildContext extends Mock implements BuildContext {}

class MockPaymentPromotionListPopupCallback extends Mock
    implements PaymentPromotionListPopupCallback {}

class MockPaymentPromotionCubit extends Mock implements PaymentPromotionCubit {}

class MockUpdateOrderCubit extends Mock implements UpdateOrderCubit {}

class MockCampaignRepo extends Mock implements CampaignRepo {}

class MockCheckOutRepo extends Mock implements CheckOutRepo {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockEvoUiUtils extends Mock implements EvoUiUtils {}

class TestPaymentPromotionListScreen extends PaymentPromotionListScreen {
  const TestPaymentPromotionListScreen({
    required this.cubit,
    required this.updateOrderCubit,
    super.arg,
    super.key,
  });

  final PaymentPromotionCubit cubit;
  final UpdateOrderCubit updateOrderCubit;

  Future<void> showBottomSheet(
    BuildContext context, {
    required OrderSessionEntity orderSession,
    required VoucherEntity currentVoucherSelected,
    required PaymentPromotionListPopupCallback callback,
  }) {
    return showModalBottomSheet<void>(
      context: context,
      builder: (BuildContext context) {
        return TestPaymentPromotionListScreen(
          cubit: cubit,
          updateOrderCubit: updateOrderCubit,
          arg: PaymentPromotionArg(
            orderSession: orderSession,
            currentVoucherSelected: currentVoucherSelected,
            callback: callback,
          ),
        );
      },
    );
  }

  @override
  // ignore: no_logic_in_create_state
  TestPaymentPromotionListScreenState createState() => TestPaymentPromotionListScreenState(
        cubit: cubit,
        updateOrderCubit: updateOrderCubit,
      );
}

class TestPaymentPromotionListScreenState extends PaymentPromotionListScreenState {
  TestPaymentPromotionListScreenState({
    required this.cubit,
    required this.updateOrderCubit,
  });

  @override
  // ignore: overridden_fields
  final PaymentPromotionCubit cubit;

  @override
  // ignore: overridden_fields
  final UpdateOrderCubit updateOrderCubit;
}

void main() {
  late FeatureToggle mockFeatureToggle;
  late EvoSnackBar mockEvoSnackBar;
  late CommonImageProvider mockCommonImageProvider;
  late DeepLinkUtils deepLinkUtils;
  late OneLinkUtils oneLinkUtils;
  late EvoUiUtils mockEvoUiUtils;

  const String fakeOrderStatus = 'fake_order_id';
  final OrderSessionEntity mockOrderSessionEntity = OrderSessionEntity(
    id: fakeOrderStatus,
  );
  const int fakeVoucherId = 1;
  final VoucherEntity mockVoucherEntity = VoucherEntity(
    id: fakeVoucherId,
    action: ActionEntity(
      args: ArgsEntity(
        link: 'fake_url',
      ),
    ),
  );
  const String fakeTenorId = 'fake_tenor_id';
  final EmiTenorOfferEntity mockEmiTenorOfferEntity = EmiTenorOfferEntity(
    id: fakeTenorId,
  );
  final EmiPackageEntity mockEmiPackageEntity = EmiPackageEntity(
    offer: mockEmiTenorOfferEntity,
  );
  final PaymentPromotionListPopupCallback mockCallback = MockPaymentPromotionListPopupCallback();

  final StreamController<PaymentPromotionState> paymentPromotionStreamController =
      StreamController<PaymentPromotionState>.broadcast();
  final StreamController<UpdateOrderState> updateOrderStreamController =
      StreamController<UpdateOrderState>.broadcast();

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(MockBuildContext());
    registerFallbackValue(SnackBarType.error);
    registerFallbackValue(EvoActionModel());
    registerFallbackValue(EvoDialogId.expiredOrderBottomSheet);

    initConfigEvoPageStateBase();

    setUtilsMockInstanceForTesting();

    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());
    mockFeatureToggle = getIt<FeatureToggle>();

    getIt.registerLazySingleton<CampaignRepo>(() => MockCampaignRepo());
    getIt.registerLazySingleton<CheckOutRepo>(() => MockCheckOutRepo());

    getIt.registerLazySingleton<EvoSnackBar>(() => MockEvoSnackBar());
    mockEvoSnackBar = getIt.get<EvoSnackBar>();

    EvoActionHandler.setInstanceForTesting(MockEvoActionHandler());

    getIt.registerSingleton<DeepLinkUtils>(MockDeepLinkUtils());
    deepLinkUtils = getIt.get<DeepLinkUtils>();

    getIt.registerSingleton<OneLinkUtils>(MockOneLinkUtils());
    oneLinkUtils = getIt.get<OneLinkUtils>();

    when(() => mockEvoSnackBar.show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInSec: any(named: 'durationInSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).thenAnswer((Invocation invocation) async {
      return true;
    });

    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    when(
      () => mockCommonImageProvider.asset(
        captureAny(),
        width: captureAny<double?>(named: 'width'),
        height: captureAny<double?>(named: 'height'),
        color: any(named: 'color'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'),
        cacheWidth: any(named: 'cacheWidth'),
        cacheHeight: any(named: 'cacheHeight'),
        package: any(named: 'package'),
      ),
    ).thenAnswer((_) => Container());

    EvoDialogHelper.setInstanceForTesting(MockEvoDialogHelper());

    when(() => EvoDialogHelper().showDialogBottomSheet(
          title: any(named: 'title'),
          content: any(named: 'content'),
          dialogId: any(named: 'dialogId'),
          buttonListOrientation: any(named: 'buttonListOrientation'),
          isDismissible: any(named: 'isDismissible'),
          textPositive: any(named: 'textPositive'),
          positiveButtonStyle: any(named: 'positiveButtonStyle'),
          contentTextStyle: any(named: 'contentTextStyle'),
          onClickPositive: any(named: 'onClickPositive'),
          textNegative: any(named: 'textNegative'),
          negativeButtonStyle: any(named: 'negativeButtonStyle'),
          onClickNegative: any(named: 'onClickNegative'),
          header: any(named: 'header'),
        )).thenAnswer((_) async => Future<void>.value());

    mockEvoUiUtils = MockEvoUiUtils();
    EvoUiUtils.setInstanceForTesting(mockEvoUiUtils);
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    when(() => mockFeatureToggle.enableRevampUiFeature).thenReturn(true);
    when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(true);

    when(() => deepLinkUtils.getRegExpOfDeeplink())
        .thenReturn(RegExp(r'^evoappvn://mobile/deeplinking(?:/[\w-]*)?(?:\?.*)?$'));

    when(() => oneLinkUtils.getRegExpOfOneLink())
        .thenReturn(RegExp(r'^https://(www\.)?evoappvn-stag\.onelink\.me(?:/.*)?(?:\?.*)?$'));

    when(() => mockCallback.hasValidVoucherChange(any())).thenAnswer((_) async {
      return Future<void>.value();
    });

    when(() => mockEvoUiUtils.calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(20.0);

    when(() => mockEvoUiUtils.showHudLoading(loadingText: any(named: 'loadingText')))
        .thenAnswer((_) {
      return Future<void>.value();
    });

    when(() => mockEvoUiUtils.hideHudLoading()).thenAnswer((_) {
      return Future<void>.value();
    });

    when(() => mockCallback.onPromotionSelectionError(
          any(),
          orderSession: any(named: 'orderSession'),
          voucher: any(named: 'voucher'),
        )).thenAnswer((_) async {
      return Future<void>.value();
    });
  });

  tearDown(() {
    reset(mockEvoUiUtils);
  });

  tearDownAll(() {
    paymentPromotionStreamController.close();
    updateOrderStreamController.close();
  });

  group('PaymentPromotionArg tests', () {
    test('PaymentPromotionArg initializes correctly', () {
      final PaymentPromotionArg arg = PaymentPromotionArg(
        orderSession: mockOrderSessionEntity,
        currentVoucherSelected: mockVoucherEntity,
        callback: mockCallback,
      );

      expect(arg.orderSession, mockOrderSessionEntity);
      expect(arg.currentVoucherSelected, mockVoucherEntity);
      expect(arg.callback, mockCallback);
    });
  });

  group('PaymentPromotionResultArg tests', () {
    test('PaymentPromotionResultArg initializes correctly', () {
      final PaymentPromotionResultArg arg = PaymentPromotionResultArg(
        orderSession: mockOrderSessionEntity,
        currentVoucherSelected: mockVoucherEntity,
        emiPackage: mockEmiPackageEntity,
      );

      expect(arg.orderSession, mockOrderSessionEntity);
      expect(arg.currentVoucherSelected, mockVoucherEntity);
      expect(arg.emiPackage, mockEmiPackageEntity);
    });
  });

  Future<void> pumpScreen(
    WidgetTester tester, {
    required PaymentPromotionCubit cubit,
    required UpdateOrderCubit updateOrderCubit,
    PaymentPromotionArg? arg,
  }) async {
    final Widget screen = MaterialApp(
      navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
      scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
      home: Scaffold(
        body: TestPaymentPromotionListScreen(
          cubit: cubit,
          updateOrderCubit: updateOrderCubit,
          arg: arg,
        ),
      ),
    );

    when(() => mockNavigatorContext.widget).thenReturn(screen);

    await tester.runAsync(() async {
      await tester.pumpWidget(screen);
    });
  }

  test('routeSettings returns correct route name', () {
    final PaymentPromotionListScreen screen = PaymentPromotionListScreen();
    expect(screen.routeSettings.name, equals(Screen.paymentPromotionListScreen.routeName));
  });

  group('PaymentPromotionListScreen tests', () {
    late PaymentPromotionCubit mockPaymentPromotionCubit;
    late UpdateOrderCubit mockUpdateOrderCubit;

    setUp(() {
      registerFallbackValue(UpdateOrderSuccess());
      mockPaymentPromotionCubit = MockPaymentPromotionCubit();
      mockUpdateOrderCubit = MockUpdateOrderCubit();

      when(() => mockPaymentPromotionCubit.getPromotion(any(),
          isShowLoading: any(named: 'isShowLoading'))).thenAnswer((_) async {});

      when(() => mockUpdateOrderCubit.updateSelectedVoucher(
            selectedVoucher: any(named: 'selectedVoucher'),
            orderSession: any(named: 'orderSession'),
            timeOutInSec: any(named: 'timeOutInSec'),
          )).thenAnswer((_) async => Future<void>.value());

      when(() => mockPaymentPromotionCubit.stream)
          .thenAnswer((_) => paymentPromotionStreamController.stream);
      when(() => mockPaymentPromotionCubit.state).thenAnswer((_) => PaymentPromotionInitial());
      when(() => mockPaymentPromotionCubit.close()).thenAnswer((_) async {});

      when(() => mockUpdateOrderCubit.stream).thenAnswer((_) => updateOrderStreamController.stream);
      when(() => mockUpdateOrderCubit.state).thenAnswer((_) => UpdateOrderInitial());
      when(() => mockUpdateOrderCubit.close()).thenAnswer((_) async {});
    });

    testWidgets('screens displays correctly', (WidgetTester tester) async {
      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
          currentVoucherSelected: mockVoucherEntity,
          callback: mockCallback,
        ),
      );

      await tester.pump();

      final TestPaymentPromotionListScreenState state = tester.state(
        find.byType(TestPaymentPromotionListScreen),
      );

      state.updateOrderWithVoucher(selectedVoucher: mockVoucherEntity);

      verify(() => mockUpdateOrderCubit.updateSelectedVoucher(
            selectedVoucher: mockVoucherEntity,
            orderSession: mockOrderSessionEntity,
            timeOutInSec: any(named: 'timeOutInSec'),
          )).called(1);
    });

    testWidgets('screens showBottomSheet correctly', (WidgetTester tester) async {
      final TestPaymentPromotionListScreen testPaymentPromotionListScreen =
          TestPaymentPromotionListScreen(
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
      );

      await tester.runAsync(() async {
        await tester.pumpWidget(MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: Scaffold(
            body: Builder(builder: (BuildContext context) {
              return TextButton(
                onPressed: () {
                  testPaymentPromotionListScreen.showBottomSheet(
                    context,
                    orderSession: mockOrderSessionEntity,
                    currentVoucherSelected: mockVoucherEntity,
                    callback: mockCallback,
                  );
                },
                child: const Text('Show Sheet'),
              );
            }),
          ),
        ));
      });

      await tester.tap(find.text('Show Sheet'));
      await tester.pumpAndSettle();

      final TestPaymentPromotionListScreenState state = tester.state(
        find.byType(TestPaymentPromotionListScreen),
      );

      state.updateOrderWithVoucher(selectedVoucher: mockVoucherEntity);

      verify(() => mockUpdateOrderCubit.updateSelectedVoucher(
            selectedVoucher: mockVoucherEntity,
            orderSession: mockOrderSessionEntity,
            timeOutInSec: any(named: 'timeOutInSec'),
          )).called(1);
    });

    testWidgets('Handles UpdateOrderSuccess with selected voucher', (WidgetTester tester) async {
      when(() => mockUpdateOrderCubit.stream).thenAnswer(
        (_) => Stream<UpdateOrderSuccess>.value(
          UpdateOrderSuccess(
            orderSession: mockOrderSessionEntity,
            selectedVoucher: mockVoucherEntity,
          ),
        ),
      );

      when(() => mockUpdateOrderCubit.emit(any())).thenAnswer((_) => Future<void>.value());

      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
          callback: mockCallback,
        ),
      );

      mockUpdateOrderCubit.emit(UpdateOrderSuccess(
        orderSession: mockOrderSessionEntity,
        selectedVoucher: mockVoucherEntity,
      ));

      await tester.pump();

      verify(() => mockCallback.onApplyVoucherChange(any(
            that: isA<PaymentPromotionResultArg>()
                .having((PaymentPromotionResultArg arg) => arg.orderSession, 'orderSession',
                    mockOrderSessionEntity)
                .having((PaymentPromotionResultArg arg) => arg.currentVoucherSelected,
                    'currentVoucherSelected', mockVoucherEntity),
          ))).called(1);
    });

    testWidgets('Handles UpdateOrderSuccess without selected voucher', (WidgetTester tester) async {
      when(() => mockUpdateOrderCubit.stream).thenAnswer(
        (_) => Stream<UpdateOrderSuccess>.value(
          UpdateOrderSuccess(
            orderSession: mockOrderSessionEntity,
          ),
        ),
      );

      when(() => mockUpdateOrderCubit.emit(any())).thenAnswer((_) => Future<void>.value());

      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
          callback: mockCallback,
        ),
      );

      mockUpdateOrderCubit.emit(UpdateOrderSuccess(
        orderSession: mockOrderSessionEntity,
      ));

      await tester.pump();

      verify(() => mockPaymentPromotionCubit.updateUnselectedVoucher()).called(1);
    });

    testWidgets('Handles UpdateOrderTimeout', (WidgetTester tester) async {
      const String fakeErrorMsg = 'fake_error_msg';
      when(() => mockUpdateOrderCubit.stream).thenAnswer(
        (_) => Stream<UpdateOrderTimeout>.value(
          UpdateOrderTimeout(fakeErrorMsg),
        ),
      );

      when(() => mockUpdateOrderCubit.emit(any())).thenAnswer((_) => Future<void>.value());

      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
          callback: mockCallback,
        ),
      );

      mockUpdateOrderCubit.emit(UpdateOrderTimeout(fakeErrorMsg));

      await tester.pump();

      verify(() => mockEvoSnackBar.show(
            fakeErrorMsg,
            typeSnackBar: SnackBarType.warning,
            durationInSec: any(named: 'durationInSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    testWidgets('Handles UpdateOrderError', (WidgetTester tester) async {
      const String fakeErrorMsg = 'fake_error_msg';
      when(() => mockUpdateOrderCubit.stream).thenAnswer(
        (_) => Stream<UpdateOrderError>.value(
          UpdateOrderError(ErrorUIModel(userMessage: fakeErrorMsg)),
        ),
      );

      when(() => mockUpdateOrderCubit.emit(any())).thenAnswer((_) => Future<void>.value());

      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
          callback: mockCallback,
        ),
      );

      mockUpdateOrderCubit.emit(UpdateOrderError(ErrorUIModel(userMessage: fakeErrorMsg)));

      await tester.pump();

      verify(() => mockEvoSnackBar.show(
            fakeErrorMsg,
            typeSnackBar: SnackBarType.error,
            durationInSec: any(named: 'durationInSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    group('verify UpdateOrderInvalidVoucher', () {
      testWidgets('verdict is Unknown', (WidgetTester tester) async {
        const String fakeErrorMsg = 'fake_error_msg';
        when(() => mockUpdateOrderCubit.stream).thenAnswer(
          (_) => Stream<UpdateOrderInvalidVoucher>.value(
            UpdateOrderInvalidVoucher(
              error: ErrorUIModel(userMessage: fakeErrorMsg, verdict: 'Unknown'),
            ),
          ),
        );

        when(() => mockUpdateOrderCubit.emit(any())).thenAnswer((_) => Future<void>.value());

        await pumpScreen(
          tester,
          cubit: mockPaymentPromotionCubit,
          updateOrderCubit: mockUpdateOrderCubit,
          arg: PaymentPromotionArg(
            orderSession: mockOrderSessionEntity,
            callback: mockCallback,
          ),
        );

        mockUpdateOrderCubit.emit(UpdateOrderInvalidVoucher(
            error: ErrorUIModel(userMessage: fakeErrorMsg, verdict: 'Unknown')));

        await tester.pump();

        verify(() => mockEvoSnackBar.show(
              fakeErrorMsg,
              typeSnackBar: SnackBarType.error,
              durationInSec: any(named: 'durationInSec'),
              description: any(named: 'description'),
              marginBottomRatio: any(named: 'marginBottomRatio'),
            )).called(1);
      });

      testWidgets('verdict is ${UpdateOrderEntity.verdictPromotionDuplicate}',
          (WidgetTester tester) async {
        const String fakeErrorMsg = 'fake_error_msg';
        when(() => mockUpdateOrderCubit.stream).thenAnswer(
          (_) => Stream<UpdateOrderInvalidVoucher>.value(
            UpdateOrderInvalidVoucher(
              error: ErrorUIModel(
                userMessage: fakeErrorMsg,
                verdict: UpdateOrderEntity.verdictPromotionDuplicate,
              ),
            ),
          ),
        );

        when(() => mockUpdateOrderCubit.emit(any())).thenAnswer((_) => Future<void>.value());

        await pumpScreen(
          tester,
          cubit: mockPaymentPromotionCubit,
          updateOrderCubit: mockUpdateOrderCubit,
          arg: PaymentPromotionArg(
            orderSession: mockOrderSessionEntity,
            callback: mockCallback,
          ),
        );

        mockUpdateOrderCubit.emit(
          UpdateOrderInvalidVoucher(
            error: ErrorUIModel(
              userMessage: fakeErrorMsg,
              verdict: UpdateOrderEntity.verdictPromotionDuplicate,
            ),
          ),
        );

        await tester.pump();

        verify(() => mockCallback.hasValidVoucherChange(any())).called(1);
        verify(() => mockCallback.onPromotionSelectionError(
              any(),
              orderSession: any(named: 'orderSession'),
              voucher: any(named: 'voucher'),
            )).called(1);
      });
    });

    testWidgets('Handles UpdateOrderLoading', (WidgetTester tester) async {
      when(() => mockUpdateOrderCubit.stream).thenAnswer(
        (_) => Stream<UpdateOrderLoading>.value(
          UpdateOrderLoading(),
        ),
      );

      when(() => mockUpdateOrderCubit.emit(any())).thenAnswer((_) => Future<void>.value());

      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
          callback: mockCallback,
        ),
      );

      mockUpdateOrderCubit.emit(UpdateOrderLoading());

      await tester.pump();

      verify(() => mockEvoUiUtils.showHudLoading(loadingText: EvoStrings.hubLoadingText)).called(1);
    });

    testWidgets('Handles success states correctly with enableRevampUiFeature is true',
        (WidgetTester tester) async {
      when(() => EvoActionHandler().handle(
            any(),
            arg: any(named: 'arg'),
          )).thenAnswer((_) async {
        return true;
      });

      final PaymentPromotionSuccess successState = PaymentPromotionSuccess(
          vouchers: <PaymentPromotionModel>[PaymentPromotionModel(voucher: mockVoucherEntity)]);

      when(() => mockPaymentPromotionCubit.stream)
          .thenAnswer((_) => Stream<PaymentPromotionSuccess>.value(successState));
      when(() => mockPaymentPromotionCubit.state).thenReturn(successState);

      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
        ),
      );

      await tester.pump();

      final Finder revampFinder = find.byType(RevampPaymentPromotionItemWidget);
      expect(revampFinder, findsOneWidget);
      expect(find.byType(PaymentPromotionItemWidget), findsNothing);

      final RevampPaymentPromotionItemWidget revampWidget =
          tester.widget<RevampPaymentPromotionItemWidget>(revampFinder);
      revampWidget.onViewDetail?.call();

      verify(() => EvoActionHandler().handle(any(), arg: any(named: 'arg'))).called(1);

      revampWidget.onSelected?.call();

      verify(() => mockUpdateOrderCubit.updateSelectedVoucher(
            orderSession: mockOrderSessionEntity,
            selectedVoucher: mockVoucherEntity,
            timeOutInSec: any(named: 'timeOutInSec'),
          )).called(1);

      revampWidget.onUnSelected?.call();
      verify(() => mockUpdateOrderCubit.updateSelectedVoucher(
            orderSession: mockOrderSessionEntity,
            timeOutInSec: any(named: 'timeOutInSec'),
          )).called(1);
    });

    testWidgets('Handles success states correctly with enableRevampUiFeature is false',
        (WidgetTester tester) async {
      when(() => mockFeatureToggle.enableRevampUiFeature).thenReturn(false);

      final PaymentPromotionSuccess successState = PaymentPromotionSuccess(
          vouchers: <PaymentPromotionModel>[PaymentPromotionModel(voucher: mockVoucherEntity)]);

      when(() => mockPaymentPromotionCubit.stream)
          .thenAnswer((_) => Stream<PaymentPromotionSuccess>.value(successState));
      when(() => mockPaymentPromotionCubit.state).thenReturn(successState);

      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
        ),
      );

      await tester.pump();

      expect(find.byType(RevampPaymentPromotionItemWidget), findsNothing);
      final Finder promotionItemFinder = find.byType(PaymentPromotionItemWidget);
      expect(promotionItemFinder, findsOneWidget);

      final PaymentPromotionItemWidget promotionItemWidget =
          tester.widget<PaymentPromotionItemWidget>(promotionItemFinder);
      promotionItemWidget.onViewDetail?.call();

      verify(() => EvoActionHandler().handle(any(), arg: any(named: 'arg'))).called(1);

      promotionItemWidget.onSelected?.call();

      verify(() => mockUpdateOrderCubit.updateSelectedVoucher(
            orderSession: mockOrderSessionEntity,
            selectedVoucher: mockVoucherEntity,
            timeOutInSec: any(named: 'timeOutInSec'),
          )).called(1);

      promotionItemWidget.onUnSelected?.call();
      verify(() => mockUpdateOrderCubit.updateSelectedVoucher(
            orderSession: mockOrderSessionEntity,
            timeOutInSec: any(named: 'timeOutInSec'),
          )).called(1);
    });

    testWidgets('Handles success states correctly with without promotion',
        (WidgetTester tester) async {
      when(() => EvoActionHandler().handle(
            any(),
            arg: any(named: 'arg'),
          )).thenAnswer((_) async {
        return true;
      });

      final PaymentPromotionSuccess successState =
          PaymentPromotionSuccess(vouchers: <PaymentPromotionModel>[]);

      when(() => mockPaymentPromotionCubit.stream)
          .thenAnswer((_) => Stream<PaymentPromotionSuccess>.value(successState));
      when(() => mockPaymentPromotionCubit.state).thenReturn(successState);

      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
        ),
      );

      await tester.pump();

      expect(find.byType(PaymentPromotionEmptyWidget), findsOneWidget);
      expect(find.byType(PaymentPromotionItemWidget), findsNothing);

      final Finder refreshView = find.byType(RefreshableView);
      expect(refreshView, findsOneWidget);
      final RefreshableView refreshableView = tester.widget<RefreshableView>(refreshView);
      refreshableView.onRefresh.call();

      /// Call 2 times: initial and refresh
      verify(() => mockPaymentPromotionCubit.getPromotion(any(),
          isShowLoading: any(named: 'isShowLoading'))).called(2);
    });

    testWidgets('Handles error states correctly', (WidgetTester tester) async {
      final ErrorUIModel mockError = ErrorUIModel();
      when(() => mockPaymentPromotionCubit.stream)
          .thenAnswer((_) => Stream<PaymentPromotionError>.value(PaymentPromotionError(mockError)));
      when(() => mockPaymentPromotionCubit.state).thenReturn(PaymentPromotionError(mockError));

      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
        ),
      );

      await tester.pump();

      verify(() => mockEvoSnackBar.show(
            any(),
            typeSnackBar: SnackBarType.error,
            durationInSec: SnackBarDuration.short.value,
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    testWidgets('Handles PreviousSelectedNotFound state', (WidgetTester tester) async {
      when(() => mockPaymentPromotionCubit.stream).thenAnswer((_) =>
          Stream<PaymentPromotionPreviousSelectedNotFound>.value(
              PaymentPromotionPreviousSelectedNotFound()));
      when(() => mockPaymentPromotionCubit.state)
          .thenReturn(PaymentPromotionPreviousSelectedNotFound());

      when(() => mockUpdateOrderCubit.updateSelectedVoucher(
            selectedVoucher: any(named: 'selectedVoucher'),
            orderSession: any(named: 'orderSession'),
            timeOutInSec: any(named: 'timeOutInSec'),
          )).thenAnswer((_) async {});

      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
        ),
      );

      await tester.pump();

      verify(() => mockUpdateOrderCubit.updateSelectedVoucher(
            orderSession: mockOrderSessionEntity,
          )).called(1);

      verify(() => mockEvoSnackBar.show(
            EvoStrings.paymentPromotionUnavailable,
            typeSnackBar: SnackBarType.error,
            durationInSec: SnackBarDuration.short.value,
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    testWidgets('Handles UpdateOrderUnqualifiedAfterApplyVoucher state',
        (WidgetTester tester) async {
      when(() => mockUpdateOrderCubit.stream).thenAnswer((_) =>
          Stream<UpdateOrderUnqualifiedAfterApplyVoucher>.value(
              UpdateOrderUnqualifiedAfterApplyVoucher(
            error: ErrorUIModel(verdict: UpdateOrderEntity.verdictPromotionDuplicate),
            selectedVoucher: mockVoucherEntity,
          )));
      when(() => mockUpdateOrderCubit.state).thenReturn(UpdateOrderUnqualifiedAfterApplyVoucher(
        error: ErrorUIModel(verdict: UpdateOrderEntity.verdictPromotionDuplicate),
        selectedVoucher: mockVoucherEntity,
      ));

      when(() => mockUpdateOrderCubit.emit(any())).thenAnswer((_) => Future<void>.value());

      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
          callback: mockCallback,
        ),
      );

      await tester.pump();

      mockUpdateOrderCubit.emit(UpdateOrderUnqualifiedAfterApplyVoucher(
        error: ErrorUIModel(verdict: UpdateOrderEntity.verdictPromotionDuplicate),
        selectedVoucher: mockVoucherEntity,
      ));

      verify(() => mockCallback.hasValidVoucherChange(any())).called(1);
      verify(() => mockCallback.onPromotionSelectionError(
            any(),
            orderSession: any(named: 'orderSession'),
            voucher: any(named: 'voucher'),
          )).called(1);
    });

    testWidgets('Handles UpdateOrderExpired state', (WidgetTester tester) async {
      when(() => mockUpdateOrderCubit.stream)
          .thenAnswer((_) => Stream<UpdateOrderExpired>.value(UpdateOrderExpired(
                error: ErrorUIModel(),
              )));
      when(() => mockUpdateOrderCubit.state).thenReturn(UpdateOrderExpired(error: ErrorUIModel()));

      when(() => mockUpdateOrderCubit.emit(any())).thenAnswer((_) => Future<void>.value());

      await pumpScreen(
        tester,
        cubit: mockPaymentPromotionCubit,
        updateOrderCubit: mockUpdateOrderCubit,
        arg: PaymentPromotionArg(
          orderSession: mockOrderSessionEntity,
          callback: mockCallback,
        ),
      );

      await tester.pump();

      mockUpdateOrderCubit.emit(UpdateOrderExpired(error: ErrorUIModel()));

      verify(() => mockCallback.hasValidVoucherChange(false)).called(1);

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            title: any(named: 'title'),
            dialogId: EvoDialogId.expiredOrderBottomSheet,
            content: EvoStrings.orderExpiredDescriptionBottomSheet,
            buttonListOrientation: any(named: 'buttonListOrientation'),
            isDismissible: any(named: 'isDismissible'),
            textPositive: any(named: 'textPositive'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: any(named: 'onClickPositive'),
            textNegative: any(named: 'textNegative'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });
  });
}
