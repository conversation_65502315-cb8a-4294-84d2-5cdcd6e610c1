import 'package:flutter_test/flutter_test.dart';

import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/payment/promotion/model/payment_promotion_model.dart';

void main() {
  final VoucherEntity voucherEntity = VoucherEntity();

  group('PaymentPromotionModel', () {
    test('should create model with default values', () {
      final PaymentPromotionModel model = PaymentPromotionModel(voucher: voucherEntity);

      expect(model.voucher, voucherEntity);
      expect(model.isSelected, false);
      expect(model.isCanSelected, false);
    });

    test('should create model with custom values', () {
      final PaymentPromotionModel model = PaymentPromotionModel(
        voucher: voucherEntity,
        isSelected: true,
        isCanSelected: true,
      );

      expect(model.voucher, voucherEntity);
      expect(model.isSelected, true);
      expect(model.isCanSelected, true);
    });

    test('should create model with null voucher', () {
      final PaymentPromotionModel model = PaymentPromotionModel(
        voucher: null,
        isSelected: true,
      );

      expect(model.voucher, null);
      expect(model.isSelected, true);
      expect(model.isCanSelected, false);
    });
  });
}
