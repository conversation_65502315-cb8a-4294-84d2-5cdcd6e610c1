import 'package:bloc_test/bloc_test.dart';
import 'package:collection/collection.dart';
import 'package:evoapp/data/repository/campaign_repo.dart';
import 'package:evoapp/data/response/payment_promotion_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/payment/promotion/bloc/payment_promotion_cubit.dart';
import 'package:evoapp/feature/payment/promotion/model/payment_promotion_model.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';
import '../../../../util/test_util.dart';

class MockCampaignRepo extends Mock implements CampaignRepo {}

void main() {
  late PaymentPromotionCubit paymentPromotionCubit;
  late CampaignRepo campaignRepo;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    campaignRepo = MockCampaignRepo();
  });

  setUp(() {
    paymentPromotionCubit = PaymentPromotionCubit(campaignRepo);
  });

  tearDown(() {
    paymentPromotionCubit.promotionsData.clear();
    paymentPromotionCubit.currentSelectedVoucher = null;
  });

  test('verify init state cubit', () {
    expect(paymentPromotionCubit.state, isA<PaymentPromotionInitial>());
  });

  group('test convertDataToModel function', () {
    test('test convertDataToModel with parameter voucher is null', () {
      final List<PaymentPromotionModel> result = paymentPromotionCubit.convertDataToModel(null);

      expect(paymentPromotionCubit.hasValidVoucher, isFalse);

      expect(result.isEmpty, isTrue);
      expect(paymentPromotionCubit.hasValidVoucher, isFalse);
    });

    test('test convertDataToModel with currentSelectedVoucher is null', () {
      final List<VoucherEntity> vouchers = <VoucherEntity>[
        VoucherEntity(id: 1),
        VoucherEntity(id: 2),
      ];

      expect(paymentPromotionCubit.currentSelectedVoucher, isNull);
      expect(paymentPromotionCubit.hasValidVoucher, isFalse);

      final List<PaymentPromotionModel> result = paymentPromotionCubit.convertDataToModel(vouchers);

      expect(result.length, 2);

      expect(result[0].isSelected, false);
      expect(result[0].isCanSelected, true);
      expect(result[0].voucher?.id, 1);

      expect(result[1].isSelected, false);
      expect(result[1].isCanSelected, true);
      expect(result[1].voucher?.id, 2);

      expect(paymentPromotionCubit.hasValidVoucher, isFalse);
    });

    test(
        'test convertDataToModel with currentSelectedVoucher is not null and voucher isQualified = true',
        () {
      final VoucherEntity currentSelectedVoucher = VoucherEntity(id: 2, isQualified: true);
      paymentPromotionCubit.currentSelectedVoucher = currentSelectedVoucher;

      expect(paymentPromotionCubit.hasValidVoucher, isFalse);

      final List<VoucherEntity> vouchers = <VoucherEntity>[
        VoucherEntity(id: 1),
        VoucherEntity(id: 2, isQualified: true),
      ];

      final List<PaymentPromotionModel> result = paymentPromotionCubit.convertDataToModel(vouchers);

      expect(result.length, 2);

      expect(result[0].isSelected, true);
      expect(result[0].isCanSelected, false);
      expect(result[0].voucher?.id, 2);

      expect(result[1].isSelected, false);
      expect(result[1].isCanSelected, false);
      expect(result[1].voucher?.id, 1);

      expect(paymentPromotionCubit.hasValidVoucher, isTrue);
    });

    test(
        'test convertDataToModel with currentSelectedVoucher is not null and voucher isQualified = false',
        () {
      paymentPromotionCubit.hasValidVoucher = true;
      final VoucherEntity currentSelectedVoucher = VoucherEntity(id: 2, isQualified: false);
      paymentPromotionCubit.currentSelectedVoucher = currentSelectedVoucher;

      final List<VoucherEntity> vouchers = <VoucherEntity>[
        VoucherEntity(id: 1),
        VoucherEntity(id: 2),
      ];

      expect(paymentPromotionCubit.hasValidVoucher, isTrue);
      final List<PaymentPromotionModel> result = paymentPromotionCubit.convertDataToModel(vouchers);

      expect(result.length, 2);

      expect(result[0].isSelected, true);
      expect(result[0].isCanSelected, false);
      expect(result[0].voucher?.id, 2);

      expect(result[1].isSelected, false);
      expect(result[1].isCanSelected, false);
      expect(result[1].voucher?.id, 1);
      expect(paymentPromotionCubit.hasValidVoucher, isTrue);
    });
  });

  group('test select/unselect voucher function', () {
    blocTest<PaymentPromotionCubit, PaymentPromotionState>(
      'test updateUnselectedVoucher function',
      setUp: () async {
        final List<PaymentPromotionModel> promotionsData = <PaymentPromotionModel>[
          PaymentPromotionModel(
            voucher: VoucherEntity(id: 1),
            isSelected: true,
          ),
          PaymentPromotionModel(voucher: VoucherEntity(id: 2)),
        ];

        paymentPromotionCubit.promotionsData.addAll(promotionsData);

        /// verify items voucher has isSelected = true
        expect(paymentPromotionCubit.promotionsData.firstOrNull?.isSelected, isTrue);

        paymentPromotionCubit.currentSelectedVoucher = promotionsData.firstOrNull?.voucher;

        expect(paymentPromotionCubit.currentSelectedVoucher, isNotNull);
      },
      build: () => paymentPromotionCubit,
      act: (PaymentPromotionCubit cubit) => cubit.updateUnselectedVoucher(),
      expect: () => <dynamic>[
        isA<PaymentPromotionSuccess>().having(
            (PaymentPromotionSuccess state) => state.vouchers?.firstOrNull?.isSelected,
            'verify isSelected of items voucher',
            isFalse),
      ],
      verify: (_) {
        /// verify items voucher has isSelected = false
        expect(paymentPromotionCubit.promotionsData.firstOrNull?.isSelected, isFalse);

        /// verify currentSelectedVoucher is null
        expect(paymentPromotionCubit.currentSelectedVoucher, isNull);
      },
    );
  });

  group('test getPromotion() function', () {
    const String sessionId = 'sessionId';

    blocTest<PaymentPromotionCubit, PaymentPromotionState>(
      'emits [PaymentPromotionLoading, PaymentPromotionSuccess] '
      'when getPromotion() called success with isShowLoading = true',
      setUp: () async {
        when(() => campaignRepo.getQualificationVoucher(
                request: any(named: 'request'), mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async => PaymentPromotionEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: await TestUtil.getResponseMock('payment_promotions.json'))));

        expect(paymentPromotionCubit.promotionsData.isEmpty, isTrue);
        paymentPromotionCubit.currentSelectedVoucher = VoucherEntity(id: 3);
      },
      build: () => paymentPromotionCubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (PaymentPromotionCubit cubit) => cubit.getPromotion(sessionId, isShowLoading: true),
      expect: () => <dynamic>[
        isA<PaymentPromotionLoading>(),
        isA<PaymentPromotionSuccess>().having(
          (PaymentPromotionSuccess state) => state.vouchers?.isNotEmpty,
          'verify data voucher is not empty',
          isTrue,
        ),
      ],
      verify: (_) {
        verify(() => campaignRepo.getQualificationVoucher(
            request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);

        expect(paymentPromotionCubit.promotionsData.isNotEmpty, isTrue);
        expect(paymentPromotionCubit.promotionsData.firstOrNull?.isSelected, isTrue);
        expect(paymentPromotionCubit.promotionsData.firstOrNull?.voucher?.id, 3);
      },
    );

    blocTest<PaymentPromotionCubit, PaymentPromotionState>(
      'Test currentSelectedVoucher is not null, get voucher list 1st time, and the voucher list DOES NOT contain currentSelectedVoucher id'
      'should emit states [PaymentPromotionLoading, PaymentPromotionSuccess, PaymentPromotionPreviousSelectedNotFound]',
      setUp: () async {
        when(() => campaignRepo.getQualificationVoucher(
                request: any(named: 'request'), mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async => PaymentPromotionEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: await TestUtil.getResponseMock('payment_promotions.json'))));

        expect(paymentPromotionCubit.promotionsData.isEmpty, isTrue);
        paymentPromotionCubit.currentSelectedVoucher = VoucherEntity(id: 100);
      },
      build: () => paymentPromotionCubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (PaymentPromotionCubit cubit) => cubit.getPromotion(sessionId, isShowLoading: true),
      expect: () => <dynamic>[
        isA<PaymentPromotionLoading>(),
        isA<PaymentPromotionSuccess>().having(
          (PaymentPromotionSuccess state) => state.vouchers?.isNotEmpty,
          'verify data voucher is not empty',
          isTrue,
        ),
        isA<PaymentPromotionPreviousSelectedNotFound>(),
      ],
      verify: (_) {
        verify(() => campaignRepo.getQualificationVoucher(
            request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);

        expect(paymentPromotionCubit.promotionsData.isNotEmpty, isTrue);
        expect(paymentPromotionCubit.promotionsData.firstOrNull?.isSelected, isFalse);
        expect(paymentPromotionCubit.promotionsData.firstOrNull?.voucher?.id, 1);
      },
    );

    blocTest<PaymentPromotionCubit, PaymentPromotionState>(
      'emits [PaymentPromotionLoading, PaymentPromotionSuccess] when getPromotion() called success'
      ' with isShowLoading = true and promotionsData.firstOrNull?.isSelected == true',
      setUp: () async {
        when(() => campaignRepo.getQualificationVoucher(
                request: any(named: 'request'), mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async => PaymentPromotionEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: await TestUtil.getResponseMock('payment_promotions.json'))));

        expect(paymentPromotionCubit.promotionsData.isEmpty, isTrue);
        paymentPromotionCubit.currentSelectedVoucher = VoucherEntity(id: 1);
      },
      build: () => paymentPromotionCubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (PaymentPromotionCubit cubit) => cubit.getPromotion(sessionId, isShowLoading: true),
      expect: () => <dynamic>[
        isA<PaymentPromotionLoading>(),
        isA<PaymentPromotionSuccess>().having(
          (PaymentPromotionSuccess state) => state.vouchers?.isNotEmpty,
          'verify data voucher is not empty',
          isTrue,
        ),
      ],
      verify: (_) {
        verify(() => campaignRepo.getQualificationVoucher(
            request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);

        expect(paymentPromotionCubit.promotionsData.isNotEmpty, isTrue);
        expect(paymentPromotionCubit.promotionsData.firstOrNull?.isSelected, isTrue);
      },
    );

    blocTest<PaymentPromotionCubit, PaymentPromotionState>(
      'emits [PaymentPromotionLoading, PaymentPromotionSuccess] when getPromotion() called success with isShowLoading = false',
      setUp: () async {
        when(() => campaignRepo.getQualificationVoucher(
                request: any(named: 'request'), mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async => PaymentPromotionEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: await TestUtil.getResponseMock('payment_promotions.json'))));

        expect(paymentPromotionCubit.promotionsData.isEmpty, isTrue);
      },
      build: () => paymentPromotionCubit,
      act: (PaymentPromotionCubit cubit) => cubit.getPromotion(sessionId),
      expect: () => <dynamic>[
        isA<PaymentPromotionSuccess>().having(
          (PaymentPromotionSuccess state) => state.vouchers?.isNotEmpty,
          'verify data voucher is not empty',
          isTrue,
        ),
      ],
      verify: (_) {
        verify(() => campaignRepo.getQualificationVoucher(
            request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);

        expect(paymentPromotionCubit.promotionsData.isNotEmpty, isTrue);
      },
    );

    blocTest<PaymentPromotionCubit, PaymentPromotionState>(
      'emits [PaymentPromotionLoading, PaymentPromotionError] when getPromotion() called error with isShowLoading = true',
      setUp: () async {
        when(() => campaignRepo.getQualificationVoucher(
                request: any(named: 'request'), mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async => PaymentPromotionEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: null)));

        expect(paymentPromotionCubit.promotionsData.isEmpty, isTrue);
      },
      build: () => paymentPromotionCubit,
      act: (PaymentPromotionCubit cubit) => cubit.getPromotion(sessionId, isShowLoading: true),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<PaymentPromotionLoading>(),
        isA<PaymentPromotionError>().having(
          (PaymentPromotionError state) => state.error.statusCode,
          'verify status code when error',
          CommonHttpClient.BAD_REQUEST,
        ),
      ],
      verify: (_) {
        verify(() => campaignRepo.getQualificationVoucher(
            request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);

        expect(paymentPromotionCubit.promotionsData.isEmpty, isTrue);
      },
    );

    blocTest<PaymentPromotionCubit, PaymentPromotionState>(
      'emits [PaymentPromotionLoading, PaymentPromotionError] when getPromotion() called error with isShowLoading = false',
      setUp: () async {
        when(() => campaignRepo.getQualificationVoucher(
                request: any(named: 'request'), mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async => PaymentPromotionEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: null)));

        expect(paymentPromotionCubit.promotionsData.isEmpty, isTrue);
      },
      build: () => paymentPromotionCubit,
      act: (PaymentPromotionCubit cubit) => cubit.getPromotion(sessionId),
      expect: () => <dynamic>[
        isA<PaymentPromotionError>().having(
          (PaymentPromotionError state) => state.error.statusCode,
          'verify status code when error',
          CommonHttpClient.BAD_REQUEST,
        ),
      ],
      verify: (_) {
        verify(() => campaignRepo.getQualificationVoucher(
            request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);

        expect(paymentPromotionCubit.promotionsData.isEmpty, isTrue);
      },
    );
  });

  group('test checkPreviousVoucherSelectedStatus() function', () {
    blocTest<PaymentPromotionCubit, PaymentPromotionState>(
      'not emit state when checkPreviousVoucherSelectedStatus() called with currentSelectedVoucher '
      'is null and promotionsData is empty',
      setUp: () async {
        expect(paymentPromotionCubit.promotionsData.isEmpty, isTrue);
        expect(paymentPromotionCubit.currentSelectedVoucher, isNull);
      },
      build: () => paymentPromotionCubit,
      act: (PaymentPromotionCubit cubit) => cubit.checkPreviousVoucherSelectedStatus(),
      expect: () => <dynamic>[],
    );

    blocTest<PaymentPromotionCubit, PaymentPromotionState>(
      'not emit state when checkPreviousVoucherSelectedStatus() called '
      'with currentSelectedVoucher is null and promotionsData is not empty',
      setUp: () async {
        final List<PaymentPromotionModel> promotionsData = <PaymentPromotionModel>[
          PaymentPromotionModel(voucher: VoucherEntity(id: 1)),
          PaymentPromotionModel(voucher: VoucherEntity(id: 2)),
        ];

        paymentPromotionCubit.promotionsData.addAll(promotionsData);

        paymentPromotionCubit.currentSelectedVoucher = VoucherEntity();

        expect(paymentPromotionCubit.promotionsData.isNotEmpty, isTrue);

        expect(paymentPromotionCubit.promotionsData.firstOrNull?.isSelected, isFalse);

        expect(paymentPromotionCubit.currentSelectedVoucher, isA<VoucherEntity>());
      },
      build: () => paymentPromotionCubit,
      act: (PaymentPromotionCubit cubit) => cubit.checkPreviousVoucherSelectedStatus(),
      expect: () => <dynamic>[
        isA<PaymentPromotionPreviousSelectedNotFound>(),
      ],
    );

    blocTest<PaymentPromotionCubit, PaymentPromotionState>(
      'not emit state when checkPreviousVoucherSelectedStatus() called '
      'with currentSelectedVoucher is not null and promotionsData has item isSelected = true',
      setUp: () async {
        final List<PaymentPromotionModel> promotionsData = <PaymentPromotionModel>[
          PaymentPromotionModel(
            voucher: VoucherEntity(id: 1),
            isSelected: true,
          ),
          PaymentPromotionModel(voucher: VoucherEntity(id: 2)),
        ];

        paymentPromotionCubit.promotionsData.addAll(promotionsData);

        expect(paymentPromotionCubit.promotionsData.isNotEmpty, isTrue);

        expect(paymentPromotionCubit.promotionsData.firstOrNull?.isSelected, isTrue);

        expect(paymentPromotionCubit.currentSelectedVoucher, isNull);
      },
      build: () => paymentPromotionCubit,
      act: (PaymentPromotionCubit cubit) => cubit.checkPreviousVoucherSelectedStatus(),
      expect: () => <dynamic>[],
    );
  });
}
