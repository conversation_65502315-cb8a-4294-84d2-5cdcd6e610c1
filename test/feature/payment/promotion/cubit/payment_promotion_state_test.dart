import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/payment/promotion/model/payment_promotion_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:evoapp/feature/payment/promotion/bloc/payment_promotion_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

void main() {
  group('PaymentPromotionSuccess', () {
    test('should be instantiated with default values', () {
      final PaymentPromotionSuccess state = PaymentPromotionSuccess();

      expect(state.vouchers, isNull);
      expect(state.hasValidVoucher, isFalse);
    });

    test('should be instantiated with provided values', () {
      final List<PaymentPromotionModel> vouchers = <PaymentPromotionModel>[
        PaymentPromotionModel(voucher: VoucherEntity(id: 1)),
        PaymentPromotionModel(voucher: VoucherEntity(id: 2))
      ];

      final PaymentPromotionSuccess state = PaymentPromotionSuccess(
        vouchers: vouchers,
        hasValidVoucher: true,
      );

      expect(state.vouchers, equals(vouchers));
      expect(state.hasValidVoucher, isTrue);
    });

    test('states with same parameters should be equal', () {
      final List<PaymentPromotionModel> vouchers1 = <PaymentPromotionModel>[
        PaymentPromotionModel(voucher: VoucherEntity(id: 1))
      ];
      final List<PaymentPromotionModel> vouchers2 = <PaymentPromotionModel>[
        PaymentPromotionModel(voucher: VoucherEntity(id: 1))
      ];

      final PaymentPromotionSuccess state1 = PaymentPromotionSuccess(
        vouchers: vouchers1,
        hasValidVoucher: true,
      );
      final PaymentPromotionSuccess state2 = PaymentPromotionSuccess(
        vouchers: vouchers2,
        hasValidVoucher: true,
      );

      expect(state1.vouchers?.length, state2.vouchers?.length);
      expect(state1.hasValidVoucher, state2.hasValidVoucher);
    });

    test('states with different parameters should not be equal', () {
      final List<PaymentPromotionModel> vouchers1 = <PaymentPromotionModel>[
        PaymentPromotionModel(voucher: VoucherEntity(id: 1))
      ];
      final List<PaymentPromotionModel> vouchers2 = <PaymentPromotionModel>[
        PaymentPromotionModel(voucher: VoucherEntity(id: 2))
      ];

      final PaymentPromotionSuccess state1 = PaymentPromotionSuccess(
        vouchers: vouchers1,
        hasValidVoucher: true,
      );
      final PaymentPromotionSuccess state2 = PaymentPromotionSuccess(
        vouchers: vouchers2,
      );

      expect(state1.hasValidVoucher, isNot(state2.hasValidVoucher));
      expect(state1.vouchers?[0].voucher?.id, isNot(state2.vouchers?[0].voucher?.id));
    });
  });

  group('PaymentPromotionError', () {
    test('should be instantiated with provided error', () {
      final ErrorUIModel error = ErrorUIModel(userMessage: 'Test error');

      final PaymentPromotionError state = PaymentPromotionError(error);

      expect(state.error, equals(error));
    });

    test('states with same error should be equal', () {
      final ErrorUIModel error1 = ErrorUIModel(userMessage: 'Test error', statusCode: 400);
      final ErrorUIModel error2 = ErrorUIModel(userMessage: 'Test error', statusCode: 400);

      final PaymentPromotionError state1 = PaymentPromotionError(error1);
      final PaymentPromotionError state2 = PaymentPromotionError(error2);

      expect(state1.error.userMessage, equals(state2.error.userMessage));
      expect(state1.error.statusCode, equals(state2.error.statusCode));
    });

    test('states with different errors should not be equal', () {
      final ErrorUIModel error1 = ErrorUIModel(userMessage: 'Test error 1');
      final ErrorUIModel error2 = ErrorUIModel(userMessage: 'Test error 2');

      final PaymentPromotionError state1 = PaymentPromotionError(error1);
      final PaymentPromotionError state2 = PaymentPromotionError(error2);

      expect(state1.error.userMessage, isNot(state2.error.userMessage));
    });
  });

  group('PaymentPromotionPreviousSelectedNotFound', () {
    test('should be instantiated', () {
      final PaymentPromotionState state = PaymentPromotionPreviousSelectedNotFound();

      expect(state, isA<PaymentPromotionPreviousSelectedNotFound>());
    });
  });
}
