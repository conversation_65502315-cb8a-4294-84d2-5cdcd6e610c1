import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/model/promotion_status_ui_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'package:evoapp/feature/payment/promotion/payment_promotion_item_widget.dart';
import 'package:evoapp/feature/payment/promotion/model/payment_promotion_model.dart';

import '../../../util/flutter_test_config.dart';

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

class MockImageProvider extends Mock implements ImageProvider {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

void main() {
  late CommonImageProvider mockCommonImageProvider;
  late CommonUtilFunction mockCommonUtilFunction;
  final EvoUiUtils mockEvoUiUtils = MockEvoUiUtils();

  const String fakeTitleVoucher = 'Fake title';
  final VoucherEntity mockVoucherEntity = VoucherEntity(
    title: fakeTitleVoucher,
    isQualified: true,
  );
  final PaymentPromotionModel mockPromotionModel = PaymentPromotionModel(
    voucher: mockVoucherEntity,
  );

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();
    EvoUiUtils.setInstanceForTesting(mockEvoUiUtils);

    getIt.registerLazySingleton<CommonUtilFunction>(() => MockCommonUtilFunction());
    mockCommonUtilFunction = getIt.get<CommonUtilFunction>();

    getIt.registerLazySingleton<CommonImageProvider>(() => MockCommonImageProvider());
    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    when(
      () => mockCommonImageProvider.asset(
        captureAny(),
        width: captureAny<double?>(named: 'width'),
        height: captureAny<double?>(named: 'height'),
        color: any(named: 'color'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'),
        cacheWidth: any(named: 'cacheWidth'),
        cacheHeight: any(named: 'cacheHeight'),
        package: any(named: 'package'),
      ),
    ).thenAnswer((_) => Container());
  });

  setUp(() {
    when(() => mockCommonUtilFunction.toDateTime(any())).thenReturn(DateTime.now());
  });

  tearDown(() {
    reset(mockCommonUtilFunction);
  });

  Widget createWidgetUnderTest({
    PaymentPromotionModel? item,
    VoidCallback? onSelected,
    VoidCallback? onUnSelected,
    VoidCallback? onViewDetail,
    PromotionStatusUIModel? promotionStatusUIModel,
  }) {
    return MaterialApp(
      home: Scaffold(
        body: PaymentPromotionItemWidget(
          item: item,
          onSelected: onSelected,
          onUnSelected: onUnSelected,
          onViewDetail: onViewDetail,
          promotionStatusUIModel: promotionStatusUIModel,
        ),
      ),
    );
  }

  group('verify voucher', () {
    testWidgets('renders correctly with null item', (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.byType(Card), findsOneWidget);
      expect(find.byType(Text), findsWidgets);
      expect(find.text(''), findsWidgets);
    });

    testWidgets('renders voucher title', (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest(item: mockPromotionModel));

      expect(find.text(fakeTitleVoucher), findsOneWidget);
    });

    testWidgets('renders unqualified title when voucher is not qualified',
        (WidgetTester tester) async {
      const String fakeTitleVoucher = 'Fake title';
      final VoucherEntity mockVoucherEntity = VoucherEntity(
        title: fakeTitleVoucher,
        isQualified: false,
      );
      final PaymentPromotionModel mockPromotionModel = PaymentPromotionModel(
        voucher: mockVoucherEntity,
      );

      await tester.pumpWidget(createWidgetUnderTest(item: mockPromotionModel));

      expect(find.text(EvoStrings.promotionItemUnqualified), findsAtLeastNWidgets(1));
    });

    testWidgets('does not render unqualified title when voucher is qualified',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest(item: mockPromotionModel));

      expect(find.text(EvoStrings.promotionItemUnqualified), findsNothing);
    });

    testWidgets('builds expiry time widget with status UI model', (WidgetTester tester) async {
      final PromotionStatusUIModel fakeVoucherStatusUIModel = PromotionStatusUIModel(
        color: Colors.red,
        title: 'Expiry time',
        hasOpacityTitle: true,
      );

      await tester.pumpWidget(createWidgetUnderTest(
        item: mockPromotionModel,
        promotionStatusUIModel: fakeVoucherStatusUIModel,
      ));

      expect(find.text('Expiry time'), findsOneWidget);
    });
  });

  group('Action widgets', () {
    testWidgets('shows unselect button when voucher is selected', (WidgetTester tester) async {
      final PaymentPromotionModel mockPromotionModel = PaymentPromotionModel(
        voucher: mockVoucherEntity,
        isSelected: true,
        isCanSelected: true,
      );

      await tester.pumpWidget(createWidgetUnderTest(item: mockPromotionModel));

      expect(find.text(EvoStrings.paymentUnSelectPromotionButton), findsOneWidget);
      expect(find.text(EvoStrings.paymentSelectPromotionButton), findsNothing);
    });

    testWidgets('shows select button when voucher is qualified and can be selected',
        (WidgetTester tester) async {
      final PaymentPromotionModel mockPromotionModel = PaymentPromotionModel(
        voucher: mockVoucherEntity,
        isCanSelected: true,
      );

      await tester.pumpWidget(createWidgetUnderTest(item: mockPromotionModel));

      expect(find.text(EvoStrings.paymentUnSelectPromotionButton), findsNothing);
      expect(find.text(EvoStrings.paymentSelectPromotionButton), findsOneWidget);
    });

    testWidgets('shows not qualified message when voucher is not qualified',
        (WidgetTester tester) async {
      final VoucherEntity mockVoucherEntity = VoucherEntity(
        title: fakeTitleVoucher,
        isQualified: false,
      );
      final PaymentPromotionModel mockPromotionModel = PaymentPromotionModel(
        voucher: mockVoucherEntity,
      );

      await tester.pumpWidget(createWidgetUnderTest(item: mockPromotionModel));

      expect(find.text(EvoStrings.paymentPromotionNotQualified), findsAtLeastNWidgets(1));
    });

    testWidgets('shows cannot be selected message when voucher cannot be selected',
        (WidgetTester tester) async {
      final VoucherEntity mockVoucherEntity = VoucherEntity(
        title: fakeTitleVoucher,
        isQualified: true,
      );
      final PaymentPromotionModel mockPromotionModel = PaymentPromotionModel(
        voucher: mockVoucherEntity,
      );

      await tester.pumpWidget(createWidgetUnderTest(item: mockPromotionModel));

      expect(find.text(EvoStrings.paymentPromotionCanNotSelected), findsOneWidget);
    });
  });

  group('Callbacks', () {
    testWidgets('calls onSelected callback when select button is tapped',
        (WidgetTester tester) async {
      bool onSelectedCalled = false;

      final VoucherEntity mockVoucherEntity = VoucherEntity(
        title: fakeTitleVoucher,
        isQualified: true,
      );
      final PaymentPromotionModel mockPromotionModel = PaymentPromotionModel(
        voucher: mockVoucherEntity,
        isCanSelected: true,
      );

      await tester.pumpWidget(createWidgetUnderTest(
        item: mockPromotionModel,
        onSelected: () => onSelectedCalled = true,
      ));

      await tester.tap(find.text(EvoStrings.paymentSelectPromotionButton));
      await tester.pump();

      expect(onSelectedCalled, true);
    });

    testWidgets('calls onUnSelected callback when unselect button is tapped',
        (WidgetTester tester) async {
      bool onUnSelectedCalled = false;
      final VoucherEntity mockVoucherEntity = VoucherEntity(
        title: fakeTitleVoucher,
        isQualified: true,
      );
      final PaymentPromotionModel mockPromotionModel = PaymentPromotionModel(
        voucher: mockVoucherEntity,
        isSelected: true,
      );

      await tester.pumpWidget(createWidgetUnderTest(
        item: mockPromotionModel,
        onUnSelected: () => onUnSelectedCalled = true,
      ));

      await tester.tap(find.text(EvoStrings.paymentUnSelectPromotionButton));
      await tester.pump();

      expect(onUnSelectedCalled, true);
    });

    testWidgets('calls onViewDetail callback when card is tapped', (WidgetTester tester) async {
      bool onViewDetailCalled = false;

      await tester.pumpWidget(createWidgetUnderTest(
        item: mockPromotionModel,
        onViewDetail: () => onViewDetailCalled = true,
      ));

      await tester.tap(find.byType(InkWell));
      await tester.pump();

      expect(onViewDetailCalled, true);
    });
  });

  group('Border Colors', () {
    testWidgets('has primary border color when selected and qualified',
        (WidgetTester tester) async {
      final VoucherEntity mockVoucherEntity = VoucherEntity(
        title: fakeTitleVoucher,
        isQualified: true,
      );
      final PaymentPromotionModel mockPromotionModel = PaymentPromotionModel(
        voucher: mockVoucherEntity,
        isSelected: true,
      );

      await tester.pumpWidget(createWidgetUnderTest(item: mockPromotionModel));

      final Finder cardFinder = find.byType(Card);
      final Card card = tester.widget<Card>(cardFinder);
      final RoundedRectangleBorder shape = card.shape as RoundedRectangleBorder;

      expect(shape.side.color, evoColors.primary);
    });

    testWidgets('has unqualified border color when selected but not qualified',
        (WidgetTester tester) async {
      final VoucherEntity mockVoucherEntity = VoucherEntity(
        title: fakeTitleVoucher,
        isQualified: false,
      );
      final PaymentPromotionModel mockPromotionModel = PaymentPromotionModel(
        voucher: mockVoucherEntity,
        isSelected: true,
      );

      await tester.pumpWidget(createWidgetUnderTest(item: mockPromotionModel));

      final Finder cardFinder = find.byType(Card);
      final Card card = tester.widget<Card>(cardFinder);
      final RoundedRectangleBorder shape = card.shape as RoundedRectangleBorder;

      expect(shape.side.color, evoColors.promotionItemUnqualifiedBorder);
    });

    testWidgets('has transparent border color when not selected', (WidgetTester tester) async {
      final VoucherEntity mockVoucherEntity = VoucherEntity(
        title: fakeTitleVoucher,
        isQualified: false,
      );
      final PaymentPromotionModel mockPromotionModel = PaymentPromotionModel(
        voucher: mockVoucherEntity,
      );

      await tester.pumpWidget(createWidgetUnderTest(item: mockPromotionModel));

      final Finder cardFinder = find.byType(Card);
      final Card card = tester.widget<Card>(cardFinder);
      final RoundedRectangleBorder shape = card.shape as RoundedRectangleBorder;

      // Assert
      expect(shape.side.color, Colors.transparent);
    });
  });
}
