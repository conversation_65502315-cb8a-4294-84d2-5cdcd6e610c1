import 'package:evoapp/feature/payment/models/payment_entry_point.dart';
import 'package:flutter_test/flutter_test.dart';

class PaymentFlowTest {
  final PaymentEntryPoint paymentFlow;

  PaymentFlowTest({required this.paymentFlow});

  String processPayment() {
    if (paymentFlow == PaymentEntryPoint.bottomNavigationBar) {
      return 'Payment Entry Point from Bottom Navigation Bar';
    } else if (paymentFlow == PaymentEntryPoint.paymentWithEMI) {
      return 'Payment Entry Point from Payment with EMI';
    } else if (paymentFlow == PaymentEntryPoint.voucherDetailScreen) {
      return 'Payment entry point from campaign detail screen';
    } else {
      throw Exception('Invalid payment flow');
    }
  }
}

void main() {
  test('Payment Entry Point from Bottom Navigation Bar', () {
    final PaymentFlowTest paymentProcessor =
        PaymentFlowTest(paymentFlow: PaymentEntryPoint.bottomNavigationBar);

    final String result = paymentProcessor.processPayment();

    expect(result, 'Payment Entry Point from Bottom Navigation Bar');
  });

  test('Payment Entry Point from Payment with EMI', () {
    final PaymentFlowTest paymentProcessor =
        PaymentFlowTest(paymentFlow: PaymentEntryPoint.paymentWithEMI);

    final String result = paymentProcessor.processPayment();

    expect(result, 'Payment Entry Point from Payment with EMI');
  });

  test('Payment entry point from campaign detail screen', () {
    final PaymentFlowTest paymentProcessor =
        PaymentFlowTest(paymentFlow: PaymentEntryPoint.voucherDetailScreen);

    final String result = paymentProcessor.processPayment();

    expect(result, 'Payment entry point from campaign detail screen');
  });
}
