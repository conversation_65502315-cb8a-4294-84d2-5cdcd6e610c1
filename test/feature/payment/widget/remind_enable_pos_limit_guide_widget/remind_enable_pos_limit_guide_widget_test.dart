import 'package:evoapp/feature/payment/widget/indexed_indicator_widget.dart';
import 'package:evoapp/feature/payment/widget/remind_enable_pos_limit_guide_widget/remind_enable_pos_limit_guide_model.dart';
import 'package:evoapp/feature/payment/widget/remind_enable_pos_limit_guide_widget/remind_enable_pos_limit_guide_widget.dart';
import 'package:evoapp/feature/payment/widget/remind_enable_pos_limit_guide_widget/remind_enable_pos_limit_title_and_description.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}


void main() {
  test('RemindEnablePosLimitGuideWidgetConfigs values', () {
    expect(RemindEnablePosLimitGuideWidgetConfigs.imageRatio, 320 / 260);
    expect(RemindEnablePosLimitGuideWidgetConfigs.imageWidthPercentage, 320 / 375);
    expect(RemindEnablePosLimitGuideWidgetConfigs.imageHeightPercentage, 260 / 812);
    expect(RemindEnablePosLimitGuideWidgetConfigs.minContentHeightPercentage, 158 / 812);
    expect(RemindEnablePosLimitGuideWidgetConfigs.animatedDurationTime,
        const Duration(milliseconds: 300));
  });

  final List<RemindEnablePosLimitGuideModel> fakeModels = <RemindEnablePosLimitGuideModel>[
    const RemindEnablePosLimitGuideModel(
      title: 'fake_title_1',
      description: 'fake_description_1',
      image: 'fake_image_1',
    ),
    const RemindEnablePosLimitGuideModel(
      title: 'fake_title_2',
      description: 'fake_description_2',
      image: 'fake_image_2',
    ),
    const RemindEnablePosLimitGuideModel(
      title: 'fake_title_3',
      description: 'fake_description_3',
      image: 'fake_image_3',
    ),
  ];

  group('Test RemindEnablePosLimitGuideWidget', () {
    const double expectedHeight = 100;
    const double expectedWidth = 50;

    setUpAll(() {
      getItRegisterMockCommonUtilFunctionAndImageProvider();
      getItRegisterColor();
      getItRegisterTextStyle();
      registerFallbackValue(MockBuildContext());
      setUtilsMockInstanceForTesting();
      when(() => evoImageProvider.asset(
            any(),
            fit: any(named: 'fit'),
            width: any(named: 'width'),
          )).thenReturn(const SizedBox());

      when(() => EvoUiUtils().calculateVerticalSpace(
            context: any(named: 'context'),
            heightPercentage: any(named: 'heightPercentage'),
          )).thenReturn(expectedHeight);

      when(() => EvoUiUtils().calculateHorizontalSpace(
            context: any(named: 'context'),
            widthPercentage: any(named: 'widthPercentage'),
          )).thenReturn(expectedWidth);
    });

    tearDownAll(() {
      resetUtilMockToOriginalInstance();
    });

    testWidgets('Give empty models', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: RemindEnablePosLimitGuideWidget(
            models: <RemindEnablePosLimitGuideModel>[],
          ),
        ),
      );

      expect(find.byType(SizedBox), findsOneWidget);
      expect(find.byType(PageView), findsNothing);
      expect(find.byType(RemindEnablePosLimitTitleAndDescription), findsNothing);
      expect(find.byType(IndexedIndicatorWidget), findsNothing);
    });

    testWidgets('Give models is NOT empty', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: RemindEnablePosLimitGuideWidget(
            models: fakeModels,
          ),
        ),
      );

      /// Verify PageView
      final Finder pageViewFinder = find.descendant(
        of: find.byWidgetPredicate((Widget widget) {
          return widget is Container &&
              widget.constraints?.minHeight == expectedHeight &&
              widget.constraints?.maxHeight == expectedHeight;
        }),
        matching: find.byType(PageView),
      );
      expect(pageViewFinder, findsOneWidget);
      final PageView pageView = tester.widget<PageView>(pageViewFinder);
      expect(pageView.controller?.viewportFraction, 0.9);
      expect(pageView.padEnds, false);
      expect(pageView.childrenDelegate.estimatedChildCount, fakeModels.length);
      // Verify PageView itemBuilder
      final Finder pageViewItemBuilderFinder = find.descendant(
        of: pageViewFinder,
        matching: find.byType(AspectRatio),
      );
      expect(pageViewItemBuilderFinder, findsNWidgets(2));
      final AspectRatio aspectRatio = tester.widget<AspectRatio>(pageViewItemBuilderFinder.first);
      expect(aspectRatio.aspectRatio, RemindEnablePosLimitGuideWidgetConfigs.imageRatio);
      verify(() => evoImageProvider.asset(
            any(),
            fit: BoxFit.contain,
            width:
                expectedWidth + expectedWidth * (1 - (pageView.controller?.viewportFraction ?? 0)),
          )).called(2);

      /// Verify RemindEnablePosLimitTitleAndDescription
      final Finder titleAndDescriptionFinder = find.byType(RemindEnablePosLimitTitleAndDescription);
      expect(titleAndDescriptionFinder, findsOneWidget);
      final RemindEnablePosLimitTitleAndDescription titleAndDescription =
          tester.widget<RemindEnablePosLimitTitleAndDescription>(titleAndDescriptionFinder);
      expect(titleAndDescription.model, fakeModels.first);
      expect(titleAndDescription.constraints?.minHeight, expectedHeight);

      /// Verify IndexedIndicatorWidget
      final Finder indexedIndicatorFinder = find.byType(IndexedIndicatorWidget);
      expect(indexedIndicatorFinder, findsOneWidget);
      final IndexedIndicatorWidget indexedIndicator =
          tester.widget<IndexedIndicatorWidget>(indexedIndicatorFinder);
      expect(indexedIndicator.text, '1/3');
      expect(
        indexedIndicator.textStyle,
        evoTextStyles.bodySmall(color: evoColors.remindPosLimitIndicatorText),
      );
      expect(indexedIndicator.iconEnableColor, evoColors.remindPosLimitIndicator);
      expect(indexedIndicator.iconDisableColor, evoColors.remindPosLimitIndicator);
    });
  });
}
