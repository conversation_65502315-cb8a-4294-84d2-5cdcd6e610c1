import 'package:evoapp/feature/payment/widget/item_text_order_summary_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../util/flutter_test_config.dart';

void main() {
  const String fakeText = 'fakeText';

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
  });

  testWidgets('test UI ItemTextOrderSummaryWidget with default value',
      (WidgetTester widgetTester) async {
    await widgetTester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: ItemTextOrderSummaryWidget(
            fakeText,
          ),
        ),
      ),
    );

    final Finder finder = find.byType(ItemTextOrderSummaryWidget);

    expect(finder, findsOneWidget);

    final ItemTextOrderSummaryWidget itemTextOrderSummaryWidget = widgetTester.widget(finder);

    expect(itemTextOrderSummaryWidget.text, fakeText);
    expect(itemTextOrderSummaryWidget.color, isNull);
    expect(itemTextOrderSummaryWidget.hasBold, isFalse);
    expect(itemTextOrderSummaryWidget.textAlign, TextAlign.left);
    expect(itemTextOrderSummaryWidget.maxLines, isNull);

    final Finder textFinder = find.text(fakeText);

    expect(textFinder, findsOneWidget);

    final Text text = widgetTester.widget(textFinder);
    expect(
        text.style,
        evoTextStyles
            .bodyMedium(evoColors.emiTenorBackground)
            .copyWith(fontWeight: FontWeight.w500));
    expect(text.textAlign, TextAlign.left);
    expect(text.maxLines, isNull);
    expect(text.overflow, isNull);
  });

  testWidgets('test UI ItemTextOrderSummaryWidget with custom value',
      (WidgetTester widgetTester) async {
    await widgetTester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: ItemTextOrderSummaryWidget(
            fakeText,
            hasBold: true,
            color: evoColors.primary,
            maxLines: 3,
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );

    final Finder finder = find.byType(ItemTextOrderSummaryWidget);

    expect(finder, findsOneWidget);

    final ItemTextOrderSummaryWidget itemTextOrderSummaryWidget = widgetTester.widget(finder);

    expect(itemTextOrderSummaryWidget.text, fakeText);
    expect(itemTextOrderSummaryWidget.color, evoColors.primary);
    expect(itemTextOrderSummaryWidget.hasBold, isTrue);
    expect(itemTextOrderSummaryWidget.textAlign, TextAlign.center);
    expect(itemTextOrderSummaryWidget.maxLines, 3);

    final Finder textFinder = find.text(fakeText);

    expect(textFinder, findsOneWidget);

    final Text text = widgetTester.widget(textFinder);
    expect(text.style,
        evoTextStyles.bodyMedium(evoColors.primary).copyWith(fontWeight: FontWeight.w700));
    expect(text.textAlign, TextAlign.center);
    expect(text.maxLines, 3);
    expect(text.overflow, TextOverflow.ellipsis);
  });
}
