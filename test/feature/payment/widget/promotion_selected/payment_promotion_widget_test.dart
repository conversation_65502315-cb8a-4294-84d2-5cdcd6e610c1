import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/payment/confirm_payment/model/order_info_ui_model.dart';
import 'package:evoapp/feature/payment/widget/promotion_selected/payment_promotion_widget.dart';
import 'package:evoapp/feature/payment/widget/promotion_selected/promotion_selected_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEvoImageProvider extends Mock implements CommonImageProvider {}

class MockVoidCallback extends Mock {
  void call();
}

class MockGlobalOffsetReadyCallback extends Mock {
  void call(Offset? offset);
}

void main() {
  late CommonImageProvider commonImageProvider;

  setUpAll(() {
    getIt.registerLazySingleton(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    commonImageProvider = getIt.get<CommonImageProvider>();

    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('PaymentPromotionV2Widget', () {
    const String fakeVoucherTitle = 'Voucher Title';

    testWidgets('displays the correct title for not selected voucher', (WidgetTester tester) async {
      const Widget widget = MaterialApp(
        home: Scaffold(
          body: PaymentPromotionWidget(),
        ),
      );

      await tester.pumpWidget(widget);

      final Finder promotionSelectedFinder = find.byType(PromotionSelectedWidget);
      expect(promotionSelectedFinder, findsOneWidget);

      final Finder titleFinder = find.text(EvoStrings.paymentSelectPromotion);
      expect(titleFinder, findsOneWidget);

      final Finder fakeTitleFinder = find.text(fakeVoucherTitle);
      expect(fakeTitleFinder, findsNothing);

      verify(() => commonImageProvider.asset(
            EvoImages.icEmiPaymentNoPromotion,
          )).called(1);
    });

    testWidgets('displays the correct title for invalidVoucher', (WidgetTester tester) async {
      final VoucherEntity fakeSelectedVoucher = VoucherEntity(title: fakeVoucherTitle);

      final Widget widget = MaterialApp(
        home: Scaffold(
          body: PaymentPromotionWidget(
            selectedPromotion: fakeSelectedVoucher,
            voucherSelectionState: VoucherSelectionState.invalidVoucher,
          ),
        ),
      );

      await tester.pumpWidget(widget);

      final Finder promotionSelectedFinder = find.byType(PromotionSelectedWidget);
      expect(promotionSelectedFinder, findsOneWidget);

      final Finder titleFinder = find.text(fakeVoucherTitle);
      expect(titleFinder, findsOneWidget);

      verify(() => commonImageProvider.asset(
            EvoImages.icEmiPaymentInvalidPromotion,
          )).called(1);
    });

    testWidgets('displays the correct title for validVoucher', (WidgetTester tester) async {
      final VoucherEntity fakeSelectedVoucher = VoucherEntity(title: fakeVoucherTitle);

      final Widget widget = MaterialApp(
        home: Scaffold(
          body: PaymentPromotionWidget(
            selectedPromotion: fakeSelectedVoucher,
            voucherSelectionState: VoucherSelectionState.validVoucher,
          ),
        ),
      );

      await tester.pumpWidget(widget);

      final Finder promotionSelectedFinder = find.byType(PromotionSelectedWidget);
      expect(promotionSelectedFinder, findsOneWidget);

      final Finder titleFinder = find.text(fakeVoucherTitle);
      expect(titleFinder, findsOneWidget);

      verify(() => commonImageProvider.asset(
            EvoImages.icEmiPaymentHasPromotion,
          )).called(1);
    });

    testWidgets('calls onOpenPromotionList when tapped', (WidgetTester tester) async {
      final VoidCallback onOpenPromotionList = MockVoidCallback().call;
      final Widget widget = MaterialApp(
        home: PaymentPromotionWidget(
          onOpenPromotionList: onOpenPromotionList,
        ),
      );

      await tester.pumpWidget(widget);
      await tester.tap(find.byType(GestureDetector));
      await tester.pumpAndSettle();

      verify(() => onOpenPromotionList.call()).called(1);
    });

    testWidgets('calls onGlobalOffsetReady after the widget is rendered',
        (WidgetTester tester) async {
      final MockGlobalOffsetReadyCallback onGlobalOffsetReady = MockGlobalOffsetReadyCallback();
      final Widget widget = MaterialApp(
        home: PaymentPromotionWidget(
          onGlobalOffsetReady: onGlobalOffsetReady.call,
        ),
      );

      await tester.pumpWidget(widget);
      await tester.pumpAndSettle();

      verify(() => onGlobalOffsetReady.call(any())).called(1);
    });
  });
}
