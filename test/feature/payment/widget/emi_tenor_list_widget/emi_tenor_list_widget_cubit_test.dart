import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/emi_tenor_offer_entity.dart';
import 'package:evoapp/feature/payment/models/emi_tenor_ui_model.dart';
import 'package:evoapp/feature/payment/widget/emi_tenor_list_widget/emi_tenor_list_widget_cubit.dart';
import 'package:evoapp/feature/payment/widget/emi_tenor_list_widget/emi_tenor_list_widget_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late EmiTenorListWidgetCubit cubit;

  setUp(() {
    cubit = EmiTenorListWidgetCubit();
  });

  test('verify init state', () {
    expect(cubit.state, isA<EmiTenorListWidgetState>());
  });

  group('test convertToEmiTenorModel() function', () {
    final List<EmiPackageEntity> fakeListEmiPackageEntity = <EmiPackageEntity>[];

    tearDown(() {
      fakeListEmiPackageEntity.clear();
      cubit.tenorSelectedIndex = null;
    });

    blocTest<EmiTenorListWidgetCubit, EmiTenorListWidgetState>(
      'keep state and do nothing if list entity is empty',
      build: () => cubit,
      act: (EmiTenorListWidgetCubit cubit) => cubit.initialize(fakeListEmiPackageEntity, ''),
      verify: (_) {
        expect(cubit.tenorSelectedIndex, isNull);
        expect(cubit.state, isA<EmiTenorListWidgetState>());
      },
    );

    blocTest<EmiTenorListWidgetCubit, EmiTenorListWidgetState>(
      'emits [ConvertToEmiTenorModelDoneState, EmiTenorSelectedState] when list entity is not empty and has selected id',
      setUp: () async {
        fakeListEmiPackageEntity.add(
          EmiPackageEntity(
            offer: EmiTenorOfferEntity(id: '1', isRecommended: true),
          ),
        );
      },
      build: () => cubit,
      act: (EmiTenorListWidgetCubit cubit) => cubit.initialize(fakeListEmiPackageEntity, '1'),
      expect: () => <dynamic>[
        isA<UpdateEmiTenorUIModelDoneState>()
            .having((UpdateEmiTenorUIModelDoneState state) => state.listTenorModel.isNotEmpty,
                'verify list tenor not empty', isTrue)
            .having((UpdateEmiTenorUIModelDoneState state) => state.indexSelectedTenorModel,
                'verify indexSelectedTenorModel', 0),
      ],
      verify: (_) {
        expect(cubit.tenorSelectedIndex, 0);
      },
    );

    blocTest<EmiTenorListWidgetCubit, EmiTenorListWidgetState>(
      'emit [ConvertToEmiTenorModelDoneState, EmiTenorSelectedState] when list entity is not empty and has not selected id default',
      setUp: () async {
        fakeListEmiPackageEntity.add(
          EmiPackageEntity(
            offer: EmiTenorOfferEntity(
              id: '1',
              isRecommended: false,
            ),
          ),
        );
      },
      build: () => cubit,
      act: (EmiTenorListWidgetCubit cubit) => cubit.initialize(fakeListEmiPackageEntity, ''),
      expect: () => <dynamic>[
        isA<UpdateEmiTenorUIModelDoneState>().having(
            (UpdateEmiTenorUIModelDoneState state) => state.listTenorModel.isNotEmpty,
            'verify list tenor not empty',
            isTrue),
      ],
      verify: (_) {
        expect(cubit.tenorSelectedIndex, isNull);
      },
    );
  });

  group('test onTapTenor() function', () {
    final List<EmiTenorUIModel?> fakeListTenorModel = <EmiTenorUIModel>[
      EmiTenorUIModel(id: '1', isRecommended: true),
      EmiTenorUIModel(id: '2', isSelected: true),
    ];

    tearDown(() {
      cubit.tenorSelectedIndex = null;
    });

    blocTest<EmiTenorListWidgetCubit, EmiTenorListWidgetState>(
      'only emit list tenor model when index selected is null',
      setUp: () async {
        cubit.tenorSelectedIndex = 1;
        cubit.listTenorModel.addAll(fakeListTenorModel);
      },
      build: () => cubit,
      act: (EmiTenorListWidgetCubit cubit) => cubit.updateTenorSelectedIndex(null),
      expect: () => <dynamic>[
        isA<UpdateEmiTenorUIModelDoneState>().having(
            (UpdateEmiTenorUIModelDoneState state) => state.listTenorModel.length == 2,
            'verify list tenor model has 2 items',
            isTrue),
      ],
      verify: (_) {
        expect(cubit.tenorSelectedIndex, 1);
      },
    );

    blocTest<EmiTenorListWidgetCubit, EmiTenorListWidgetState>(
      'emit [UpdateEmiTenorUIModelDoneState] when index selected is not equal index selected before',
      setUp: () async {
        cubit.tenorSelectedIndex = 1;
        cubit.listTenorModel.addAll(fakeListTenorModel);
      },
      build: () => cubit,
      act: (EmiTenorListWidgetCubit cubit) => cubit.updateTenorSelectedIndex(0),
      expect: () => <dynamic>[
        isA<UpdateEmiTenorUIModelDoneState>().having(
            (UpdateEmiTenorUIModelDoneState state) => state.listTenorModel.length == 2,
            'verify list tenor model has 2 items',
            isTrue),
      ],
      verify: (_) {
        expect(cubit.tenorSelectedIndex, 0);
      },
    );

    blocTest<EmiTenorListWidgetCubit, EmiTenorListWidgetState>(
      'do nothing when index selected is equal index selected before',
      setUp: () async {
        cubit.tenorSelectedIndex = 1;
        cubit.listTenorModel.addAll(fakeListTenorModel);
      },
      build: () => cubit,
      act: (EmiTenorListWidgetCubit cubit) => cubit.updateTenorSelectedIndex(1),
      verify: (_) {
        expect(cubit.tenorSelectedIndex, 1);
      },
    );
  });
}
