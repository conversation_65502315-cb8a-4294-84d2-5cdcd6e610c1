import 'package:evoapp/feature/payment/widget/payment_title_widget/emi_payment_title_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  setUpAll(() {
    getIt.registerLazySingleton(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<CommonImageProvider>(() => CommonImageProviderImpl());

    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('EmiPaymentTitle', () {
    testWidgets('should render correct information when tenor is null',
        (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: EmiPaymentTitle(
            title: 'Payment for {0}',
            merchantStoreName: 'Test Store',
            amount: 10000,
          ),
        ),
      ));

      expect(find.text('Payment for Test Store'), findsOneWidget);
      expect(find.text('10,000đ'), findsOneWidget);
      expect(find.text(EvoStrings.emiOptionSelectTenorTitle), findsOneWidget);
    });

    testWidgets('should render correct information when tenor is not null',
        (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: EmiPaymentTitle(
            title: 'Payment for {0}',
            merchantStoreName: 'Test Store',
            amount: 10000,
            tenor: 3,
          ),
        ),
      ));

      expect(find.text('Payment for Test Store'), findsOneWidget);
      expect(find.text('10,000đ'), findsOneWidget);
      expect(find.text('trong 3 tháng'), findsOneWidget);
    });
  });
}
