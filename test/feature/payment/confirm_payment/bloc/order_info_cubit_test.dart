import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/payment/confirm_payment/bloc/order_info_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/model/order_info_ui_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late OrderInfoCubit orderInfoCubit;
  late AppState appState;

  final OrderSessionEntity orderSessionEntity = OrderSessionEntity(
    id: 'id',
    transactionId: 'transactionId',
  );

  const double fakeConversionFee = 1.0;
  final EmiPackageEntity emiPackageEntity = EmiPackageEntity(
    conversionFee: fakeConversionFee,
  );

  final VoucherEntity voucherEntity = VoucherEntity(
    id: 1,
    code: 'code',
  );

  final ErrorUIModel errorUIModel = ErrorUIModel(userMessage: 'userMessage');

  setUpAll(() {
    appState = AppState();
  });

  setUp(() {
    orderInfoCubit = OrderInfoCubit(appState);
  });

  tearDown(() {
    appState.paymentSharedData.selectedVoucher = null;
    appState.paymentSharedData.orderSession = null;
  });

  test('emit [OrderInfoUpdateState] when init success', () {
    expect(orderInfoCubit.state, isA<OrderInfoUpdateInitial>());
  });

  group('Test updateOrderInfo() function', () {
    blocTest<OrderInfoCubit, OrderInfoState>(
      'Give only order session, should emit [OrderInfoUpdateState] with only order session data',
      build: () => orderInfoCubit,
      act: (OrderInfoCubit cubit) => cubit.updateOrderInfo(
        order: orderSessionEntity,
      ),
      expect: () => <dynamic>[
        isA<OrderInfoUpdateState>()
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.orderSession,
              'verify data order session',
              orderSessionEntity,
            )
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.selectedVoucher,
              'test voucher',
              isNull,
            )
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.voucherSelectionState,
              'test voucherSelectionState',
              VoucherSelectionState.noSelect,
            )
      ],
      verify: (_) {
        expect(orderInfoCubit.order, orderSessionEntity);
        expect(orderInfoCubit.selectedVoucher, isNull);
      },
    );

    blocTest<OrderInfoCubit, OrderInfoState>(
      'Give emiPackage, should emit [OrderInfoUpdateState] with emiPackage',
      build: () => orderInfoCubit,
      act: (OrderInfoCubit cubit) => cubit.updateOrderInfo(
        order: orderSessionEntity,
        emiPackage: emiPackageEntity,
      ),
      expect: () => <dynamic>[
        isA<OrderInfoUpdateState>()
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.orderSession,
              'verify data order session',
              orderSessionEntity,
            )
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.emiPackage,
              'verify data EMI package',
              emiPackageEntity,
            )
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.selectedVoucher,
              'test voucher',
              isNull,
            )
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.voucherSelectionState,
              'test voucherSelectionState',
              VoucherSelectionState.noSelect,
            )
      ],
      verify: (_) {
        expect(orderInfoCubit.order, orderSessionEntity);
        expect(orderInfoCubit.emiPackage, emiPackageEntity);
        expect(orderInfoCubit.selectedVoucher, isNull);
      },
    );

    blocTest<OrderInfoCubit, OrderInfoState>(
      'Give only selected voucher, should emit [OrderInfoUpdateState] with only selected voucher data',
      build: () => orderInfoCubit,
      act: (OrderInfoCubit cubit) => cubit.updateOrderInfo(
        selectedVoucher: voucherEntity,
      ),
      expect: () => <dynamic>[
        isA<OrderInfoUpdateState>()
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.orderSession,
              'verify data order session',
              isNull,
            )
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.emiPackage,
              'verify data EMI package',
              isNull,
            )
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.selectedVoucher,
              'test voucher',
              voucherEntity,
            )
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.voucherSelectionState,
              'test voucherSelectionState',
              VoucherSelectionState.validVoucher,
            )
      ],
      verify: (_) {
        expect(orderInfoCubit.order, isNull);
        expect(orderInfoCubit.emiPackage, isNull);
        expect(orderInfoCubit.selectedVoucher, voucherEntity);
      },
    );

    blocTest<OrderInfoCubit, OrderInfoState>(
      'Give only order session, emi package and voucher,'
      'should emit [OrderInfoUpdateState] with order session, emi package and voucher data',
      build: () => orderInfoCubit,
      act: (OrderInfoCubit cubit) => cubit.updateOrderInfo(
        order: orderSessionEntity,
        emiPackage: emiPackageEntity,
        selectedVoucher: voucherEntity,
      ),
      expect: () => <dynamic>[
        isA<OrderInfoUpdateState>()
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.orderSession,
              'verify data order session',
              orderSessionEntity,
            )
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.emiPackage,
              'verify data EMI package',
              emiPackageEntity,
            )
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.selectedVoucher,
              'test voucher',
              voucherEntity,
            )
            .having(
              (OrderInfoUpdateState p0) => p0.orderInfoUIModel.voucherSelectionState,
              'test voucherSelectionState',
              VoucherSelectionState.validVoucher,
            )
      ],
      verify: (OrderInfoCubit cubit) {
        expect(orderInfoCubit.order, orderSessionEntity);
        expect(orderInfoCubit.emiPackage, emiPackageEntity);
        expect(cubit.selectedVoucherInAppState, voucherEntity);
      },
    );
  });

  group('test needShowNotSelectVoucherWarning() function', () {
    test('case selectedVoucher == null && hasValidVoucher == false', () {
      expect(orderInfoCubit.selectedVoucher, isNull);
      expect(orderInfoCubit.hasValidVoucher, isFalse);

      expect(orderInfoCubit.needShowNotSelectVoucherWarning(), isFalse);
    });

    test('case selectedVoucher != null && hasValidVoucher == false', () {
      appState.paymentSharedData.selectedVoucher = VoucherEntity();

      expect(orderInfoCubit.selectedVoucher, isNotNull);

      expect(orderInfoCubit.needShowNotSelectVoucherWarning(), isFalse);
    });

    test('case selectedVoucher != null && hasValidVoucher == true', () {
      appState.paymentSharedData.selectedVoucher = VoucherEntity(isQualified: true);
      orderInfoCubit.hasValidVoucher = true;

      expect(orderInfoCubit.selectedVoucher, isNotNull);
      expect(orderInfoCubit.needShowNotSelectVoucherWarning(), isFalse);
    });

    test('case selectedVoucher == null && hasValidVoucher == true', () {
      orderInfoCubit.hasValidVoucher = true;

      expect(orderInfoCubit.selectedVoucher, isNull);
      expect(orderInfoCubit.needShowNotSelectVoucherWarning(), isTrue);
    });
  });

  group('Test updateOrderInfoWithInvalidVoucher() function', () {
    blocTest<OrderInfoCubit, OrderInfoState>(
      'Give only order session, should emit [OrderInfoUpdateInvalidVoucherState] with only order session data',
      build: () => orderInfoCubit,
      act: (OrderInfoCubit cubit) => cubit.updateOrderInfoWithInvalidVoucher(
        order: orderSessionEntity,
      ),
      expect: () => <dynamic>[
        isA<OrderInfoUpdateInvalidVoucherState>()
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.orderSession,
              'verify data order session',
              orderSessionEntity,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.selectedVoucher,
              'test voucher',
              isNull,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.voucherSelectionState,
              'test voucherSelectionState',
              VoucherSelectionState.noSelect,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.errorUIModel,
              'test ErrorUIModel',
              isNull,
            )
      ],
      verify: (_) {
        expect(orderInfoCubit.order, orderSessionEntity);
        expect(orderInfoCubit.selectedVoucher, isNull);
      },
    );

    blocTest<OrderInfoCubit, OrderInfoState>(
      'Give Emi package, should emit [OrderInfoUpdateInvalidVoucherState] with Emi package data',
      build: () => orderInfoCubit,
      act: (OrderInfoCubit cubit) => cubit.updateOrderInfoWithInvalidVoucher(
        order: orderSessionEntity,
        emiPackage: emiPackageEntity,
      ),
      expect: () => <dynamic>[
        isA<OrderInfoUpdateInvalidVoucherState>()
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.orderSession,
              'verify data order session',
              orderSessionEntity,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.emiPackage,
              'verify data EMI package',
              emiPackageEntity,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.selectedVoucher,
              'test voucher',
              isNull,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.voucherSelectionState,
              'test voucherSelectionState',
              VoucherSelectionState.noSelect,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.errorUIModel,
              'test ErrorUIModel',
              isNull,
            )
      ],
      verify: (_) {
        expect(orderInfoCubit.order, orderSessionEntity);
        expect(orderInfoCubit.emiPackage, emiPackageEntity);
        expect(orderInfoCubit.selectedVoucher, isNull);
      },
    );

    blocTest<OrderInfoCubit, OrderInfoState>(
      'Give only selected voucher, should emit [OrderInfoUpdateInvalidVoucherState] with only selected voucher data',
      build: () => orderInfoCubit,
      act: (OrderInfoCubit cubit) => cubit.updateOrderInfoWithInvalidVoucher(
        selectedVoucher: voucherEntity,
      ),
      expect: () => <dynamic>[
        isA<OrderInfoUpdateInvalidVoucherState>()
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.orderSession,
              'verify data order session',
              isNull,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.emiPackage,
              'verify data EMI package',
              isNull,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.selectedVoucher,
              'test voucher',
              voucherEntity,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.voucherSelectionState,
              'test voucherSelectionState',
              VoucherSelectionState.invalidVoucher,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.errorUIModel,
              'test ErrorUIModel',
              isNull,
            )
      ],
      verify: (_) {
        expect(orderInfoCubit.order, isNull);
        expect(orderInfoCubit.emiPackage, isNull);
        expect(orderInfoCubit.selectedVoucher, voucherEntity);
      },
    );

    blocTest<OrderInfoCubit, OrderInfoState>(
      'Give only order session and voucher,'
      'should emit [OrderInfoUpdateInvalidVoucherState] with order session and voucher data',
      build: () => orderInfoCubit,
      act: (OrderInfoCubit cubit) => cubit.updateOrderInfoWithInvalidVoucher(
        order: orderSessionEntity,
        emiPackage: emiPackageEntity,
        selectedVoucher: voucherEntity,
      ),
      expect: () => <dynamic>[
        isA<OrderInfoUpdateInvalidVoucherState>()
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.orderSession,
              'verify data order session',
              orderSessionEntity,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.emiPackage,
              'verify data EMI package',
              emiPackageEntity,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.selectedVoucher,
              'test voucher',
              voucherEntity,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.voucherSelectionState,
              'test voucherSelectionState',
              VoucherSelectionState.invalidVoucher,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.errorUIModel,
              'test ErrorUIModel',
              isNull,
            )
      ],
      verify: (OrderInfoCubit cubit) {
        expect(orderInfoCubit.order, orderSessionEntity);
        expect(orderInfoCubit.emiPackage, emiPackageEntity);
        expect(cubit.selectedVoucherInAppState, voucherEntity);
      },
    );

    blocTest<OrderInfoCubit, OrderInfoState>(
      'Give only order session, emi package and voucher,'
      'should emit [OrderInfoUpdateInvalidVoucherState] with order session, emi package, voucher and ErrorUIModel data',
      build: () => orderInfoCubit,
      act: (OrderInfoCubit cubit) => cubit.updateOrderInfoWithInvalidVoucher(
        order: orderSessionEntity,
        emiPackage: emiPackageEntity,
        selectedVoucher: voucherEntity,
        errorUIModel: errorUIModel,
      ),
      expect: () => <dynamic>[
        isA<OrderInfoUpdateInvalidVoucherState>()
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.orderSession,
              'verify data order session',
              orderSessionEntity,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.emiPackage,
              'verify data EMI package',
              emiPackageEntity,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.selectedVoucher,
              'test voucher',
              voucherEntity,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.orderInfoUIModel.voucherSelectionState,
              'test voucherSelectionState',
              VoucherSelectionState.invalidVoucher,
            )
            .having(
              (OrderInfoUpdateInvalidVoucherState p0) => p0.errorUIModel,
              'test ErrorUIModel',
              errorUIModel,
            )
      ],
      verify: (OrderInfoCubit cubit) {
        expect(orderInfoCubit.order, orderSessionEntity);
        expect(orderInfoCubit.emiPackage, emiPackageEntity);
        expect(cubit.selectedVoucherInAppState, voucherEntity);
      },
    );
  });
}
