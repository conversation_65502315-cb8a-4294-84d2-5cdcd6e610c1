import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/emi_tenor_offer_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/activated_pos_limit/utils/activate_pos_limit_constants.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/main_screen/main_screen.dart';
import 'package:evoapp/feature/main_screen/main_screen_controller.dart';
import 'package:evoapp/feature/main_screen/main_screen_initial_action/go_to_voucher_detail_action.dart';
import 'package:evoapp/feature/payment/confirm_payment/clone_confirm_payment_screen/clone_confirm_payment_screen.dart';
import 'package:evoapp/feature/payment/confirm_payment/confirm_payment_fail_screen/confirm_payment_fail_screen.dart';
import 'package:evoapp/feature/payment/models/payment_entry_point.dart';
import 'package:evoapp/feature/payment/payment_shared_data.dart';
import 'package:evoapp/feature/payment/qrcode_scanner/model/qr_code_type.dart';
import 'package:evoapp/feature/payment/result/payment_result_screen.dart';
import 'package:evoapp/feature/payment/utils/payment_navigate_helper_mixin.dart';
import 'package:evoapp/feature/promotion_list/promotion_list_page.dart';
import 'package:evoapp/feature/transaction_detail/transaction_detail_screen.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/widget/countdown/circular_countdown/circular_countdown_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockBuildContext extends Mock implements BuildContext {}

class MockPaymentSharedData extends Mock implements PaymentSharedData {}

class MockAppState extends Mock implements AppState {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockEvoDialogHelper extends Mock implements EvoDialogHelper {}

class TestPaymentNavigationHelperMixin with PaymentNavigationHelperMixin {
  final BuildContext _context;

  TestPaymentNavigationHelperMixin(this._context);

  BuildContext? get navigatorContext => _context;

  int showWaitingBottomSheetMethodCount = 0;

  @override
  Future<void> showWaitingBottomSheet(int remainingTime) {
    showWaitingBottomSheetMethodCount++;
    return super.showWaitingBottomSheet(remainingTime);
  }
}

class MockPaymentNavigationHelperMixin with PaymentNavigationHelperMixin {
  int handlePopToOrderCreationScreenMethodCount = 0;
  int showExpiredOrderBottomSheetMethodCount = 0;

  @override
  void handlePopToOrderCreationScreen() {
    handlePopToOrderCreationScreenMethodCount++;
    super.handlePopToOrderCreationScreen();
  }

  @override
  Future<void> showExpiredOrderBottomSheet() {
    showExpiredOrderBottomSheetMethodCount++;
    return super.showExpiredOrderBottomSheet();
  }

  void resetMethodCount() {
    handlePopToOrderCreationScreenMethodCount = 0;
    showExpiredOrderBottomSheetMethodCount = 0;
  }
}

class MockCommonButtonStyles extends Mock implements CommonButtonStyles {}

void verifyGotoHomeScreenAtPaymentFlowCompleted(BuildContext mockNavigatorContext) {
  expect(
    verify(
      () => mockNavigatorContext.removeUntilAndPushReplacementNamed(
        Screen.mainScreen.name,
        any(),
        extra: captureAny(named: 'extra'),
      ),
    ).captured.single,
    isA<MainScreenArg>()
        .having(
          (MainScreenArg arg) => arg.isLoggedIn,
          'verify isLoggedIn',
          true,
        )
        .having(
          (MainScreenArg arg) => arg.initialPage,
          'verify initialPage',
          MainScreenChild.home,
        )
        .having(
          (MainScreenArg arg) => arg.activateBiometricUseCase,
          'verify activateBiometricUseCase',
          ActivateBiometricUseCase.paymentFlowCompleted,
        ),
  );
}

void main() {
  const String fakeId = 'fakeId';
  const String fakeTransactionId = 'fake_transaction_id';
  const String fakeUserMessage = 'fake_error_message';

  final ButtonStyle expectNegativeButtonStyle = ButtonStyle(
    elevation: WidgetStateProperty.all(0),
  );
  final ButtonStyle expectPositiveButtonStyle = ButtonStyle(
    elevation: WidgetStateProperty.all(10),
  );

  late MockPaymentNavigationHelperMixin mockPaymentNavigationHelperMixin;
  late BuildContext mockNavigatorContext;
  late CommonNavigator mockCommonNavigator;
  late AppState appState;
  late EvoLocalStorageHelper mockEvoLocalStorageHelper;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(EvoDialogId.activePosLimitCancel3DSDialog);
    setUpOneLinkDeepLinkRegExForTest();
    getItRegisterColor();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getIt.registerLazySingleton<CommonButtonStyles>(() => MockCommonButtonStyles());
    getItRegisterTextStyle();

    getIt.registerSingleton<FeatureToggle>(FeatureToggle());
    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());
    mockCommonNavigator = getIt.get<CommonNavigator>();
    setUtilsMockInstanceForTesting();
    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    getIt.registerLazySingleton(() => AppState());
    appState = getIt.get<AppState>();

    getIt.registerSingleton<EvoLocalStorageHelper>(MockEvoLocalStorageHelper());
    mockEvoLocalStorageHelper = getIt.get<EvoLocalStorageHelper>();

    mockPaymentNavigationHelperMixin = MockPaymentNavigationHelperMixin();
  });

  setUp(() {
    when(() => EvoDialogHelper().showDialogConfirm(
          isDismissible: any(named: 'isDismissible'),
          dialogId: any(named: 'dialogId'),
          title: any(named: 'title'),
          content: any(named: 'content'),
          textPositive: any(named: 'textPositive'),
          textNegative: any(named: 'textNegative'),
          onClickPositive: any(named: 'onClickPositive'),
          onClickNegative: any(named: 'onClickNegative'),
        )).thenAnswer((_) => Future<void>.value());

    when(
      () => EvoDialogHelper().showDialogBottomSheet(
        isDismissible: any(named: 'isDismissible'),
        title: any(named: 'title'),
        content: any(named: 'content'),
        dialogId: any(named: 'dialogId'),
        isShowButtonClose: any(named: 'isShowButtonClose'),
        header: any(named: 'header'),
        buttonListOrientation: any(named: 'buttonListOrientation'),
        contentTextStyle: any(named: 'contentTextStyle'),

        /// Positive button
        textPositive: any(named: 'textPositive'),
        positiveButtonStyle: any(named: 'positiveButtonStyle'),
        onClickPositive: any(named: 'onClickPositive'),

        /// Negative button
        textNegative: any(named: 'textNegative'),
        negativeButtonStyle: any(named: 'negativeButtonStyle'),
        onClickNegative: any(named: 'onClickNegative'),
      ),
    ).thenAnswer((_) => Future<void>.value());

    when(() => mockEvoLocalStorageHelper.setLastTimeRequest3DSCardActivation(any()))
        .thenAnswer((_) => Future<void>.value());
  });

  tearDownAll(() {
    resetUtilMockToOriginalInstance();
  });

  group('test handleNavigateToPaymentResultScreen() function', () {
    test('handleNavigateToPaymentResultScreen() called', () {
      mockPaymentNavigationHelperMixin.handleNavigateToPaymentResultScreen(fakeId);

      final Object? capturedData = verify(
        () => mockCommonNavigator.pushReplacementNamed(
          mockNavigatorContext,
          Screen.paymentResultScreen.name,
          extra: captureAny(named: 'extra'),
        ),
      ).captured.single;

      final PaymentResultArg arg = capturedData as PaymentResultArg;
      expect(arg.transactionId, fakeId);
      expect(arg.needPollingProcessingStatus, true);
    });
  });

  group('test handleNavigateToTransactionDetailScreen() function', () {
    test('handleNavigateToTransactionDetailScreen() called', () {
      mockPaymentNavigationHelperMixin.handleNavigateToTransactionDetailScreen(fakeId);

      final Object? capturedData = verify(
        () => mockCommonNavigator.pushNamed(
          mockNavigatorContext,
          Screen.transactionHistoryDetailScreen.name,
          extra: captureAny(named: 'extra'),
        ),
      ).captured.single;

      final TransactionDetailArg arg = capturedData as TransactionDetailArg;
      expect(arg.transactionId, fakeId);
    });
  });

  group('test handleNavigateToConfirmPaymentFailScreen() function', () {
    final OrderSessionEntity orderSession = OrderSessionEntity(transactionId: fakeTransactionId);
    final ErrorUIModel error = ErrorUIModel(userMessage: fakeUserMessage);
    test('handleNavigateToConfirmPaymentFailScreen() called', () {
      mockPaymentNavigationHelperMixin.handleNavigateToConfirmPaymentFailScreen(
        orderSession: orderSession,
        error: error,
      );

      final Object? capturedData = verify(
        () => mockCommonNavigator.pushReplacementNamed(
          mockNavigatorContext,
          Screen.confirmPaymentFail.name,
          extra: captureAny(named: 'extra'),
        ),
      ).captured.single;

      final ConfirmPaymentFailArg arg = capturedData as ConfirmPaymentFailArg;
      expect(arg.error?.userMessage, fakeUserMessage);
    });
  });

  group('test handleNavigateToCloneOrderScreenIfNeeded() function', () {
    const String fakeSessionId = 'fake_session_id';
    const int fakeVoucherId = 1;
    final OrderSessionEntity orderSession =
        OrderSessionEntity(id: fakeSessionId, transactionId: fakeTransactionId);
    final VoucherEntity voucherEntity = VoucherEntity(id: 1);

    tearDown(() {
      appState.paymentSharedData.qrProductCode = null;
    });

    test('CloneConfirmPaymentScreen is called', () {
      mockPaymentNavigationHelperMixin.handleNavigateToCloneOrderScreenIfNeeded(
        orderSessionId: orderSession.id,
        selectedVoucher: voucherEntity,
      );

      final Object? capturedData = verify(
        () => mockCommonNavigator.pushReplacementNamed(
          mockNavigatorContext,
          Screen.cloneConfirmPayment.name,
          extra: captureAny(named: 'extra'),
        ),
      ).captured.single;

      final CloneConfirmPaymentArg arg = capturedData as CloneConfirmPaymentArg;
      expect(arg.orderSessionId, fakeSessionId);
      expect(arg.selectedVoucher?.id, fakeVoucherId);
    });

    test('CloneConfirmPaymentScreen is not called with qrProductCode is VN01 in MVP1', () async {
      appState.paymentSharedData.qrProductCode = QrProductCode.vn01;

      await mockPaymentNavigationHelperMixin.handleNavigateToCloneOrderScreenIfNeeded(
        orderSessionId: orderSession.id,
        selectedVoucher: voucherEntity,
      );

      verifyNever(
        () => mockCommonNavigator.pushReplacementNamed(
          mockNavigatorContext,
          Screen.cloneConfirmPayment.name,
          extra: captureAny(named: 'extra'),
        ),
      );

      verify(() => mockNavigatorContext.popUntilNamed(Screen.qrCodeScannerScreen.name)).called(1);
    });
  });

  group('Test handlePopToCreationScreen', () {
    setUp(() {
      appState.paymentSharedData.orderCreationScreen = null;
    });

    test('Case appState.paymentSharedData.orderCreationScreen null', () {
      ///action
      mockPaymentNavigationHelperMixin.handlePopToOrderCreationScreen();

      ///assert
      verify(() => mockNavigatorContext.pop()).called(1);
      verifyNever(() => mockNavigatorContext.popUntilNamed(any()));
    });

    test('Case appState.paymentSharedData.orderCreationScreen NOT null', () {
      /// arrange
      const Screen expectOrderCreationScreen = Screen.paymentInputAmount;
      appState.paymentSharedData.orderCreationScreen = expectOrderCreationScreen;

      /// action
      mockPaymentNavigationHelperMixin.handlePopToOrderCreationScreen();

      /// assert
      verifyNever(() => mockNavigatorContext.pop());
      expect(
        verify(() => mockNavigatorContext.popUntilNamed(captureAny())).captured.single,
        Screen.paymentInputAmount.name,
      );
    });
  });

  group('verify showExpiredOrderBottomSheet', () {
    const Screen expectOrderCreationScreen = Screen.paymentInputAmount;

    setUpAll(() {
      /// Register any dialog id that will be used in this test
      /// This instance of `EvoDialogId` will only be passed around, but never be interacted with.
      /// Therefore, if `EvoDialogId` is a function, it does not have to return a valid object and
      /// could throw unconditionally
      registerFallbackValue(EvoDialogId.expiredOrderBottomSheet);
      when(() => evoButtonStyles.tertiary(ButtonSize.large)).thenReturn(expectNegativeButtonStyle);
      when(() => evoButtonStyles.primary(ButtonSize.large)).thenReturn(expectPositiveButtonStyle);
    });

    setUp(() {
      appState.paymentSharedData.orderCreationScreen = expectOrderCreationScreen;
      appState.paymentSharedData.orderSession = OrderSessionEntity(transactionId: 'fake_order_id');
      appState.paymentSharedData.selectedVoucher = VoucherEntity(id: 1);
      appState.paymentSharedData.selectedPaymentService = PaymentService.outrightPurchase;
      appState.paymentSharedData.selectedEmiPackage =
          EmiPackageEntity(offer: EmiTenorOfferEntity());
      appState.paymentSharedData.paymentEntryPoint = PaymentEntryPoint.paymentWithEMI;
    });

    tearDown(() {
      mockPaymentNavigationHelperMixin.resetMethodCount();
      appState.paymentSharedData.orderCreationScreen = null;
      appState.paymentSharedData.orderSession = null;
      appState.paymentSharedData.selectedVoucher = null;
      appState.paymentSharedData.selectedPaymentService = null;
      appState.paymentSharedData.selectedEmiPackage = null;
      appState.paymentSharedData.paymentEntryPoint = null;
    });

    tearDownAll(() {
      getItUnRegisterMockCommonUtilFunctionAndImageProvider();
      getItUnRegisterTextStyle();
      getItUnregisterColor();
      getIt.unregister<CommonButtonStyles>();
    });

    test('show bottomSheet with expected info ', () {
      /// action
      mockPaymentNavigationHelperMixin.showExpiredOrderBottomSheet();

      /// verify
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.expiredOrderBottomSheet,
            textPositive: EvoStrings.doAgain,
            title: EvoStrings.orderExpiredTitleBottomSheet,
            content: EvoStrings.orderExpiredDescriptionBottomSheet,
            buttonListOrientation: ButtonListOrientation.verticalDown,
            positiveButtonStyle: expectPositiveButtonStyle,
            textNegative: EvoStrings.moveToHome,
            isDismissible: false,
            contentTextStyle: evoTextStyles.bodyMedium(evoColors.textPassive),
            negativeButtonStyle: expectNegativeButtonStyle,
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });

    test('action of POSITIVE button works as expectation ', () {
      /// action
      mockPaymentNavigationHelperMixin.showExpiredOrderBottomSheet();

      final void Function()? onClickPositive = verify(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.expiredOrderBottomSheet,
            textPositive: EvoStrings.doAgain,
            title: EvoStrings.orderExpiredTitleBottomSheet,
            content: EvoStrings.orderExpiredDescriptionBottomSheet,
            buttonListOrientation: ButtonListOrientation.verticalDown,
            positiveButtonStyle: expectPositiveButtonStyle,
            textNegative: EvoStrings.moveToHome,
            isDismissible: false,
            contentTextStyle: evoTextStyles.bodyMedium(evoColors.textPassive),
            negativeButtonStyle: expectNegativeButtonStyle,
            onClickPositive: captureAny(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).captured.single;

      onClickPositive?.call();

      /// assert
      expect(appState.paymentSharedData.orderSession, isNull);
      expect(appState.paymentSharedData.selectedVoucher, isNull);
      expect(appState.paymentSharedData.selectedPaymentService, isNull);
      expect(appState.paymentSharedData.selectedEmiPackage, isNull);

      expect(appState.paymentSharedData.orderCreationScreen, expectOrderCreationScreen);
      expect(appState.paymentSharedData.paymentEntryPoint, PaymentEntryPoint.paymentWithEMI);

      verify(() => mockCommonNavigator.pop(mockNavigatorContext)).called(1);

      expect(mockPaymentNavigationHelperMixin.handlePopToOrderCreationScreenMethodCount, 1);
    });

    test('action of NEGATIVE button works as expectation ', () {
      /// action
      mockPaymentNavigationHelperMixin.showExpiredOrderBottomSheet();

      final void Function()? onClickNegative = verify(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.expiredOrderBottomSheet,
            textPositive: EvoStrings.doAgain,
            title: EvoStrings.orderExpiredTitleBottomSheet,
            content: EvoStrings.orderExpiredDescriptionBottomSheet,
            buttonListOrientation: ButtonListOrientation.verticalDown,
            positiveButtonStyle: expectPositiveButtonStyle,
            textNegative: EvoStrings.moveToHome,
            isDismissible: false,
            contentTextStyle: evoTextStyles.bodyMedium(evoColors.textPassive),
            negativeButtonStyle: expectNegativeButtonStyle,
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: captureAny(named: 'onClickNegative'),
          )).captured.single;

      onClickNegative?.call();

      /// assert
      expect(appState.paymentSharedData.orderSession, isNull);
      expect(appState.paymentSharedData.selectedVoucher, isNull);
      expect(appState.paymentSharedData.selectedPaymentService, isNull);
      expect(appState.paymentSharedData.selectedEmiPackage, isNull);

      expect(appState.paymentSharedData.orderCreationScreen, isNull);
      expect(appState.paymentSharedData.paymentEntryPoint, isNull);

      verify(() => mockCommonNavigator.pop(mockNavigatorContext)).called(1);

      final MainScreenArg mainScreenArg =
          verify(() => mockCommonNavigator.removeUntilAndPushReplacementNamed(
                mockNavigatorContext,
                Screen.mainScreen.name,
                any(),
                extra: captureAny(named: 'extra'),
              )).captured.single;
      expect(mainScreenArg.isLoggedIn, true);
    });
  });

  group('test handleNavigateWhenCheckoutOrderIsExpired() function', () {
    final OrderSessionEntity orderSession = OrderSessionEntity(transactionId: fakeTransactionId);
    final ErrorUIModel error = ErrorUIModel(userMessage: fakeUserMessage);

    setUpAll(() {
      getItRegisterMockCommonUtilFunctionAndImageProvider();
      getIt.registerLazySingleton<CommonButtonStyles>(() => MockCommonButtonStyles());
      getItRegisterTextStyle();
      getItRegisterColor();

      /// Register any dialog id that will be used in this test
      /// This instance of `EvoDialogId` will only be passed around, but never be interacted with.
      /// Therefore, if `EvoDialogId` is a function, it does not have to return a valid object and
      /// could throw unconditionally
      registerFallbackValue(EvoDialogId.expiredOrderBottomSheet);

      when(
        () => EvoDialogHelper().showDialogBottomSheet(
          title: any(named: 'title'),
          content: any(named: 'content'),
          dialogId: any(named: 'dialogId'),
          isDismissible: any(named: 'isDismissible'),
          isShowButtonClose: any(named: 'isShowButtonClose'),
          header: any(named: 'header'),
          buttonListOrientation: any(named: 'buttonListOrientation'),
          contentTextStyle: any(named: 'contentTextStyle'),

          /// Positive button
          textPositive: any(named: 'textPositive'),
          positiveButtonStyle: any(named: 'positiveButtonStyle'),
          onClickPositive: any(named: 'onClickPositive'),

          /// Negative button
          textNegative: any(named: 'textNegative'),
          negativeButtonStyle: any(named: 'negativeButtonStyle'),
          onClickNegative: any(named: 'onClickNegative'),
        ),
      ).thenAnswer((_) => Future<void>.value());

      when(() => evoButtonStyles.tertiary(ButtonSize.large)).thenReturn(expectNegativeButtonStyle);
      when(() => evoButtonStyles.primary(ButtonSize.large)).thenReturn(expectPositiveButtonStyle);
    });

    tearDown(() {
      mockPaymentNavigationHelperMixin.resetMethodCount();
    });

    tearDownAll(() {
      getItUnRegisterMockCommonUtilFunctionAndImageProvider();
      getItUnRegisterTextStyle();
      getItUnregisterColor();
      getIt.unregister<CommonButtonStyles>();
    });

    test('handleNavigateWhenCheckoutOrderIsExpired() called', () {
      mockPaymentNavigationHelperMixin.handleNavigateWhenCheckoutOrderIsExpired(
        orderSession: orderSession,
        error: error,
      );

      ///verify show bottom sheet
      expect(mockPaymentNavigationHelperMixin.showExpiredOrderBottomSheetMethodCount, 1);
    });
  });

  void verifyGoToMainScreen() {
    expect(
      verify(
        () => mockNavigatorContext.removeUntilAndPushReplacementNamed(
          Screen.mainScreen.name,
          any(),
          extra: captureAny(named: 'extra'),
        ),
      ).captured.single,
      isA<MainScreenArg>()
          .having(
            (MainScreenArg arg) => arg.isLoggedIn,
            'verify isLoggedIn',
            true,
          )
          .having(
            (MainScreenArg arg) => arg.initialAction,
            'verify initialAction',
            null,
          ),
    );
  }

  void verifyGoToVoucherDetail(VoucherEntity expectedVoucher) {
    expect(
      verify(
        () => mockNavigatorContext.removeUntilAndPushReplacementNamed(
          Screen.mainScreen.name,
          any(),
          extra: captureAny(named: 'extra'),
        ),
      ).captured.single,
      isA<MainScreenArg>()
          .having(
            (MainScreenArg arg) => arg.isLoggedIn,
            'verify isLoggedIn',
            true,
          )
          .having(
            (MainScreenArg p0) => p0.initialPage,
            'verify initialPage',
            MainScreenChild.promotion,
          )
          .having(
            (MainScreenArg p0) => p0.initialPromotionTab,
            'verify initialPromotionTab',
            PromotionTabType.myVoucher,
          )
          .having(
            (MainScreenArg arg) => arg.initialAction,
            'verify initialAction',
            isA<GoToVoucherDetailAction>().having(
              (GoToVoucherDetailAction p0) => p0.voucher,
              'verify voucher',
              expectedVoucher,
            ),
          ),
    );
  }

  group('Test handleBackToEntryPointWhenEMIUnqualified', () {
    setUpAll(() {
      when(() => mockNavigatorContext.removeUntilAndPushReplacementNamed(
            Screen.mainScreen.name,
            (Route<dynamic> route) => route.isFirst,
            extra: captureAny(named: 'extra'),
          )).thenAnswer(
        (_) {},
      );
    });

    tearDown(() {
      appState.paymentSharedData.paymentEntryPoint = null;
      appState.paymentSharedData.selectedVoucher = null;
    });

    test(
      'Should call _backToHomeScreen when paymentEntryPoint is null',
      () {
        mockPaymentNavigationHelperMixin.handleBackToEntryPointWhenEMIUnqualified();

        verifyGoToMainScreen();
      },
    );

    test(
      'Should call _backToHomeScreen when paymentEntryPoint is paymentWithEMI',
      () {
        appState.paymentSharedData.paymentEntryPoint = PaymentEntryPoint.paymentWithEMI;
        mockPaymentNavigationHelperMixin.handleBackToEntryPointWhenEMIUnqualified();

        verifyGoToMainScreen();
      },
    );

    test(
      'Should call _backToHomeScreen when paymentEntryPoint is bottomNavigationBar',
      () {
        appState.paymentSharedData.paymentEntryPoint = PaymentEntryPoint.bottomNavigationBar;
        mockPaymentNavigationHelperMixin.handleBackToEntryPointWhenEMIUnqualified();

        verifyGoToMainScreen();
      },
    );

    test(
      'Should call _backToHomeScreen when paymentEntryPoint is voucherDetailScreen, but selectedVoucher is null',
      () {
        appState.paymentSharedData.paymentEntryPoint = PaymentEntryPoint.voucherDetailScreen;
        mockPaymentNavigationHelperMixin.handleBackToEntryPointWhenEMIUnqualified();

        verifyGoToMainScreen();
      },
    );

    test(
      'Should call _backVoucherDetailScreen when paymentEntryPoint is voucherDetailScreen',
      () {
        const int fakeVoucherId = 123;
        final VoucherEntity fakeVoucher = VoucherEntity(id: fakeVoucherId);

        appState.paymentSharedData.paymentEntryPoint = PaymentEntryPoint.voucherDetailScreen;
        appState.paymentSharedData.selectedVoucher = fakeVoucher;

        mockPaymentNavigationHelperMixin.handleBackToEntryPointWhenEMIUnqualified();

        verifyGoToVoucherDetail(fakeVoucher);
      },
    );
  });

  test('Test handlePaymentFlowComplete', () {
    mockPaymentNavigationHelperMixin.handlePaymentFlowComplete();

    verifyGotoHomeScreenAtPaymentFlowCompleted(mockNavigatorContext);
  });

  group('Test openPosSetupUserGuild', () {
    setUpAll(() {
      getItRegisterColor();
      getItRegisterTextStyle();
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );
    });

    tearDownAll(() {
      getItUnregisterColor();
      getItUnRegisterTextStyle();
    });

    test('openPosSetupUserGuild should open web view Enable POS Tutorial after back to home screen',
        () async {
      mockPaymentNavigationHelperMixin.openPosSetupUserGuild();

      verifyGotoHomeScreenAtPaymentFlowCompleted(mockNavigatorContext);

      // Delay to wait the navigation to MainScreen is completed
      await Future<void>.delayed(Duration.zero);
      expect(
        verify(
          () => mockCommonNavigator.pushNamed(
            mockNavigatorContext,
            CommonScreen.webViewPage.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.single,
        isA<CommonWebViewArg>()
            .having(
              (CommonWebViewArg p0) => p0.title,
              'verify title',
              EvoStrings.enablePosLimitWebTitle,
            )
            .having(
              (CommonWebViewArg p0) => p0.url,
              'verify url',
              WebsiteUrl.evoEnablePOSTutorialUrl,
            ),
      );
    });
  });

  group('Test showTransactionTooSoonBottomSheet', () {
    setUpAll(() {
      getIt.registerLazySingleton<CommonButtonStyles>(() => MockCommonButtonStyles());
      getItRegisterMockCommonUtilFunctionAndImageProvider();
      getItRegisterTextStyle();
      getItRegisterColor();
      registerFallbackValue(EvoDialogId.transactionTooSoonBottomSheet);

      when(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            buttonListOrientation: any(named: 'buttonListOrientation'),

            /// Positive button
            textPositive: any(named: 'textPositive'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: any(named: 'onClickPositive'),
          )).thenAnswer((_) => Future<void>.value());

      when(() => evoImageProvider.asset(
            any(),
            width: any(named: 'width'),
            fit: any(named: 'fit'),
          )).thenReturn(const SizedBox());

      when(() => evoButtonStyles.primary(ButtonSize.xLarge)).thenReturn(expectPositiveButtonStyle);
    });

    tearDownAll(() {
      getIt.unregister<CommonButtonStyles>();
      getItUnRegisterMockCommonUtilFunctionAndImageProvider();
      getItUnRegisterTextStyle();
      getItUnregisterColor();
    });

    test('Test default error string', () {
      mockPaymentNavigationHelperMixin.showTransactionTooSoonBottomSheet();
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: EvoStrings.transactionTooSoonBottomSheetTitle,
            content: EvoStrings.transactionTooSoonBottomSheetDescription,
            dialogId: EvoDialogId.transactionTooSoonBottomSheet,
            isShowButtonClose: true,

            /// Positive button
            textPositive: EvoStrings.understand,
            positiveButtonStyle: expectPositiveButtonStyle,
            contentTextStyle: evoTextStyles.bodyLarge(evoColors.textPassive),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);

      verify(() => evoImageProvider.asset(
            EvoImages.imgTransactionTooSoon,
            width: double.infinity,
            fit: BoxFit.fitWidth,
          )).called(1);
    });

    test('Test passed error string', () {
      const String fakeErrorString = 'fake_error_string';

      mockPaymentNavigationHelperMixin.showTransactionTooSoonBottomSheet(
        errorString: fakeErrorString,
      );
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: EvoStrings.transactionTooSoonBottomSheetTitle,
            content: fakeErrorString,
            dialogId: EvoDialogId.transactionTooSoonBottomSheet,
            isShowButtonClose: true,

            /// Positive button
            textPositive: EvoStrings.understand,
            positiveButtonStyle: expectPositiveButtonStyle,
            contentTextStyle: evoTextStyles.bodyLarge(evoColors.textPassive),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);

      verify(() => evoImageProvider.asset(
            EvoImages.imgTransactionTooSoon,
            width: double.infinity,
            fit: BoxFit.fitWidth,
          )).called(1);
    });

    test('Test onClickPositive function closes the bottom sheet', () {
      // Capture the onClickPositive function
      VoidCallback? onClickPositiveCallback;

      when(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            textPositive: any(named: 'textPositive'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: captureAny(named: 'onClickPositive'),
          )).thenAnswer((Invocation invocation) {
        onClickPositiveCallback =
            invocation.namedArguments[const Symbol('onClickPositive')] as VoidCallback?;
        return Future<void>.value();
      });

      // Call the method to trigger the dialog
      mockPaymentNavigationHelperMixin.showTransactionTooSoonBottomSheet();

      // Verify the dialog was shown
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            textPositive: any(named: 'textPositive'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);

      // Ensure the callback was captured
      expect(onClickPositiveCallback, isNotNull);

      // Call the callback
      onClickPositiveCallback!();

      // Verify that navigatorContext.pop() was called
      verify(() => mockNavigatorContext.pop()).called(1);
    });
  });

  group('Test showInsufficientCreditLimitBottomSheet', () {
    setUpAll(() {
      getIt.registerLazySingleton<CommonButtonStyles>(() => MockCommonButtonStyles());
      getItRegisterMockCommonUtilFunctionAndImageProvider();
      getItRegisterTextStyle();
      getItRegisterColor();
      registerFallbackValue(EvoDialogId.creditLimitInsufficientBottomSheet);

      when(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            buttonListOrientation: any(named: 'buttonListOrientation'),

            /// Positive button
            textPositive: any(named: 'textPositive'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: any(named: 'onClickPositive'),
          )).thenAnswer((_) => Future<void>.value());

      when(() => evoImageProvider.asset(
            any(),
            width: any(named: 'width'),
            fit: any(named: 'fit'),
          )).thenReturn(const SizedBox());

      when(() => evoButtonStyles.primary(ButtonSize.large)).thenReturn(expectPositiveButtonStyle);
    });

    tearDownAll(() {
      getIt.unregister<CommonButtonStyles>();
      getItUnRegisterMockCommonUtilFunctionAndImageProvider();
      getItUnRegisterTextStyle();
      getItUnregisterColor();
    });

    test('Test default error string', () {
      mockPaymentNavigationHelperMixin.showInsufficientCreditLimitBottomSheet();
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: EvoStrings.creditLimitInsufficientBottomSheetTitle,
            content: EvoStrings.creditLimitInsufficientBottomSheetDescription,
            dialogId: EvoDialogId.creditLimitInsufficientBottomSheet,
            isShowButtonClose: true,

            /// Positive button
            textPositive: EvoStrings.moveToHome,
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);

      verify(() => evoImageProvider.asset(
            EvoImages.imgInsufficientCreditLimit,
          )).called(1);
    });

    test('Test onClickPositive function closes the bottom sheet and navigates to Main Screen', () {
      // Capture the onClickPositive function
      VoidCallback? onClickPositiveCallback;

      when(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            textPositive: any(named: 'textPositive'),
            onClickPositive: captureAny(named: 'onClickPositive'),
          )).thenAnswer((Invocation invocation) {
        onClickPositiveCallback =
            invocation.namedArguments[const Symbol('onClickPositive')] as VoidCallback?;
        return Future<void>.value();
      });

      // Set up the mock for removeUntilAndPushReplacementNamed
      when(() => mockNavigatorContext.removeUntilAndPushReplacementNamed(
            any(),
            any(),
            extra: any(named: 'extra'),
          )).thenAnswer((_) {});

      // Call the method to trigger the dialog
      mockPaymentNavigationHelperMixin.showInsufficientCreditLimitBottomSheet();

      // Verify the dialog was shown
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            textPositive: any(named: 'textPositive'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);

      // Ensure the callback was captured
      expect(onClickPositiveCallback, isNotNull);

      // Call the callback
      onClickPositiveCallback!();

      // Verify that navigatorContext.pop() was called
      verify(() => mockNavigatorContext.pop()).called(1);

      // Verify that MainScreen.removeUntilAndPushReplacementNamed was called
      verify(() => mockNavigatorContext.removeUntilAndPushReplacementNamed(
            Screen.mainScreen.name,
            any(),
            extra: any(named: 'extra'),
          )).called(1);
    });
  });

  group('Test showWaitingBottomSheet', () {
    const int fakeRemainingTime = 30;

    test('showWaitingBottomSheet should call EvoDialogHelper.showDialogBottomSheet', () {
      // Act
      mockPaymentNavigationHelperMixin.showWaitingBottomSheet(fakeRemainingTime);

      final List<dynamic> capturedData = verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: captureAny(named: 'header'),
            title: EvoStrings.systemNeedTimeToProcess,
            content: EvoStrings.youWaitAndRetryLater,
            dialogId: EvoDialogId.waitingAfterActivateCardBottomSheet,
            isShowButtonClose: true,
            textPositive: EvoStrings.understand,
            onClickPositive: captureAny(named: 'onClickPositive'),
          )).captured;

      expect(capturedData.length, 2);
      final dynamic header = capturedData[0] as CircularCountdownWidget;
      expect(header, isA<CircularCountdownWidget>());
      final CircularCountdownWidget headerWidget = header;
      expect(headerWidget.diameter, 170);
      expect(headerWidget.gapFactor, 1.2);
      expect(headerWidget.strokeWidth, 10);
      expect(headerWidget.remainingCountdown, fakeRemainingTime);
      expect(headerWidget.countdownCurrentColor, evoColors.primary);
      expect(headerWidget.countdownRemainingColor, evoColors.primary);
      expect(headerWidget.textStyle, evoTextStyles.h600(evoColors.primary));
      expect(headerWidget.countdownTotalColor, evoColors.countdownTotalColor);
      expect(headerWidget.backgroundColor, evoColors.countdownBackgroundGradientCenterColor);
      expect(headerWidget.countdownTotal,
          ActivatePosLimitConstants.needToWaitAfterCardActivationInSecond);
      expect(headerWidget.onFinished, isNotNull);

      headerWidget.onFinished?.call();
      verify(() => mockNavigatorContext.pop()).called(1);
      verify(() => mockEvoLocalStorageHelper.setLastTimeRequest3DSCardActivation(null)).called(1);

      final VoidCallback onClickPositiveCallback = capturedData[1] as VoidCallback;
      expect(onClickPositiveCallback, isNotNull);
      onClickPositiveCallback();
      verify(() => mockNavigatorContext.pop()).called(1);
    });
  });

  test('showDialogConfirmCancel3DSActivatePosLimit should call EvoDialogHelper.showDialogConfirm',
      () {
    // Act
    mockPaymentNavigationHelperMixin.showDialogConfirmCancel3DSActivatePosLimit();

    verify(() => EvoDialogHelper().showDialogConfirm(
          isDismissible: any(named: 'isDismissible'),
          dialogId: any(named: 'dialogId'),
          title: any(named: 'title'),
          content: any(named: 'content'),
          textPositive: any(named: 'textPositive'),
          textNegative: any(named: 'textNegative'),
          onClickPositive: any(named: 'onClickPositive'),
          onClickNegative: any(named: 'onClickNegative'),
        )).called(1);
  });
}
