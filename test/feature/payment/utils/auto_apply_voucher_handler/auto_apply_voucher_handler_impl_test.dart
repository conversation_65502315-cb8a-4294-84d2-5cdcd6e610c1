// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/data/repository/campaign_repo.dart';
import 'package:evoapp/data/request/payment_promotion_request.dart';
import 'package:evoapp/data/response/order_extra_info_entity.dart';
import 'package:evoapp/data/response/payment_promotion_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/payment/utils/auto_apply_voucher_handler/auto_apply_voucher_handler_impl.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/mock_file_name_utils/mock_campaign_file_name.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCampaignRepo extends Mock implements CampaignRepo {}

void main() {
  late MockCampaignRepo mockCampaignRepo;
  late AppState appState;
  late AutoApplyVoucherHandlerImpl autoApplyVoucherHandler;

  const String testSessionId = 'test_session_id';

  setUp(() {
    mockCampaignRepo = MockCampaignRepo();
    appState = AppState();

    autoApplyVoucherHandler = AutoApplyVoucherHandlerImpl(mockCampaignRepo, appState);

    // Reset the payment shared data before each test
    appState.paymentSharedData.clearAll();
  });

  group('getAutoApplyVoucher', () {
    test('should return null when autoApplyVoucher is false', () async {
      // Arrange
      appState.paymentSharedData.orderExtraInfo = OrderExtraInfoEntity(autoApplyVoucher: false);

      // Act
      final VoucherEntity? result =
          await autoApplyVoucherHandler.getAutoApplyVoucher(testSessionId);

      // Assert
      expect(result, isNull);
      verifyNever(() => mockCampaignRepo.getQualificationVoucher(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          ));
    });

    test('should return null when autoApplyVoucher is null', () async {
      // Arrange
      appState.paymentSharedData.orderExtraInfo = OrderExtraInfoEntity();

      // Act
      final VoucherEntity? result =
          await autoApplyVoucherHandler.getAutoApplyVoucher(testSessionId);

      // Assert
      expect(result, isNull);
      verifyNever(() => mockCampaignRepo.getQualificationVoucher(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          ));
    });

    test('should return null when orderExtraInfo is null', () async {
      // Arrange
      appState.paymentSharedData.orderExtraInfo = null;

      // Act
      final VoucherEntity? result =
          await autoApplyVoucherHandler.getAutoApplyVoucher(testSessionId);

      // Assert
      expect(result, isNull);
      verifyNever(() => mockCampaignRepo.getQualificationVoucher(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          ));
    });

    test('should return null when no vouchers are available', () async {
      // Arrange
      appState.paymentSharedData.orderExtraInfo = OrderExtraInfoEntity(autoApplyVoucher: true);

      when(() => mockCampaignRepo.getQualificationVoucher(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => PaymentPromotionEntity(vouchers: <VoucherEntity>[]));

      // Act
      final VoucherEntity? result =
          await autoApplyVoucherHandler.getAutoApplyVoucher(testSessionId);

      // Assert
      expect(result, isNull);
      verify(() => mockCampaignRepo.getQualificationVoucher(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('should return null when first voucher is not qualified', () async {
      // Arrange
      appState.paymentSharedData.orderExtraInfo = OrderExtraInfoEntity(autoApplyVoucher: true);

      final List<VoucherEntity> vouchers = <VoucherEntity>[
        VoucherEntity(id: 1, isQualified: false),
        VoucherEntity(id: 2, isQualified: true),
      ];

      when(() => mockCampaignRepo.getQualificationVoucher(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => PaymentPromotionEntity(vouchers: vouchers));

      // Act
      final VoucherEntity? result =
          await autoApplyVoucherHandler.getAutoApplyVoucher(testSessionId);

      // Assert
      expect(result, isNull);
      verify(() => mockCampaignRepo.getQualificationVoucher(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('should return first voucher when it is qualified', () async {
      // Arrange
      appState.paymentSharedData.orderExtraInfo = OrderExtraInfoEntity(autoApplyVoucher: true);

      final VoucherEntity qualifiedVoucher = VoucherEntity(id: 1, isQualified: true);
      final List<VoucherEntity> vouchers = <VoucherEntity>[
        qualifiedVoucher,
        VoucherEntity(id: 2, isQualified: true),
      ];

      when(() => mockCampaignRepo.getQualificationVoucher(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => PaymentPromotionEntity(vouchers: vouchers));

      // Act
      final VoucherEntity? result =
          await autoApplyVoucherHandler.getAutoApplyVoucher(testSessionId);

      // Assert
      expect(result, equals(qualifiedVoucher));
      verify(() => mockCampaignRepo.getQualificationVoucher(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('should pass correct sessionId to getQualificationVoucher', () async {
      // Arrange
      appState.paymentSharedData.orderExtraInfo = OrderExtraInfoEntity(autoApplyVoucher: true);

      final VoucherEntity qualifiedVoucher = VoucherEntity(id: 1, isQualified: true);
      final List<VoucherEntity> vouchers = <VoucherEntity>[qualifiedVoucher];

      // Use captureAny to capture the request parameter
      PaymentPromotionRequest? capturedRequest;
      when(() => mockCampaignRepo.getQualificationVoucher(
            request: captureAny(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((Invocation invocation) {
        capturedRequest = invocation.namedArguments[#request] as PaymentPromotionRequest;
        return Future<PaymentPromotionEntity>.value(PaymentPromotionEntity(vouchers: vouchers));
      });

      // Act
      await autoApplyVoucherHandler.getAutoApplyVoucher(testSessionId);

      // Assert
      verify(() => mockCampaignRepo.getQualificationVoucher(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);

      // Verify the sessionId was passed correctly
      expect(capturedRequest, isNotNull);
      expect(capturedRequest!.sessionId, equals(testSessionId));
    });

    test('should verify mock config is passed correctly', () async {
      // Arrange
      appState.paymentSharedData.orderExtraInfo = OrderExtraInfoEntity(autoApplyVoucher: true);

      final VoucherEntity qualifiedVoucher = VoucherEntity(id: 1, isQualified: true);
      final List<VoucherEntity> vouchers = <VoucherEntity>[qualifiedVoucher];

      // Use captureAny to capture the mockConfig parameter
      MockConfig? capturedConfig;
      when(() => mockCampaignRepo.getQualificationVoucher(
            request: any(named: 'request'),
            mockConfig: captureAny(named: 'mockConfig'),
          )).thenAnswer((Invocation invocation) {
        capturedConfig = invocation.namedArguments[#mockConfig] as MockConfig;
        return Future<PaymentPromotionEntity>.value(PaymentPromotionEntity(vouchers: vouchers));
      });

      // Act
      await autoApplyVoucherHandler.getAutoApplyVoucher(testSessionId);

      // Assert
      verify(() => mockCampaignRepo.getQualificationVoucher(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);

      // Verify the mock config was passed correctly
      expect(capturedConfig, isNotNull);
      expect(capturedConfig!.enable, equals(false));
      expect(capturedConfig!.fileName, equals(getQualificationVoucherMockFileName()));
    });
  });
}
