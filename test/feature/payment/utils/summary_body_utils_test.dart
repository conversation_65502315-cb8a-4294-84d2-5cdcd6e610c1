import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/response/payment_result_transaction_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/payment/utils/summary_body_utils.dart';
import 'package:evoapp/feature/payment/widget/payment_tooltips_widget.dart';
import 'package:evoapp/feature/payment/widget/title_and_description_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

class MockNavigator extends Mock implements NavigatorObserver {}

class TestSummaryBodyUtils with SummaryBodyUtils {}

void main() {
  late TestSummaryBodyUtils utils;
  late FeatureToggle mockFeatureToggle;
  late EvoUtilFunction mockUtilFunction;
  late CommonImageProvider mockImageProvider;
  late BuildContext mockNavigatorContext;

  const String fakePaymentResultEmiDescription = 'paymentResultEmiDescription';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(MockBuildContext());

    getIt.registerSingleton<EvoUtilFunction>(MockEvoUtilFunction());
    mockUtilFunction = getIt.get<EvoUtilFunction>();

    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());
    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    getIt.registerSingleton<FeatureToggle>(MockFeatureToggle());
    mockFeatureToggle = getIt.get<FeatureToggle>();
    when(() => mockFeatureToggle.enableRevampUiFeature).thenReturn(true);

    getIt.registerSingleton<CommonImageProvider>(MockCommonImageProvider());
    mockImageProvider = getIt.get<CommonImageProvider>();
    when(() => mockImageProvider.asset(any(),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
        color: any(named: 'color'))).thenReturn(const SizedBox(width: 20, height: 20));

    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
  });

  setUp(() {
    utils = TestSummaryBodyUtils();
    when(() =>
            mockUtilFunction.evoFormatCurrency(any(), currencySymbol: any(named: 'currencySymbol')))
        .thenReturn('100,000 ₫');

    when(() => mockUtilFunction.cropStringWithMaxLength(any(), any()))
        .thenAnswer((_) => fakePaymentResultEmiDescription);
  });

  group('isEmiTransaction', () {
    test('returns true when transaction is EMI', () {
      final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity(
        paymentService: PaymentService.emi.value,
      );

      expect(utils.isEmiTransaction(transaction), true);
    });

    test('returns false when transaction is not EMI', () {
      final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity(
        paymentService: PaymentService.outrightPurchase.value,
      );

      expect(utils.isEmiTransaction(transaction), isFalse);
    });

    test('returns false when transaction is null', () {
      expect(utils.isEmiTransaction(null), isFalse);
    });
  });

  group('buildItemOrderNumber', () {
    testWidgets('builds widget with correct order number', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemOrderNumber('123456'),
          ),
        ),
      );

      expect(find.text('123456'), findsOneWidget);
    });
  });

  group('buildItemPartnerOrderId', () {
    testWidgets('builds widget with correct partner order ID', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemPartnerOrderId('ORD123'),
          ),
        ),
      );

      expect(find.text('ORD123'), findsOneWidget);
    });

    testWidgets('returns empty widget when partner order ID is null', (WidgetTester tester) async {
      final Widget widget = utils.buildItemPartnerOrderId(null);

      expect(widget, isA<SizedBox>());
      expect((widget as SizedBox).width, 0);
    });
  });

  group('buildItemDescription', () {
    testWidgets('builds widget with correct description', (WidgetTester tester) async {
      when(() => mockUtilFunction.cropStringWithMaxLength('Test description', any()))
          .thenReturn('Test description');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemDescription('Test description'),
          ),
        ),
      );

      expect(find.text('Test description'), findsOneWidget);
    });

    testWidgets('returns empty widget when description is null', (WidgetTester tester) async {
      final Widget widget = utils.buildItemDescription(null);

      expect(widget, isA<SizedBox>());
    });
  });

  group('buildItemOfferTenor', () {
    testWidgets('builds widget with correct tenor', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemOfferTenor(12),
          ),
        ),
      );

      expect(find.textContaining('12'), findsOneWidget);
    });
  });

  group('buildItemPromotionAmount', () {
    testWidgets('builds widget with correct promotion amount', (WidgetTester tester) async {
      when(() => mockUtilFunction.evoFormatCurrency(50000,
          currencySymbol: any(named: 'currencySymbol'))).thenReturn('50,000 ₫');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemPromotionAmount(50000),
          ),
        ),
      );

      expect(find.textContaining('-50,000'), findsOneWidget);
    });

    testWidgets('returns empty widget when promotion amount is null', (WidgetTester tester) async {
      final Widget widget = utils.buildItemPromotionAmount(null);

      expect(widget, isA<SizedBox>());
    });

    testWidgets('returns empty widget when promotion amount is 0', (WidgetTester tester) async {
      final Widget widget = utils.buildItemPromotionAmount(0);

      expect(widget, isA<SizedBox>());
    });
  });

  group('buildItemCashback', () {
    testWidgets('builds widget with correct cashback amount when feature enabled',
        (WidgetTester tester) async {
      when(() => mockFeatureToggle.enableInstantCashbackFeature).thenReturn(true);
      when(() => mockUtilFunction.evoFormatCurrency(10000,
          currencySymbol: any(named: 'currencySymbol'))).thenReturn('10,000 ₫');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemCashback(10000),
          ),
        ),
      );

      expect(find.textContaining('+10,000'), findsOneWidget);
    });

    testWidgets('returns empty widget when cashback feature is disabled',
        (WidgetTester tester) async {
      when(() => mockFeatureToggle.enableInstantCashbackFeature).thenReturn(false);

      final Widget widget = utils.buildItemCashback(10000);

      expect(widget, isA<SizedBox>());
    });

    testWidgets('returns empty widget when cashback amount is null', (WidgetTester tester) async {
      when(() => mockFeatureToggle.enableInstantCashbackFeature).thenReturn(true);

      final Widget widget = utils.buildItemCashback(null);

      expect(widget, isA<SizedBox>());
    });

    testWidgets('returns empty widget when cashback amount is 0', (WidgetTester tester) async {
      when(() => mockFeatureToggle.enableInstantCashbackFeature).thenReturn(true);

      final Widget widget = utils.buildItemCashback(0);

      expect(widget, isA<SizedBox>());
    });
  });

  group('orderNumberCheckoutWidget', () {
    testWidgets('builds widget with correct order number', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.orderNumberCheckoutWidget('ORD456'),
          ),
        ),
      );

      expect(find.text('ORD456'), findsOneWidget);
    });

    testWidgets('returns empty widget when order number is null', (WidgetTester tester) async {
      final Widget widget = utils.orderNumberCheckoutWidget(null);

      expect(widget, isA<SizedBox>());
    });

    testWidgets('returns empty widget when order number is empty', (WidgetTester tester) async {
      final Widget widget = utils.orderNumberCheckoutWidget('');

      expect(widget, isA<SizedBox>());
    });
  });

  group('partnerReferenceLabelWidget', () {
    testWidgets('builds widget with correct partnerReferenceLabel', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.partnerReferenceLabelWidget('ORD456'),
          ),
        ),
      );

      expect(find.text('ORD456'), findsOneWidget);
    });

    testWidgets('returns empty widget when partnerReferenceLabel is null',
        (WidgetTester tester) async {
      final Widget widget = utils.partnerReferenceLabelWidget(null);

      expect(widget, isA<SizedBox>());
    });

    testWidgets('returns empty widget when partnerReferenceLabel is empty',
        (WidgetTester tester) async {
      final Widget widget = utils.partnerReferenceLabelWidget('');

      expect(widget, isA<SizedBox>());
    });
  });

  group('buildItemPartnerReferenceLabel', () {
    testWidgets('builds widget with correct partner reference label', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemPartnerReferenceLabel('REF123'),
          ),
        ),
      );

      expect(find.text('REF123'), findsOneWidget);
    });

    testWidgets('returns empty widget when partner reference label is null',
        (WidgetTester tester) async {
      final Widget widget = utils.buildItemPartnerReferenceLabel(null);

      expect(widget, isA<SizedBox>());
      expect((widget as SizedBox).width, 0);
    });
  });

  group('buildItemCreateAtWithPaymentResult', () {
    testWidgets('builds widget with correct date time', (WidgetTester tester) async {
      final DateTime testDate = DateTime(2023, 5, 15, 10, 30);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemCreateAtWithPaymentResult(testDate),
          ),
        ),
      );

      expect(find.byType(TitleAndDescriptionWidget), findsOneWidget);
    });
  });

  group('buildItemCreateAtWithTransactionDetail', () {
    testWidgets('builds widget with correct date time', (WidgetTester tester) async {
      final DateTime testDate = DateTime(2023, 5, 15, 10, 30);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemCreateAtWithTransactionDetail(testDate),
          ),
        ),
      );

      expect(find.byType(TitleAndDescriptionWidget), findsOneWidget);
    });
  });

  group('buildItemMonthlyAmount', () {
    testWidgets('builds widget with correct monthly amount', (WidgetTester tester) async {
      when(() => mockUtilFunction.evoFormatCurrency(100000,
          currencySymbol: any(named: 'currencySymbol'))).thenReturn('100,000 ₫');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemMonthlyAmount(100000),
          ),
        ),
      );

      expect(find.text('100,000 ₫'), findsOneWidget);
    });
  });

  group('buildItemTradeFee', () {
    testWidgets('builds widget with correct trade fee', (WidgetTester tester) async {
      when(() => mockUtilFunction.evoFormatCurrency(50000,
          currencySymbol: any(named: 'currencySymbol'))).thenReturn('50,000 ₫');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemTradeFee(50000),
          ),
        ),
      );

      expect(find.text('50,000 ₫'), findsOneWidget);
    });
  });

  group('buildItemConversionFee', () {
    testWidgets('builds widget with correct conversion fee', (WidgetTester tester) async {
      when(() => mockUtilFunction.evoFormatCurrency(25000,
          currencySymbol: any(named: 'currencySymbol'))).thenReturn('25,000 ₫');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemConversionFee(25000),
          ),
        ),
      );

      // Verify the title text is displayed
      expect(find.text(EvoStrings.paymentResultEmiConversionFee), findsOneWidget);

      // Verify the tooltip is present
      expect(find.byType(PaymentTooltipsWidget), findsOneWidget);
      expect(find.text(EvoStrings.emiFeeInfoText),
          findsNothing); // Tooltip text is not visible until tapped

      // Verify the formatted amount is displayed
      expect(find.text('25,000 ₫'), findsOneWidget);

      // Verify the TitleAndDescriptionWidget is used
      expect(find.byType(TitleAndDescriptionWidget), findsOneWidget);
    });

    testWidgets('builds widget with null conversion fee', (WidgetTester tester) async {
      when(() => mockUtilFunction.evoFormatCurrency(null,
          currencySymbol: any(named: 'currencySymbol'))).thenReturn('0 ₫');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemConversionFee(null),
          ),
        ),
      );

      // Verify the title text is displayed
      expect(find.text(EvoStrings.paymentResultEmiConversionFee), findsOneWidget);

      // Verify the tooltip is present
      expect(find.byType(PaymentTooltipsWidget), findsOneWidget);

      // Verify the formatted amount is displayed with default value
      expect(find.text('0 ₫'), findsOneWidget);
    });
  });

  group('buildItemInterestRate', () {
    testWidgets('builds widget with correct interest rate', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemInterestRate(5.5),
          ),
        ),
      );

      expect(find.textContaining('5'), findsOneWidget);
    });

    testWidgets('builds widget with hasDashSeparator set to false', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemInterestRate(5.5, hasDashSeparator: false),
          ),
        ),
      );

      expect(find.textContaining('5'), findsOneWidget);
    });
  });

  group('buildEmiRemainingAmount', () {
    testWidgets('builds widget with correct remaining amount', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildEmiRemainingAmount('500,000 ₫'),
          ),
        ),
      );

      expect(find.text('500,000 ₫'), findsOneWidget);
    });
  });

  group('buildEmiRemainingPeriod', () {
    testWidgets('builds widget with correct remaining period', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildEmiRemainingPeriod('3 tháng'),
          ),
        ),
      );

      expect(find.text('3 tháng'), findsOneWidget);
    });
  });

  group('partnerOrderIdCheckoutWidget', () {
    testWidgets('builds widget with correct partner order ID', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.partnerOrderIdCheckoutWidget('ORD789'),
          ),
        ),
      );

      expect(find.text('ORD789'), findsOneWidget);
    });

    testWidgets('returns empty widget when partner order ID is null', (WidgetTester tester) async {
      final Widget widget = utils.partnerOrderIdCheckoutWidget(null);
      expect(widget, isA<SizedBox>());
    });

    testWidgets('returns empty widget when partner order ID is empty', (WidgetTester tester) async {
      final Widget widget = utils.partnerOrderIdCheckoutWidget('');
      expect(widget, isA<SizedBox>());
    });
  });

  group('buildItemMonthlyAmount', () {
    testWidgets('builds widget with correct monthly amount', (WidgetTester tester) async {
      when(() => mockUtilFunction.evoFormatCurrency(200000,
          currencySymbol: any(named: 'currencySymbol'))).thenReturn('200,000 ₫');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemMonthlyAmount(200000),
          ),
        ),
      );

      expect(find.text('200,000 ₫'), findsOneWidget);
    });
  });

  group('buildItemPaymentMethodSource', () {
    testWidgets('builds widget with correct payment method source name',
        (WidgetTester tester) async {
      const String sourceName = 'TPBank';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemPaymentMethodSource(sourceName),
          ),
        ),
      );

      expect(find.text(sourceName), findsOneWidget);
      expect(find.byType(TitleAndDescriptionWidget), findsOneWidget);
      // The credit card icon should be present
      expect(find.text(EvoStrings.paymentResultEmiCheckPaymentMethod), findsOneWidget);
    });

    testWidgets('builds widget with hasDashSeparator set to false', (WidgetTester tester) async {
      const String sourceName = 'TPBank';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemPaymentMethodSource(sourceName, hasDashSeparator: false),
          ),
        ),
      );

      expect(find.text(sourceName), findsOneWidget);
      expect(find.byType(TitleAndDescriptionWidget), findsOneWidget);
    });

    testWidgets('builds widget with null source name', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemPaymentMethodSource(null),
          ),
        ),
      );

      expect(find.byType(TitleAndDescriptionWidget), findsOneWidget);
      // Should still render the widget even with null source name
      expect(find.text(EvoStrings.paymentResultEmiCheckPaymentMethod), findsOneWidget);
    });
  });

  group('buildItemSupport', () {
    testWidgets('builds InkWell widget with correct title', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemSupport(),
          ),
        ),
      );

      // Verify the widget structure
      expect(find.byType(InkWell), findsOneWidget);
      expect(find.byType(TitleAndDescriptionWidget), findsOneWidget);
      expect(find.text(EvoStrings.paymentResultEmiSupport), findsOneWidget);
    });

    testWidgets('has hasDashSeparator set to false', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemSupport(),
          ),
        ),
      );

      // Find the TitleAndDescriptionWidget and verify its properties
      final TitleAndDescriptionWidget widget = tester.widget<TitleAndDescriptionWidget>(
        find.byType(TitleAndDescriptionWidget),
      );
      expect(widget.hasDashSeparator, isFalse);
    });

    testWidgets('has InkWell with onTap function', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: utils.buildItemSupport(),
          ),
        ),
      );

      // Find the InkWell widget
      final InkWell inkWell = tester.widget<InkWell>(find.byType(InkWell));

      // Verify that the onTap function is not null
      expect(inkWell.onTap, isNotNull);

      inkWell.onTap?.call();
      verify(() => getIt<CommonNavigator>().pushNamed(
            any(),
            Screen.feedbackScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });
  });
}
