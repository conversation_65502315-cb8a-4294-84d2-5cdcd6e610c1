import 'package:evoapp/feature/payment/qrcode_scanner/utils/mock_file/mock_merchant_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('enum value MockMerchantUseCase', () {
    expect(
      MockMerchantUseCase.storeInfoActivated.value,
      'store_info_activated.json',
    );
    expect(
      MockMerchantUseCase.storeInfoDeactivated.value,
      'store_info_deactivated.json',
    );
    expect(
      MockMerchantUseCase.storeInfoRecordNotFound.value,
      'store_info_record_not_found.json',
    );
    expect(
      MockMerchantUseCase.storeInfoVerdictUnknown.value,
      'store_info_verdict_unknown.json',
    );
  });

  test('getMockMerchantUseCase returns correct values', () {
    expect(
      getMockMerchantUseCase(MockMerchantUseCase.storeInfoActivated),
      'store_info_activated.json',
    );
    expect(
      getMockMerchantUseCase(MockMerchantUseCase.storeInfoDeactivated),
      'store_info_deactivated.json',
    );
    expect(
      getMockMerchantUseCase(MockMerchantUseCase.storeInfoRecordNotFound),
      'store_info_record_not_found.json',
    );
    expect(
      getMockMerchantUseCase(MockMerchantUseCase.storeInfoVerdictUnknown),
      'store_info_verdict_unknown.json',
    );
  });
}
