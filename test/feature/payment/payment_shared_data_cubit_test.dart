import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/order_extra_info_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/manual_link_card/manual_link_card_config.dart';
import 'package:evoapp/feature/payment/models/payment_entry_point.dart';
import 'package:evoapp/feature/payment/payment_shared_data.dart';
import 'package:evoapp/feature/payment/payment_shared_data_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_test/flutter_test.dart';

class PaymentSharedDataCubitTest extends PaymentSharedDataCubit<String> {
  PaymentSharedDataCubitTest(super.initialState, super.appState);
}

void main() {
  late PaymentSharedDataCubitTest paymentSharedDataCubitTest;
  late AppState appState;
  const String sessionId = 'sessionId';
  const String linkCardRequestId = 'linkCardRequestId';
  const int voucherId = 69;
  const double fakeConversionFee = 0.1;

  final OrderSessionEntity orderSessionEntity = OrderSessionEntity(
    id: sessionId,
  );
  final EmiPackageEntity emiPackageEntity = EmiPackageEntity(conversionFee: fakeConversionFee);
  final VoucherEntity voucherEntity = VoucherEntity(id: voucherId);
  final OrderExtraInfoEntity extraInfo = OrderExtraInfoEntity(
    enableRatingPrompt: true,
    autoApplyVoucher: true,
  );

  setUpAll(() {
    getIt.registerLazySingleton<AppState>(() => AppState());
    appState = getIt.get<AppState>();
    paymentSharedDataCubitTest = PaymentSharedDataCubitTest('state', getIt.get());
  });

  tearDown(() {
    appState.manualLinkCardSharedData = null;
    paymentSharedDataCubitTest.clearPaymentSharedData();
  });

  tearDownAll(() {
    paymentSharedDataCubitTest.close();
  });

  group('Test cubit\'s shared data is AppState\'s data', () {
    test('Test default shared data', () {
      /// Default shared data
      expect(paymentSharedDataCubitTest.hasSharedOrderSession, false);
      expect(paymentSharedDataCubitTest.sharedOrderSessionId, null);
      expect(paymentSharedDataCubitTest.orderSessionInAppState?.id, null);
      expect(paymentSharedDataCubitTest.emiPackageInAppState?.conversionFee, null);
      expect(appState.paymentSharedData.orderSession?.id, null);
      expect(appState.paymentSharedData.orderExtraInfo, null);
      expect(paymentSharedDataCubitTest.enableRatingPrompt, null);

      expect(paymentSharedDataCubitTest.selectedVoucherInAppState, null);
      expect(appState.paymentSharedData.selectedVoucher, null);

      expect(paymentSharedDataCubitTest.linkCardRequestId, null);
      expect(appState.manualLinkCardSharedData.linkCardRequestId, null);
      expect(paymentSharedDataCubitTest.linkCardSession, null);
      expect(appState.manualLinkCardSharedData.linkCardSession, null);
    });

    test('Test update shared data in cubit, the cubit data is the with appState\'s shared data',
        () {
      paymentSharedDataCubitTest.orderSessionInAppState = orderSessionEntity;
      paymentSharedDataCubitTest.emiPackageInAppState = emiPackageEntity;
      paymentSharedDataCubitTest.selectedVoucherInAppState = voucherEntity;
      paymentSharedDataCubitTest.linkCardRequestId = linkCardRequestId;
      paymentSharedDataCubitTest.linkCardSession = sessionId;
      appState.paymentSharedData.orderExtraInfo = extraInfo;

      expect(paymentSharedDataCubitTest.hasSharedOrderSession, true);
      expect(paymentSharedDataCubitTest.sharedOrderSessionId, orderSessionEntity.id);
      expect(paymentSharedDataCubitTest.orderSessionInAppState?.id, orderSessionEntity.id);
      expect(paymentSharedDataCubitTest.emiPackageInAppState?.conversionFee,
          emiPackageEntity.conversionFee);
      expect(appState.paymentSharedData.orderSession?.id, orderSessionEntity.id);
      expect(appState.paymentSharedData.orderExtraInfo, extraInfo);
      expect(paymentSharedDataCubitTest.enableRatingPrompt, true);

      expect(paymentSharedDataCubitTest.selectedVoucherInAppState, voucherEntity);
      expect(appState.paymentSharedData.selectedVoucher, voucherEntity);

      expect(paymentSharedDataCubitTest.linkCardRequestId, linkCardRequestId);
      expect(appState.manualLinkCardSharedData.linkCardRequestId, linkCardRequestId);
      expect(paymentSharedDataCubitTest.linkCardSession, sessionId);
      expect(appState.manualLinkCardSharedData.linkCardSession, sessionId);
    });

    test(
        'Test clear clearLinkCardRequestData, the clearLinkCardRequestData in appState is also cleared',
        () {
      paymentSharedDataCubitTest.linkCardRequestId = linkCardRequestId;
      paymentSharedDataCubitTest.linkCardSession = sessionId;

      paymentSharedDataCubitTest.clearLinkCardRequestData();

      expect(paymentSharedDataCubitTest.linkCardRequestId, null);
      expect(appState.manualLinkCardSharedData.linkCardRequestId, null);

      expect(paymentSharedDataCubitTest.linkCardSession, null);
      expect(appState.manualLinkCardSharedData.linkCardSession, null);
    });
  });

  group('test updateOrderSessionInAppState', () {
    test('Update right order session', () {
      expect(paymentSharedDataCubitTest.hasSharedOrderSession, false);
      expect(paymentSharedDataCubitTest.sharedOrderSessionId, null);

      paymentSharedDataCubitTest.orderSessionInAppState = orderSessionEntity;

      expect(paymentSharedDataCubitTest.hasSharedOrderSession, true);
      expect(paymentSharedDataCubitTest.sharedOrderSessionId, sessionId);
    });
  });

  group('test updateEmiPackageInAppState', () {
    test('Update right order session', () {
      expect(paymentSharedDataCubitTest.emiPackageInAppState, null);

      paymentSharedDataCubitTest.emiPackageInAppState = emiPackageEntity;

      expect(paymentSharedDataCubitTest.emiPackageInAppState?.conversionFee, fakeConversionFee);
    });
  });

  group('test updateSelectedVoucherInAppState', () {
    test('updateSelectedVoucherInAppState success', () {
      expect(paymentSharedDataCubitTest.selectedVoucherInAppState, null);

      paymentSharedDataCubitTest.selectedVoucherInAppState = voucherEntity;

      expect(paymentSharedDataCubitTest.selectedVoucherInAppState, isNotNull);
      expect(paymentSharedDataCubitTest.selectedVoucherInAppState?.id, voucherId);
    });
  });

  group('test updateTimeToWaitingForNextLinkCard', () {
    test('Can update updateTimeToWaitingForNextLinkCard', () {
      expect(
        paymentSharedDataCubitTest.timeToWaitingForNextLinkCardInMin,
        ManualLinkCardConfig.defaultNextRetryIfExitDurationInMinute,
      );

      paymentSharedDataCubitTest.updateTimeToWaitingForNextLinkCard = 10;
      expect(paymentSharedDataCubitTest.timeToWaitingForNextLinkCardInMin, 10);
      expect(appState.manualLinkCardSharedData.timeToWaitingForNextLinkCardInMin, 10);
    });

    test('Give null data, can not update updateTimeToWaitingForNextLinkCard', () {
      paymentSharedDataCubitTest.updateTimeToWaitingForNextLinkCard = 10;

      paymentSharedDataCubitTest.updateTimeToWaitingForNextLinkCard = null;
      expect(paymentSharedDataCubitTest.timeToWaitingForNextLinkCardInMin, 10);
      expect(appState.manualLinkCardSharedData.timeToWaitingForNextLinkCardInMin, 10);
    });
  });

  group('test clearPaymentSharedData', () {
    test('clearPaymentSharedData success', () {
      expect(paymentSharedDataCubitTest.hasSharedOrderSession, false);
      expect(paymentSharedDataCubitTest.sharedOrderSessionId, null);

      paymentSharedDataCubitTest.orderSessionInAppState = orderSessionEntity;
      paymentSharedDataCubitTest.emiPackageInAppState = emiPackageEntity;
      expect(paymentSharedDataCubitTest.hasSharedOrderSession, true);
      expect(paymentSharedDataCubitTest.sharedOrderSessionId, sessionId);

      paymentSharedDataCubitTest.clearPaymentSharedData();
      expect(paymentSharedDataCubitTest.hasSharedOrderSession, false);
      expect(paymentSharedDataCubitTest.emiPackageInAppState, null);
      expect(paymentSharedDataCubitTest.sharedOrderSessionId, null);
    });
  });

  group('test clearManualSharedData', () {
    test('clearManualSharedData success', () {
      paymentSharedDataCubitTest.updateTimeToWaitingForNextLinkCard = 15;

      paymentSharedDataCubitTest.clearTimeToWaitingForNextLinkCard();
      expect(
        paymentSharedDataCubitTest.timeToWaitingForNextLinkCardInMin,
        ManualLinkCardConfig.defaultNextRetryIfExitDurationInMinute,
      );
    });
  });

  group('test clearOrderInfo', () {
    late PaymentSharedData paymentSharedData;

    setUp(() {
      paymentSharedData = appState.paymentSharedData;
    });

    tearDown(() {
      paymentSharedData.clearAll();
    });

    test('clearOrderInfo invoked success', () {
      /// arrange
      paymentSharedData.selectedVoucher = voucherEntity;
      paymentSharedData.orderSession = orderSessionEntity;
      paymentSharedData.selectedEmiPackage = emiPackageEntity;
      paymentSharedData.selectedPaymentService = PaymentService.emi;
      paymentSharedData.paymentEntryPoint = PaymentEntryPoint.paymentWithEMI;

      /// action
      paymentSharedDataCubitTest.clearOrderInfo();

      /// assert
      expect(paymentSharedData.orderSession, isNull);
      expect(paymentSharedData.selectedVoucher, isNull);
      expect(paymentSharedData.selectedPaymentService, isNull);
      expect(paymentSharedData.selectedEmiPackage, isNull);

      expect(paymentSharedData.paymentEntryPoint, PaymentEntryPoint.paymentWithEMI);
    });

    test('clearSelectedVoucher invoked success', () {
      /// arrange
      paymentSharedData.selectedVoucher = voucherEntity;

      /// action
      paymentSharedDataCubitTest.clearSelectedVoucher();

      /// assert
      expect(paymentSharedData.selectedVoucher, isNull);
    });
  });
}
