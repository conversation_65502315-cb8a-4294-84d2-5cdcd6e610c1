import 'package:evoapp/feature/payment/conditions/emi_conditions_utils.dart';
import 'package:evoapp/feature/payment/emi_shared_data.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_test/flutter_test.dart';

class TestEmiConditionsUtils with EmiConditionsUtilsMixin {}

void main() {
  final TestEmiConditionsUtils emiConditionsUtils = TestEmiConditionsUtils();
  late AppState appState;

  setUpAll(() {
    appState = AppState();
    getIt.registerLazySingleton<AppState>(() => appState);
  });

  group('verify checkIfProductCodeValid', () {
    test('should return true if product code is valid', () {
      // Arrange
      const String validProductCode = 'validCode';
      appState.paymentSharedData.emiSharedData.emiConditionInfo = EmiConditionInfo(
        allowProductCodes: <String>[validProductCode],
      );

      // Act
      final bool isValid = emiConditionsUtils.checkIfProductCodeValid(validProductCode);

      // Assert
      expect(isValid, true);
    });

    test('should return false if product code is null', () {
      // Arrange
      const String validProductCode = 'validCode';
      appState.paymentSharedData.emiSharedData.emiConditionInfo = EmiConditionInfo(
        allowProductCodes: <String>[validProductCode],
      );

      // Act
      final bool isValid = emiConditionsUtils.checkIfProductCodeValid(null);

      // Assert
      expect(isValid, false);
    });
  });

  group('verify checkIfMerchantIsValid', () {
    test('should return true if merchant is not in blocked list', () {
      // Arrange
      const String productCode = 'product1';
      const String validMerchantId = 'validMerchant';
      const String blockedMerchantId = 'blockedMerchant';

      appState.paymentSharedData.emiSharedData.emiConditionInfo = EmiConditionInfo(
        productCodeToBlockedMerchants: <String, List<String>>{
          productCode: <String>[blockedMerchantId],
        },
      );

      // Act
      final bool isValid = emiConditionsUtils.checkIfMerchantIsValid(productCode, validMerchantId);

      // Assert
      expect(isValid, true);
    });

    test('should return false if merchant is in blocked list', () {
      // Arrange
      const String productCode = 'product1';
      const String blockedMerchantId = 'blockedMerchant';

      appState.paymentSharedData.emiSharedData.emiConditionInfo = EmiConditionInfo(
        productCodeToBlockedMerchants: <String, List<String>>{
          productCode: <String>[blockedMerchantId],
        },
      );

      // Act
      final bool isBlocked =
          emiConditionsUtils.checkIfMerchantIsValid(productCode, blockedMerchantId);

      // Assert
      expect(isBlocked, false);
    });

    test('should return false if product code is null', () {
      // Act
      final bool isValid = emiConditionsUtils.checkIfMerchantIsValid(null, 'validMerchant');

      // Assert
      expect(isValid, false);
    });

    test('should return false if merchant id is null', () {
      // Act
      final bool isValid = emiConditionsUtils.checkIfMerchantIsValid('validCode', null);

      // Assert
      expect(isValid, false);
    });

    test('should return true if product has no blocked merchants', () {
      // Arrange
      const String productCode = 'product1';
      const String merchantId = 'merchant1';

      appState.paymentSharedData.emiSharedData.emiConditionInfo = EmiConditionInfo(
        productCodeToBlockedMerchants: <String, List<String>>{
          'otherProduct': <String>['blockedMerchant'],
        },
      );

      // Act
      final bool isValid = emiConditionsUtils.checkIfMerchantIsValid(productCode, merchantId);

      // Assert
      expect(isValid, true);
    });
  });

  test('checkIfUserChargeAmountValid should return true if amount is valid', () {
    appState.paymentSharedData.emiSharedData.emiConditionInfo = EmiConditionInfo();

    const int validAmount = 3000000;
    final bool isValid = emiConditionsUtils.checkIfUserChargeAmountValid(validAmount);
    expect(isValid, true);

    const int otherValidAmount = 5000000;
    final bool isOtherValid = emiConditionsUtils.checkIfUserChargeAmountValid(otherValidAmount);
    expect(isOtherValid, true);
  });

  group('checkIfOrderValidForEMI', () {
    test('should return true if all conditions are valid', () {
      // Arrange
      const String validProductCode = 'validCode';
      const int validAmount = 3000000;
      const String validMerchantId = 'validMerchant';

      appState.paymentSharedData.emiSharedData.emiConditionInfo = EmiConditionInfo(
        allowProductCodes: <String>[validProductCode],
        productCodeToBlockedMerchants: <String, List<String>>{
          validProductCode: <String>['blockedMerchant'],
        },
      );

      // Act
      final bool isValid = emiConditionsUtils.checkIfOrderValidForEMI(
          productCode: validProductCode, amount: validAmount, merchantId: validMerchantId);

      // Assert
      expect(isValid, true);
    });

    test('should return false if merchant is blocked', () {
      // Arrange
      const String validProductCode = 'validCode';
      const int validAmount = 3000000;
      const String blockedMerchantId = 'blockedMerchant';

      appState.paymentSharedData.emiSharedData.emiConditionInfo = EmiConditionInfo(
        allowProductCodes: <String>[validProductCode],
        productCodeToBlockedMerchants: <String, List<String>>{
          validProductCode: <String>[blockedMerchantId],
        },
      );

      // Act
      final bool isValid = emiConditionsUtils.checkIfOrderValidForEMI(
          productCode: validProductCode, amount: validAmount, merchantId: blockedMerchantId);

      // Assert
      expect(isValid, false);
    });
  });
}
