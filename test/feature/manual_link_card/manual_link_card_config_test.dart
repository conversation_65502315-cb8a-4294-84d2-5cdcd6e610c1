import 'package:evoapp/feature/manual_link_card/manual_link_card_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Test config value', () {
    expect(ManualLinkCardConfig.intervalPollingSubmitLinkCardInMs, 1000);
    expect(ManualLinkCardConfig.submitLinkCardPollingLimitTime, 120 * 1000);
    expect(ManualLinkCardConfig.linkCardProcessingRedirectUrl, 'evo://card-linking-processing');
    expect(ManualLinkCardConfig.otpWebLoadingTimeLimitInMillisecond, 15 * 1000);
    expect(ManualLinkCardConfig.submissionStatusPollingLimitTime, 120 * 1000);
    expect(ManualLinkCardConfig.submissionStatusPollingDefaultIntervalTime, 5 * 1000);
    expect(ManualLinkCardConfig.defaultNextRetryIfExitDurationInMinute, 15);
  });
}
