import 'package:evoapp/feature/biometric_pin_confirm/biometric_pin_data.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AuthenticateType', () {
    test('biometricToken has correct value', () {
      expect(AuthenticateType.biometricToken.value, 'biometric_token');
    });

    test('pin has correct value', () {
      expect(AuthenticateType.pin.value, 'pin');
    });
  });

  group('BiometricAndPinData', () {
    test('creates instance with correct type and biometricToken', () {
      final BiometricAndPinData data = BiometricAndPinData(
        type: AuthenticateType.biometricToken,
        biometricToken: 'sample_token',
      );

      expect(data.type, AuthenticateType.biometricToken);
      expect(data.biometricToken, 'sample_token');
      expect(data.pin, isNull);
    });

    test('creates instance with correct type and pin', () {
      final BiometricAndPinData data = BiometricAndPinData(
        type: AuthenticateType.pin,
        pin: '1234',
      );

      expect(data.type, AuthenticateType.pin);
      expect(data.pin, '1234');
      expect(data.biometricToken, isNull);
    });

    test('creates instance with null biometricToken and pin when not provided', () {
      final BiometricAndPinData data = BiometricAndPinData(
        type: AuthenticateType.biometricToken,
      );

      expect(data.type, AuthenticateType.biometricToken);
      expect(data.biometricToken, isNull);
      expect(data.pin, isNull);
    });
  });
}