import 'package:evoapp/data/response/private_policy_entity.dart';
import 'package:evoapp/feature/main_screen/user/cubit/user_main_state.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

void main() {
  group('UserMainState', () {
    test('UserMainInitialState should be a subclass of UserMainState', () {
      final UserMainInitialState state = UserMainInitialState();
      expect(state, isA<UserMainState>());
    });

    test('BiometricChangedState should be a subclass of UserMainState', () {
      final BiometricChangedState state = BiometricChangedState();
      expect(state, isA<UserMainState>());
    });

    test('BiometricUnusableState should be a subclass of UserMainState', () {
      final BiometricUnusableState state = BiometricUnusableState();
      expect(state, isA<UserMainState>());
    });

    test('BiometricValidState should be a subclass of UserMainState', () {
      final BiometricValidState state = BiometricValidState();
      expect(state, isA<UserMainState>());
    });

    test('DecreeConsentedState should be a subclass of UserMainState', () {
      final DecreeConsentedState state = DecreeConsentedState();
      expect(state, isA<UserMainState>());
    });

    test('RequestUserActivateBiometricHandledState should be a subclass of UserMainState', () {
      final RequestUserActivateBiometricHandledState state =
          RequestUserActivateBiometricHandledState();
      expect(state, isA<UserMainState>());
    });

    group('DecreeNotYetConsentState', () {
      test('should be a subclass of UserMainState', () {
        final DecreeNotYetConsentState state = DecreeNotYetConsentState(null);
        expect(state, isA<UserMainState>());
      });

      test('should store the provided entity', () {
        final PrivacyPolicyEntity entity = PrivacyPolicyEntity();
        final DecreeNotYetConsentState state = DecreeNotYetConsentState(entity);
        expect(state.entity, equals(entity));
      });

      test('should allow null entity', () {
        final DecreeNotYetConsentState state = DecreeNotYetConsentState(null);
        expect(state.entity, isNull);
      });
    });

    group('DecreeConsentErrorState', () {
      test('should be a subclass of UserMainState', () {
        final ErrorUIModel errorModel = ErrorUIModel();
        final DecreeConsentErrorState state = DecreeConsentErrorState(errorModel);
        expect(state, isA<UserMainState>());
      });

      test('should store the provided errorUIModel', () {
        final ErrorUIModel errorModel = ErrorUIModel();
        final DecreeConsentErrorState state = DecreeConsentErrorState(errorModel);
        expect(state.errorUIModel, equals(errorModel));
      });
    });
  });
}
