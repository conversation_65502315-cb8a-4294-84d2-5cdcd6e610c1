import 'package:evoapp/data/repository/cashback_repo.dart';
import 'package:evoapp/data/request/cashback_records_request.dart';
import 'package:evoapp/data/response/records_cashback_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/main_screen/main_screen_initial_action/show_cashback_list_bottom_sheet_action.dart';
import 'package:evoapp/feature/transaction_history_screen/dialog/cashback_transaction_dialog.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockCashbackRepo extends Mock implements CashbackRepo {}

class MockEventTrackingUtils extends Mock implements EventTrackingUtils {}

void main() {
  const String clickText = 'test_dialog';
  final ShowCashbackListBottomSheetAction action = ShowCashbackListBottomSheetAction();

  late CommonImageProvider mockCommonImageProvider;
  late CashbackRepo mockCashbackRepo;
  late EventTrackingUtils mockEventTrackingUtils;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(CashbackRecordsRequest());

    setUtilsMockInstanceForTesting();

    getIt.registerLazySingleton<GlobalKeyProvider>(() => GlobalKeyProvider());
    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<AppState>(() => AppState());
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
    getIt.registerLazySingleton<EventTrackingUtils>(() => MockEventTrackingUtils());
    mockEventTrackingUtils = getIt<EventTrackingUtils>();

    getIt.registerSingleton<CashbackRepo>(MockCashbackRepo());
    mockCashbackRepo = getIt.get<CashbackRepo>();

    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenAnswer((_) => Container());

    when(() => mockCashbackRepo.getCashbackRecords(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) => Future<RecordsCashbackEntity>.value(RecordsCashbackEntity()));

    when(() => mockEventTrackingUtils.sendUserActionEvent(
          eventId: any(named: 'eventId'),
          metaData: any(named: 'metaData'),
        )).thenAnswer((_) async => Future<void>.value());

    when(() => EvoUiUtils().showHudLoading()).thenAnswer((_) => Future<void>.value());
    when(() => EvoUiUtils().hideHudLoading()).thenAnswer((_) => Future<void>.value());
  });

  tearDownAll(() {
    resetUtilMockToOriginalInstance();
  });

  group('ShowCashbackListBottomSheetAction', () {
    testWidgets('process should call CashbackTransactionDialog.show with onError callback',
        (WidgetTester tester) async {
      // Create a widget that uses the action to show the dialog
      await tester.pumpWidget(
        MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          home: Builder(builder: (BuildContext context) {
            return ElevatedButton(
              onPressed: () {
                action.process();
              },
              child: Text(clickText),
            );
          }),
        ),
      );

      expect(find.text(clickText), findsOneWidget);
      await tester.tap(find.text(clickText));
      await tester.pumpAndSettle();

      // Verify that the CashbackTransactionDialog is shown
      expect(find.byType(CashbackTransactionDialog), findsOneWidget);

      // Verify the dialog title is displayed
      expect(find.text(EvoStrings.transactionHistoryCashbackTitle), findsOneWidget);

      // Verify that the close icon is rendered (called 2 times: init & render UI)
      verify(() => mockCommonImageProvider.asset(EvoImages.icClear)).called(2);
    });

    test('should extend MainScreenInitialAction', () {
      expect(action, isA<ShowCashbackListBottomSheetAction>());
    });

    test('should have empty constructor', () {
      final ShowCashbackListBottomSheetAction newAction = ShowCashbackListBottomSheetAction();
      expect(newAction, isNotNull);
    });

    test('process method calls CashbackTransactionDialog.show with empty onError callback', () {
      // We can verify that the process method doesn't throw an exception
      // when called with proper context setup in the widget test above
      expect(action.process, isA<Function>());
    });
  });
}
