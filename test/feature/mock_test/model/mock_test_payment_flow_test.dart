import 'package:evoapp/feature/mock_test/model/mock_test_payment_flow.dart';
import 'package:evoapp/feature/mock_test/model/mock_test_qr_code_scanning.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MockTestPaymentFlow', () {
    test('Default constructor should set qrCodeScanning to null', () {
      final MockTestPaymentFlow mockTestPaymentFlow = MockTestPaymentFlow();

      expect(mockTestPaymentFlow.qrCodeScanning, isNull);
    });

    test('Constructor with parameters should initialize fields correctly', () {
      final MockTestQrCodeScanning mockTestQrCodeScanning = MockTestQrCodeScanning(
        enable: true,
        qrCodeImage: 'image123',
      );
      final MockTestPaymentFlow mockTestPaymentFlow =
          MockTestPaymentFlow(qrCodeScanning: mockTestQrCodeScanning);

      expect(mockTestPaymentFlow.qrCodeScanning, isNotNull);
      expect(mockTestPaymentFlow.qrCodeScanning!.enable, isTrue);
      expect(mockTestPaymentFlow.qrCodeScanning!.qrCodeImage, equals('image123'));
    });

    test('fromJson should correctly parse a valid JSON with qrCodeScanning', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'qr_code_scanning': <String, dynamic>{
          'enable': true,
          'qr_code_image': 'image456',
        },
      };

      final MockTestPaymentFlow mockTestPaymentFlow = MockTestPaymentFlow.fromJson(json);

      expect(mockTestPaymentFlow.qrCodeScanning, isNotNull);
      expect(mockTestPaymentFlow.qrCodeScanning!.enable, isTrue);
      expect(mockTestPaymentFlow.qrCodeScanning!.qrCodeImage, equals('image456'));
    });

    test('fromJson should handle missing qrCodeScanning gracefully', () {
      final Map<String, dynamic> json = <String, dynamic>{};

      final MockTestPaymentFlow mockTestPaymentFlow = MockTestPaymentFlow.fromJson(json);

      expect(mockTestPaymentFlow.qrCodeScanning, isNull);
    });

    test('fromJson should handle null qrCodeScanning field gracefully', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'qr_code_scanning': null,
      };

      final MockTestPaymentFlow mockTestPaymentFlow = MockTestPaymentFlow.fromJson(json);

      expect(mockTestPaymentFlow.qrCodeScanning, isNull);
    });
  });
}
