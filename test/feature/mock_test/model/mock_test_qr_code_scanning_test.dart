import 'package:evoapp/feature/mock_test/model/mock_test_qr_code_scanning.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MockTestQrCodeScanning', () {
    test('Default constructor should set enable to false and qrCodeImage to null', () {
      final MockTestQrCodeScanning mockTestQrCodeScanning = MockTestQrCodeScanning();

      expect(mockTestQrCodeScanning.enable, isFalse);
      expect(mockTestQrCodeScanning.qrCodeImage, isNull);
    });

    test('Constructor with parameters should initialize fields correctly', () {
      final MockTestQrCodeScanning mockTestQrCodeScanning = MockTestQrCodeScanning(
        enable: true,
        qrCodeImage: 'image123',
      );

      expect(mockTestQrCodeScanning.enable, isTrue);
      expect(mockTestQrCodeScanning.qrCodeImage, equals('image123'));
    });

    test('fromJson should correctly parse a valid JSON', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'enable': true,
        'qr_code_image': 'image456',
      };

      final MockTestQrCodeScanning mockTestQrCodeScanning = MockTestQrCodeScanning.fromJson(json);

      expect(mockTestQrCodeScanning.enable, isTrue);
      expect(mockTestQrCodeScanning.qrCodeImage, equals('image456'));
    });

    test('fromJson should handle missing qrCodeImage', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'enable': false,
      };

      final MockTestQrCodeScanning mockTestQrCodeScanning = MockTestQrCodeScanning.fromJson(json);

      expect(mockTestQrCodeScanning.enable, isFalse);
      expect(mockTestQrCodeScanning.qrCodeImage, isNull);
    });
  });
}
