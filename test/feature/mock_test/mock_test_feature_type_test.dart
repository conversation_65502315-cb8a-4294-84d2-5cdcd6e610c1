import 'package:evoapp/feature/mock_test/mock_test_feature_type.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MockTestFeatureType', () {
    test('payment has the expected value', () {
      expect(MockTestFeatureType.payment.toString(), equals('MockTestFeatureType.payment'));
    });

    test('manualLinkCard has the expected value', () {
      expect(MockTestFeatureType.manualLinkCard.toString(),
          equals('MockTestFeatureType.manualLinkCard'));
    });
  });
}
