import 'package:evoapp/feature/activated_pos_limit/card_confirm_activation_cubit/card_confirm_activation_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CardConfirmActivationState', () {
    test('CardConfirmActivationInitialState can be instantiated', () {
      final CardConfirmActivationInitialState state = CardConfirmActivationInitialState();
      expect(state, isA<CardConfirmActivationInitialState>());
    });

    test('CardConfirmActivationLoadingState can be instantiated', () {
      final CardConfirmActivationLoadingState state = CardConfirmActivationLoadingState();
      expect(state, isA<CardConfirmActivationLoadingState>());
    });

    test('CardConfirmActivationSucceedState can be instantiated', () {
      final CardConfirmActivationSucceedState state = CardConfirmActivationSucceedState();
      expect(state, isA<CardConfirmActivationSucceedState>());
    });

    test('CardConfirmActivationFailureState can be instantiated with errorUIModel', () {
      final ErrorUIModel errorUIModel = ErrorUIModel(userMessage: 'Error occurred');
      final CardConfirmActivationFailureState state =
          CardConfirmActivationFailureState(errorUIModel: errorUIModel);
      expect(state, isA<CardConfirmActivationFailureState>());
      expect(state.errorUIModel, errorUIModel);
    });
  });
}
