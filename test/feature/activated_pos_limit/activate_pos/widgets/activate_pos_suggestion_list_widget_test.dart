import 'package:evoapp/feature/activated_pos_limit/activate_pos/widgets/activate_pos_suggestion_list_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  late EvoUtilFunction evoUtilFunction;

  const String fakeFormatCurrency = '50.0';

  setUpAll(() {
    getItRegisterTextStyle();
    getItRegisterColor();
    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
    evoUtilFunction = getIt.get<EvoUtilFunction>();

    when(() => evoUtilFunction.evoFormatCurrency(
          any(),
          currencySymbol: any(named: 'currencySymbol'),
        )).thenReturn(fakeFormatCurrency);
  });

  testWidgets('ActivatePosSuggestionListWidget displays suggestions and handles taps',
      (WidgetTester tester) async {
    final List<int> suggestions = <int>[1000000, 2000000, 3000000];
    int? tappedSuggestion;

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: ActivatePosSuggestionListWidget(
            suggestions: suggestions,
            onSuggestionTap: (int suggestion) {
              tappedSuggestion = suggestion;
            },
          ),
        ),
      ),
    );

    final Finder sizedBoxFinder = find.byType(SizedBox);
    expect(sizedBoxFinder, findsAtLeast(1));
    final SizedBox sizedBox = tester.widget<SizedBox>(sizedBoxFinder.first);
    expect(sizedBox.height, 24);

    final Finder listViewFinder = find.byType(ListView);
    expect(listViewFinder, findsOneWidget);
    final ListView listView = tester.widget<ListView>(listViewFinder);
    expect(listView.padding, EdgeInsets.symmetric(horizontal: 20));
    expect(listView.scrollDirection, Axis.horizontal);

    final Finder separatorFinder = find.byWidgetPredicate(
      (Widget widget) => widget is SizedBox && widget.width == 8,
    );
    expect(separatorFinder, findsNWidgets(2));

    // Verify the number of items (3 suggestions) and separators (2 separators)
    final Finder itemFinder = find.byType(InkWell);
    expect(itemFinder, findsNWidgets(3));

    for (int i = 0; i < suggestions.length; i++) {
      final Container container = tester.widget<Container>(
        find.descendant(
          of: itemFinder.at(i),
          matching: find.byType(Container),
        ),
      );
      expect(container.padding, const EdgeInsets.symmetric(horizontal: 8, vertical: 4));
      expect(
          container.decoration,
          BoxDecoration(
            color: evoColors.itemPOSLimitSuggestionBg,
            borderRadius: BorderRadius.circular(32),
          ));

      final Text textWidget = tester.widget<Text>(
        find.descendant(
          of: itemFinder.at(i),
          matching: find.byType(Text),
        ),
      );
      expect(textWidget.style?.fontWeight, FontWeight.w700);
      expect(textWidget.style?.color, evoColors.textActive);

      await tester.tap(itemFinder.at(i));
      await tester.pump();
      expect(tappedSuggestion, suggestions[i]);
    }
  });
}
