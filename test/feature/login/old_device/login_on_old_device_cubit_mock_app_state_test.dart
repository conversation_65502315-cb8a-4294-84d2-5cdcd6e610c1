import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/sign_in_otp_entity.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/login/old_device/login_on_old_device_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';
import '../../../util/test_util.dart';
import 'login_on_old_device_test_class_mock.dart';

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockAppState extends Mock implements AppState {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  late AuthenticationRepoMock authenticationRepoMock;
  late EvoLocalStorageHelperMock localStorageHelperMock;
  late BiometricsAuthenticateMock biometricsAuthenticateMock;
  late JwtHelperMock jwtHelperMock;
  late BiometricsTokenModule biometricsTokenModuleMock;
  late EvoUtilFunction mockEvoUtilFunction;

  const String phoneNumber = '0355089123';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    authenticationRepoMock = AuthenticationRepoMock();
    localStorageHelperMock = EvoLocalStorageHelperMock();
    biometricsAuthenticateMock = BiometricsAuthenticateMock();
    jwtHelperMock = JwtHelperMock();

    getIt.registerLazySingleton<BiometricsTokenModule>(() => BiometricsTokenModuleMock());
    biometricsTokenModuleMock = getIt.get<BiometricsTokenModule>();

    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    getIt.registerLazySingleton<AppState>(() => MockAppState());

    when(() => localStorageHelperMock.getUserPhoneNumber()).thenAnswer((_) async {
      return phoneNumber;
    });
  });

  setUp(() {
    when(() => mockEvoUtilFunction.updateProcessUserStatus(any())).thenAnswer((_) async {
      return Future<void>.value();
    });

    when(() => mockEvoUtilFunction.getFacialVerificationVersion()).thenReturn(
      FacialVerificationVersion.version_3,
    );
  });

  tearDown(() {
    reset(mockEvoUtilFunction);
  });

  LoginOnOldDeviceCubit getLoginCubit() {
    return LoginOnOldDeviceCubit(
      authenticationRepoMock,
      localStorageHelperMock,
      biometricsAuthenticateMock,
      jwtHelperMock,
      biometricsTokenModuleMock,
    );
  }

  group('test loginByPinCode function', () {
    const String pin = '123456';
    late SignInOtpEntity expectResponse;

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'test login with pin success status revoke_deletion',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('input_pin_success_revoke_deletion.json');
        expectResponse = SignInOtpEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        ));
        when(() => authenticationRepoMock.login(
              TypeLogin.pin,
              pin: pin,
              phoneNumber: phoneNumber,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return expectResponse;
        });
      },
      build: () => getLoginCubit(),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (LoginOnOldDeviceCubit cubit) => cubit.loginByPinCode(pin),
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceLoading>(),
        isA<LoginOnOldDeviceSuccess>()
            .having(
              (LoginOnOldDeviceSuccess state) => state.loginType,
              'verify loginType value is TypeLogin.pin',
              TypeLogin.pin,
            )
            .having(
              (LoginOnOldDeviceSuccess p0) => p0.entity?.action,
              'test response',
              expectResponse.action,
            )
            .having(
              (LoginOnOldDeviceSuccess p0) => p0.entity?.challengeType,
              'test challengeType',
              expectResponse.challengeType,
            ),
      ],
      verify: (_) {
        verify(() => mockEvoUtilFunction.updateProcessUserStatus('revoke_deletion')).called(1);
      },
    );

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'test login with pin success status normal',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('input_pin_success_normal.json');
        expectResponse = SignInOtpEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        ));

        when(() => authenticationRepoMock.login(
              TypeLogin.pin,
              pin: pin,
              phoneNumber: phoneNumber,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return expectResponse;
        });
      },
      build: () => getLoginCubit(),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (LoginOnOldDeviceCubit cubit) => cubit.loginByPinCode(pin),
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceLoading>(),
        isA<LoginOnOldDeviceSuccess>()
            .having(
              (LoginOnOldDeviceSuccess state) => state.loginType,
              'verify loginType value is TypeLogin.pin',
              TypeLogin.pin,
            )
            .having(
              (LoginOnOldDeviceSuccess p0) => p0.entity?.action,
              'test response',
              expectResponse.action,
            )
            .having(
              (LoginOnOldDeviceSuccess p0) => p0.entity?.challengeType,
              'test challengeType',
              expectResponse.challengeType,
            ),
      ],
      verify: (_) {
        verify(() => mockEvoUtilFunction.updateProcessUserStatus('normal')).called(1);
      },
    );

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'test login with pin success status normal',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('input_pin_success_unhandled.json');
        expectResponse = SignInOtpEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        ));
        when(() => authenticationRepoMock.login(
              TypeLogin.pin,
              pin: pin,
              phoneNumber: phoneNumber,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return expectResponse;
        });
      },
      build: () => getLoginCubit(),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (LoginOnOldDeviceCubit cubit) => cubit.loginByPinCode(pin),
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceLoading>(),
        isA<LoginOnOldDeviceSuccess>()
            .having(
              (LoginOnOldDeviceSuccess state) => state.loginType,
              'verify loginType value is TypeLogin.pin',
              TypeLogin.pin,
            )
            .having(
              (LoginOnOldDeviceSuccess p0) => p0.entity?.action,
              'test response',
              expectResponse.action,
            )
            .having(
              (LoginOnOldDeviceSuccess p0) => p0.entity?.challengeType,
              'test challengeType',
              expectResponse.challengeType,
            ),
      ],
      verify: (_) {
        verify(() => mockEvoUtilFunction.updateProcessUserStatus('unhandled_status')).called(1);
      },
    );

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'test login with pin error',
      setUp: () {
        when(() => authenticationRepoMock.login(
              TypeLogin.pin,
              pin: pin,
              phoneNumber: phoneNumber,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return SignInOtpEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: <String, dynamic>{'statusCode': CommonHttpClient.BAD_REQUEST},
          ));
        });
      },
      build: () => getLoginCubit(),
      act: (LoginOnOldDeviceCubit cubit) => cubit.loginByPinCode(pin),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceLoading>(),
        isA<LoginOnOldDeviceError>()
            .having(
              (LoginOnOldDeviceError state) => state.loginType,
              'verify loginType value is TypeLogin.pin',
              TypeLogin.pin,
            )
            .having(
              (LoginOnOldDeviceError state) => state.error.statusCode,
              'verify error statusCode value is BAD_REQUEST',
              CommonHttpClient.BAD_REQUEST,
            ),
      ],
      verify: (_) {
        verifyNever(() => mockEvoUtilFunction.updateProcessUserStatus(any()));
      },
    );
  });

  group('test loginByBiometric function', () {
    const String biometricToken = 'biometricToken';
    late SignInOtpEntity expectResponse;

    setUpAll(() {
      when(() => localStorageHelperMock.getBiometricToken()).thenAnswer((_) async {
        return biometricToken;
      });
    });

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'test login with biometric success status revoke_deletion',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('input_pin_success_revoke_deletion.json');
        expectResponse = SignInOtpEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        ));
        when(() => authenticationRepoMock.login(
              TypeLogin.token,
              token: biometricToken,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return expectResponse;
        });
      },
      build: () => getLoginCubit(),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (LoginOnOldDeviceCubit cubit) => cubit.loginByBiometric(),
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceLoading>(),
        isA<LoginOnOldDeviceSuccess>()
            .having(
              (LoginOnOldDeviceSuccess state) => state.loginType,
              'verify loginType value is TypeLogin.token',
              TypeLogin.token,
            )
            .having(
              (LoginOnOldDeviceSuccess p0) => p0.entity?.action,
              'test response',
              expectResponse.action,
            )
            .having(
              (LoginOnOldDeviceSuccess p0) => p0.entity?.challengeType,
              'test challengeType',
              expectResponse.challengeType,
            ),
      ],
      verify: (_) {
        verify(() => mockEvoUtilFunction.updateProcessUserStatus('revoke_deletion')).called(1);
      },
    );

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'test login with biometric success status normal',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('input_pin_success_normal.json');
        expectResponse = SignInOtpEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        ));
        when(() => authenticationRepoMock.login(
              TypeLogin.token,
              token: biometricToken,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return expectResponse;
        });
      },
      build: () => getLoginCubit(),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (LoginOnOldDeviceCubit cubit) => cubit.loginByBiometric(),
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceLoading>(),
        isA<LoginOnOldDeviceSuccess>()
            .having(
              (LoginOnOldDeviceSuccess state) => state.loginType,
              'verify loginType value is TypeLogin.token',
              TypeLogin.token,
            )
            .having(
              (LoginOnOldDeviceSuccess p0) => p0.entity?.action,
              'test response',
              expectResponse.action,
            )
            .having(
              (LoginOnOldDeviceSuccess p0) => p0.entity?.challengeType,
              'test challengeType',
              expectResponse.challengeType,
            ),
      ],
      verify: (_) {
        verify(() => mockEvoUtilFunction.updateProcessUserStatus('normal')).called(1);
      },
    );

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'test login with biometric success status unhandled',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('input_pin_success_unhandled.json');
        expectResponse = SignInOtpEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        ));
        when(() => authenticationRepoMock.login(
              TypeLogin.token,
              token: biometricToken,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return expectResponse;
        });
      },
      build: () => getLoginCubit(),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (LoginOnOldDeviceCubit cubit) => cubit.loginByBiometric(),
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceLoading>(),
        isA<LoginOnOldDeviceSuccess>()
            .having(
              (LoginOnOldDeviceSuccess state) => state.loginType,
              'verify loginType value is TypeLogin.token',
              TypeLogin.token,
            )
            .having(
              (LoginOnOldDeviceSuccess p0) => p0.entity?.action,
              'test response',
              expectResponse.action,
            )
            .having(
              (LoginOnOldDeviceSuccess p0) => p0.entity?.challengeType,
              'test challengeType',
              expectResponse.challengeType,
            ),
      ],
      verify: (_) {
        verify(() => mockEvoUtilFunction.updateProcessUserStatus('unhandled_status')).called(1);
      },
    );

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'test login with biometric error',
      setUp: () {
        when(() => authenticationRepoMock.login(
              TypeLogin.token,
              token: biometricToken,
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return SignInOtpEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: <String, dynamic>{'statusCode': CommonHttpClient.BAD_REQUEST},
          ));
        });
      },
      build: () => getLoginCubit(),
      act: (LoginOnOldDeviceCubit cubit) => cubit.loginByBiometric(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceLoading>(),
        isA<LoginOnOldDeviceError>()
            .having(
              (LoginOnOldDeviceError state) => state.loginType,
              'verify loginType value is TypeLogin.token',
              TypeLogin.token,
            )
            .having(
              (LoginOnOldDeviceError state) => state.error.statusCode,
              'verify error statusCode value is BAD_REQUEST',
              CommonHttpClient.BAD_REQUEST,
            ),
      ],
      verify: (_) {
        verifyNever(() => mockEvoUtilFunction.updateProcessUserStatus(any()));
      },
    );
  });
}
