import 'package:evoapp/feature/ekyc_v2/ekyc_v2_flow_callback.dart';
import 'package:evoapp/feature/ekyc_v2/ekyc_v2_flow_type.dart';
import 'package:flutter_test/flutter_test.dart';

class TestEkycV2FlowPayload implements EkycV2FlowPayload {}

void main() {
  group('EkycV2FlowCallback', () {
    test('should initialize with correct flowType', () {
      final EkycV2FlowCallback callback = EkycV2FlowCallback(flowType: EkycV2FlowType.linkCard);
      expect(callback.flowType, equals(EkycV2FlowType.linkCard));
    });

    test('onSuccess callback should be invoked with correct payload', () {
      final TestEkycV2FlowPayload payload = TestEkycV2FlowPayload();
      bool wasCalled = false;
      final EkycV2FlowCallback callback = EkycV2FlowCallback(
        flowType: EkycV2FlowType.signIn,
        onSuccess: (EkycV2FlowPayload p) {
          expect(p, equals(payload));
          wasCalled = true;
        },
      );

      callback.onSuccess?.call(payload);
      expect(wasCalled, isTrue);
    });

    test('onError callback should be invoked with correct error reason', () {
      bool wasCalled = false;
      final EkycV2FlowCallback callback = EkycV2FlowCallback(
        flowType: EkycV2FlowType.linkCard,
        onError: (EkycV2FlowErrorReason errorType) {
          expect(errorType, equals(EkycV2FlowErrorReason.userCancelled));
          wasCalled = true;
        },
      );

      callback.onError?.call(EkycV2FlowErrorReason.userCancelled);
      expect(wasCalled, isTrue);
    });
  });

  group('EkycV2FlowErrorReason', () {
    test('should have correct enum values', () {
      expect(EkycV2FlowErrorReason.values.length, equals(4));
      expect(EkycV2FlowErrorReason.userCancelled, equals(EkycV2FlowErrorReason.values[0]));
      expect(EkycV2FlowErrorReason.exceedLimit, equals(EkycV2FlowErrorReason.values[1]));
      expect(EkycV2FlowErrorReason.sessionError, equals(EkycV2FlowErrorReason.values[2]));
      expect(EkycV2FlowErrorReason.initializeError, equals(EkycV2FlowErrorReason.values[3]));
    });

    test('toString should return correct names', () {
      expect(EkycV2FlowErrorReason.userCancelled.toString(),
          equals('EkycV2FlowErrorReason.userCancelled'));
      expect(EkycV2FlowErrorReason.exceedLimit.toString(),
          equals('EkycV2FlowErrorReason.exceedLimit'));
      expect(EkycV2FlowErrorReason.sessionError.toString(),
          equals('EkycV2FlowErrorReason.sessionError'));
      expect(EkycV2FlowErrorReason.initializeError.toString(),
          equals('EkycV2FlowErrorReason.initializeError'));
    });
  });
}
