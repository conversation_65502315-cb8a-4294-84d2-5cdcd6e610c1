import 'package:evoapp/data/response/remote_config_feature_toggle_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('constructor works correctly', () {
    test('create FeatureToggle instance', () {
      final FeatureToggle featureToggle = FeatureToggle();

      expect(featureToggle.reminderVisibility, false);
      expect(featureToggle.enableTransactionNotificationFeature, false);
      expect(
          featureToggle.enableDeleteAccountFeatureVersion, DeleteAccountFeatureVersion.version_2);
      expect(featureToggle.enableCardStatusFeatureVersion, CardStatusFeatureVersion.version_5);
      expect(featureToggle.enableDynamicQrCodeFeature, QrCodeFeatureVersion.version_2);
      expect(featureToggle.enableEmiFeatureVersion, EmiFeatureVersion.version_1);
      expect(featureToggle.enablePreApplyVoucher, true);
      expect(featureToggle.enableEvoAliceChat, AliceVersion.version_3);
      expect(featureToggle.enableEventTrackingFeature, true);
      expect(featureToggle.enableDOPNativeFeature, true);
      expect(featureToggle.facialVerificationVersion, FacialVerificationVersion.version_3);
      expect(featureToggle.enableRemoteConfigFeature, false);
      expect(featureToggle.enableEmiManagementFeature, true);
      expect(featureToggle.nfcSdkProvider, NfcSdkProvider.fpt);
      expect(featureToggle.enableActivatePOSLimitFeature, true);
      expect(featureToggle.enableDOPNativeCollectLocationFeature, true);
      expect(featureToggle.enableInstantCashbackFeature, true);
      expect(featureToggle.enableRevampUiFeature, true);
      expect(featureToggle.transactionHistoryVersion, TransactionHistoryVersion.version_2);
      expect(featureToggle.enableRequestReviewRatingFeature, true);
    });
  });

  group('mapFromRemoteConfigEntity() method works correctly', () {
    test('RemoteConfigFeatureToggleEntity instance with default value', () {
      final FeatureToggle featureToggle = FeatureToggle();
      final RemoteConfigFeatureToggleEntity entity = RemoteConfigFeatureToggleEntity();
      featureToggle.mapFromRemoteConfigEntity(entity);

      expect(featureToggle.reminderVisibility, false);
      expect(featureToggle.enableTransactionNotificationFeature, false);
      expect(
          featureToggle.enableDeleteAccountFeatureVersion, DeleteAccountFeatureVersion.version_2);
      expect(featureToggle.enableCardStatusFeatureVersion, CardStatusFeatureVersion.version_5);
      expect(featureToggle.enableEmiFeatureVersion, EmiFeatureVersion.version_1);
      expect(featureToggle.enablePreApplyVoucher, true);
      expect(featureToggle.enableEvoAliceChat, AliceVersion.version_3);
      expect(featureToggle.enableEventTrackingFeature, true);
      expect(featureToggle.enableDOPNativeFeature, true);
      expect(featureToggle.facialVerificationVersion, FacialVerificationVersion.version_3);
      expect(featureToggle.enableRemoteConfigFeature, false);
      expect(featureToggle.enableEmiManagementFeature, true);
      expect(featureToggle.nfcSdkProvider, NfcSdkProvider.fpt);
      expect(featureToggle.enableActivatePOSLimitFeature, true);
      expect(featureToggle.enableDOPNativeCollectLocationFeature, true);
      expect(featureToggle.enableInstantCashbackFeature, true);
      expect(featureToggle.enableRevampUiFeature, true);
      expect(featureToggle.transactionHistoryVersion, TransactionHistoryVersion.version_2);
      expect(featureToggle.enableRequestReviewRatingFeature, true);
    });
  });
}
