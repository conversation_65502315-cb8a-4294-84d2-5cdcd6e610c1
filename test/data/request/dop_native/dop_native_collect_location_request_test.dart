import 'package:evoapp/data/request/dop_native/dop_native_collect_location_request.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeCollectLocationRequest', () {
    test('to<PERSON><PERSON> returns null when all fields are null', () {
      final DOPNativeCollectLocationRequest request = DOPNativeCollectLocationRequest();
      expect(request.toJson(), isNull);
    });

    test('to<PERSON><PERSON> returns correct map when all fields are provided', () {
      final DOPNativeCollectLocationRequest request = DOPNativeCollectLocationRequest(
        latitude: '12.345678',
        longitude: '98.765432',
        hasLocationRequest: true,
        isAllowedLocationTracking: false,
      );
      expect(request.toJson(), <String, Object>{
        'lat': '12.345678',
        'long': '98.765432',
        'has_location_request': true,
        'is_allowed_location_tracking': 'false',
      });
    });

    test('to<PERSON><PERSON> excludes null fields', () {
      final DOPNativeCollectLocationRequest request = DOPNativeCollectLocationRequest(
        latitude: '12.345678',
        isAllowedLocationTracking: true,
      );
      expect(request.toJson(), <String, String>{
        'lat': '12.345678',
        'is_allowed_location_tracking': 'true',
      });
    });

    test('toJson returns correct map when some fields are null', () {
      final DOPNativeCollectLocationRequest request = DOPNativeCollectLocationRequest(
        longitude: '98.765432',
        hasLocationRequest: false,
      );
      expect(request.toJson(), <String, Object>{
        'long': '98.765432',
        'has_location_request': false,
      });
    });
  });
}
