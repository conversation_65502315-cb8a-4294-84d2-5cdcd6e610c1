import 'package:evoapp/data/response/decree_version_entity.dart';
import 'package:evoapp/data/response/private_policy_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';

void main() {
  group('PrivacyPolicyEntity', () {
    const Map<String, dynamic> fakeDecreeVersionJson = <String, dynamic>{
      'id': 1,
      'version': 2,
    };

    test('init constructor', () {
      final PrivacyPolicyEntity entity = PrivacyPolicyEntity(
        consented: true,
        createdAt: '2023-01-01',
        privacyPolicyTitle: 'Privacy Policy',
        decreeVersion: DecreeVersionEntity(id: 1),
      );

      expect(entity.consented, true);
      expect(entity.createdAt, '2023-01-01');
      expect(entity.privacyPolicyTitle, 'Privacy Policy');
      expect(entity.decreeVersion?.id, 1);
    });

    test('fromBaseResponse creates a valid object', () {
      final BaseResponse response = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'consented': true,
            'created_at': '2023-01-01',
            'privacy_policy_title': 'Privacy Policy',
            'decree_version': fakeDecreeVersionJson,
          }
        },
      );

      final PrivacyPolicyEntity entity = PrivacyPolicyEntity.fromBaseResponse(response);

      expect(entity.consented, true);
      expect(entity.createdAt, '2023-01-01');
      expect(entity.privacyPolicyTitle, 'Privacy Policy');
      expect(entity.decreeVersion, isNotNull);
      expect(entity.decreeVersion?.id, 1);
      expect(entity.decreeVersion?.version, 2);
    });

    test('unserializable constructor sets localExceptionCode', () {
      final PrivacyPolicyEntity entity = PrivacyPolicyEntity.unserializable();

      expect(entity.consented, isNull);
      expect(entity.createdAt, isNull);
      expect(entity.privacyPolicyTitle, isNull);
      expect(entity.decreeVersion, isNull);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });
  });
}
