import 'package:evoapp/data/response/dop_native/dop_native_cif_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeCifInfoEntity', () {
    test('from<PERSON>son should properly construct entity from JSON', () {
      final Map<String, dynamic> jsonMap = <String, dynamic>{
        'idCard': '1234567890',
        'useNewCif': true,
      };

      final DOPNativeCifInfoEntity entity = DOPNativeCifInfoEntity.fromJson(jsonMap);

      expect(entity.idCard, '1234567890');
      expect(entity.useNewCif, true);
    });

    test('toJson should properly convert entity to JSON', () {
      final DOPNativeCifInfoEntity entity = DOPNativeCifInfoEntity.fromJson(<String, dynamic>{
        'idCard': '0987654321',
        'useNewCif': false,
      });

      final Map<String, dynamic> json = entity.toJson();

      expect(json, <String, dynamic>{
        'idCard': '0987654321',
        'useNewCif': false,
      });
    });

    test('fromJson should handle null values', () {
      final Map<String, dynamic> jsonMap = <String, dynamic>{
        'idCard': null,
        'useNewCif': null,
      };

      final DOPNativeCifInfoEntity entity = DOPNativeCifInfoEntity.fromJson(jsonMap);

      expect(entity.idCard, null);
      expect(entity.useNewCif, null);
    });

    test('toJson should handle null values', () {
      final DOPNativeCifInfoEntity entity = DOPNativeCifInfoEntity.fromJson(<String, dynamic>{
        'idCard': null,
        'useNewCif': null,
      });

      final Map<String, dynamic> json = entity.toJson();

      expect(json, <String, dynamic>{
        'idCard': null,
        'useNewCif': null,
      });
    });
  });
}
