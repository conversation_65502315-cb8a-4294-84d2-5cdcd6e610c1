import 'package:evoapp/data/response/dop_native/dop_native_selfie_submit_gestures_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('toJson should return a map with the correct jobId', () {
    const String jobId = 'mock-job-id';
    final DOPNativeSelfieSubmitGesturesEntity entity =
        DOPNativeSelfieSubmitGesturesEntity(jobId: jobId);

    final Map<String, dynamic> json = entity.toJson();

    expect(json, containsPair('job_id', jobId));
  });

  test('toJson should handle null job_id correctly', () {
    final DOPNativeSelfieSubmitGesturesEntity entity = DOPNativeSelfieSubmitGesturesEntity();

    final Map<String, dynamic> json = entity.toJson();

    expect(json.containsKey('job_id'), true);
    expect(json['job_id'], null);
  });

  test(
      'unserializable constructor should create an entity with null file_id and correct localExceptionCode',
      () {
    final DOPNativeSelfieSubmitGesturesEntity entity =
        DOPNativeSelfieSubmitGesturesEntity.unserializable();

    expect(entity.jobId, null);
    expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
  });

  test('fromBaseResponse constructor should extract file_id from the base response', () {
    const String expectedFileId = 'mock-file-id';
    final BaseResponse baseResponse = BaseResponse(
      statusCode: CommonHttpClient.SUCCESS,
      response: <String, dynamic>{
        'data': <String, dynamic>{'job_id': expectedFileId}
      },
    );

    final DOPNativeSelfieSubmitGesturesEntity entity =
        DOPNativeSelfieSubmitGesturesEntity.fromBaseResponse(baseResponse);

    expect(entity.jobId, expectedFileId);
  });
}
