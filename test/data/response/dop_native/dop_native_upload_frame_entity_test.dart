import 'package:evoapp/data/response/dop_native/dop_native_upload_frame_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('toJson should return a map with the correct fileId', () {
    const String fileId = 'mock-file-id';
    final DOPNativeUploadFrameEntity entity = DOPNativeUploadFrameEntity(fileId: fileId);

    final Map<String, dynamic> json = entity.toJson();

    expect(json, containsPair('file_id', fileId));
  });

  test('toJson should handle null job_id correctly', () {
    final DOPNativeUploadFrameEntity entity = DOPNativeUploadFrameEntity();

    final Map<String, dynamic> json = entity.toJson();

    expect(json.containsKey('file_id'), true);
    expect(json['file_id'], null);
  });

  test(
      'unserializable constructor should create an entity with null file_id and correct localExceptionCode',
      () {
    final DOPNativeUploadFrameEntity entity = DOPNativeUploadFrameEntity.unserializable();

    expect(entity.fileId, null);
    expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
  });

  test('fromBaseResponse constructor should extract file_id from the base response', () {
    const String expectedFileId = 'mock-file-id';
    final BaseResponse baseResponse = BaseResponse(
      statusCode: CommonHttpClient.SUCCESS,
      response: <String, dynamic>{
        'data': <String, dynamic>{'file_id': expectedFileId}
      },
    );

    final DOPNativeUploadFrameEntity entity =
        DOPNativeUploadFrameEntity.fromBaseResponse(baseResponse);

    expect(entity.fileId, expectedFileId);
  });
}
