import 'package:evoapp/data/response/dop_native/dop_native_metadata_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_metadata_item_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeMessage = 'fake_message';
  const String fakeTime = 'fake_time';
  const String fakeVerdict = 'fake_verdict';

  group('DOPNativeMetadataEntity', () {
    test('verify constant', () {
      expect(DOPNativeMetadataEntity.verdictMissingParameters, 'missing_parameters');
      expect(DOPNativeMetadataEntity.verdictForbiddenParameters, 'forbidden_parameters');
      expect(DOPNativeMetadataEntity.verdictInvalidParameters, 'invalid_parameters');
    });

    test('should create an instance with provided metadata', () {
      final List<DOPNativeMetadataItemEntity> items = <DOPNativeMetadataItemEntity>[
        const DOPNativeMetadataItemEntity(code: 'code1', name: 'name1', level: 'level1'),
        const DOPNativeMetadataItemEntity(code: 'code2', name: 'name2', level: 'level2'),
      ];
      final DOPNativeMetadataEntity entity = DOPNativeMetadataEntity(
        metadata: items,
        statusCode: CommonHttpClient.SUCCESS,
        message: fakeMessage,
        time: fakeTime,
        verdict: fakeVerdict,
      );

      expect(entity.metadata, items);
      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.message, fakeMessage);
      expect(entity.time, fakeTime);
      expect(entity.verdict, fakeVerdict);
    });

    test('unserializable constructor should create instance with null metadata', () {
      final DOPNativeMetadataEntity entity = DOPNativeMetadataEntity.unserializable();

      expect(entity.metadata, null);
      expect(entity.statusCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('fromBaseResponse constructor should correctly parse base response', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <dynamic>[
            <String, dynamic>{'code': 'code1', 'name': 'name1', 'level': 'level1'},
            <String, dynamic>{'code': 'code2', 'name': 'name2', 'level': 'level2'},
          ],
        },
      );
      final DOPNativeMetadataEntity entity = DOPNativeMetadataEntity.fromJson(baseResponse);

      expect(entity.metadata?.length, 2);
      expect(entity.metadata?[0].code, 'code1');
      expect(entity.metadata?[1].name, 'name2');
    });

    test('toJson should serialize properties to a JSON map', () {
      final List<DOPNativeMetadataItemEntity> items = <DOPNativeMetadataItemEntity>[
        const DOPNativeMetadataItemEntity(code: 'code1', name: 'name1', level: 'level1'),
        const DOPNativeMetadataItemEntity(code: 'code2', name: 'name2', level: 'level2'),
      ];
      final DOPNativeMetadataEntity entity = DOPNativeMetadataEntity(
        metadata: items,
        statusCode: CommonHttpClient.SUCCESS,
        message: fakeMessage,
        time: fakeTime,
        verdict: fakeVerdict,
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json['data'], isNotNull);
      expect(json['data'].length, 2);
      expect(json['data'][0]['code'], 'code1');
      expect(json['data'][1]['name'], 'name2');

      expect(json['statusCode'], CommonHttpClient.SUCCESS);
      expect(json['message'], fakeMessage);
      expect(json['time'], fakeTime);
      expect(json['verdict'], fakeVerdict);
    });
  });

  group('MetadataType', () {
    test('test value metadata', () {
      expect(MetadataType.ACCOMMODATIONS_ID.value, 'ACCOMMODATIONS_ID');
      expect(MetadataType.APPROVED_FLOW_TYPE.value, 'APPROVED_FLOW_TYPE');
      expect(MetadataType.AUTO_PAYMENT_BANK.value, 'AUTO_PAYMENT_BANK');
      expect(MetadataType.BANK.value, 'BANK');
      expect(MetadataType.BANK_BRANCH.value, 'BANK_BRANCH');
      expect(MetadataType.BANK_BRAND.value, 'BANK_BRAND');
      expect(MetadataType.BANK_PROVINCE.value, 'BANK_PROVINCE');
      expect(MetadataType.BRANCH.value, 'BRANCH');
      expect(MetadataType.BRANCH_DISTRICT.value, 'BRANCH_DISTRICT');
      expect(MetadataType.BRANCH_PROVINCE.value, 'BRANCH_PROVINCE');
      expect(MetadataType.BRAND_TRADE.value, 'BRAND_TRADE');
      expect(MetadataType.CARD_DELIVERY_BRANCH.value, 'CARD_DELIVERY_BRANCH');
      expect(MetadataType.CARD_DELIVERY_TYPE.value, 'CARD_DELIVERY_TYPE');
      expect(MetadataType.CARD_DESIGN.value, 'CARD_DESIGN');
      expect(MetadataType.CARD_DESIGN_GROUP.value, 'CARD_DESIGN_GROUP');
      expect(MetadataType.CARD_ISSUE_PLACE.value, 'CARD_ISSUE_PLACE');
      expect(MetadataType.CARD_OPEN_PURPOSE.value, 'CARD_OPEN_PURPOSE');
      expect(MetadataType.CARD_TYPE.value, 'CARD_TYPE');
      expect(MetadataType.CAREER_ID.value, 'CAREER_ID');
      expect(MetadataType.COMPANY_TYPE.value, 'COMPANY_TYPE');
      expect(MetadataType.COMPANY_TYPE_ID.value, 'COMPANY_TYPE_ID');
      expect(MetadataType.CONTACT_TIME_RANGE.value, 'CONTACT_TIME_RANGE');
      expect(MetadataType.CREDIT_CARD_CATEGORY.value, 'CREDIT_CARD_CATEGORY');
      expect(MetadataType.DELIVERY_CARD_ADDR.value, 'DELIVERY_CARD_ADDR');
      expect(MetadataType.DISTRICT.value, 'DISTRICT');
      expect(MetadataType.DUE_DATE.value, 'DUE_DATE');
      expect(MetadataType.EDUCATION.value, 'EDUCATION');
      expect(MetadataType.EDUCATION_ID.value, 'EDUCATION_ID');
      expect(MetadataType.EMPLOYMENT.value, 'EMPLOYMENT');
      expect(MetadataType.EMPLOYMENT_CAREER.value, 'EMPLOYMENT_CAREER');
      expect(MetadataType.EMPLOYMENT_POS.value, 'EMPLOYMENT_POS');
      expect(MetadataType.EMPLOYMENT_STATUS.value, 'EMPLOYMENT_STATUS');
      expect(MetadataType.EMPLOYMENT_TYPE.value, 'EMPLOYMENT_TYPE');
      expect(MetadataType.ETHNIC.value, 'ETHNIC');
      expect(MetadataType.EXTRA_SERVICE.value, 'EXTRA_SERVICE');
      expect(MetadataType.FEEDBACK_USER_INSIGHT.value, 'FEEDBACK_USER_INSIGHT');
      expect(MetadataType.GENDER.value, 'GENDER');
      expect(MetadataType.HOUSE_OWNERSHIP.value, 'HOUSE_OWNERSHIP');
      expect(MetadataType.ID_CARD_TYPE.value, 'ID_CARD_TYPE');
      expect(MetadataType.LABOR_CONTRACT.value, 'LABOR_CONTRACT');
      expect(MetadataType.LOAN_CATEGORY.value, 'LOAN_CATEGORY');
      expect(MetadataType.LOAN_PURPOSE.value, 'LOAN_PURPOSE');
      expect(MetadataType.LOAN_PURPOSE_ID.value, 'LOAN_PURPOSE_ID');
      expect(MetadataType.MAIL_ADDRESS_ID.value, 'MAIL_ADDRESS_ID');
      expect(MetadataType.MARITAL.value, 'MARITAL');
      expect(MetadataType.MARRIED_ID.value, 'MARRIED_ID');
      expect(MetadataType.NATIONALITY.value, 'NATIONALITY');
      expect(MetadataType.NB_LENDER_RELATIONSHIP.value, 'NB_LENDER_RELATIONSHIP');
      expect(MetadataType.PAYMENT_DATE.value, 'PAYMENT_DATE');
      expect(MetadataType.PAYMENT_TYPE.value, 'PAYMENT_TYPE');
      expect(MetadataType.PIN_MAILER.value, 'PIN_MAILER');
      expect(MetadataType.POSITION_ID.value, 'POSITION_ID');
      expect(MetadataType.PROVINCE.value, 'PROVINCE');
      expect(MetadataType.REG_ADDRESS_STATUS.value, 'REG_ADDRESS_STATUS');
      expect(MetadataType.RELATIONSHIP.value, 'RELATIONSHIP');
      expect(MetadataType.RELATIONSHIP_1.value, 'RELATIONSHIP_1');
      expect(MetadataType.RELATIONSHIP_2.value, 'RELATIONSHIP_2');
      expect(MetadataType.RELATIONSHIP_EMPLOYEE.value, 'RELATIONSHIP_EMPLOYEE');
      expect(MetadataType.RELATIONSHIP_STUDENT.value, 'RELATIONSHIP_STUDENT');
      expect(MetadataType.RESIDENTIAL_PERIOD.value, 'RESIDENTIAL_PERIOD');
      expect(MetadataType.SALE_OFFICE.value, 'SALE_OFFICE');
      expect(MetadataType.SECURITY_QUESTION.value, 'SECURITY_QUESTION');
      expect(MetadataType.STATEMENT_DATE.value, 'STATEMENT_DATE');
      expect(MetadataType.SUBSCRIBE_CHANNEL.value, 'SUBSCRIBE_CHANNEL');
      expect(MetadataType.TITLE.value, 'TITLE');
      expect(MetadataType.WARD.value, 'WARD');
      expect(MetadataType.WORKING_ADDRESS.value, 'WORKING_ADDRESS');
      expect(MetadataType.WORKING_TYPE_ID.value, 'WORKING_TYPE_ID');
      expect(MetadataType.ACQUISITION_REWARD.value, 'ACQUISITION_REWARD');
      expect(MetadataType.ACQUISITION_REWARD_TC.value, 'ACQUISITION_REWARD_TC');
    });

    test('byValue should return correct enum instance for valid value', () {
      const String testValue = 'BANK';
      final MetadataType? result = MetadataType.byValue(testValue);

      expect(result, MetadataType.BANK);
    });

    test('byValue should return null for invalid value', () {
      const String testValue = 'INVALID_TYPE';
      final MetadataType? result = MetadataType.byValue(testValue);

      expect(result, null);
    });

    test('byValue should return null for null value', () {
      final MetadataType? result = MetadataType.byValue(null);

      expect(result, null);
    });

    test('byValue should return correct enum instance for all valid values', () {
      for (final MetadataType type in MetadataType.values) {
        final MetadataType? result = MetadataType.byValue(type.value);

        expect(result, type);
      }
    });
  });
}
