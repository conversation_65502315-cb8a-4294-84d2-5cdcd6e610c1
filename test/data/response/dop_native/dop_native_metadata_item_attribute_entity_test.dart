import 'package:evoapp/data/response/dop_native/dop_native_metadata_item_attribute_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeMetadataItemAttributeEntity', () {
    test('attribute const', () {
      expect(DOPNativeMetadataItemAttributeEntity.fileName, 'filename');
      expect(DOPNativeMetadataItemAttributeEntity.title, 'title');
    });

    test('from<PERSON><PERSON> creates an instance from a JSON map', () {
      final Map<String, String> json = <String, String>{
        'name': 'exampleName',
        'value': 'exampleValue',
      };

      final DOPNativeMetadataItemAttributeEntity entity =
          DOPNativeMetadataItemAttributeEntity.fromJson(json);

      expect(entity.name, 'exampleName');
      expect(entity.value, 'exampleValue');
    });

    test('to<PERSON><PERSON> converts an instance to a JSON map', () {
      const DOPNativeMetadataItemAttributeEntity entity = DOPNativeMetadataItemAttributeEntity(
        name: 'exampleName',
        value: 'exampleValue',
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json, <String, String>{
        'name': 'exampleName',
        'value': 'exampleValue',
      });
    });

    test('supports value equality', () {
      const DOPNativeMetadataItemAttributeEntity entity1 = DOPNativeMetadataItemAttributeEntity(
        name: 'exampleName',
        value: 'exampleValue',
      );
      const DOPNativeMetadataItemAttributeEntity entity2 = DOPNativeMetadataItemAttributeEntity(
        name: 'exampleName',
        value: 'exampleValue',
      );

      expect(entity1, entity2);
    });

    test('props contains the correct values', () {
      const DOPNativeMetadataItemAttributeEntity entity = DOPNativeMetadataItemAttributeEntity(
        name: 'exampleName',
        value: 'exampleValue',
      );

      expect(entity.props, <String>['exampleName', 'exampleValue']);
    });
  });
}
