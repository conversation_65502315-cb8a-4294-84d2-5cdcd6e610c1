import 'package:evoapp/data/response/dop_native/dop_native_reward_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeRewardInfoEntity', () {
    test('from<PERSON><PERSON> creates an instance with the correct values', () {
      final Map<String, String> json = <String, String>{'rewardID': '12345'};
      final DOPNativeRewardInfoEntity entity = DOPNativeRewardInfoEntity.fromJson(json);

      expect(entity.rewardID, '12345');
    });

    test('fromJ<PERSON> handles null values correctly', () {
      final Map<String, Null> json = <String, Null>{'rewardID': null};
      final DOPNativeRewardInfoEntity entity = DOPNativeRewardInfoEntity.fromJson(json);

      expect(entity.rewardID, isNull);
    });

    test('toJson returns a correct map', () {
      const DOPNativeRewardInfoEntity entity = DOPNativeRewardInfoEntity(rewardID: '12345');
      final Map<String, dynamic>? json = entity.toJson();

      expect(json, <String, String>{'rewardID': '12345'});
    });

    test('toJson returns null if all properties are null', () {
      const DOPNativeRewardInfoEntity entity = DOPNativeRewardInfoEntity();
      final Map<String, dynamic>? json = entity.toJson();

      expect(json, isNull);
    });

    test('toJson removes null values from the map', () {
      const DOPNativeRewardInfoEntity entity = DOPNativeRewardInfoEntity();
      final Map<String, dynamic>? json = entity.toJson();

      expect(json, isNull);
    });
  });
}
