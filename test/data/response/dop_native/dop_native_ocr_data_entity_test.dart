import 'package:evoapp/data/response/dop_native/dop_native_contact_info_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_ocr_data_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_personal_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeOCRDataEntity', () {
    test('fromJson should initialize fields correctly with non-null values', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'contactInfo': <String, dynamic>{
          'familyAddress': 'A10 05 C/C 262/15 17 LBBÍCH',
          'familyBookAddressDistId': '767',
          'familyBookAddressProvinceId': '79',
          'familyBookAddressWardId': '27034'
        },
        'personalInfo': <String, dynamic>{
          'birthday': '1989-01-01',
          'fullName': 'PHAN THỊ AN',
          'gender': '2',
          'idCard': '011111123456',
          'idIssueDate': '2022-07-08',
          'idIssuePlaceId': '998',
          'oldIDCard': '123456789'
        },
      };

      final DOPNativeOCRDataEntity entity = DOPNativeOCRDataEntity.fromJson(json);

      expect(entity.contactInfo, isNotNull);
      expect(entity.personalInfo, isNotNull);
    });

    test('fromJson should initialize fields correctly with null values', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'contactInfo': null,
        'personalInfo': null,
      };

      final DOPNativeOCRDataEntity entity = DOPNativeOCRDataEntity.fromJson(json);

      expect(entity.contactInfo, isNull);
      expect(entity.personalInfo, isNull);
    });

    test('toJson should convert object to json correctly', () {
      const DOPNativeContactInfoEntity contactInfo = DOPNativeContactInfoEntity(
          familyAddress: 'A10 05 C/C 262/15 17 LBBÍCH',
          familyBookAddressDistId: '767',
          familyBookAddressProvinceId: '79',
          familyBookAddressWardId: '27034');

      const DOPNativePersonalInfoEntity personalInfo = DOPNativePersonalInfoEntity(
        birthday: '1989-01-01',
        fullName: 'PHAN THỊ AN',
      );

      final DOPNativeOCRDataEntity entity = DOPNativeOCRDataEntity(
        contactInfo: contactInfo,
        personalInfo: personalInfo,
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json['contactInfo'], isNotNull);
      expect(json['personalInfo'], isNotNull);
    });

    test('toJson should handle null values correctly', () {
      final DOPNativeOCRDataEntity entity = DOPNativeOCRDataEntity();

      final Map<String, dynamic> json = entity.toJson();

      expect(json['contactInfo'], isNull);
      expect(json['personalInfo'], isNull);
    });
  });
}
