import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/create_order_entity.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/order_extra_info_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeSessionId = 'fake_session_id';
  const String fakeOfferId = 'fake_offer_id';
  const String fakeType = 'fake_type';

  test('verify verdict constant', () {
    expect(CreateOrderEntity.verdictEMIUnqualified, 'emi_unqualified');
    expect(CreateOrderEntity.verdictUnqualifiedToActive, 'card_unqualified_to_activate');
    expect(CreateOrderEntity.verdictPosLimitInsufficient, 'pos_limit_insufficient');
    expect(
        CreateOrderEntity.verdictCardUnqualifiedToSetPosLimit, 'card_unqualified_to_set_pos_limit');
  });

  group('CreateOrderEntity', () {
    test('unserializable should create an unserializable CreateOrderEntity', () {
      final CreateOrderEntity entity = CreateOrderEntity.unserializable();

      expect(entity, isA<CreateOrderEntity>());
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      expect(entity.session, isNull);
      expect(entity.emiPackages, isNull);
      expect(entity.prerequisitesAction, isNull);
    });

    test('fromJson should create a valid CreateOrderEntity', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'session': <String, dynamic>{
              'id': fakeSessionId,
            },
            'emi_packages': <dynamic>[
              <String, dynamic>{
                'offer': <String, dynamic>{
                  'id': fakeOfferId,
                },
                'conversion_fee': 1.0,
                'monthly_installment_amount': 2.0,
                'outright_purchase_diff': 3.0,
              },
            ],
            'prerequisites_action': <String, dynamic>{
              'type': fakeType,
            },
          },
        },
      );

      final CreateOrderEntity entity = CreateOrderEntity.fromBaseResponse(baseResponse);

      expect(entity, isA<CreateOrderEntity>());
      expect(entity.session, isA<OrderSessionEntity>());
      expect(entity.session?.id, fakeSessionId);
      expect(entity.emiPackages?.first.offer?.id, fakeOfferId);
      expect(entity.prerequisitesAction?.type, fakeType);
    });

    test('toJson should return a valid Map<String, dynamic>', () {
      final CreateOrderEntity createOrderEntity = CreateOrderEntity(
        session: OrderSessionEntity(
          id: fakeSessionId,
        ),
        emiPackages: <EmiPackageEntity>[
          EmiPackageEntity(
            conversionFee: 1.0,
          ),
        ],
        prerequisitesAction: ActionEntity(
          type: fakeType,
        ),
      );

      final Map<String, dynamic> toJsonResult = createOrderEntity.toJson();

      expect(toJsonResult['session'], isNotNull);
      expect(toJsonResult['emi_packages'], isNotNull);
      expect(toJsonResult['prerequisites_action'], isNotNull);
    });

    test('toString should return a valid string representation', () {
      final OrderSessionEntity session = OrderSessionEntity(
        id: fakeSessionId,
      );

      final EmiPackageEntity emiPackage = EmiPackageEntity(
        conversionFee: 1.0,
      );

      final ActionEntity action = ActionEntity(
        type: fakeType,
      );

      final OrderExtraInfoEntity extraInfo = OrderExtraInfoEntity(
        enableRatingPrompt: true,
        autoApplyVoucher: true,
      );

      final CreateOrderEntity createOrderEntity = CreateOrderEntity(
        session: session,
        emiPackages: <EmiPackageEntity>[emiPackage],
        prerequisitesAction: action,
        extraInfo: extraInfo,
      );

      final String expectedString =
          'CreateOrderEntity{session: $session, emi_packages: [$emiPackage], prerequisites_action: $action, extra_info: $extraInfo}';
      expect(createOrderEntity.toString(), expectedString);
    });
  });
}
