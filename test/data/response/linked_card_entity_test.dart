import 'package:evoapp/data/response/linked_card_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeCardImage = 'test_card_image';
  const String fakeMaxPerTrans = 'fake_max_per_trans';
  const String fakeMinPerTrans = 'fake_min_per_trans';

  group('verify LinkedCardEntity', () {
    test('fromJson constructor should create a valid object', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'card_image': fakeCardImage,
        'payment_permission': <String, dynamic>{
          'max_per_trans': fakeMaxPerTrans,
          'min_per_trans': fakeMinPerTrans,
        },
      };

      final LinkedCardEntity entity = LinkedCardEntity.fromJson(json);

      expect(entity.cardImage, fakeCardImage);
      expect(entity.paymentPermission?.maxPerTrans, fakeMaxPerTrans);
      expect(entity.paymentPermission?.minPerTrans, fakeMinPerTrans);
    });

    test('to<PERSON><PERSON> should return a JSON map containing the proper data', () {
      final LinkedCardEntity entity = LinkedCardEntity(
        cardImage: fakeCardImage,
        paymentPermission: PaymentPermission(
          maxPerTrans: fakeMaxPerTrans,
          minPerTrans: fakeMinPerTrans,
        ),
      );

      final Map<String, dynamic> json = entity.toJson();

      // Verify the JSON map has expected keys and values
      expect(json['card_image'], fakeCardImage);
      expect(json['payment_permission'], isNotNull);
    });
  });

  group('verify PaymentPermission', () {
    test('fromJson constructor should create a valid object', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'max_per_trans': fakeMaxPerTrans,
        'min_per_trans': fakeMinPerTrans,
      };

      final PaymentPermission paymentPermission = PaymentPermission.fromJson(json);

      expect(paymentPermission.maxPerTrans, fakeMaxPerTrans);
      expect(paymentPermission.minPerTrans, fakeMinPerTrans);
    });

    test('toJson should return a JSON map containing the proper data', () {
      final PaymentPermission paymentPermission = PaymentPermission(
        maxPerTrans: fakeMaxPerTrans,
        minPerTrans: fakeMinPerTrans,
      );

      final Map<String, dynamic> json = paymentPermission.toJson();

      expect(json['max_per_trans'], fakeMaxPerTrans);
      expect(json['min_per_trans'], fakeMinPerTrans);
    });
  });
}
