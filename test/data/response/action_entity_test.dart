import 'package:evoapp/data/response/action_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  // Action
  const String fakeLink = 'https://example.com';
  const String fakeScreenName = 'screen_name';
  const String fakeActionLabel = 'example_action_label';
  const String fakeType = 'example_type';
  const Map<String, dynamic> fakeParameter = <String, dynamic>{
    'id': '123',
    'code': 'abc',
    'credit_limit': 1000,
    'pos_limit_allow': 5000,
    'should_open_cashback_sheet': 'true',
  };

  // Next Action Earn Action
  const String fakeNextLink = 'https://example_next.com';
  const String fakeNextScreenName = 'screen_name_next';
  const String fakeNextActionLabel = 'example_action_label_next';
  const String fakeNextType = 'example_type_next';
  const Map<String, dynamic> fakeNextParameter = <String, dynamic>{
    'id': '456',
    'code': 'cdf',
    'credit_limit': 1000,
    'pos_limit_allow': 5000,
    'should_open_cashback_sheet': 'false',
  };

  group('verify ActionEntity', () {
    test('verify fromJson() method creates a valid ActionEntity', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'args': <String, dynamic>{
          'link': fakeLink,
          'screen_name': fakeScreenName,
          'action_label': fakeActionLabel,
          'parameters': fakeParameter,
          'next_action': <String, dynamic>{
            'args': <String, dynamic>{
              'link': fakeNextLink,
              'screen_name': fakeNextScreenName,
              'action_label': fakeNextActionLabel,
              'parameters': fakeNextParameter,
            },
            'type': fakeNextType,
          },
        },
        'type': fakeType,
      };

      final ActionEntity actionEntity = ActionEntity.fromJson(json);
      expect(actionEntity.type, fakeType);
      expect(actionEntity.args?.link, fakeLink);
      expect(actionEntity.args?.screenName, fakeScreenName);
      expect(actionEntity.args?.actionLabel, fakeActionLabel);
      expect(actionEntity.args?.parameters?.id, fakeParameter['id']);
      expect(actionEntity.args?.parameters?.creditLimit, fakeParameter['credit_limit']);
      expect(actionEntity.args?.parameters?.posLimitAllow, fakeParameter['pos_limit_allow']);
      expect(actionEntity.args?.parameters?.shouldOpenCashbackSheet.toString(),
          fakeParameter['should_open_cashback_sheet']);

      // next action
      expect(actionEntity.args?.nextAction?.type, fakeNextType);
      expect(actionEntity.args?.nextAction?.args?.link, fakeNextLink);
      expect(actionEntity.args?.nextAction?.args?.screenName, fakeNextScreenName);
      expect(actionEntity.args?.nextAction?.args?.actionLabel, fakeNextActionLabel);
      expect(actionEntity.args?.nextAction?.args?.parameters?.id, fakeNextParameter['id']);
      expect(actionEntity.args?.nextAction?.args?.parameters?.creditLimit,
          fakeParameter['credit_limit']);
      expect(actionEntity.args?.nextAction?.args?.parameters?.posLimitAllow,
          fakeParameter['pos_limit_allow']);
      expect(actionEntity.args?.nextAction?.args?.parameters?.shouldOpenCashbackSheet.toString(),
          fakeNextParameter['should_open_cashback_sheet']);
    });

    test('toJson returns a valid Map', () {
      final ActionEntity actionEntity = ActionEntity(
        args: ArgsEntity(
          link: fakeLink,
          screenName: fakeScreenName,
          actionLabel: fakeActionLabel,
          parameters: ParametersEntity(
            id: '123',
            code: 'abc',
            creditLimit: 1000,
            posLimitAllow: 5000,
            shouldOpenCashbackSheet: true,
          ),
        ),
        type: fakeType,
      );

      final Map<String, dynamic> json = actionEntity.toJson();
      expect(json['type'], fakeType);
      expect(json['args']['link'], fakeLink);
      expect(json['args']['screen_name'], fakeScreenName);
      expect(json['args']['action_label'], fakeActionLabel);
      expect(json['args']['parameters'], isNotNull);
      expect(json['args']['parameters']['id'], '123');
      expect(json['args']['parameters']['credit_limit'], 1000);
      expect(json['args']['parameters']['pos_limit_allow'], 5000);
      expect(json['args']['parameters']['should_open_cashback_sheet'], 'true');
    });

    test('verify copyWith() returns a new copy with the given values', () {
      const String fakeNewType = 'new_type';
      final ActionEntity original = ActionEntity(
        type: 'original_type',
        args: ArgsEntity(
          link: fakeLink,
        ),
      );
      final ActionEntity copied = original.copyWith(type: fakeNewType, args: ArgsEntity());

      expect(copied.type, fakeNewType);
      expect(copied.args, isNotNull);
      expect(copied.args?.link, null);
    });

    test('verify copyWith() with params are null', () {
      const String fakeOriginalType = 'original_type';
      final ActionEntity original = ActionEntity(
        type: fakeOriginalType,
        args: ArgsEntity(
          link: fakeLink,
        ),
      );
      final ActionEntity copied = original.copyWith();

      expect(copied.type, fakeOriginalType);
      expect(copied.args, isNotNull);
      expect(copied.args?.link, fakeLink);
    });
  });

  group('verify Args', () {
    test('fromJson', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'link': fakeLink,
        'screen_name': fakeScreenName,
        'action_label': fakeActionLabel,
        'parameters': fakeParameter,
        'next_action': <String, dynamic>{
          'args': <String, dynamic>{
            'link': fakeNextLink,
            'screen_name': fakeNextScreenName,
            'action_label': fakeNextActionLabel,
            'parameters': fakeNextParameter,
          },
          'type': fakeNextType,
        },
      };

      final ArgsEntity args = ArgsEntity.fromJson(json);
      expect(args.link, json['link']);
      expect(args.screenName, json['screen_name']);
      expect(args.actionLabel, json['action_label']);
      expect(args.parameters, isNotNull);
      expect(args.parameters?.toJson()['id'], fakeParameter['id']);
      expect(args.parameters?.shouldOpenCashbackSheet.toString(),
          fakeParameter['should_open_cashback_sheet']);
      expect(args.nextAction, isNotNull);
      expect(args.nextAction?.toJson()['type'], json['next_action']['type']);
      expect(args.nextAction?.toJson()['args']['link'], json['next_action']['args']['link']);
    });

    test('verify toJson', () {
      final ArgsEntity args = ArgsEntity(
        link: fakeLink,
        screenName: fakeScreenName,
        actionLabel: fakeActionLabel,
        parameters: ParametersEntity(id: '123', code: 'abc', shouldOpenCashbackSheet: true),
        nextAction: ActionEntity(
          type: fakeNextType,
          args: ArgsEntity(
            link: fakeNextLink,
            screenName: fakeNextScreenName,
            actionLabel: fakeNextActionLabel,
            parameters: ParametersEntity(id: '456', code: 'cdf', shouldOpenCashbackSheet: false),
          ),
        ),
      );

      final Map<String, dynamic> json = args.toJson();
      expect(json['link'], fakeLink);
      expect(json['screen_name'], fakeScreenName);
      expect(json['action_label'], fakeActionLabel);
      expect(json['parameters'], isNotNull);
      expect(json['parameters']['id'], '123');
      expect(json['parameters']['should_open_cashback_sheet'], 'true');
      expect(json['next_action'], isNotNull);
    });

    test('copyWith', () {
      const String fakeNewLink = 'https://new.example.com';
      const String fakeNewScreenName = 'NewScreen';
      const String fakeNewActionLabel = 'Update';
      const String fakeNewNextType = 'newNextActionType';

      final ArgsEntity args = ArgsEntity(
        link: fakeLink,
        screenName: fakeScreenName,
        actionLabel: fakeActionLabel,
        parameters: ParametersEntity.fromJson(fakeParameter),
        nextAction: ActionEntity.fromJson(<String, dynamic>{
          'type': fakeNextType,
        }),
      );

      final ArgsEntity updatedArgs = args.copyWith(
        link: fakeNewLink,
        screenName: fakeNewScreenName,
        actionLabel: fakeNewActionLabel,
        parameters: ParametersEntity.fromJson(fakeNextParameter),
        nextAction: ActionEntity.fromJson(<String, dynamic>{
          'type': fakeNewNextType,
        }),
      );

      expect(updatedArgs.link, fakeNewLink);
      expect(updatedArgs.screenName, fakeNewScreenName);
      expect(updatedArgs.actionLabel, fakeNewActionLabel);
      expect(updatedArgs.parameters, isNotNull);
      expect(updatedArgs.parameters?.toJson()['id'], fakeNextParameter['id']);
      expect(updatedArgs.parameters?.shouldOpenCashbackSheet.toString(),
          fakeNextParameter['should_open_cashback_sheet']);
      expect(updatedArgs.nextAction, isNotNull);
      expect(updatedArgs.nextAction?.toJson()['type'], fakeNewNextType);
    });

    test('copyWith with params are null fields', () {
      final ArgsEntity args = ArgsEntity(
        link: fakeLink,
        screenName: fakeScreenName,
        actionLabel: fakeActionLabel,
        parameters: ParametersEntity.fromJson(fakeParameter),
        nextAction: ActionEntity.fromJson(<String, dynamic>{
          'type': fakeNextType,
        }),
      );

      final ArgsEntity updatedArgs = args.copyWith();

      expect(updatedArgs.link, fakeLink);
      expect(updatedArgs.screenName, fakeScreenName);
      expect(updatedArgs.actionLabel, fakeActionLabel);
      expect(updatedArgs.parameters, isNotNull);
      expect(updatedArgs.parameters?.toJson()['id'], fakeParameter['id']);
      expect(updatedArgs.parameters?.shouldOpenCashbackSheet.toString(),
          fakeParameter['should_open_cashback_sheet']);
      expect(updatedArgs.nextAction, isNotNull);
      expect(updatedArgs.nextAction?.toJson()['type'], fakeNextType);
    });
  });

  group('verify Parameters', () {
    const String fakeId = '123';
    const String fakeCode = 'ABC';

    test('fromJson', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'id': fakeId,
        'code': fakeCode,
        'should_open_cashback_sheet': 'true',
      };

      final ParametersEntity parameters = ParametersEntity.fromJson(json);
      expect(parameters.id, fakeId);
      expect(parameters.code, fakeCode);
      expect(parameters.shouldOpenCashbackSheet, true);
    });

    test('verify toJson', () {
      final ParametersEntity parameters = ParametersEntity(
        id: fakeId,
        code: fakeCode,
        shouldOpenCashbackSheet: true,
      );

      final Map<String, dynamic> json = parameters.toJson();
      expect(json['id'], fakeId);
      expect(json['code'], fakeCode);
      expect(json['should_open_cashback_sheet'], 'true');
    });

    test('copyWith', () {
      final ParametersEntity parameters = ParametersEntity(
        id: fakeId,
        code: fakeCode,
        creditLimit: 2000,
        posLimitAllow: 2000,
        shouldOpenCashbackSheet: true,
      );

      const String fakeNewId = '456';
      const String fakeNewCode = 'DEF';
      const int fakeNewCreditLimit = 1000;
      const int fakeNewPosLimitAllow = 5000;
      const bool fakeNewShouldOpenCashbackSheet = false;

      final ParametersEntity updatedParameters = parameters.copyWith(
        id: fakeNewId,
        code: fakeNewCode,
        creditLimit: fakeNewCreditLimit,
        posLimitAllow: fakeNewPosLimitAllow,
        shouldOpenCashbackSheet: fakeNewShouldOpenCashbackSheet,
      );

      expect(updatedParameters.id, fakeNewId);
      expect(updatedParameters.code, fakeNewCode);
      expect(updatedParameters.creditLimit, fakeNewCreditLimit);
      expect(updatedParameters.posLimitAllow, fakeNewPosLimitAllow);
      expect(updatedParameters.shouldOpenCashbackSheet, fakeNewShouldOpenCashbackSheet);
    });

    test('copyWith with params are null', () {
      const int fakeCreditLimit = 200;
      const int fakePosLimitAllow = 2000;
      final ParametersEntity parameters = ParametersEntity(
        id: fakeId,
        code: fakeCode,
        creditLimit: fakeCreditLimit,
        posLimitAllow: fakePosLimitAllow,
        shouldOpenCashbackSheet: true,
      );

      final ParametersEntity updatedParameters = parameters.copyWith();

      expect(updatedParameters.id, fakeId);
      expect(updatedParameters.code, fakeCode);
      expect(updatedParameters.creditLimit, fakeCreditLimit);
      expect(updatedParameters.posLimitAllow, fakePosLimitAllow);
      expect(updatedParameters.shouldOpenCashbackSheet, true);
    });
  });
}
