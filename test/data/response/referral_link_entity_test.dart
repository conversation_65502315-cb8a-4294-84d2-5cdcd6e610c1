import 'package:evoapp/data/response/referral_link_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify verdict constant', () {
    expect(ReferralLinkEntity.verdictSuccess, 'success');
    expect(ReferralLinkEntity.verdictUserNotQualified, 'user_not_qualified');
    expect(ReferralLinkEntity.verdictInvalidCampaign, 'invalid_campaign');
  });

  group('ReferralLinkEntity', () {
    const String fakeReferralLink = 'fake_referral_link';
    const String fakeShareContent = 'fake_share_content';

    test('ReferralLinkEntity.unserializable creates a ReferralLinkEntity with localExceptionCode',
        () {
      final ReferralLinkEntity referralLinkEntity = ReferralLinkEntity.unserializable();

      expect(referralLinkEntity.referralLink, null);
      expect(referralLinkEntity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('ReferralLinkEntity.fromBaseResponse creates a valid ReferralLinkEntity object', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'referral_link': fakeReferralLink,
            'share_content': fakeShareContent,
          },
        },
      );

      final ReferralLinkEntity referralLinkEntity =
          ReferralLinkEntity.fromBaseResponse(baseResponse);

      expect(referralLinkEntity.referralLink, fakeReferralLink);
      expect(referralLinkEntity.shareContent, fakeShareContent);
    });

    test('toJson creates a valid JSON object from ReferralLinkEntity', () {
      final ReferralLinkEntity referralLinkEntity = ReferralLinkEntity(
        referralLink: fakeReferralLink,
        shareContent: fakeShareContent,
      );

      final Map<String, dynamic> json = referralLinkEntity.toJson();

      expect(json['referral_link'], fakeReferralLink);
      expect(json['share_content'], fakeShareContent);
    });
  });
}
