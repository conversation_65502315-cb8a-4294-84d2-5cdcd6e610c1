import 'package:evoapp/data/response/confirm_and_pay_order_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeId = 'fake_id';

  test('verify verdict', () {
    expect(ConfirmAndPayOrderEntity.verdictExpiredToken, 'expired_token');
    expect(ConfirmAndPayOrderEntity.verdictInvalidCredential, 'invalid_credential');
    expect(ConfirmAndPayOrderEntity.verdictOneLastTry, 'one_last_try');
    expect(ConfirmAndPayOrderEntity.verdictSessionExpired, 'session_expired');
    expect(ConfirmAndPayOrderEntity.verdictSessionNotOpened, 'session_not_opened');
    expect(ConfirmAndPayOrderEntity.verdictUserInactive, 'user_inactive');
    expect(ConfirmAndPayOrderEntity.verdictPaymentMethodInvalid, 'payment_method_invalid');
    expect(ConfirmAndPayOrderEntity.verdictCreditLimitInsufficientLimit, 'credit_limit_insufficient');
    expect(ConfirmAndPayOrderEntity.verdictLimitExceed, 'limit_exceed');
    expect(ConfirmAndPayOrderEntity.verdictTransactionTooSoon, 'transaction_too_soon');
    expect(ConfirmAndPayOrderEntity.verdictMissingPaymentMethod, 'payment_invalid');
    expect(ConfirmAndPayOrderEntity.verdictPromotionInvalid, 'promotion_invalid');
    expect(ConfirmAndPayOrderEntity.verdictPromotionExpired, 'promotion_expired_data');
    expect(ConfirmAndPayOrderEntity.verdictPromotionUnqualified, 'promotion_unqualified');
    expect(ConfirmAndPayOrderEntity.verdictPromotionDuplicate, 'promotion_duplicate');
    expect(
        ConfirmAndPayOrderEntity.verdictPromotionPermissionDenied, 'promotion_permission_denied');
  });

  group('ConfirmAndPayOrderEntity', () {
    test('unserializable should create an unserializable ConfirmAndPayOrderEntity', () {
      final ConfirmAndPayOrderEntity entity = ConfirmAndPayOrderEntity.unserializable();

      expect(entity, isA<ConfirmAndPayOrderEntity>());
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      expect(entity.session, isNull);
    });

    test('fromJson should create a valid ConfirmAndPayOrderEntity', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'session': <String, dynamic>{
              'id': fakeId,
            },
          }
        },
      );

      final ConfirmAndPayOrderEntity entity =
          ConfirmAndPayOrderEntity.fromBaseResponse(baseResponse);

      expect(entity, isA<ConfirmAndPayOrderEntity>());
      expect(entity.session, isA<OrderSessionEntity>());
      expect(entity.session?.id, fakeId);
    });

    test('toJson should return a valid Map<String, dynamic>', () {
      final ConfirmAndPayOrderEntity confirmAndPayOrderEntity = ConfirmAndPayOrderEntity(
        session: OrderSessionEntity(
          id: fakeId,
        ),
      );

      final Map<String, dynamic> toJsonResult = confirmAndPayOrderEntity.toJson();

      expect(toJsonResult['session'], isNotNull);
    });

    test('verify toString()', () {
      final OrderSessionEntity session = OrderSessionEntity(
        id: fakeId,
      );

      final ConfirmAndPayOrderEntity entity = ConfirmAndPayOrderEntity(
        session: session,
      );

      expect(entity.toString(), 'ConfirmAndPayOrderEntity{session: $session}');
    });
  });
}
