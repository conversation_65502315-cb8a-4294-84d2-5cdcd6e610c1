import 'package:evoapp/data/response/clone_order_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeId = 'fake_id';

  group('CloneOrderEntity', () {
    test('unserializable', () async {
      final CloneOrderEntity cloneOrderEntity = CloneOrderEntity.unserializable();

      expect(cloneOrderEntity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      expect(cloneOrderEntity.session, null);
    });

    test('fromBaseResponse', () async {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'session': <String, dynamic>{
              'id': fakeId,
            },
          }
        },
      );

      final CloneOrderEntity cloneOrderEntity = CloneOrderEntity.fromBaseResponse(baseResponse);
      expect(cloneOrderEntity.session?.id, fakeId);
    });

    test('toJson', () async {
      final CloneOrderEntity cloneOrderEntity = CloneOrderEntity(
        session: OrderSessionEntity(
          id: fakeId,
        ),
      );

      final Map<String, dynamic> toJsonResult = cloneOrderEntity.toJson();

      expect(toJsonResult['session'], isNotNull);
    });
  });
}
