import 'package:evoapp/data/response/payment_result_entity.dart';
import 'package:evoapp/data/response/payment_result_transaction_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';

void main() {
  group('PaymentResultEntity', () {
    const Map<String, dynamic> fakeTransactionJson = <String, dynamic>{
      'id': 'test_id',
      'status': 'test_status',
    };

    test('fromBaseResponse creates a valid object', () {
      final BaseResponse response = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'transaction': fakeTransactionJson,
          }
        },
      );

      final PaymentResultEntity entity = PaymentResultEntity.fromBaseResponse(response);

      expect(entity.transaction, isNotNull);
      expect(entity.transaction?.id, 'test_id');
      expect(entity.transaction?.status, 'test_status');
    });

    test('toJson returns a valid JSON representation', () {
      final PaymentResultTransactionEntity transaction =
          PaymentResultTransactionEntity.fromJson(fakeTransactionJson);

      final PaymentResultEntity entity = PaymentResultEntity(
        transaction: transaction,
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json, isNotNull);
      expect(json['transaction'], isNotNull);
      expect(json['transaction']['id'], 'test_id');
      expect(json['transaction']['status'], 'test_status');
    });

    test('unserializable constructor sets localExceptionCode', () {
      final PaymentResultEntity entity = PaymentResultEntity.unserializable();

      expect(entity.transaction, isNull);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });
  });
}
