import 'package:evoapp/data/response/cancel_transaction_entity.dart';
import 'package:evoapp/data/response/payment_result_transaction_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeId = 'fake_id';

  group('CancelTransactionEntity', () {
    test('unserializable creates a CancelTransactionEntity with localExceptionCode', () {
      final CancelTransactionEntity entity = CancelTransactionEntity.unserializable();
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      expect(entity.transaction, null);
    });

    test('fromBaseResponse creates a valid CancelTransactionEntity', () {
      final BaseResponse response = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'transaction': <String, dynamic>{
              'id': fakeId,
            },
          }
        },
      );

      final CancelTransactionEntity entity = CancelTransactionEntity.fromBaseResponse(response);
      expect(entity.transaction, isNotNull);
      expect(entity.transaction?.id, fakeId);
    });

    test('toJson returns a valid Map', () {
      final CancelTransactionEntity entity = CancelTransactionEntity(
        transaction: PaymentResultTransactionEntity(
          id: fakeId,
        ),
      );

      final Map<String, dynamic> json = entity.toJson();
      expect(json['transaction']['id'], fakeId);
    });
    
    test('verify toString()', () {
      final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity(
        id: fakeId,
      );

      final CancelTransactionEntity entity = CancelTransactionEntity(
        transaction: transaction,
      );

      expect(entity.toString(), 'CancelTransactionEntity{transaction: $transaction}');
    });
  });
}
