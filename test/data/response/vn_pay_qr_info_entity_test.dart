import 'package:evoapp/data/response/vn_pay_qr_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VNPayQrInfoEntity', () {
    const String fakeStoreLabel = 'fakeStoreLabel';
    const String fakeMerchantName = 'fakeMerchantName';
    const String fakeMid = 'fakeMid';
    const String fakeType = 'fakeType';
    const String fakeQRCode = 'fakeQRCode';
    const String fakeReferenceLabel = 'fakeReferenceLabel';
    const int fakeAmount = 10000;

    test('VNPayQrInfoEntity.fromBaseResponse creates a valid VNPayQrInfoEntity object', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'store_label': fakeStoreLabel,
        'merchant_name': fakeMerchantName,
        'mid': fakeMid,
        'type': fakeType,
        'amount': fakeAmount,
        'qr_code': fakeQRCode,
        'reference_label': fakeReferenceLabel,
      };

      final VNPayQrInfoEntity vnPayQrInfoEntity = VNPayQrInfoEntity.fromJson(json);

      expect(vnPayQrInfoEntity.storeLabel, fakeStoreLabel);
      expect(vnPayQrInfoEntity.merchantName, fakeMerchantName);
      expect(vnPayQrInfoEntity.mid, fakeMid);
      expect(vnPayQrInfoEntity.type, fakeType);
      expect(vnPayQrInfoEntity.amount, fakeAmount);
      expect(vnPayQrInfoEntity.qrCode, fakeQRCode);
      expect(vnPayQrInfoEntity.referenceLabel, fakeReferenceLabel);
    });

    test('VNPayQrInfoEntity.toJson returns a valid JSON representation', () {
      final VNPayQrInfoEntity vnPayQrInfoEntity = VNPayQrInfoEntity(
        storeLabel: fakeStoreLabel,
        merchantName: fakeMerchantName,
        mid: fakeMid,
        type: fakeType,
        amount: fakeAmount,
        qrCode: fakeQRCode,
        referenceLabel: fakeReferenceLabel,
      );

      final Map<String, dynamic> json = vnPayQrInfoEntity.toJson();

      expect(json['store_label'], fakeStoreLabel);
      expect(json['merchant_name'], fakeMerchantName);
      expect(json['mid'], fakeMid);
      expect(json['type'], fakeType);
      expect(json['amount'], fakeAmount);
      expect(json['qr_code'], fakeQRCode);
      expect(json['reference_label'], fakeReferenceLabel);
    });

    test('toString', () {
      final VNPayQrInfoEntity vnPayQrInfoEntity = VNPayQrInfoEntity(
        storeLabel: fakeStoreLabel,
        merchantName: fakeMerchantName,
        mid: fakeMid,
        type: fakeType,
        amount: fakeAmount,
        qrCode: fakeQRCode,
        referenceLabel: fakeReferenceLabel,
      );

      final String str = vnPayQrInfoEntity.toString();
      expect(str,
          'VNPayQrInfoEntity { storeLabel: $fakeStoreLabel, merchantName: $fakeMerchantName ,mid: $fakeMid, type: $fakeType, amount: $fakeAmount, qr_code: $fakeQRCode, referenceLabel: $fakeReferenceLabel}');
    });
  });
}
