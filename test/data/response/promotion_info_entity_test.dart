import 'package:evoapp/data/response/invalid_voucher_entity.dart';
import 'package:evoapp/data/response/promotion_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PromotionInfoEntity', () {
    const int fakeDiscountAmount = 100;
    const Map<String, dynamic> fakeInvalidVoucherJson = <String, dynamic>{
      'voucher_id': 1,
      'user_message': 'test_reason',
    };
    const List<int> fakeVoucherIds = <int>[1, 2, 3];
    const int fakeCashbackAmount = 100;

    test('from<PERSON><PERSON> creates a valid object', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'discount_amount': fakeDiscountAmount,
        'invalid_vouchers': <dynamic>[fakeInvalidVoucherJson],
        'voucher_ids': fakeVoucherIds,
        'cashback_amount': fakeCashbackAmount,
      };

      final PromotionInfoEntity entity = PromotionInfoEntity.fromJson(json);

      expect(entity.discountAmount, fakeDiscountAmount);
      expect(entity.invalidVouchers, isNotNull);
      expect(entity.invalidVouchers?.first?.voucherId, 1);
      expect(entity.invalidVouchers?.first?.userMessage, 'test_reason');
      expect(entity.voucherIds, fakeVoucherIds);
      expect(entity.cashbackAmount, fakeCashbackAmount);
    });

    test('toJson returns a valid JSON representation', () {
      final InvalidVoucherEntity invalidVoucher =
          InvalidVoucherEntity.fromJson(fakeInvalidVoucherJson);

      final PromotionInfoEntity entity = PromotionInfoEntity(
        discountAmount: fakeDiscountAmount,
        invalidVouchers: <InvalidVoucherEntity>[invalidVoucher],
        voucherIds: fakeVoucherIds,
        cashbackAmount: fakeCashbackAmount,
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json, isNotNull);
      expect(json['discount_amount'], fakeDiscountAmount);
      expect(json['invalid_vouchers'], isNotNull);
      expect(json['voucher_ids'], fakeVoucherIds);
      expect(json['cashback_amount'], fakeCashbackAmount);
    });
  });
}
