import 'package:evoapp/data/response/card_activate_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CardActivateEntity', () {
    test('verify verdict', () {
      expect(CardActivateEntity.verdictInvalidState, 'invalid_state');
      expect(CardActivateEntity.verdictRedirectTPBApp, 'redirect_tpb_app');
    });

    test('fromBaseResponse creates a valid entity', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: 200,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'redirect_url': 'https://example.com',
          }
        },
      );

      final CardActivateEntity entity = CardActivateEntity.fromBaseResponse(baseResponse);

      expect(entity.redirectUrl, 'https://example.com');
      expect(entity.statusCode, CommonHttpClient.SUCCESS);
    });

    test('fromBaseResponse creates an unserializable entity on invalid data', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: 200,
        response: null,
      );

      final CardActivateEntity entity = CardActivateEntity.fromBaseResponse(baseResponse);

      expect(entity.redirectUrl, isNull);
    });

    test('toJson returns correct map', () {
      final CardActivateEntity entity = CardActivateEntity(redirectUrl: 'https://example.com');

      final Map<String, dynamic> json = entity.toJson();

      expect(json['redirect_url'], 'https://example.com');
    });

    test('unserializable entity has correct properties', () {
      final CardActivateEntity entity = CardActivateEntity.unserializable();

      expect(entity.redirectUrl, isNull);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });
  });
}
