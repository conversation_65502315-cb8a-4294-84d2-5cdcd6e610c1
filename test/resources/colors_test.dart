import 'package:evoapp/resources/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late EvoColors evoColors;

  setUpAll(() {
    evoColors = EvoColors();
  });

  test('verify region page color constants', () {
    expect(evoColors.primary, const Color(0xFF09B364));

    expect(evoColors.foreground, const Color(0xFF1D1D1D));
    expect(evoColors.background, const Color(0xFFFFFFFF));

    expect(evoColors.error, const Color(0xFFE54D2E));
    expect(evoColors.highlighted, const Color(0xFF09B364));
    expect(evoColors.appBarShadow, const Color(0x14000000));
    expect(evoColors.divider, const Color(0xFFE9E9E9));

    // bottom sheet color
    expect(evoColors.bottomSheetBackground, Colors.white);

    expect(evoColors.bottomSheetSelectedItem, const Color(0xFF1D1D1D));
    expect(evoColors.bottomSheetUnselectedItem, const Color(0xFFC2C2C2));
  });

  test('verify region TextField color constants', () {
    expect(evoColors.focusedTextFieldBorder, const Color(0xFF09B364));
    expect(evoColors.textFieldBorder, const Color(0xFFD1D1D1));
    expect(evoColors.disableTextFieldBorder, const Color(0xFFE9E9E9));
    expect(evoColors.disableTextFieldBg, const Color(0x0A1D1D1D));

    expect(evoColors.textFieldBg, const Color(0xFFFFFFFF));
    expect(evoColors.textSelectedBg, const Color(0x1D1D1D14));
  });

  test('verify region button color constants', () {
    // primary button
    expect(evoColors.primaryButtonForeground, Colors.white);
    expect(evoColors.primaryButtonBg, const Color(0xFF1D1D1D));
    expect(evoColors.primaryButtonForegroundDisable, Colors.white);
    expect(evoColors.primaryButtonBgDisable, const Color(0xFFE9E9E9));

    // secondary button
    expect(evoColors.secondaryButtonForeground, const Color(0xFF09B364));
    expect(evoColors.secondaryButtonBg, const Color(0xFFECF9F3));
    expect(evoColors.secondaryButtonForegroundDisable, const Color(0xFFD1D1D1));
    expect(evoColors.secondaryButtonBgDisable, const Color(0xFFE9E9E9));

    // accent button
    expect(evoColors.accentButtonForeground, Colors.white);
    expect(evoColors.accentButtonBg, const Color(0xFF09B364));
    expect(evoColors.accentButtonForegroundDisable, Colors.white);
    expect(evoColors.accentButtonBgDisable, const Color(0xFFD5F6E7));

    // tertiary button
    expect(evoColors.tertiaryButtonForeground, const Color(0xFF1D1D1D));
    expect(evoColors.tertiaryButtonBg, const Color(0xFFFFFFFF));
    expect(evoColors.tertiaryButtonForegroundDisable, const Color(0xFFD1D1D1));
    expect(evoColors.tertiaryButtonBgDisable, const Color(0xFFFFFFFF));

    // negative button
    expect(evoColors.negativeButtonForeground, const Color(0xFFFFFFFF));
    expect(evoColors.negativeButtonBg, const Color(0xFFE73F3C));
    expect(evoColors.negativeButtonBgDisable, const Color(0xFFD1D1D1));
    expect(evoColors.negativeButtonForegroundDisable, const Color(0xFFFFFFFF));
  });

  test('verify region otp text input field color constants', () {
    expect(evoColors.inputFocusedColor, const Color(0xFF09B364));
    expect(evoColors.inputUnfocusedColor, const Color(0xFFD1D1D1));
    expect(evoColors.selectedRadioButton, const Color(0xFF60A5FA));
  });

  test('verify region WebView loading Progress Bg color constants', () {
    expect(evoColors.webViewProgressBg, const Color(0xFFC2C2C2));
    expect(evoColors.webViewProgressValue, const Color(0xFF4AC58D));
    expect(evoColors.iconColor, const Color(0xFFE52722));
    expect(evoColors.loadingViewColor, const Color(0xFF09B364));
  });

  test('verify Evo checkbox', () {
    expect(evoColors.checkBoxChecked, const Color(0xFF1F71F4));
    expect(evoColors.checkBoxUnChecked, const Color(0xFFFFFFFF));
    expect(evoColors.checkBoxBorderUnChecked, const Color(0xFFD1D1D1));
    expect(evoColors.checkBoxBorderChecked, const Color(0xFF1F71F4));
  });

  test('verify region EVO color constants', () {
    expect(evoColors.secondaryBackground, const Color(0xFFFAFAFA));

    expect(evoColors.profileContainerBackground, const Color(0xFFECF9F3));

    expect(evoColors.activeStepBackground, const Color(0xFFD5F6E7));

    expect(evoColors.activeStepIcon, const Color(0xFF01AA4F));

    expect(evoColors.inActiveStepIcon, const Color(0xFFC2C2C2));

    expect(evoColors.snackBarSuccessBackground, const Color(0xFFECF9F3));

    expect(evoColors.snackBarSuccessBorder, const Color(0xFFD7F4E5));

    expect(evoColors.inactiveSwitchBackground, const Color(0xFFE9E9E9));

    expect(evoColors.activeSwitchBackground, const Color(0xFF09B364));

    expect(evoColors.snackBarErrorBackground, const Color(0xFFFFE5DC));

    expect(evoColors.snackBarErrorBorder, const Color(0xFFFFC6B2));

    // Promotion
    expect(evoColors.voucherBeforeRunningOutTime, const Color(0xFF09B364));

    expect(evoColors.campaignBeforeRunningOutTime, const Color(0xFFF5A70B));

    expect(evoColors.promotionRunningOutTime, const Color(0xFFF5A70B));

    expect(evoColors.promotionNotRunningOutTime, const Color(0xFF999999));

    expect(evoColors.voucherBackgroundBorder, const Color(0xFFD1D1D1));

    expect(evoColors.transactionPending, const Color(0xFFF5A70B));

    expect(evoColors.snackBarWarningBackground, const Color(0xFFFEF3DC));

    expect(evoColors.snackBarWarningBorder, const Color(0xFFFEE7B9));

    expect(evoColors.snackBarNeutralBackground, const Color(0xFFEBF3FF));

    expect(evoColors.snackBarNeutralBorder, const Color(0xFFBFDBFE));

    expect(evoColors.snackBarDefaultBackground, const Color(0xFFFFFFFF));

    expect(evoColors.snackBarDefaultBorder, const Color(0xFF1D1D1D));

    expect(evoColors.promotionIsUsed, const Color(0xFFC2C2C2));

    expect(evoColors.promotionTimeout, const Color(0xFF5E5E5E));

    expect(evoColors.promotionHotTime, const Color(0xFFE54D2E));

    expect(evoColors.promotionText, const Color(0xFFFFFFFF));

    expect(evoColors.campaignIsComing, const Color(0xFF09B364));

    expect(evoColors.linkCardMessage, const Color(0xFFF5A70B));

    expect(evoColors.textActiveReminder, Colors.black);

    expect(evoColors.paymentInfoCardShadow, Colors.black.withOpacity(0.04));

    expect(evoColors.paymentPromotionTitle, const Color(0xFF0F0F0F));

    expect(evoColors.paymentPromotionCardItemShadow, const Color(0xFF000008));

    expect(evoColors.promotionItemUnqualifiedBorder, const Color(0xFFFBC760));

    expect(evoColors.promotionItemUnqualifiedText, const Color(0xFFF5A70B));

    // Referral
    expect(evoColors.referralNewMemberText, const Color(0xFFFFFFFF));

    // Transaction history
    expect(evoColors.transactionHistoryProcessing, const Color(0xFFF5A70B));
    expect(evoColors.transactionHistorySuccess, const Color(0xFF01AA4F));
    expect(evoColors.transactionHistoryFailure, const Color(0xFFE54D2E));
    expect(evoColors.transactionListHistoryEmiLabel, const Color(0xFF1F71F4));
    expect(evoColors.transactionListHistoryEmiNewLabel, const Color(0xFF0D6D40));

    expect(evoColors.transactionHistoryBgProcessing, const Color(0xFFFEF3DC));
    expect(evoColors.transactionHistoryBgSuccess, const Color(0xFFECF9F3));
    expect(evoColors.transactionHistoryBgFailure, const Color(0xFFFFE5DC));
    expect(evoColors.transactionListHistoryBgEmiLabel, const Color(0xFFEBF3FF));
    expect(evoColors.transactionListHistoryBgEmiNewLabel, const Color(0xFFECF9F3));
    expect(evoColors.placeholderBorder, const Color(0xFFE9E9E9));

    expect(evoColors.transactionHistoryEmiStatusReceived, const Color(0xFF999999));
    expect(evoColors.transactionHistoryEmiStatusApproved, const Color(0xFF01AA4F));
    expect(evoColors.transactionHistoryEmiStatusRejected, const Color(0xFFE54D2E));
    expect(evoColors.transactionHistoryEmiStatusDone, const Color(0xFF01AA4F));
    expect(evoColors.transactionHistoryEmiStatusUnknown, const Color(0xFF999999));
    expect(evoColors.transactionHistoryCashbackBackground, Colors.white);
    expect(evoColors.transactionHistoryTotalAmountProgress, const Color(0xFFE9E9E9));
    expect(evoColors.transactionHistoryPaidAmount, const Color(0xFF09B364));

    expect(evoColors.transactionHistoryEmiTitleReceived, const Color(0xFF5E5E5E));
    expect(evoColors.transactionHistoryEmiTitleApproved, const Color(0xFF01AA4F));
    expect(evoColors.transactionHistoryEmiTitleRejected, const Color(0xFFE54D2E));
    expect(evoColors.transactionHistoryEmiTitleDone, const Color(0xFF01AA4F));
    expect(evoColors.transactionHistoryEmiTitleUnknown, const Color(0xFF5E5E5E));

    // Card linked
    expect(evoColors.linkedCardLimitationBackground, const Color(0xFF1D1D1D).withOpacity(0.04));
    expect(evoColors.linkedCardLimitationTitle, const Color(0xFF5E5E5E));
    expect(evoColors.linkedCardLimitationValue, Colors.black);
    expect(evoColors.linkedCardLimitationPerTrans, const Color(0xFF999999));
    expect(evoColors.linkedCardLine, const Color(0xFF1D1D1D).withOpacity(0.08));

    // Intro
    expect(evoColors.passiveIndicator, const Color(0x331D1D1D));
    expect(evoColors.activeIndicator, const Color(0xFF222222));

    // Policy
    expect(evoColors.activePrivatePolicy, const Color(0xFF1F71F4));

    // Feed back
    expect(evoColors.feedBackEmail, const Color(0xFF1C1C1C));

    // Card status
    expect(evoColors.cardStatusRemainingSteps, const Color(0xFFF5A70B));
    expect(evoColors.creditLimitAwaitingForApproval, const Color(0xFF999999));
    expect(evoColors.creditLimitNotReadyForPayment, const Color(0xFF1D1D1D));
    expect(evoColors.creditLimitReadyForPaymentOrOutOfSync, const Color(0xFF09B364));
    expect(evoColors.creditLimitIconApprovedOrOutOfSync, const Color(0xFF1D1D1D));
    expect(evoColors.creditLimitIconAwaitingForApproval, const Color(0xFF999999));
    expect(evoColors.cardStatusTitleNotReadyForPayment, const Color(0xFF999999));
    expect(evoColors.cardStatusTitleReadyForPaymentOrOutOfSync, const Color(0xFF09B364));

    // profile settings
    expect(evoColors.settingsCardShadow, const Color(0x0A000000));

    // delete account
    expect(evoColors.deleteAccountSuccessTitle, const Color(0xFF0D121C));
    expect(evoColors.surveyItemBackground, const Color(0x0A1D1D1D));

    // referral QR code
    expect(evoColors.referralQrCodeBackground, const Color(0xFF0D6C40));
    expect(evoColors.referralQrCodeWhiteText, const Color(0xFFFFFFFF));

    // Payment Input Amount V2
    expect(evoColors.paymentInputAmountV2TextHint, const Color(0xFFC2C2C2));
    expect(evoColors.paymentManualLinkCard, const Color(0xFFF5A70B));
    expect(evoColors.pendingTransactionNoteLeftBorder, const Color(0xFF1F71F4));

    //EMI
    expect(evoColors.emiTenorBackground, const Color(0xFF1C1C1C));
    expect(evoColors.emiContainerBackground, const Color(0xFFECF9F3));
    expect(evoColors.paymentResultEmiNoteErrorShadow, Colors.black);
    expect(
        evoColors.paymentResultEmiMoreDetailBackground, const Color(0xFF1D1D1D).withOpacity(0.08));
    expect(evoColors.emiRegularText, const Color(0xFF1C1C1C));
    expect(evoColors.emiTooltipsBackground, const Color(0xFF1C1C1C));
    expect(evoColors.emiVoucherSelectedBorder, const Color(0xFF09B364));
    expect(evoColors.emiVoucherSelectedBackground, const Color(0xFFECF9F3));
    expect(evoColors.emiInvalidVoucherSelectedBorder, const Color(0xFFF5A70B));
    expect(evoColors.emiTransactionPaymentSuccess, const Color(0xFF1F71F4));
    expect(evoColors.mwgEmiInvalidVoucherSelectedBackground, const Color(0xFFFEF3DC));
    expect(evoColors.mwgEmiInvalidVoucherSelectedBorder, const Color(0xFFF5A70B));

    // Revamp Homepage for non-user
    expect(evoColors.storyProgressBarBackground, Colors.white.withOpacity(0.3));
    expect(evoColors.storyTitleColor, const Color(0xFF010F1D).withOpacity(0.7));
    expect(evoColors.storyViewDetailColor, const Color(0xFF000000));
    expect(evoColors.storyViewFooterBackgroundColor, const Color(0xFFFFFFFF));
    expect(evoColors.storyViewFooterShadowColor, const Color(0xFF000000).withOpacity(0.08));
    expect(evoColors.storyViewFooterTitleColor, const Color(0xFF5E5E5E));

    // Pos limit enable guide
    expect(evoColors.posLimitIndicatorTextColor, const Color(0xFFFFFFFF));
    expect(evoColors.posLimitIndicatorEnable, const Color(0xFFFFFFFF));
    expect(evoColors.posLimitIndicatorDisable, const Color(0xFF5E5E5E));
    expect(evoColors.posLimitIndicatorBackground, const Color(0xFF000000));

    // Remind POS limit enable guide
    expect(evoColors.remindPosLimitTitle, const Color(0xFF000000));
    expect(evoColors.remindPosLimitDescription, const Color(0xFF5E5E5E));
    expect(evoColors.remindPosLimitIndicator, const Color(0xFF000000));
    expect(evoColors.remindPosLimitIndicatorBackground, const Color(0xFFFFFFFF));
    expect(evoColors.remindPosLimitIndicatorBorder, const Color(0xFF000000));
    expect(evoColors.remindPosLimitIndicatorShadow, const Color(0xFFFFFFFF).withOpacity(0.08));
    expect(evoColors.remindPosLimitIndicatorText, const Color(0xFF000000));

    /// EMI Management
    expect(evoColors.emiManagementApproved, const Color(0xFF09B364));
    expect(evoColors.emiManagementRejected, const Color(0xFFE54D2E));
    expect(evoColors.emiManagementProcessing, const Color(0xFF999999));
    expect(evoColors.emiManagementBottomBorder, const Color(0xFFE9E9E9));
    expect(evoColors.emiManagementDefaultItemBackground, Colors.white);
    expect(evoColors.emiManagementAmountItem, const Color(0xFF0F0F0F));
    expect(evoColors.emiManagementPaidAmount, const Color(0xFF09B364));
    expect(evoColors.emiManagementTotalAmountProgress, const Color(0xFFE9E9E9));

    /// Active POS Limit
    expect(evoColors.activatedCardGuideColor, const Color(0xFF1F71F4));

    // Setup POS limit
    expect(evoColors.itemPOSLimitSuggestionBg, const Color(0xFFE9E9E9));
    expect(evoColors.posLimitHintTextColor, const Color(0x331D1D1D));

    /// Circular countdown
    expect(evoColors.countdownTotalColor, const Color(0xFFD1D1D1));
    expect(evoColors.countdownBackgroundGradientCenterColor, const Color(0xffEEFBF5));
  });
}
