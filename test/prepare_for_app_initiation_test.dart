// ignore_for_file: depend_on_referenced_packages
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'package:evoapp/data/repository/announcement_repo.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/campaign_repo.dart';
import 'package:evoapp/data/repository/cashback_repo.dart';
import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/repository/common_repo.dart';
import 'package:evoapp/data/repository/decree_consent_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/repository/ekyc_repo.dart';
import 'package:evoapp/data/repository/emi_repo.dart';
import 'package:evoapp/data/repository/home_repo.dart';
import 'package:evoapp/data/repository/merchant_repo.dart';
import 'package:evoapp/data/repository/mock_test_config_repo.dart';
import 'package:evoapp/data/repository/qr_code_repo.dart';
import 'package:evoapp/data/repository/referral_repo.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/feature/activated_pos_limit/utils/activated_pos_limit_flow/activated_pos_limit_flow.dart';
import 'package:evoapp/feature/alice/v2/evo_alice_chatwoot_builder/evo_alice_chatwoot_builder.dart';
import 'package:evoapp/feature/announcement/utils/unread_announcement_checker.dart';
import 'package:evoapp/feature/appsflyer/appsflyer_handler.dart';
import 'package:evoapp/feature/appsflyer/one_link_utils.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_handler.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_popup.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_handler.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_util.dart';
import 'package:evoapp/feature/biometric/utils/biometric_status_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometric_type_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/deep_link/deep_link_handler.dart';
import 'package:evoapp/feature/deep_link/deep_link_utils.dart';
import 'package:evoapp/feature/dop_native/dialogs/input_phone_number/cubit/dop_native_input_phone_cubit.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/fpt_sdk_bridge.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/nfc_reader.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/ekyc_ui_only_bridge.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/utils/dop_native_ekyc_api_response_handler/dop_native_ekyc_api_response_handler.dart';
import 'package:evoapp/feature/dop_native/features/recaptcha/recaptcha_handler.dart';
import 'package:evoapp/feature/dop_native/features/verify_otp/cubit/dop_native_verify_otp_cubit.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/dop_native/util/dop_native_navigation_utils.dart';
import 'package:evoapp/feature/dop_native/util/nfc_availability_wrapper/nfc_availability_wrapper.dart';
import 'package:evoapp/feature/ekyc/ekyc_bridge/ekyc_bridge.dart';
import 'package:evoapp/feature/ekyc/ekyc_sdk_helper/init_ekyc_sdk_helper.dart';
import 'package:evoapp/feature/emi_management/utils/emi_management_utils.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/widgets/story_indicator/story_indicator_widget.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_handler.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_utils/evo_event_tracking_utils.dart';
import 'package:evoapp/feature/logging/evo_navigator_observer.dart';
import 'package:evoapp/feature/maintenance/maintenance_handler.dart';
import 'package:evoapp/feature/manual_link_card/manual_link_card_cubit.dart';
import 'package:evoapp/feature/mock_test/mock_test_helper.dart';
import 'package:evoapp/feature/payment/base_page_payment/cubit/update_order_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/bloc/confirm_button_cubit/confirm_button_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/bloc/confirm_payment_cubit.dart';
import 'package:evoapp/feature/payment/confirm_payment/bloc/order_info_cubit.dart';
import 'package:evoapp/feature/payment/qrcode_scanner/bloc/after_parse/qr_scanner_after_parse_cubit.dart';
import 'package:evoapp/feature/payment/qrcode_scanner/bloc/parse_qr_code/parse_qr_code_cubit.dart';
import 'package:evoapp/feature/payment/qrcode_scanner/bloc/setup_emi_condition/setup_emi_condition_cubit.dart';
import 'package:evoapp/feature/payment/qrcode_scanner/widget/evo_qr_code_controller.dart';
import 'package:evoapp/feature/payment/utils/active_pos_limit_handler/active_pos_limit_handler.dart';
import 'package:evoapp/feature/payment/utils/auto_apply_voucher_handler/auto_apply_voucher_handler.dart';
import 'package:evoapp/feature/payment/utils/payment_with_emi_utils.dart';
import 'package:evoapp/feature/payment/utils/voucher_detail_action_helper.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_handler.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/widget/credit_limit_widget/credit_limit_amount/credit_limit_amount_cubit.dart';
import 'package:evoapp/feature/remote_config/remote_config_helper.dart';
import 'package:evoapp/feature/sharing/sharing_feature.dart';
import 'package:evoapp/feature/splash_screen/utils/exit_app_feature/exit_app_feature.dart';
import 'package:evoapp/feature/splash_screen/utils/secure_detection_utils/secure_detection.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/colors.dart';
import 'package:evoapp/resources/input_borders.dart';
import 'package:evoapp/util/download_file_handler/download_file_handler.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/file_browser/file_browser_helper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/navigator/evo_router_navigator.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/task_polling_handler/task_polling_handler.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:evoapp/util/url_launcher_uri_wrapper.dart';
import 'package:evoapp/util/web_link_utils.dart';
import 'package:evoapp/widget/evo_overlay/evo_overlay_util_functions.dart';
import 'package:firebase_core_platform_interface/firebase_core_platform_interface.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/data_collection/data_collector.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_crashlytics.dart';
import 'package:flutter_common_package/feature/webview/webview_utils.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/alert_manager.dart';
import 'package:flutter_common_package/util/clear_all_notifications_wrapper.dart';
import 'package:flutter_common_package/util/clipboard_wrapper.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/flutter_downloader/common_flutter_downloader.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/util/local_storage_helper.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_common_package/util/otp_auto_fill/otp_auto_fill.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/uuid/uuid_generator.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_common_package/widget/default_widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

class MockFirebasePlatform extends Mock implements FirebasePlatform {}

// ignore: avoid_implementing_value_types
class MockFirebaseOptions extends Mock implements FirebaseOptions {}

class MockFirebaseCrashlytics extends Mock implements FirebaseCrashlytics {}

void main() {
  group('verify all dependencies are injected to GetIt', () {
    setUpAll(() async {
      TestWidgetsFlutterBinding.ensureInitialized();
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );
      _setupMockData();
      DatadogSdk.initializeForTesting();
      await prepareForAppInitiation();
    });

    tearDownAll(() {
      _tearDownMockData();
    });

    test('dependencies in common package should be all registered', () {
      expect(getIt.isRegistered<DevicePlatform>(), isTrue);
      expect(getIt.isRegistered<PackageInfo>(), isTrue);
      expect(getIt.isRegistered<DeviceInfoPlugin>(), isTrue);
      expect(getIt.isRegistered<DeviceInfoPluginWrapper>(), isTrue);
      expect(getIt.isRegistered<FirebaseAnalyticsWrapper>(), isTrue);
      expect(getIt.isRegistered<Connectivity>(), isTrue);
      expect(getIt.isRegistered<NetworkManager>(), isTrue);
      expect(getIt.isRegistered<FlutterSecureStorage>(), isTrue);
      expect(getIt.isRegistered<CommonLocalStorageHelper>(), isTrue);
      expect(getIt.isRegistered<CommonSharedPreferencesHelper>(), isTrue);
      expect(getIt.isRegistered<CommonUtilFunction>(), isTrue);
      expect(getIt.isRegistered<CommonHttpClient>(), isTrue);
      expect(getIt.isRegistered<Dio>(), isTrue);
      expect(getIt.isRegistered<LoggingRepo>(), isTrue);
      expect(getIt.isRegistered<UUIDGenerator>(), isTrue);
      expect(getIt.isRegistered<EventTrackingUtils>(), isTrue);
      expect(getIt.isRegistered<DataCollector>(), isTrue);
      expect(getIt.isRegistered<AlertManager>(), isTrue);
      expect(getIt.isRegistered<CommonImageProvider>(), isTrue);
      expect(getIt.isRegistered<CommonNavigatorObserver>(), isTrue);
      expect(getIt.isRegistered<OtpAutoFill>(), isTrue);
      expect(getIt.isRegistered<ClipboardWrapper>(), isTrue);
      expect(getIt.isRegistered<UrlLauncherWrapper>(), isTrue);
      expect(getIt.isRegistered<ClearAllNotificationsWrapper>(), isTrue);
      expect(getIt.isRegistered<CommonFlutterDownloader>(), isTrue);
      expect(getIt.isRegistered<GlobalKeyProvider>(), isTrue);
      expect(getIt.isRegistered<CommonWebViewUtils>(), isTrue);
    });

    test('api call related dependencies should be all registered', () {
      expect(getIt.isRegistered<Dio>(instanceName: nonAuthenticationHttpClientInstance), isTrue);
      expect(
          getIt.isRegistered<CommonHttpClient>(instanceName: nonAuthenticationHttpClientInstance),
          isTrue);
      expect(getIt.isRegistered<Dio>(instanceName: getItInstanceNameForDOPNative), isTrue);
      expect(getIt.isRegistered<CommonHttpClient>(instanceName: getItInstanceNameForDOPNative),
          isTrue);
      expect(getIt.isRegistered<AuthorizationSessionExpiredHandler>(), isTrue);
      expect(getIt.isRegistered<AuthorizationSessionExpiredPopup>(), isTrue);
      expect(getIt.isRegistered<MaintenanceHandler>(), isTrue);
    });

    test('logging dependencies should be all registered', () {
      expect(getIt.isRegistered<EvoEventTrackingUtils>(), isTrue);
      expect(getIt.isRegistered<EvoNavigatorObserver>(), isTrue);
      expect(getIt.isRegistered<EvoEventTrackingHandler>(), isTrue);
    });

    test('ui components dependencies should be all registered', () {
      expect(getIt.isRegistered<CommonTextStyles>(), isTrue);
      expect(getIt.isRegistered<CommonColors>(), isTrue);
      expect(getIt.isRegistered<EvoColors>(), isTrue);
      expect(getIt.isRegistered<CommonButtonDimensions>(), isTrue);
      expect(getIt.isRegistered<CommonButtonStyles>(), isTrue);
      expect(getIt.isRegistered<CommonDefaultWidgets>(), isTrue);
      expect(getIt.isRegistered<EvoInputBorders>(), isTrue);
      expect(getIt.isRegistered<DOPNativeColors>(), isTrue);
      expect(getIt.isRegistered<DopNativeButtonStyles>(), isTrue);
      expect(getIt.isRegistered<DOPNativeTextStyles>(), isTrue);
    });

    test('storage dependencies should be all registered', () {
      expect(getIt.isRegistered<EvoLocalStorageHelper>(), isTrue);
      expect(getIt.isRegistered<JwtHelper>(), isTrue);
      expect(getIt.isRegistered<FileBrowserHelper>(), isTrue);
    });

    test('repository should be all registered', () {
      expect(getIt.isRegistered<UserRepo>(), isTrue);
      expect(getIt.isRegistered<AuthenticationRepo>(), isTrue);
      expect(getIt.isRegistered<CampaignRepo>(), isTrue);
      expect(getIt.isRegistered<HomeRepo>(), isTrue);
      expect(getIt.isRegistered<AnnouncementRepo>(), isTrue);
      expect(getIt.isRegistered<MerchantRepo>(), isTrue);
      expect(getIt.isRegistered<CheckOutRepo>(), isTrue);
      expect(getIt.isRegistered<QrCodeRepo>(), isTrue);
      expect(getIt.isRegistered<CommonRepo>(), isTrue);
      expect(getIt.isRegistered<DecreeConsentRepo>(), isTrue);
      expect(getIt.isRegistered<EKYCRepo>(), isTrue);
      expect(getIt.isRegistered<MockTestConfigRepo>(), isTrue);
      expect(getIt.isRegistered<ReferralRepo>(), isTrue);
      expect(getIt.isRegistered<EmiRepo>(), isTrue);
      expect(getIt.isRegistered<DOPNativeRepo>(), isTrue);
      expect(getIt.isRegistered<DopNativeEkycUIOnlyRepo>(), isTrue);
      expect(getIt.isRegistered<CashbackRepo>(), isTrue);
    });

    test('biometric dependencies should be all registered', () {
      expect(getIt.isRegistered<BiometricsAuthenticate>(), isTrue);
      expect(getIt.isRegistered<TsBioDetectChanged>(), isTrue);
      expect(getIt.isRegistered<BiometricsTokenModule>(), isTrue);
      expect(getIt.isRegistered<BiometricTypeHelper>(), isTrue);
      expect(getIt.isRegistered<RequestUserActiveBiometricUtil>(), isTrue);
      expect(getIt.isRegistered<RequestUserActivateBiometricHandler>(), isTrue);
      expect(getIt.isRegistered<BiometricStatusHelper>(), isTrue);
    });

    test('ekyc sdk dependencies should be all registered', () {
      expect(getIt.isRegistered<EkycBridge>(), isTrue);
      expect(getIt.isRegistered<InitEKYCSdkHelper>(), isTrue);
      expect(getIt.isRegistered<NFCReader>(), isTrue);
      expect(getIt.isRegistered<EkycUiOnlyBridge>(), isTrue);
      expect(getIt.isRegistered<FptSdkBridge>(), isTrue);
      expect(getIt.isRegistered<NfcAvailabilityWrapper>(), isTrue);
    });

    test('link utils dependencies should be all registered', () {
      expect(getIt.isRegistered<DeepLinkHandler>(), isTrue);
      expect(getIt.isRegistered<DeepLinkUtils>(), isTrue);
      expect(getIt.isRegistered<WebLinkUtils>(), isTrue);
    });

    test('dop native util dependencies should be all registered', () {
      expect(getIt.isRegistered<DOPUtilFunctions>(), isTrue);
      expect(getIt.isRegistered<DOPNativeNavigationUtils>(), isTrue);
      expect(getIt.isRegistered<DOPNativeVerifyOtpCubit>(), isTrue);
      expect(getIt.isRegistered<DOPNativeInputPhoneNumberCubit>(), isTrue);
      expect(getIt.isRegistered<EkycCommonApiResponsesHandler>(), isTrue);
      expect(getIt.isRegistered<RecaptchaHandler>(), isTrue);
    });

    test('all other dependencies in host app should be all registered', () {
      expect(getIt.isRegistered<EvoFlutterWrapper>(), isTrue);
      expect(getIt.isRegistered<EvoUtilFunction>(), isTrue);
      expect(getIt.isRegistered<EmiManagementUtilFunctions>(), isTrue);
      expect(getIt.isRegistered<EvoOverlayUtilFunctions>(), isTrue);
      expect(getIt.isRegistered<SecureDetection>(), isTrue);
      expect(getIt.isRegistered<ExitAppFeature>(), isTrue);
      expect(getIt.isRegistered<AppState>(), isTrue);
      expect(getIt.isRegistered<EvoSnackBar>(), isTrue);
      expect(getIt.isRegistered<EvoNavigatorTypeFactory>(), isTrue);
      expect(getIt.isRegistered<CommonNavigator>(), isTrue);
      expect(getIt.isRegistered<FeatureToggle>(), isTrue);
      expect(getIt.isRegistered<RemoteConfigHelper>(), isTrue);
      expect(getIt.isRegistered<ResetPinHandler>(), isTrue);
      expect(getIt.isRegistered<UnreadAnnouncementChecker>(), isTrue);
      expect(getIt.isRegistered<MockTestHelper>(), isTrue);
      expect(getIt.isRegistered<UrlLauncherWrapper>(), isTrue);
      expect(getIt.isRegistered<SharingFeature>(), isTrue);
      expect(getIt.isRegistered<CreditLimitAmountCubit>(), isTrue);
      expect(getIt.isRegistered<VoucherDetailActionHelper>(), isTrue);
      expect(getIt.isRegistered<StoryIndicatorController>(), isTrue);
      expect(getIt.isRegistered<EvoAliceChatwootBuilder>(), isTrue);
      expect(getIt.isRegistered<AppsflyerHandler>(), isTrue);
      expect(getIt.isRegistered<DownloadFileHandler>(), isTrue);
      expect(getIt.isRegistered<OneLinkUtils>(), isTrue);
      expect(getIt.isRegistered<TaskPollingHandler>(), isTrue);
      expect(getIt.isRegistered<ActivatedPOSLimitFlow>(), isTrue);
    });

    test('check all instance factory should be registered', () {
      expect(getIt.isRegistered<ParseQRCodeCubit>(), isTrue);
      expect(getIt.isRegistered<SetupEmiConditionCubit>(), isTrue);
      expect(getIt.isRegistered<QrScannerAfterParseCubit>(), isTrue);
      expect(getIt.isRegistered<ManualLinkCardCubit>(), isTrue);
      expect(getIt.isRegistered<ActivePosLimitHandler>(), isTrue);
      expect(getIt.isRegistered<PaymentWithEMIUtils>(), isTrue);
      expect(getIt.isRegistered<EvoMobileScannerController>(), isTrue);
      expect(getIt.isRegistered<ConfirmPaymentCubit>(), isTrue);
      expect(getIt.isRegistered<ConfirmButtonCubit>(), isTrue);
      expect(getIt.isRegistered<UpdateOrderCubit>(), isTrue);
      expect(getIt.isRegistered<OrderInfoCubit>(), isTrue);
      expect(getIt.isRegistered<AutoApplyVoucherHandler>(), isTrue);
    });
  });
}

void _setupMockData() {
  // firebase
  final FirebasePlatform mockFirebase = MockFirebasePlatform();
  Firebase.delegatePackingProperty = mockFirebase;
  when(() => mockFirebase.initializeApp(name: any(named: 'name'), options: any(named: 'options')))
      .thenAnswer((_) async => FirebaseAppPlatform(defaultFirebaseAppName, MockFirebaseOptions()));
  when(() => mockFirebase.app(any()))
      .thenAnswer((_) => FirebaseAppPlatform(defaultFirebaseAppName, MockFirebaseOptions()));

  // crashlytics
  final FirebaseCrashlytics mockFirebaseCrashlytics = MockFirebaseCrashlytics();
  FirebaseCrashlyticsWrapper.instanceForTesting = mockFirebaseCrashlytics;

  when(
    () => mockFirebaseCrashlytics.setCustomKey(HeaderKey.deviceId, any()),
  ).thenAnswer((_) async {});

  // mock channels
  for (final _MockChannel mockChannel in _MockChannel.values) {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      MethodChannel(mockChannel.channelName),
      (MethodCall methodCall) async {
        return mockChannel.data;
      },
    );
  }
}

void _tearDownMockData() {
  FirebaseCrashlyticsWrapper.resetToOriginalInstance();

  Firebase.delegatePackingProperty = null;
  //mock channels
  for (final _MockChannel mockChannel in _MockChannel.values) {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      MethodChannel(mockChannel.channelName),
      null,
    );
  }
}

enum _MockChannel {
  oneSignal('OneSignal'),
  downloader('vn.hunghd/downloader'),
  packageInfo('dev.fluttercommunity.plus/package_info', <String, dynamic>{}),
  appsFlyerCallBacks('callbacks'),
  appsFlyerAfApi('af-api'),
  firebasePerformance('plugins.flutter.io/firebase_performance'),
  sensorPlusMethod('dev.fluttercommunity.plus/sensors/method'),
  sensorPlusAccelerometer('dev.fluttercommunity.plus/sensors/accelerometer'),
  connectivity('dev.fluttercommunity.plus/connectivity'),
  connectivityStatus('dev.fluttercommunity.plus/connectivity_status'),
  deviceInfo('dev.fluttercommunity.plus/device_info', <String, dynamic>{
    'version': <String, dynamic>{
      'sdkInt': 30,
      'baseOS': 'baseOS',
      'codename': 'codename',
      'incremental': 'incremental',
      'previewSdkInt': 30,
      'release': 'release',
      'securityPatch': 'securityPatch',
    },
    'board': '.board',
    'bootloader': '.bootloader',
    'brand': '.brand',
    'device': '.device',
    'display': '.display',
    'fingerprint': '.fingerprint',
    'hardware': '.hardware',
    'host': '.host',
    'id': '.id',
    'manufacturer': '.manufacturer',
    'model': '.model',
    'product': '.product',
    'supported32BitAbis': <String>[],
    'supported64BitAbis': <String>[],
    'supportedAbis': <String>[],
    'tags': '.tags',
    'type': '.type',
    'isPhysicalDevice': false,
    'systemFeatures': <String>[],
    'serialNumber': 'serialNumber',
    'isLowRamDevice': false,
  });

  final String channelName;
  final dynamic data;

  const _MockChannel(this.channelName, [this.data]);
}
