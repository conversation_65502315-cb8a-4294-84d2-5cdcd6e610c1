<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/resources/colors.dart</title>
  <link rel="stylesheet" type="text/css" href="../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/resources">lib/resources</a> - colors.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">99.5&nbsp;%</td>
            <td class="headerCovTableEntry">183</td>
            <td class="headerCovTableEntry">182</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/resources/colors.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : class EvoColors extends CommonColors {</span>
<span id="L5"><span class="lineNum">       5</span>              :   // region page</span>
<span id="L6"><span class="lineNum">       6</span> <span class="tlaGNC">          48 :   @override</span></span>
<span id="L7"><span class="lineNum">       7</span>              :   Color get primary =&gt; const Color(0xFF09B364);</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span> <span class="tlaGNC">          23 :   @override</span></span>
<span id="L10"><span class="lineNum">      10</span>              :   Color get foreground =&gt; const Color(0xFF1D1D1D);</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span> <span class="tlaGNC">          53 :   @override</span></span>
<span id="L13"><span class="lineNum">      13</span>              :   Color get background =&gt; const Color(0xFFFFFFFF);</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span> <span class="tlaGNC">          21 :   @override</span></span>
<span id="L16"><span class="lineNum">      16</span>              :   Color get error =&gt; const Color(0xFFE54D2E);</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L19"><span class="lineNum">      19</span>              :   Color get highlighted =&gt; const Color(0xFF09B364);</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC">          49 :   @override</span></span>
<span id="L22"><span class="lineNum">      22</span>              :   Color get appBarShadow =&gt; const Color(0x14000000);</span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">          28 :   Color get divider =&gt; const Color(0xFFE9E9E9);</span></span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              :   // bottom sheet color</span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           3 :   @override</span></span>
<span id="L28"><span class="lineNum">      28</span>              :   Color get bottomSheetBackground =&gt; Colors.white;</span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L31"><span class="lineNum">      31</span>              :   Color get bottomSheetSelectedItem =&gt; const Color(0xFF1D1D1D);</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L34"><span class="lineNum">      34</span>              :   Color get bottomSheetUnselectedItem =&gt; const Color(0xFFC2C2C2);</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span>              :   // endregion</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span>              :   // region text</span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">         112 :   @override</span></span>
<span id="L40"><span class="lineNum">      40</span>              :   Color get textActive =&gt; const Color(0xFF1D1D1D);</span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">          68 :   @override</span></span>
<span id="L43"><span class="lineNum">      43</span>              :   Color get textPassive =&gt; const Color(0xFF5E5E5E);</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">          40 :   @override</span></span>
<span id="L46"><span class="lineNum">      46</span>              :   Color get textPassive2 =&gt; const Color(0xFF999999);</span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">          23 :   @override</span></span>
<span id="L49"><span class="lineNum">      49</span>              :   Color get textHint =&gt; const Color(0xFFD1D1D1);</span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L52"><span class="lineNum">      52</span>              :   Color get textNormal =&gt; const Color(0xFF505560);</span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           6 :   @override</span></span>
<span id="L55"><span class="lineNum">      55</span>              :   Color get icon =&gt; const Color(0xFF1D1D1D);</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span>              :   // endregion</span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span>              :   // region TextField</span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaGNC">           4 :   @override</span></span>
<span id="L61"><span class="lineNum">      61</span>              :   Color get focusedTextFieldBorder =&gt; const Color(0xFF09B364);</span>
<span id="L62"><span class="lineNum">      62</span>              : </span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaGNC">           4 :   @override</span></span>
<span id="L64"><span class="lineNum">      64</span>              :   Color get textFieldBorder =&gt; const Color(0xFFD1D1D1);</span>
<span id="L65"><span class="lineNum">      65</span>              : </span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaGNC">          16 :   @override</span></span>
<span id="L67"><span class="lineNum">      67</span>              :   Color get disableTextFieldBorder =&gt; const Color(0xFFE9E9E9);</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">           3 :   @override</span></span>
<span id="L70"><span class="lineNum">      70</span>              :   Color get disableTextFieldBg =&gt; const Color(0x0A1D1D1D);</span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaGNC">           1 :   Color get textFieldBg =&gt; background;</span></span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L76"><span class="lineNum">      76</span>              :   Color get textSelectedBg =&gt; const Color(0x1D1D1D14);</span>
<span id="L77"><span class="lineNum">      77</span>              : </span>
<span id="L78"><span class="lineNum">      78</span>              :   // endregion</span>
<span id="L79"><span class="lineNum">      79</span>              : </span>
<span id="L80"><span class="lineNum">      80</span>              :   // region button</span>
<span id="L81"><span class="lineNum">      81</span>              :   // primary button</span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaGNC">          45 :   @override</span></span>
<span id="L83"><span class="lineNum">      83</span>              :   Color get primaryButtonForeground =&gt; Colors.white;</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaGNC">          37 :   @override</span></span>
<span id="L86"><span class="lineNum">      86</span>              :   Color get primaryButtonBg =&gt; const Color(0xFF1D1D1D);</span>
<span id="L87"><span class="lineNum">      87</span>              : </span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">          13 :   @override</span></span>
<span id="L89"><span class="lineNum">      89</span>              :   Color get primaryButtonForegroundDisable =&gt; Colors.white;</span>
<span id="L90"><span class="lineNum">      90</span>              : </span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaGNC">          10 :   @override</span></span>
<span id="L92"><span class="lineNum">      92</span>              :   Color get primaryButtonBgDisable =&gt; const Color(0xFFE9E9E9);</span>
<span id="L93"><span class="lineNum">      93</span>              : </span>
<span id="L94"><span class="lineNum">      94</span>              :   // secondary button</span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L96"><span class="lineNum">      96</span>              :   Color get secondaryButtonForeground =&gt; const Color(0xFF09B364);</span>
<span id="L97"><span class="lineNum">      97</span>              : </span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L99"><span class="lineNum">      99</span>              :   Color get secondaryButtonBg =&gt; const Color(0xFFECF9F3);</span>
<span id="L100"><span class="lineNum">     100</span>              : </span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L102"><span class="lineNum">     102</span>              :   Color get secondaryButtonForegroundDisable =&gt; const Color(0xFFD1D1D1);</span>
<span id="L103"><span class="lineNum">     103</span>              : </span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L105"><span class="lineNum">     105</span>              :   Color get secondaryButtonBgDisable =&gt; const Color(0xFFE9E9E9);</span>
<span id="L106"><span class="lineNum">     106</span>              : </span>
<span id="L107"><span class="lineNum">     107</span>              :   // accent button</span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L109"><span class="lineNum">     109</span>              :   Color get accentButtonForeground =&gt; Colors.white;</span>
<span id="L110"><span class="lineNum">     110</span>              : </span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L112"><span class="lineNum">     112</span>              :   Color get accentButtonBg =&gt; const Color(0xFF09B364);</span>
<span id="L113"><span class="lineNum">     113</span>              : </span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L115"><span class="lineNum">     115</span>              :   Color get accentButtonForegroundDisable =&gt; Colors.white;</span>
<span id="L116"><span class="lineNum">     116</span>              : </span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L118"><span class="lineNum">     118</span>              :   Color get accentButtonBgDisable =&gt; const Color(0xFFD5F6E7);</span>
<span id="L119"><span class="lineNum">     119</span>              : </span>
<span id="L120"><span class="lineNum">     120</span>              :   // tertiary button</span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaGNC">           9 :   @override</span></span>
<span id="L122"><span class="lineNum">     122</span>              :   Color get tertiaryButtonForeground =&gt; const Color(0xFF1D1D1D);</span>
<span id="L123"><span class="lineNum">     123</span>              : </span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaGNC">           6 :   @override</span></span>
<span id="L125"><span class="lineNum">     125</span>              :   Color get tertiaryButtonBg =&gt; const Color(0xFFFFFFFF);</span>
<span id="L126"><span class="lineNum">     126</span>              : </span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaGNC">           5 :   @override</span></span>
<span id="L128"><span class="lineNum">     128</span>              :   Color get tertiaryButtonForegroundDisable =&gt; const Color(0xFFD1D1D1);</span>
<span id="L129"><span class="lineNum">     129</span>              : </span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaGNC">           3 :   @override</span></span>
<span id="L131"><span class="lineNum">     131</span>              :   Color get tertiaryButtonBgDisable =&gt; const Color(0xFFFFFFFF);</span>
<span id="L132"><span class="lineNum">     132</span>              : </span>
<span id="L133"><span class="lineNum">     133</span>              :   // negative button</span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L135"><span class="lineNum">     135</span>              :   Color get negativeButtonForeground =&gt; const Color(0xFFFFFFFF);</span>
<span id="L136"><span class="lineNum">     136</span>              : </span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L138"><span class="lineNum">     138</span>              :   Color get negativeButtonBg =&gt; const Color(0xFFE73F3C);</span>
<span id="L139"><span class="lineNum">     139</span>              : </span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L141"><span class="lineNum">     141</span>              :   Color get negativeButtonBgDisable =&gt; const Color(0xFFD1D1D1);</span>
<span id="L142"><span class="lineNum">     142</span>              : </span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L144"><span class="lineNum">     144</span>              :   Color get negativeButtonForegroundDisable =&gt; const Color(0xFFFFFFFF);</span>
<span id="L145"><span class="lineNum">     145</span>              : </span>
<span id="L146"><span class="lineNum">     146</span>              :   // endregion</span>
<span id="L147"><span class="lineNum">     147</span>              : </span>
<span id="L148"><span class="lineNum">     148</span>              :   // otp text input field</span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaGNC">           6 :   @override</span></span>
<span id="L150"><span class="lineNum">     150</span>              :   Color get inputFocusedColor =&gt; const Color(0xFF09B364);</span>
<span id="L151"><span class="lineNum">     151</span>              : </span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaGNC">           6 :   @override</span></span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaGNC">           6 :   Color get inputUnfocusedColor =&gt; textHint;</span></span>
<span id="L154"><span class="lineNum">     154</span>              : </span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L156"><span class="lineNum">     156</span>              :   Color get selectedRadioButton =&gt; const Color(0xFF60A5FA);</span>
<span id="L157"><span class="lineNum">     157</span>              : </span>
<span id="L158"><span class="lineNum">     158</span>              :   // WebView loading Progress Bg;</span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaGNC">           6 :   @override</span></span>
<span id="L160"><span class="lineNum">     160</span>              :   Color get webViewProgressBg =&gt; const Color(0xFFC2C2C2);</span>
<span id="L161"><span class="lineNum">     161</span>              : </span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaGNC">           4 :   @override</span></span>
<span id="L163"><span class="lineNum">     163</span>              :   Color get webViewProgressValue =&gt; const Color(0xFF4AC58D);</span>
<span id="L164"><span class="lineNum">     164</span>              : </span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L166"><span class="lineNum">     166</span>              :   Color get iconColor =&gt; const Color(0xFFE52722);</span>
<span id="L167"><span class="lineNum">     167</span>              : </span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L169"><span class="lineNum">     169</span> <span class="tlaGNC">           2 :   Color get loadingViewColor =&gt; primary;</span></span>
<span id="L170"><span class="lineNum">     170</span>              : </span>
<span id="L171"><span class="lineNum">     171</span>              :   /// EVO checkbox</span>
<span id="L172"><span class="lineNum">     172</span> <span class="tlaGNC">           5 :   Color get checkBoxChecked =&gt; const Color(0xFF1F71F4);</span></span>
<span id="L173"><span class="lineNum">     173</span>              : </span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaGNC">           2 :   Color get checkBoxUnChecked =&gt; const Color(0xFFFFFFFF);</span></span>
<span id="L175"><span class="lineNum">     175</span>              : </span>
<span id="L176"><span class="lineNum">     176</span> <span class="tlaGNC">           2 :   Color get checkBoxBorderUnChecked =&gt; const Color(0xFFD1D1D1);</span></span>
<span id="L177"><span class="lineNum">     177</span>              : </span>
<span id="L178"><span class="lineNum">     178</span> <span class="tlaGNC">           5 :   Color get checkBoxBorderChecked =&gt; const Color(0xFF1F71F4);</span></span>
<span id="L179"><span class="lineNum">     179</span>              : </span>
<span id="L180"><span class="lineNum">     180</span>              :   /// Define Evo's color</span>
<span id="L181"><span class="lineNum">     181</span> <span class="tlaGNC">           3 :   Color get secondaryBackground =&gt; const Color(0xFFFAFAFA);</span></span>
<span id="L182"><span class="lineNum">     182</span>              : </span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaGNC">           2 :   Color get profileContainerBackground =&gt; const Color(0xFFECF9F3);</span></span>
<span id="L184"><span class="lineNum">     184</span>              : </span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaGNC">           1 :   Color get activeStepBackground =&gt; const Color(0xFFD5F6E7);</span></span>
<span id="L186"><span class="lineNum">     186</span>              : </span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaGNC">           1 :   Color get activeStepIcon =&gt; const Color(0xFF01AA4F);</span></span>
<span id="L188"><span class="lineNum">     188</span>              : </span>
<span id="L189"><span class="lineNum">     189</span> <span class="tlaGNC">           1 :   Color get inActiveStepIcon =&gt; const Color(0xFFC2C2C2);</span></span>
<span id="L190"><span class="lineNum">     190</span>              : </span>
<span id="L191"><span class="lineNum">     191</span> <span class="tlaGNC">           4 :   Color get snackBarSuccessBackground =&gt; profileContainerBackground;</span></span>
<span id="L192"><span class="lineNum">     192</span>              : </span>
<span id="L193"><span class="lineNum">     193</span> <span class="tlaGNC">           2 :   Color get snackBarSuccessBorder =&gt; const Color(0xFFD7F4E5);</span></span>
<span id="L194"><span class="lineNum">     194</span>              : </span>
<span id="L195"><span class="lineNum">     195</span> <span class="tlaGNC">           2 :   Color get inactiveSwitchBackground =&gt; disableTextFieldBorder;</span></span>
<span id="L196"><span class="lineNum">     196</span>              : </span>
<span id="L197"><span class="lineNum">     197</span> <span class="tlaGNC">           2 :   Color get activeSwitchBackground =&gt; primary;</span></span>
<span id="L198"><span class="lineNum">     198</span>              : </span>
<span id="L199"><span class="lineNum">     199</span> <span class="tlaGNC">           2 :   Color get snackBarErrorBackground =&gt; const Color(0xFFFFE5DC);</span></span>
<span id="L200"><span class="lineNum">     200</span>              : </span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaGNC">           2 :   Color get snackBarErrorBorder =&gt; const Color(0xFFFFC6B2);</span></span>
<span id="L202"><span class="lineNum">     202</span>              : </span>
<span id="L203"><span class="lineNum">     203</span>              :   // Promotion</span>
<span id="L204"><span class="lineNum">     204</span> <span class="tlaGNC">           2 :   Color get voucherBeforeRunningOutTime =&gt; const Color(0xFF09B364);</span></span>
<span id="L205"><span class="lineNum">     205</span>              : </span>
<span id="L206"><span class="lineNum">     206</span> <span class="tlaGNC">           2 :   Color get campaignBeforeRunningOutTime =&gt; const Color(0xFFF5A70B);</span></span>
<span id="L207"><span class="lineNum">     207</span>              : </span>
<span id="L208"><span class="lineNum">     208</span> <span class="tlaGNC">           3 :   Color get promotionRunningOutTime =&gt; const Color(0xFFF5A70B);</span></span>
<span id="L209"><span class="lineNum">     209</span>              : </span>
<span id="L210"><span class="lineNum">     210</span> <span class="tlaGNC">           3 :   Color get promotionNotRunningOutTime =&gt; const Color(0xFF999999);</span></span>
<span id="L211"><span class="lineNum">     211</span>              : </span>
<span id="L212"><span class="lineNum">     212</span> <span class="tlaGNC">           1 :   Color get voucherBackgroundBorder =&gt; const Color(0xFFD1D1D1);</span></span>
<span id="L213"><span class="lineNum">     213</span>              : </span>
<span id="L214"><span class="lineNum">     214</span> <span class="tlaGNC">           5 :   Color get transactionPending =&gt; const Color(0xFFF5A70B);</span></span>
<span id="L215"><span class="lineNum">     215</span>              : </span>
<span id="L216"><span class="lineNum">     216</span> <span class="tlaGNC">           2 :   Color get snackBarWarningBackground =&gt; const Color(0xFFFEF3DC);</span></span>
<span id="L217"><span class="lineNum">     217</span>              : </span>
<span id="L218"><span class="lineNum">     218</span> <span class="tlaGNC">           2 :   Color get snackBarWarningBorder =&gt; const Color(0xFFFEE7B9);</span></span>
<span id="L219"><span class="lineNum">     219</span>              : </span>
<span id="L220"><span class="lineNum">     220</span> <span class="tlaGNC">           2 :   Color get snackBarNeutralBackground =&gt; const Color(0xFFEBF3FF);</span></span>
<span id="L221"><span class="lineNum">     221</span>              : </span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaGNC">           2 :   Color get snackBarNeutralBorder =&gt; const Color(0xFFBFDBFE);</span></span>
<span id="L223"><span class="lineNum">     223</span>              : </span>
<span id="L224"><span class="lineNum">     224</span> <span class="tlaGNC">           2 :   Color get snackBarDefaultBackground =&gt; background;</span></span>
<span id="L225"><span class="lineNum">     225</span>              : </span>
<span id="L226"><span class="lineNum">     226</span> <span class="tlaGNC">           2 :   Color get snackBarDefaultBorder =&gt; foreground;</span></span>
<span id="L227"><span class="lineNum">     227</span>              : </span>
<span id="L228"><span class="lineNum">     228</span> <span class="tlaGNC">           1 :   Color get promotionIsUsed =&gt; const Color(0xFFC2C2C2);</span></span>
<span id="L229"><span class="lineNum">     229</span>              : </span>
<span id="L230"><span class="lineNum">     230</span> <span class="tlaGNC">           5 :   Color get promotionTimeout =&gt; const Color(0xFF5E5E5E);</span></span>
<span id="L231"><span class="lineNum">     231</span>              : </span>
<span id="L232"><span class="lineNum">     232</span> <span class="tlaGNC">           3 :   Color get promotionHotTime =&gt; const Color(0xFFE54D2E);</span></span>
<span id="L233"><span class="lineNum">     233</span>              : </span>
<span id="L234"><span class="lineNum">     234</span> <span class="tlaGNC">           4 :   Color get promotionText =&gt; const Color(0xFFFFFFFF);</span></span>
<span id="L235"><span class="lineNum">     235</span>              : </span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaGNC">           3 :   Color get campaignIsComing =&gt; const Color(0xFF09B364);</span></span>
<span id="L237"><span class="lineNum">     237</span>              : </span>
<span id="L238"><span class="lineNum">     238</span> <span class="tlaGNC">           1 :   Color get linkCardMessage =&gt; const Color(0xFFF5A70B);</span></span>
<span id="L239"><span class="lineNum">     239</span>              : </span>
<span id="L240"><span class="lineNum">     240</span> <span class="tlaGNC">           1 :   Color get textActiveReminder =&gt; Colors.black;</span></span>
<span id="L241"><span class="lineNum">     241</span>              : </span>
<span id="L242"><span class="lineNum">     242</span> <span class="tlaGNC">           2 :   Color get paymentInfoCardShadow =&gt; Colors.black.withOpacity(0.04);</span></span>
<span id="L243"><span class="lineNum">     243</span>              : </span>
<span id="L244"><span class="lineNum">     244</span> <span class="tlaGNC">           3 :   Color get paymentPromotionTitle =&gt; const Color(0xFF0F0F0F);</span></span>
<span id="L245"><span class="lineNum">     245</span>              : </span>
<span id="L246"><span class="lineNum">     246</span> <span class="tlaGNC">           2 :   Color get paymentPromotionCardItemShadow =&gt; const Color(0xFF000008);</span></span>
<span id="L247"><span class="lineNum">     247</span>              : </span>
<span id="L248"><span class="lineNum">     248</span> <span class="tlaGNC">           1 :   Color get promotionItemUnqualifiedBorder =&gt; const Color(0xFFFBC760);</span></span>
<span id="L249"><span class="lineNum">     249</span>              : </span>
<span id="L250"><span class="lineNum">     250</span> <span class="tlaGNC">           3 :   Color get promotionItemUnqualifiedText =&gt; const Color(0xFFF5A70B);</span></span>
<span id="L251"><span class="lineNum">     251</span>              : </span>
<span id="L252"><span class="lineNum">     252</span>              :   // Referral</span>
<span id="L253"><span class="lineNum">     253</span> <span class="tlaGNC">           1 :   Color get referralNewMemberText =&gt; const Color(0xFFFFFFFF);</span></span>
<span id="L254"><span class="lineNum">     254</span>              : </span>
<span id="L255"><span class="lineNum">     255</span>              :   // Transaction history</span>
<span id="L256"><span class="lineNum">     256</span> <span class="tlaGNC">           4 :   Color get transactionHistoryProcessing =&gt; const Color(0xFFF5A70B);</span></span>
<span id="L257"><span class="lineNum">     257</span>              : </span>
<span id="L258"><span class="lineNum">     258</span> <span class="tlaGNC">           3 :   Color get transactionHistorySuccess =&gt; const Color(0xFF01AA4F);</span></span>
<span id="L259"><span class="lineNum">     259</span>              : </span>
<span id="L260"><span class="lineNum">     260</span> <span class="tlaGNC">           3 :   Color get transactionHistoryFailure =&gt; const Color(0xFFE54D2E);</span></span>
<span id="L261"><span class="lineNum">     261</span>              : </span>
<span id="L262"><span class="lineNum">     262</span> <span class="tlaGNC">           4 :   Color get transactionListHistoryEmiLabel =&gt; const Color(0xFF1F71F4);</span></span>
<span id="L263"><span class="lineNum">     263</span>              : </span>
<span id="L264"><span class="lineNum">     264</span> <span class="tlaGNC">           3 :   Color get transactionListHistoryEmiNewLabel =&gt; const Color(0xFF0D6D40);</span></span>
<span id="L265"><span class="lineNum">     265</span>              : </span>
<span id="L266"><span class="lineNum">     266</span> <span class="tlaGNC">           5 :   Color get transactionHistoryBgProcessing =&gt; const Color(0xFFFEF3DC);</span></span>
<span id="L267"><span class="lineNum">     267</span>              : </span>
<span id="L268"><span class="lineNum">     268</span> <span class="tlaGNC">           4 :   Color get transactionHistoryBgSuccess =&gt; const Color(0xFFECF9F3);</span></span>
<span id="L269"><span class="lineNum">     269</span>              : </span>
<span id="L270"><span class="lineNum">     270</span> <span class="tlaGNC">           4 :   Color get transactionHistoryBgFailure =&gt; const Color(0xFFFFE5DC);</span></span>
<span id="L271"><span class="lineNum">     271</span>              : </span>
<span id="L272"><span class="lineNum">     272</span> <span class="tlaGNC">           4 :   Color get transactionListHistoryBgEmiLabel =&gt; const Color(0xFFEBF3FF);</span></span>
<span id="L273"><span class="lineNum">     273</span>              : </span>
<span id="L274"><span class="lineNum">     274</span> <span class="tlaGNC">           3 :   Color get transactionListHistoryBgEmiNewLabel =&gt; const Color(0xFFECF9F3);</span></span>
<span id="L275"><span class="lineNum">     275</span>              : </span>
<span id="L276"><span class="lineNum">     276</span> <span class="tlaGNC">          18 :   Color get placeholderBorder =&gt; disableTextFieldBorder;</span></span>
<span id="L277"><span class="lineNum">     277</span>              : </span>
<span id="L278"><span class="lineNum">     278</span> <span class="tlaGNC">           3 :   Color get transactionHistoryEmiStatusReceived =&gt; const Color(0xFF999999);</span></span>
<span id="L279"><span class="lineNum">     279</span>              : </span>
<span id="L280"><span class="lineNum">     280</span> <span class="tlaGNC">           2 :   Color get transactionHistoryEmiStatusApproved =&gt; const Color(0xFF01AA4F);</span></span>
<span id="L281"><span class="lineNum">     281</span>              : </span>
<span id="L282"><span class="lineNum">     282</span> <span class="tlaGNC">           2 :   Color get transactionHistoryEmiStatusDone =&gt; const Color(0xFF01AA4F);</span></span>
<span id="L283"><span class="lineNum">     283</span>              : </span>
<span id="L284"><span class="lineNum">     284</span> <span class="tlaGNC">           2 :   Color get transactionHistoryEmiStatusRejected =&gt; const Color(0xFFE54D2E);</span></span>
<span id="L285"><span class="lineNum">     285</span>              : </span>
<span id="L286"><span class="lineNum">     286</span> <span class="tlaGNC">           2 :   Color get transactionHistoryEmiStatusUnknown =&gt; const Color(0xFF999999);</span></span>
<span id="L287"><span class="lineNum">     287</span>              : </span>
<span id="L288"><span class="lineNum">     288</span> <span class="tlaGNC">           3 :   Color get transactionHistoryEmiTitleReceived =&gt; const Color(0xFF5E5E5E);</span></span>
<span id="L289"><span class="lineNum">     289</span>              : </span>
<span id="L290"><span class="lineNum">     290</span> <span class="tlaGNC">           2 :   Color get transactionHistoryEmiTitleApproved =&gt; const Color(0xFF01AA4F);</span></span>
<span id="L291"><span class="lineNum">     291</span>              : </span>
<span id="L292"><span class="lineNum">     292</span> <span class="tlaGNC">           2 :   Color get transactionHistoryEmiTitleDone =&gt; const Color(0xFF01AA4F);</span></span>
<span id="L293"><span class="lineNum">     293</span>              : </span>
<span id="L294"><span class="lineNum">     294</span> <span class="tlaGNC">           2 :   Color get transactionHistoryEmiTitleRejected =&gt; const Color(0xFFE54D2E);</span></span>
<span id="L295"><span class="lineNum">     295</span>              : </span>
<span id="L296"><span class="lineNum">     296</span> <span class="tlaGNC">           2 :   Color get transactionHistoryEmiTitleUnknown =&gt; const Color(0xFF5E5E5E);</span></span>
<span id="L297"><span class="lineNum">     297</span>              : </span>
<span id="L298"><span class="lineNum">     298</span> <span class="tlaGNC">           3 :   Color get transactionHistoryCashbackBackground =&gt; Colors.white;</span></span>
<span id="L299"><span class="lineNum">     299</span>              : </span>
<span id="L300"><span class="lineNum">     300</span> <span class="tlaGNC">           3 :   Color get transactionHistoryTotalAmountProgress =&gt; const Color(0xFFE9E9E9);</span></span>
<span id="L301"><span class="lineNum">     301</span>              : </span>
<span id="L302"><span class="lineNum">     302</span> <span class="tlaGNC">           3 :   Color get transactionHistoryPaidAmount =&gt; const Color(0xFF09B364);</span></span>
<span id="L303"><span class="lineNum">     303</span>              : </span>
<span id="L304"><span class="lineNum">     304</span>              :   // Card linked</span>
<span id="L305"><span class="lineNum">     305</span> <span class="tlaGNC">           2 :   Color get linkedCardLimitationBackground =&gt; const Color(0xFF1D1D1D).withOpacity(0.04);</span></span>
<span id="L306"><span class="lineNum">     306</span>              : </span>
<span id="L307"><span class="lineNum">     307</span> <span class="tlaGNC">           1 :   Color get linkedCardLimitationTitle =&gt; const Color(0xFF5E5E5E);</span></span>
<span id="L308"><span class="lineNum">     308</span>              : </span>
<span id="L309"><span class="lineNum">     309</span> <span class="tlaGNC">           1 :   Color get linkedCardLimitationValue =&gt; Colors.black;</span></span>
<span id="L310"><span class="lineNum">     310</span>              : </span>
<span id="L311"><span class="lineNum">     311</span> <span class="tlaGNC">           1 :   Color get linkedCardLimitationPerTrans =&gt; const Color(0xFF999999);</span></span>
<span id="L312"><span class="lineNum">     312</span>              : </span>
<span id="L313"><span class="lineNum">     313</span> <span class="tlaGNC">           2 :   Color get linkedCardLine =&gt; const Color(0xFF1D1D1D).withOpacity(0.08);</span></span>
<span id="L314"><span class="lineNum">     314</span>              : </span>
<span id="L315"><span class="lineNum">     315</span>              :   // Intro</span>
<span id="L316"><span class="lineNum">     316</span> <span class="tlaGNC">           2 :   Color get passiveIndicator =&gt; const Color(0x331D1D1D);</span></span>
<span id="L317"><span class="lineNum">     317</span>              : </span>
<span id="L318"><span class="lineNum">     318</span> <span class="tlaGNC">           2 :   Color get activeIndicator =&gt; const Color(0xFF222222);</span></span>
<span id="L319"><span class="lineNum">     319</span>              : </span>
<span id="L320"><span class="lineNum">     320</span>              :   // Policy</span>
<span id="L321"><span class="lineNum">     321</span> <span class="tlaGNC">           1 :   Color get activePrivatePolicy =&gt; const Color(0xFF1F71F4);</span></span>
<span id="L322"><span class="lineNum">     322</span>              : </span>
<span id="L323"><span class="lineNum">     323</span>              :   // Feed back</span>
<span id="L324"><span class="lineNum">     324</span> <span class="tlaGNC">           2 :   Color get feedBackEmail =&gt; const Color(0xFF1C1C1C);</span></span>
<span id="L325"><span class="lineNum">     325</span>              : </span>
<span id="L326"><span class="lineNum">     326</span>              :   // Card status</span>
<span id="L327"><span class="lineNum">     327</span> <span class="tlaGNC">           3 :   Color get cardStatusRemainingSteps =&gt; const Color(0xFFF5A70B);</span></span>
<span id="L328"><span class="lineNum">     328</span>              : </span>
<span id="L329"><span class="lineNum">     329</span> <span class="tlaGNC">           3 :   Color get creditLimitAwaitingForApproval =&gt; const Color(0xFF999999);</span></span>
<span id="L330"><span class="lineNum">     330</span>              : </span>
<span id="L331"><span class="lineNum">     331</span> <span class="tlaGNC">           2 :   Color get creditLimitNotReadyForPayment =&gt; const Color(0xFF1D1D1D);</span></span>
<span id="L332"><span class="lineNum">     332</span>              : </span>
<span id="L333"><span class="lineNum">     333</span> <span class="tlaGNC">           2 :   Color get creditLimitReadyForPaymentOrOutOfSync =&gt; const Color(0xFF09B364);</span></span>
<span id="L334"><span class="lineNum">     334</span>              : </span>
<span id="L335"><span class="lineNum">     335</span> <span class="tlaGNC">           2 :   Color get creditLimitIconApprovedOrOutOfSync =&gt; const Color(0xFF1D1D1D);</span></span>
<span id="L336"><span class="lineNum">     336</span>              : </span>
<span id="L337"><span class="lineNum">     337</span> <span class="tlaGNC">           3 :   Color get creditLimitIconAwaitingForApproval =&gt; const Color(0xFF999999);</span></span>
<span id="L338"><span class="lineNum">     338</span>              : </span>
<span id="L339"><span class="lineNum">     339</span> <span class="tlaGNC">           3 :   Color get cardStatusTitleNotReadyForPayment =&gt; const Color(0xFF999999);</span></span>
<span id="L340"><span class="lineNum">     340</span>              : </span>
<span id="L341"><span class="lineNum">     341</span> <span class="tlaGNC">           2 :   Color get cardStatusTitleReadyForPaymentOrOutOfSync =&gt; const Color(0xFF09B364);</span></span>
<span id="L342"><span class="lineNum">     342</span>              : </span>
<span id="L343"><span class="lineNum">     343</span>              :   // profile settings</span>
<span id="L344"><span class="lineNum">     344</span> <span class="tlaGNC">           1 :   Color get settingsCardShadow =&gt; const Color(0x0A000000);</span></span>
<span id="L345"><span class="lineNum">     345</span>              : </span>
<span id="L346"><span class="lineNum">     346</span>              :   // delete account</span>
<span id="L347"><span class="lineNum">     347</span> <span class="tlaGNC">           1 :   Color get deleteAccountSuccessTitle =&gt; const Color(0xFF0D121C);</span></span>
<span id="L348"><span class="lineNum">     348</span>              : </span>
<span id="L349"><span class="lineNum">     349</span> <span class="tlaGNC">           3 :   Color get surveyItemBackground =&gt; const Color(0x0A1D1D1D);</span></span>
<span id="L350"><span class="lineNum">     350</span>              : </span>
<span id="L351"><span class="lineNum">     351</span>              :   // referral QR code</span>
<span id="L352"><span class="lineNum">     352</span> <span class="tlaGNC">           1 :   Color get referralQrCodeBackground =&gt; const Color(0xFF0D6C40);</span></span>
<span id="L353"><span class="lineNum">     353</span>              : </span>
<span id="L354"><span class="lineNum">     354</span> <span class="tlaGNC">           1 :   Color get referralQrCodeWhiteText =&gt; const Color(0xFFFFFFFF);</span></span>
<span id="L355"><span class="lineNum">     355</span>              : </span>
<span id="L356"><span class="lineNum">     356</span>              :   // Payment V2</span>
<span id="L357"><span class="lineNum">     357</span> <span class="tlaGNC">           3 :   Color get paymentInputAmountV2TextHint =&gt; const Color(0xFFC2C2C2);</span></span>
<span id="L358"><span class="lineNum">     358</span>              : </span>
<span id="L359"><span class="lineNum">     359</span> <span class="tlaGNC">           7 :   Color get paymentManualLinkCard =&gt; const Color(0xFFF5A70B);</span></span>
<span id="L360"><span class="lineNum">     360</span>              : </span>
<span id="L361"><span class="lineNum">     361</span> <span class="tlaGNC">           2 :   Color get pendingTransactionNoteLeftBorder =&gt; const Color(0xFF1F71F4);</span></span>
<span id="L362"><span class="lineNum">     362</span>              : </span>
<span id="L363"><span class="lineNum">     363</span>              :   //EMI</span>
<span id="L364"><span class="lineNum">     364</span> <span class="tlaGNC">          33 :   Color get emiTenorBackground =&gt; const Color(0xFF1C1C1C);</span></span>
<span id="L365"><span class="lineNum">     365</span>              : </span>
<span id="L366"><span class="lineNum">     366</span> <span class="tlaGNC">           2 :   Color get emiContainerBackground =&gt; const Color(0xFFECF9F3);</span></span>
<span id="L367"><span class="lineNum">     367</span>              : </span>
<span id="L368"><span class="lineNum">     368</span> <span class="tlaGNC">           2 :   Color get paymentResultEmiNoteErrorShadow =&gt; Colors.black;</span></span>
<span id="L369"><span class="lineNum">     369</span>              : </span>
<span id="L370"><span class="lineNum">     370</span> <span class="tlaGNC">           4 :   Color get paymentResultEmiMoreDetailBackground =&gt; const Color(0xFF1D1D1D).withOpacity(0.08);</span></span>
<span id="L371"><span class="lineNum">     371</span>              : </span>
<span id="L372"><span class="lineNum">     372</span> <span class="tlaGNC">          14 :   Color get emiRegularText =&gt; emiTenorBackground;</span></span>
<span id="L373"><span class="lineNum">     373</span>              : </span>
<span id="L374"><span class="lineNum">     374</span> <span class="tlaGNC">           2 :   Color get emiTooltipsBackground =&gt; emiTenorBackground;</span></span>
<span id="L375"><span class="lineNum">     375</span>              : </span>
<span id="L376"><span class="lineNum">     376</span> <span class="tlaGNC">           1 :   Color get emiVoucherSelectedBorder =&gt; const Color(0xFF09B364);</span></span>
<span id="L377"><span class="lineNum">     377</span>              : </span>
<span id="L378"><span class="lineNum">     378</span> <span class="tlaGNC">           4 :   Color get emiVoucherSelectedBackground =&gt; const Color(0xFFECF9F3);</span></span>
<span id="L379"><span class="lineNum">     379</span>              : </span>
<span id="L380"><span class="lineNum">     380</span> <span class="tlaGNC">           2 :   Color get emiInvalidVoucherSelectedBorder =&gt; const Color(0xFFF5A70B);</span></span>
<span id="L381"><span class="lineNum">     381</span>              : </span>
<span id="L382"><span class="lineNum">     382</span> <span class="tlaGNC">           2 :   Color get mwgEmiInvalidVoucherSelectedBackground =&gt; const Color(0xFFFEF3DC);</span></span>
<span id="L383"><span class="lineNum">     383</span>              : </span>
<span id="L384"><span class="lineNum">     384</span> <span class="tlaGNC">           2 :   Color get mwgEmiInvalidVoucherSelectedBorder =&gt; const Color(0xFFF5A70B);</span></span>
<span id="L385"><span class="lineNum">     385</span>              : </span>
<span id="L386"><span class="lineNum">     386</span> <span class="tlaGNC">           1 :   Color get emiTransactionPaymentSuccess =&gt; const Color(0xFF1F71F4);</span></span>
<span id="L387"><span class="lineNum">     387</span>              : </span>
<span id="L388"><span class="lineNum">     388</span>              :   // Revamp Home Page for non-user</span>
<span id="L389"><span class="lineNum">     389</span> <span class="tlaGNC">           4 :   Color get storyProgressBarBackground =&gt; Colors.white.withOpacity(0.3);</span></span>
<span id="L390"><span class="lineNum">     390</span>              : </span>
<span id="L391"><span class="lineNum">     391</span> <span class="tlaGNC">           4 :   Color get storyTitleColor =&gt; const Color(0xFF010F1D).withOpacity(0.7);</span></span>
<span id="L392"><span class="lineNum">     392</span>              : </span>
<span id="L393"><span class="lineNum">     393</span> <span class="tlaGNC">           2 :   Color get storyViewDetailColor =&gt; const Color(0xFF000000);</span></span>
<span id="L394"><span class="lineNum">     394</span>              : </span>
<span id="L395"><span class="lineNum">     395</span> <span class="tlaGNC">           3 :   Color get storyViewFooterBackgroundColor =&gt; const Color(0xFFFFFFFF);</span></span>
<span id="L396"><span class="lineNum">     396</span>              : </span>
<span id="L397"><span class="lineNum">     397</span> <span class="tlaGNC">           6 :   Color get storyViewFooterShadowColor =&gt; const Color(0xFF000000).withOpacity(0.08);</span></span>
<span id="L398"><span class="lineNum">     398</span>              : </span>
<span id="L399"><span class="lineNum">     399</span> <span class="tlaGNC">           3 :   Color get storyViewFooterTitleColor =&gt; const Color(0xFF5E5E5E);</span></span>
<span id="L400"><span class="lineNum">     400</span>              : </span>
<span id="L401"><span class="lineNum">     401</span>              :   // Pos limit enable guide</span>
<span id="L402"><span class="lineNum">     402</span> <span class="tlaGNC">           2 :   Color get posLimitIndicatorTextColor =&gt; const Color(0xFFFFFFFF);</span></span>
<span id="L403"><span class="lineNum">     403</span>              : </span>
<span id="L404"><span class="lineNum">     404</span> <span class="tlaGNC">           2 :   Color get posLimitIndicatorEnable =&gt; const Color(0xFFFFFFFF);</span></span>
<span id="L405"><span class="lineNum">     405</span>              : </span>
<span id="L406"><span class="lineNum">     406</span> <span class="tlaGNC">           2 :   Color get posLimitIndicatorDisable =&gt; const Color(0xFF5E5E5E);</span></span>
<span id="L407"><span class="lineNum">     407</span>              : </span>
<span id="L408"><span class="lineNum">     408</span> <span class="tlaGNC">           2 :   Color get posLimitIndicatorBackground =&gt; const Color(0xFF000000);</span></span>
<span id="L409"><span class="lineNum">     409</span>              : </span>
<span id="L410"><span class="lineNum">     410</span>              :   // Remind POS limit enable guide</span>
<span id="L411"><span class="lineNum">     411</span> <span class="tlaGNC">           5 :   Color get remindPosLimitTitle =&gt; const Color(0xFF000000);</span></span>
<span id="L412"><span class="lineNum">     412</span>              : </span>
<span id="L413"><span class="lineNum">     413</span> <span class="tlaGNC">           5 :   Color get remindPosLimitDescription =&gt; const Color(0xFF5E5E5E);</span></span>
<span id="L414"><span class="lineNum">     414</span>              : </span>
<span id="L415"><span class="lineNum">     415</span> <span class="tlaGNC">           4 :   Color get remindPosLimitIndicator =&gt; const Color(0xFF000000);</span></span>
<span id="L416"><span class="lineNum">     416</span>              : </span>
<span id="L417"><span class="lineNum">     417</span> <span class="tlaGNC">           4 :   Color get remindPosLimitIndicatorBackground =&gt; const Color(0xFFFFFFFF);</span></span>
<span id="L418"><span class="lineNum">     418</span>              : </span>
<span id="L419"><span class="lineNum">     419</span> <span class="tlaGNC">           4 :   Color get remindPosLimitIndicatorBorder =&gt; const Color(0xFF000000);</span></span>
<span id="L420"><span class="lineNum">     420</span>              : </span>
<span id="L421"><span class="lineNum">     421</span> <span class="tlaGNC">           8 :   Color get remindPosLimitIndicatorShadow =&gt; const Color(0xFFFFFFFF).withOpacity(0.08);</span></span>
<span id="L422"><span class="lineNum">     422</span>              : </span>
<span id="L423"><span class="lineNum">     423</span> <span class="tlaGNC">           4 :   Color get remindPosLimitIndicatorText =&gt; const Color(0xFF000000);</span></span>
<span id="L424"><span class="lineNum">     424</span>              : </span>
<span id="L425"><span class="lineNum">     425</span>              :   /// EMI Management</span>
<span id="L426"><span class="lineNum">     426</span> <span class="tlaGNC">           3 :   Color get emiManagementApproved =&gt; const Color(0xFF09B364);</span></span>
<span id="L427"><span class="lineNum">     427</span>              : </span>
<span id="L428"><span class="lineNum">     428</span> <span class="tlaGNC">           2 :   Color get emiManagementRejected =&gt; const Color(0xFFE54D2E);</span></span>
<span id="L429"><span class="lineNum">     429</span>              : </span>
<span id="L430"><span class="lineNum">     430</span> <span class="tlaGNC">           2 :   Color get emiManagementProcessing =&gt; const Color(0xFF999999);</span></span>
<span id="L431"><span class="lineNum">     431</span>              : </span>
<span id="L432"><span class="lineNum">     432</span> <span class="tlaGNC">           2 :   Color get emiManagementBottomBorder =&gt; const Color(0xFFE9E9E9);</span></span>
<span id="L433"><span class="lineNum">     433</span>              : </span>
<span id="L434"><span class="lineNum">     434</span> <span class="tlaGNC">           4 :   Color get emiManagementDefaultItemBackground =&gt; Colors.white;</span></span>
<span id="L435"><span class="lineNum">     435</span>              : </span>
<span id="L436"><span class="lineNum">     436</span> <span class="tlaGNC">           3 :   Color get emiManagementAmountItem =&gt; const Color(0xFF0F0F0F);</span></span>
<span id="L437"><span class="lineNum">     437</span>              : </span>
<span id="L438"><span class="lineNum">     438</span> <span class="tlaGNC">           2 :   Color get emiManagementPaidAmount =&gt; const Color(0xFF09B364);</span></span>
<span id="L439"><span class="lineNum">     439</span>              : </span>
<span id="L440"><span class="lineNum">     440</span> <span class="tlaGNC">           2 :   Color get emiManagementTotalAmountProgress =&gt; const Color(0xFFE9E9E9);</span></span>
<span id="L441"><span class="lineNum">     441</span>              : </span>
<span id="L442"><span class="lineNum">     442</span>              :   // Active POS Limit</span>
<span id="L443"><span class="lineNum">     443</span> <span class="tlaGNC">           6 :   Color get activatedCardGuideColor =&gt; const Color(0xFF1F71F4);</span></span>
<span id="L444"><span class="lineNum">     444</span>              : </span>
<span id="L445"><span class="lineNum">     445</span>              :   // setup POS limit</span>
<span id="L446"><span class="lineNum">     446</span> <span class="tlaGNC">           3 :   Color get itemPOSLimitSuggestionBg =&gt; const Color(0xFFE9E9E9);</span></span>
<span id="L447"><span class="lineNum">     447</span>              : </span>
<span id="L448"><span class="lineNum">     448</span> <span class="tlaGNC">           2 :   Color get posLimitHintTextColor =&gt; const Color(0x331D1D1D);</span></span>
<span id="L449"><span class="lineNum">     449</span>              : </span>
<span id="L450"><span class="lineNum">     450</span>              :   /// Circular countdown</span>
<span id="L451"><span class="lineNum">     451</span> <span class="tlaGNC">           3 :   Color get countdownTotalColor =&gt; const Color(0xFFD1D1D1);</span></span>
<span id="L452"><span class="lineNum">     452</span>              : </span>
<span id="L453"><span class="lineNum">     453</span> <span class="tlaGNC">           3 :   Color get countdownBackgroundGradientCenterColor =&gt; const Color(0xffEEFBF5);</span></span>
<span id="L454"><span class="lineNum">     454</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
