<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/term_and_condition/term_and_condition_overlay_widget.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/term_and_condition">lib/feature/term_and_condition</a> - term_and_condition_overlay_widget.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">95.7&nbsp;%</td>
            <td class="headerCovTableEntry">47</td>
            <td class="headerCovTableEntry">45</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:async';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter/material.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/resources/resources.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/util/network_manager.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'package:flutter_common_package/widget/widgets.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../feature/logging/evo_logging_event.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../prepare_for_app_initiation.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../resources/resources.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../util/evo_snackbar.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../util/secure_storage_helper/secure_storage_helper.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../widget/evo_overlay/evo_overlay_util_functions.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import 'widget/term_and_condition_widget.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span>              : class TermAndConditionOverlayWidget extends StatefulWidget {</span>
<span id="L18"><span class="lineNum">      18</span>              :   final void Function(String selectedContent)? handleLogClickPositiveButton;</span>
<span id="L19"><span class="lineNum">      19</span>              : </span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaGNC">           1 :   static String get title =&gt; EvoStrings.termAndConditionPopupTitle;</span></span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           2 :   static String get content =&gt; &lt;String&gt;[</span></span>
<span id="L23"><span class="lineNum">      23</span>              :         EvoStrings.termAndConditionPopupContent1,</span>
<span id="L24"><span class="lineNum">      24</span>              :         EvoStrings.termAndConditionPopupContent2,</span>
<span id="L25"><span class="lineNum">      25</span>              :         EvoStrings.termAndConditionPopupContent3,</span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC">           1 :       ].join(' ');</span></span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           3 :   const TermAndConditionOverlayWidget({</span></span>
<span id="L29"><span class="lineNum">      29</span>              :     this.handleLogClickPositiveButton,</span>
<span id="L30"><span class="lineNum">      30</span>              :     super.key,</span>
<span id="L31"><span class="lineNum">      31</span>              :   });</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           2 :   State&lt;TermAndConditionOverlayWidget&gt; createState() =&gt; _TermAndConditionOverlayWidgetState();</span></span>
<span id="L35"><span class="lineNum">      35</span>              : }</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span>              : class _TermAndConditionOverlayWidgetState extends State&lt;TermAndConditionOverlayWidget&gt; {</span>
<span id="L38"><span class="lineNum">      38</span>              :   final ValueNotifier&lt;bool&gt; _consentValueNotifier = ValueNotifier&lt;bool&gt;(true);</span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L41"><span class="lineNum">      41</span>              :   Widget build(BuildContext context) {</span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           2 :     return Container(</span></span>
<span id="L43"><span class="lineNum">      43</span>              :       margin: const EdgeInsets.symmetric(horizontal: 20),</span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           2 :       decoration: BoxDecoration(</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           2 :         borderRadius: BorderRadius.circular(20),</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           4 :         color: evoColors.background,</span></span>
<span id="L47"><span class="lineNum">      47</span>              :       ),</span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           2 :       child: SingleChildScrollView(</span></span>
<span id="L49"><span class="lineNum">      49</span>              :         padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),</span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           2 :         child: Column(</span></span>
<span id="L51"><span class="lineNum">      51</span>              :           crossAxisAlignment: CrossAxisAlignment.start,</span>
<span id="L52"><span class="lineNum">      52</span>              :           mainAxisSize: MainAxisSize.min,</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           2 :           children: &lt;Widget&gt;[</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           2 :             Text(</span></span>
<span id="L55"><span class="lineNum">      55</span>              :               EvoStrings.termAndConditionPopupTitle,</span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           4 :               style: evoTextStyles.h400(),</span></span>
<span id="L57"><span class="lineNum">      57</span>              :             ),</span>
<span id="L58"><span class="lineNum">      58</span>              :             const SizedBox(height: 10),</span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           2 :             _buildTermAndCondition(),</span></span>
<span id="L60"><span class="lineNum">      60</span>              :             const SizedBox(height: 20),</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaGNC">           2 :             _buildCTAButton(),</span></span>
<span id="L62"><span class="lineNum">      62</span>              :           ],</span>
<span id="L63"><span class="lineNum">      63</span>              :         ),</span>
<span id="L64"><span class="lineNum">      64</span>              :       ),</span>
<span id="L65"><span class="lineNum">      65</span>              :     );</span>
<span id="L66"><span class="lineNum">      66</span>              :   }</span>
<span id="L67"><span class="lineNum">      67</span>              : </span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaGNC">           2 :   Widget _buildTermAndCondition() {</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">           2 :     return ValueListenableBuilder&lt;bool&gt;(</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaGNC">           2 :       valueListenable: _consentValueNotifier,</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">           2 :       builder: (_, bool checked, __) {</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaGNC">           2 :         return TermAndConditionWidget(</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :           onTap: () {</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :             _consentValueNotifier.value = !checked;</span></span>
<span id="L75"><span class="lineNum">      75</span>              :           },</span>
<span id="L76"><span class="lineNum">      76</span>              :           consentValue: checked,</span>
<span id="L77"><span class="lineNum">      77</span>              :         );</span>
<span id="L78"><span class="lineNum">      78</span>              :       },</span>
<span id="L79"><span class="lineNum">      79</span>              :     );</span>
<span id="L80"><span class="lineNum">      80</span>              :   }</span>
<span id="L81"><span class="lineNum">      81</span>              : </span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaGNC">           2 :   Widget _buildCTAButton() {</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaGNC">           2 :     return ValueListenableBuilder&lt;bool&gt;(</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">           2 :       valueListenable: _consentValueNotifier,</span></span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaGNC">           2 :       builder: (_, bool checked, __) {</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaGNC">           2 :         return CommonButton(</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           4 :           onPressed: _consentValueNotifier.value</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           2 :               ? () async {</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">           5 :                   widget.handleLogClickPositiveButton?.call(EvoStrings.termAndConditionPopupCTA);</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           2 :                   await _onSubmitted();</span></span>
<span id="L91"><span class="lineNum">      91</span>              :                 }</span>
<span id="L92"><span class="lineNum">      92</span>              :               : null,</span>
<span id="L93"><span class="lineNum">      93</span>              :           isWrapContent: false,</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           4 :           style: evoButtonStyles.primary(ButtonSize.xLarge),</span></span>
<span id="L95"><span class="lineNum">      95</span>              :           child: const Text(EvoStrings.termAndConditionPopupCTA),</span>
<span id="L96"><span class="lineNum">      96</span>              :         );</span>
<span id="L97"><span class="lineNum">      97</span>              :       },</span>
<span id="L98"><span class="lineNum">      98</span>              :     );</span>
<span id="L99"><span class="lineNum">      99</span>              :   }</span>
<span id="L100"><span class="lineNum">     100</span>              : </span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaGNC">           2 :   Future&lt;void&gt; _onSubmitted() async {</span></span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaGNC">           4 :     final NetworkManager networkManager = getIt.get&lt;NetworkManager&gt;();</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaGNC">           2 :     if (!networkManager.hasInternet) {</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaGNC">           2 :       final EvoSnackBar snackBar = getIt.get&lt;EvoSnackBar&gt;();</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaGNC">           1 :       snackBar.show(</span></span>
<span id="L106"><span class="lineNum">     106</span>              :         CommonStrings.genericNoInternetErrorMessage,</span>
<span id="L107"><span class="lineNum">     107</span>              :         typeSnackBar: SnackBarType.error,</span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaGNC">           1 :         durationInSec: SnackBarDuration.short.value,</span></span>
<span id="L109"><span class="lineNum">     109</span>              :       );</span>
<span id="L110"><span class="lineNum">     110</span>              :       return;</span>
<span id="L111"><span class="lineNum">     111</span>              :     }</span>
<span id="L112"><span class="lineNum">     112</span>              : </span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaGNC">           4 :     final EvoLocalStorageHelper secureStorageHelper = getIt.get&lt;EvoLocalStorageHelper&gt;();</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaGNC">           4 :     final LoggingRepo loggingRepo = getIt.get&lt;LoggingRepo&gt;();</span></span>
<span id="L115"><span class="lineNum">     115</span>              :     // send event to logging</span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaGNC">           4 :     unawaited(loggingRepo.logEvent(</span></span>
<span id="L117"><span class="lineNum">     117</span>              :       eventType: EvoEventType.consentAgreed,</span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaGNC">           2 :       data: &lt;String, dynamic&gt;{},</span></span>
<span id="L119"><span class="lineNum">     119</span>              :     ));</span>
<span id="L120"><span class="lineNum">     120</span>              : </span>
<span id="L121"><span class="lineNum">     121</span>              :     // update flag in secure storage</span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaGNC">           2 :     await secureStorageHelper.setConsentAgreed(true);</span></span>
<span id="L123"><span class="lineNum">     123</span>              : </span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaGNC">           4 :     evoOverlayUtilFunctions.hideTermAndConditionOverlay();</span></span>
<span id="L125"><span class="lineNum">     125</span>              :   }</span>
<span id="L126"><span class="lineNum">     126</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
