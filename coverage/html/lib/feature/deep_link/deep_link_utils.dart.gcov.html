<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/deep_link/deep_link_utils.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/deep_link">lib/feature/deep_link</a> - deep_link_utils.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">50</td>
            <td class="headerCovTableEntry">50</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/widgets.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/util/extension.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../data/response/dop_native/dop_native_application_state_entity.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import '../../prepare_for_app_initiation.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import '../appsflyer/one_link_constants.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'deep_link_constants.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import 'model/deep_link_model.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : </span>
<span id="L10"><span class="lineNum">      10</span> <span class="tlaGNC">          99 : DeepLinkUtils get deepLinkUtils =&gt; getIt.get&lt;DeepLinkUtils&gt;();</span></span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span>              : class DeepLinkUtils {</span>
<span id="L13"><span class="lineNum">      13</span> <span class="tlaGNC">           1 :   bool isEvoAppDeepLink(String? link) {</span></span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaGNC">           2 :     return link?.startsWith(DeepLinkConstants.evoAppDeepLinkPrefix) == true;</span></span>
<span id="L15"><span class="lineNum">      15</span>              :   }</span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L18"><span class="lineNum">      18</span>              :   String? extractScreenNameFromUri(Uri uri) {</span>
<span id="L19"><span class="lineNum">      19</span> <span class="tlaGNC">           1 :     final Map&lt;String, String&gt; params = uri.queryParameters;</span></span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span>              :     /// If the app is opened by OneLink, the screen name is defined in the 'screen_name' query parameter of the deep link</span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           3 :     if (params.isNotEmpty &amp;&amp; params.containsKey(EVODeeplinkKey.screenNameKey.value)) {</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           2 :       return params[EVODeeplinkKey.screenNameKey.value];</span></span>
<span id="L24"><span class="lineNum">      24</span>              :     }</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              :     /// This is backward compatible with the old deep link format</span>
<span id="L27"><span class="lineNum">      27</span>              :     /// If the app is opened by by `evoappvn://mobile/deeplinking/dop_completed`,</span>
<span id="L28"><span class="lineNum">      28</span>              :     /// the screen name is `dop_completed` which is the last segment of the path</span>
<span id="L29"><span class="lineNum">      29</span>              :     /// example:</span>
<span id="L30"><span class="lineNum">      30</span>              :     /// uri.pathSegments = ['deeplinking', 'dop_completed']</span>
<span id="L31"><span class="lineNum">      31</span>              :     /// =&gt; uri.pathSegments[1] = 'dop_completed'</span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           3 :     if (uri.pathSegments.length == 2) {</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaGNC">           2 :       return uri.pathSegments[1];</span></span>
<span id="L34"><span class="lineNum">      34</span>              :     }</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span>              :     return null;</span>
<span id="L37"><span class="lineNum">      37</span>              :   }</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           1 :   DeepLinkModel generateDeepLinkModel({required String deepLinkValue, bool? isDeferred}) {</span></span>
<span id="L40"><span class="lineNum">      40</span>              :     // Parse the URI</span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           1 :     final Uri uri = Uri.parse(deepLinkValue);</span></span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span>              :     // Retrieve the 'screen_name' &amp; data query parameter</span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           1 :     final String? screenName = extractScreenNameFromUri(uri);</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           1 :     final Map&lt;String, String&gt; params = uri.queryParameters;</span></span>
<span id="L46"><span class="lineNum">      46</span>              : </span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           2 :     commonLog('parseDeepLink: $screenName, $params');</span></span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           1 :     final DeepLinkModel model = DeepLinkModel(</span></span>
<span id="L50"><span class="lineNum">      50</span>              :       screenName: screenName,</span>
<span id="L51"><span class="lineNum">      51</span>              :       params: params,</span>
<span id="L52"><span class="lineNum">      52</span>              :       isDeferred: isDeferred,</span>
<span id="L53"><span class="lineNum">      53</span>              :     );</span>
<span id="L54"><span class="lineNum">      54</span>              : </span>
<span id="L55"><span class="lineNum">      55</span>              :     /// Save deep link shared data</span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           4 :     getIt.get&lt;AppState&gt;().deepLinkSharedData.deepLink = model;</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           4 :     getIt.get&lt;AppState&gt;().deepLinkSharedData.originalUrl = deepLinkValue;</span></span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span>              :     return model;</span>
<span id="L60"><span class="lineNum">      60</span>              :   }</span>
<span id="L61"><span class="lineNum">      61</span>              : </span>
<span id="L62"><span class="lineNum">      62</span>              :   /// Extract the evo deep link value from the URL</span>
<span id="L63"><span class="lineNum">      63</span>              :   /// there are 2 cases:</span>
<span id="L64"><span class="lineNum">      64</span>              :   /// * the URL is a AppFlyers OneLink URL,</span>
<span id="L65"><span class="lineNum">      65</span>              :   /// * the URL is a Custom URL scheme, the deep link value is the URL itself if it is an Evo App deep link</span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaGNC">           1 :   String? extractEvoDeepLinkFromUrl(String url) {</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           1 :     final String deepLink = getDeepLinkFromAppsflyerOneLink(url) ?? url;</span></span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">           1 :     return isEvoAppDeepLink(deepLink) ? deepLink : null;</span></span>
<span id="L70"><span class="lineNum">      70</span>              :   }</span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span>              :   /// Appsflyer OneLink URL is used by MKT team to track the app install from the marketing campaign.</span>
<span id="L73"><span class="lineNum">      73</span>              :   /// MKT team setup the OneLink URL with DeepLink value to open the app with the specific screen.</span>
<span id="L74"><span class="lineNum">      74</span>              :   /// to get the Deep Link, we need to extract the 'deep_link_value' query parameter</span>
<span id="L75"><span class="lineNum">      75</span>              :   /// ticket: https://trustingsocial1.atlassian.net/browse/EMA-1922</span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L77"><span class="lineNum">      77</span>              :   String? getDeepLinkFromAppsflyerOneLink(String url) {</span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaGNC">           1 :     final Uri uri = Uri.parse(url);</span></span>
<span id="L79"><span class="lineNum">      79</span>              : </span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaGNC">           3 :     return uri.queryParameters[OneLinkKey.deepLinkValue.value];</span></span>
<span id="L81"><span class="lineNum">      81</span>              :   }</span>
<span id="L82"><span class="lineNum">      82</span>              : </span>
<span id="L83"><span class="lineNum">      83</span>              :   /// Generate Deeplink from Deeplink Model</span>
<span id="L84"><span class="lineNum">      84</span>              :   ///</span>
<span id="L85"><span class="lineNum">      85</span>              :   /// This function should not use Uri parse URL,</span>
<span id="L86"><span class="lineNum">      86</span>              :   /// Because if the query params contains other website (eg. web_link=https://example.com?key1=value1 )</span>
<span id="L87"><span class="lineNum">      87</span>              :   /// When use Uri.parse(url).replace(queryParameters: queryParameters);</span>
<span id="L88"><span class="lineNum">      88</span>              :   /// Value of web_link will be normalized or make substring, that can be return the result different with encode.</span>
<span id="L89"><span class="lineNum">      89</span>              :   ///</span>
<span id="L90"><span class="lineNum">      90</span>              :   /// So we should manually encode before</span>
<span id="L91"><span class="lineNum">      91</span>              :   ///</span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaGNC">           1 :   String generateDeepLink(DeepLinkModel deepLinkModel) {</span></span>
<span id="L93"><span class="lineNum">      93</span>              :     String deeplink = DeepLinkConstants.evoAppDeepLinkPrefix;</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           1 :     final String? screenName = deepLinkModel.screenName;</span></span>
<span id="L95"><span class="lineNum">      95</span>              : </span>
<span id="L96"><span class="lineNum">      96</span>              :     if (screenName != null) {</span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaGNC">           3 :       deeplink += '?${EVODeeplinkKey.screenNameKey.value}=$screenName';</span></span>
<span id="L98"><span class="lineNum">      98</span>              :     }</span>
<span id="L99"><span class="lineNum">      99</span>              : </span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaGNC">           1 :     final Map&lt;String, String&gt;? additionalParams = deepLinkModel.params;</span></span>
<span id="L101"><span class="lineNum">     101</span>              :     if (additionalParams != null) {</span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaGNC">           2 :       final String queryParams = additionalParams.entries.map(</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaGNC">           1 :         (MapEntry&lt;String, dynamic&gt; entry) {</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaGNC">           4 :           return '${entry.key}=${entry.value.toString()}';</span></span>
<span id="L105"><span class="lineNum">     105</span>              :         },</span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaGNC">           1 :       ).join('&amp;');</span></span>
<span id="L107"><span class="lineNum">     107</span>              : </span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaGNC">           3 :       deeplink += screenName != null ? '&amp;$queryParams' : '?$queryParams';</span></span>
<span id="L109"><span class="lineNum">     109</span>              :     }</span>
<span id="L110"><span class="lineNum">     110</span>              : </span>
<span id="L111"><span class="lineNum">     111</span>              :     return deeplink;</span>
<span id="L112"><span class="lineNum">     112</span>              :   }</span>
<span id="L113"><span class="lineNum">     113</span>              : </span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaGNC">           1 :   bool isDOPWebLinkNfcStep(String? link) {</span></span>
<span id="L115"><span class="lineNum">     115</span>              :     if (link == null) {</span>
<span id="L116"><span class="lineNum">     116</span>              :       return false;</span>
<span id="L117"><span class="lineNum">     117</span>              :     }</span>
<span id="L118"><span class="lineNum">     118</span>              : </span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaGNC">           1 :     final Uri? uri = Uri.tryParse(link);</span></span>
<span id="L120"><span class="lineNum">     120</span>              :     if (uri == null) {</span>
<span id="L121"><span class="lineNum">     121</span>              :       return false;</span>
<span id="L122"><span class="lineNum">     122</span>              :     }</span>
<span id="L123"><span class="lineNum">     123</span>              : </span>
<span id="L124"><span class="lineNum">     124</span>              :     /// With deep-link for NFC</span>
<span id="L125"><span class="lineNum">     125</span>              :     /// DOP web always pass an web_link has a param [redirect_from_nfc]</span>
<span id="L126"><span class="lineNum">     126</span>              :     /// E.g: https://staging-tpbank.tsengineering.io/YRFOE8VY?impersonate_id=d9c02883-7fa0-4472-bf7b-3c3eac8eed27&amp;enable_webview=true&amp;redirect_from_nfc=true</span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaGNC">           3 :     return uri.queryParameters[DeepLinkConstants.dopWebNfcRedirect] == 'true';</span></span>
<span id="L128"><span class="lineNum">     128</span>              :   }</span>
<span id="L129"><span class="lineNum">     129</span>              : </span>
<span id="L130"><span class="lineNum">     130</span>              :   /// Remove all keys which are defined by [EVODeeplinkKey] from the map to ensure that the map only</span>
<span id="L131"><span class="lineNum">     131</span>              :   /// contains the keys which are additional keys.</span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaGNC">           2 :   Map&lt;String, String&gt;? removeEvoDeepLinkKeysFromMap(Map&lt;String, String&gt;? params) {</span></span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaGNC">           2 :     if (params == null || params.isEmpty) {</span></span>
<span id="L134"><span class="lineNum">     134</span>              :       return null;</span>
<span id="L135"><span class="lineNum">     135</span>              :     }</span>
<span id="L136"><span class="lineNum">     136</span>              : </span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaGNC">           2 :     final List&lt;String&gt; predefinedKeys = getDeepLinkKeyUnnecessaryToRemove();</span></span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaGNC">           2 :     final Map&lt;String, String&gt; finalParams = Map&lt;String, String&gt;.from(params);</span></span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaGNC">           4 :     for (final String key in predefinedKeys) {</span></span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaGNC">           2 :       finalParams.remove(key);</span></span>
<span id="L141"><span class="lineNum">     141</span>              :     }</span>
<span id="L142"><span class="lineNum">     142</span>              :     return finalParams;</span>
<span id="L143"><span class="lineNum">     143</span>              :   }</span>
<span id="L144"><span class="lineNum">     144</span>              : </span>
<span id="L145"><span class="lineNum">     145</span>              :   /// Regex for Evo deeplink: evoappvn://mobile/deeplinking</span>
<span id="L146"><span class="lineNum">     146</span>              :   ///     Refer document: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3433234592/EVO+App+Deep+Links</span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaGNC">           2 :   RegExp getRegExpOfDeeplink() {</span></span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaGNC">           2 :     return RegExp(r'^evoappvn:\/\/mobile\/deeplinking(?:\/[\w-]*)?(?:\?.*)?$');</span></span>
<span id="L149"><span class="lineNum">     149</span>              :   }</span>
<span id="L150"><span class="lineNum">     150</span>              : </span>
<span id="L151"><span class="lineNum">     151</span>              :   /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/Mobile/pages/3882975332/EMA-4751+Mobile+Redirect+user+to+DOP+flow+after+Drop-off+before+EVO+sign-in#Conclusion</span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaGNC">           1 :   String getDOEDeepLinkNonRelaxRule({String? phoneNumber}) {</span></span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaGNC">           1 :     return '${DeepLinkConstants.evoAppDeepLinkPrefix}?'</span></span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaGNC">           1 :         '${EVODeeplinkKey.screenNameKey.value}=${DeepLinkConstants.dopNativeIntroductionScreenName}'</span></span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaGNC">           1 :         '&amp;${EVODeeplinkKey.phoneNumberKey.value}=${phoneNumber ?? ''}'</span></span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaGNC">           1 :         '&amp;${EVODeeplinkKey.leadSourceKey.value}=${LeadSource.evoNative}'</span></span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaGNC">           1 :         '&amp;${EVODeeplinkKey.utmSourceKey.value}=${DeepLinkConstants.utmSourceFromEvoLogin}'</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaGNC">           1 :         '&amp;${EVODeeplinkKey.autoRequestOTPKey.value}=true';</span></span>
<span id="L159"><span class="lineNum">     159</span>              :   }</span>
<span id="L160"><span class="lineNum">     160</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
