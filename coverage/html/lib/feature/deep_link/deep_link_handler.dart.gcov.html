<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/deep_link/deep_link_handler.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/deep_link">lib/feature/deep_link</a> - deep_link_handler.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">78</td>
            <td class="headerCovTableEntry">78</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/widgets.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/ui_model/action_model.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/util/extension.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../model/evo_action_model.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../prepare_for_app_initiation.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../util/evo_action_handler.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../util/web_link_utils.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../widget/evo_next_action/evo_next_action_widget.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../dop_native/features/ekyc_ui_only/nfc_reader_introduction/constants/dop_native_nfc_entry_point.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../dop_native/features/ekyc_ui_only/nfc_reader_introduction/models/dop_native_nfc_shared_model.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../dop_native/features/ekyc_ui_only/nfc_reader_introduction/unsupported/dop_native_nfc_device_unsupported_screen.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/result/nfc_availability_type.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../dop_native/features/logging/metadata_define/dop_native_event_metadata.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../dop_native/features/logging/screen_action_define/dop_native_special_action_event.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../dop_native/models/dop_native_state.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../dop_native/util/nfc_availability_wrapper/nfc_availability_wrapper.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../logging/evo_event_tracking_utils/evo_event_tracking_utils_impl.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../logging/evo_logging_event.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../login/new_device/input_phone_number/input_phone_number_page.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import '../main_screen/main_screen.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import '../splash_screen/splash_screen.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import '../webview/models/evo_webview_arg.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import 'deep_link_constants.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : import 'deep_link_utils.dart';</span>
<span id="L27"><span class="lineNum">      27</span>              : import 'model/deep_link_model.dart';</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span>              : class DeepLinkHandler with LogErrorMixin {</span>
<span id="L30"><span class="lineNum">      30</span>              :   @visibleForTesting</span>
<span id="L31"><span class="lineNum">      31</span>              :   DeepLinkModel? deepLinkData;</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span>              :   /// First time app opened by deep link</span>
<span id="L34"><span class="lineNum">      34</span>              :   /// Default is true</span>
<span id="L35"><span class="lineNum">      35</span>              :   ///</span>
<span id="L36"><span class="lineNum">      36</span>              :   /// After the first time app handled by deep link, set to false, to restart app when open deep link again</span>
<span id="L37"><span class="lineNum">      37</span>              :   @visibleForTesting</span>
<span id="L38"><span class="lineNum">      38</span>              :   bool firstTimeOpenDeepLink = true;</span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           1 :   void saveDeepLinkNeedToBeHandled({required DeepLinkModel deepLink}) {</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           2 :     commonLog('saveDeepLinkNeedToBeHandled: $deepLink');</span></span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           1 :     deepLinkData = deepLink;</span></span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           1 :     if (!firstTimeOpenDeepLink) {</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           1 :       SplashScreen.goNamed();</span></span>
<span id="L47"><span class="lineNum">      47</span>              :     }</span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span>              :     /// set to false after the first time open deep link</span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           1 :     firstTimeOpenDeepLink = false;</span></span>
<span id="L51"><span class="lineNum">      51</span>              :   }</span>
<span id="L52"><span class="lineNum">      52</span>              : </span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; executeDeepLink({DeepLinkModel? deepLink, bool isLoggedIn = false}) async {</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           1 :     final String? screenName = deepLink?.screenName;</span></span>
<span id="L55"><span class="lineNum">      55</span>              : </span>
<span id="L56"><span class="lineNum">      56</span>              :     if (screenName == null) {</span>
<span id="L57"><span class="lineNum">      57</span>              :       return false;</span>
<span id="L58"><span class="lineNum">      58</span>              :     }</span>
<span id="L59"><span class="lineNum">      59</span>              : </span>
<span id="L60"><span class="lineNum">      60</span>              :     bool isProceed = true;</span>
<span id="L61"><span class="lineNum">      61</span>              :     switch (screenName) {</span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaGNC">           1 :       case DeepLinkConstants.dopCompletedScreenName:</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaGNC">           1 :         handleDopCompletedScreen(isUserLoggedIn: isLoggedIn);</span></span>
<span id="L64"><span class="lineNum">      64</span>              :         break;</span>
<span id="L65"><span class="lineNum">      65</span>              : </span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaGNC">           1 :       case DeepLinkConstants.webViewScreenName:</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           1 :         final Map&lt;String, String&gt;? params = deepLink?.params;</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaGNC">           2 :         final String? link = params?[EVODeeplinkKey.webViewLinkKey.value];</span></span>
<span id="L69"><span class="lineNum">      69</span>              : </span>
<span id="L70"><span class="lineNum">      70</span>              :         /// Optimize NFC Support Check on EVO App Launch</span>
<span id="L71"><span class="lineNum">      71</span>              :         /// refer: https://trustingsocial1.atlassian.net/browse/EMA-4416</span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaGNC">           1 :         if (await checkNFCIsNotSupported(link)) {</span></span>
<span id="L73"><span class="lineNum">      73</span>              :           break;</span>
<span id="L74"><span class="lineNum">      74</span>              :         }</span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           1 :         await handleWebViewScreen(params: params);</span></span>
<span id="L77"><span class="lineNum">      77</span>              :         break;</span>
<span id="L78"><span class="lineNum">      78</span>              : </span>
<span id="L79"><span class="lineNum">      79</span>              :       default:</span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaGNC">           1 :         final bool isNavigated = await navigateToEvoScreen(deepLink);</span></span>
<span id="L81"><span class="lineNum">      81</span>              : </span>
<span id="L82"><span class="lineNum">      82</span>              :         if (isNavigated) {</span>
<span id="L83"><span class="lineNum">      83</span>              :           break;</span>
<span id="L84"><span class="lineNum">      84</span>              :         }</span>
<span id="L85"><span class="lineNum">      85</span>              : </span>
<span id="L86"><span class="lineNum">      86</span>              :         /// don't support other screen</span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           1 :         final String errorMessage = 'Unsupported screen: $screenName';</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           1 :         logPlatformErrorEvent(</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">           1 :           errorType: EvoEventType.deepLink.name,</span></span>
<span id="L90"><span class="lineNum">      90</span>              :           action: 'process_deep_linking',</span>
<span id="L91"><span class="lineNum">      91</span>              :           error: errorMessage,</span>
<span id="L92"><span class="lineNum">      92</span>              :         );</span>
<span id="L93"><span class="lineNum">      93</span>              :         isProceed = false;</span>
<span id="L94"><span class="lineNum">      94</span>              :         break;</span>
<span id="L95"><span class="lineNum">      95</span>              :     }</span>
<span id="L96"><span class="lineNum">      96</span>              : </span>
<span id="L97"><span class="lineNum">      97</span>              :     return isProceed;</span>
<span id="L98"><span class="lineNum">      98</span>              :   }</span>
<span id="L99"><span class="lineNum">      99</span>              : </span>
<span id="L100"><span class="lineNum">     100</span>              :   /// This function will be called After the MainScreen is initialized and ready to handle the deep link</span>
<span id="L101"><span class="lineNum">     101</span>              :   /// &amp; handle the [deepLinkData] if it is available</span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; executeCachedDeeplink({required bool isLoggedIn}) async {</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaGNC">           1 :     if (deepLinkData == null) {</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaGNC">           1 :       firstTimeOpenDeepLink = false;</span></span>
<span id="L105"><span class="lineNum">     105</span>              :       return false;</span>
<span id="L106"><span class="lineNum">     106</span>              :     }</span>
<span id="L107"><span class="lineNum">     107</span>              : </span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaGNC">           2 :     final bool isProceed = await executeDeepLink(deepLink: deepLinkData, isLoggedIn: isLoggedIn);</span></span>
<span id="L109"><span class="lineNum">     109</span>              : </span>
<span id="L110"><span class="lineNum">     110</span>              :     /// clear data after done process</span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaGNC">           1 :     deepLinkData = null;</span></span>
<span id="L112"><span class="lineNum">     112</span>              : </span>
<span id="L113"><span class="lineNum">     113</span>              :     return isProceed;</span>
<span id="L114"><span class="lineNum">     114</span>              :   }</span>
<span id="L115"><span class="lineNum">     115</span>              : </span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L117"><span class="lineNum">     117</span>              :   void handleDopCompletedScreen({required bool isUserLoggedIn}) {</span>
<span id="L118"><span class="lineNum">     118</span>              :     if (isUserLoggedIn) {</span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaGNC">           1 :       MainScreen.goNamed(isLoggedIn: true);</span></span>
<span id="L120"><span class="lineNum">     120</span>              :     } else {</span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaGNC">           1 :       InputPhoneNumberPage.pushNamed();</span></span>
<span id="L122"><span class="lineNum">     122</span>              :     }</span>
<span id="L123"><span class="lineNum">     123</span>              :   }</span>
<span id="L124"><span class="lineNum">     124</span>              : </span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L126"><span class="lineNum">     126</span>              :   Future&lt;void&gt; handleWebViewScreen({required Map&lt;String, String&gt;? params}) async {</span>
<span id="L127"><span class="lineNum">     127</span>              :     /// At current, we support open WebView screen with a given URL for all users (anonymous and evo-users)</span>
<span id="L128"><span class="lineNum">     128</span>              :     /// Jira: https://trustingsocial1.atlassian.net/browse/EMA-1922</span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaGNC">           2 :     final String? title = params?[EVODeeplinkKey.webViewTitleKey.value];</span></span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaGNC">           2 :     final String? link = params?[EVODeeplinkKey.webViewLinkKey.value];</span></span>
<span id="L131"><span class="lineNum">     131</span>              : </span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaGNC">           1 :     final EvoActionModel evoActionModel = EvoActionModel(</span></span>
<span id="L133"><span class="lineNum">     133</span>              :       type: ActionModel.openInAppWebView,</span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaGNC">           1 :       args: EvoArgs(</span></span>
<span id="L135"><span class="lineNum">     135</span>              :         link: link,</span>
<span id="L136"><span class="lineNum">     136</span>              :       ),</span>
<span id="L137"><span class="lineNum">     137</span>              :     );</span>
<span id="L138"><span class="lineNum">     138</span>              : </span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaGNC">           1 :     final EvoWebViewArg arg = EvoWebViewArg(</span></span>
<span id="L140"><span class="lineNum">     140</span>              :       title: title,</span>
<span id="L141"><span class="lineNum">     141</span>              :       url: link,</span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaGNC">           1 :       nextActionWidget: EvoNextActionWidget(</span></span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaGNC">           1 :         nextAction: createNextActionModelIfNeed(params),</span></span>
<span id="L144"><span class="lineNum">     144</span>              :       ),</span>
<span id="L145"><span class="lineNum">     145</span>              :     );</span>
<span id="L146"><span class="lineNum">     146</span>              : </span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaGNC">           2 :     await EvoActionHandler().handle(evoActionModel, arg: arg);</span></span>
<span id="L148"><span class="lineNum">     148</span>              :   }</span>
<span id="L149"><span class="lineNum">     149</span>              : </span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L151"><span class="lineNum">     151</span>              :   Future&lt;bool&gt; checkNFCIsNotSupported(String? link) async {</span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaGNC">           3 :     if (deepLinkUtils.isDOPWebLinkNfcStep(link) != true) {</span></span>
<span id="L153"><span class="lineNum">     153</span>              :       return false;</span>
<span id="L154"><span class="lineNum">     154</span>              :     }</span>
<span id="L155"><span class="lineNum">     155</span>              : </span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaGNC">           2 :     final String? uniqueToken = webLinkUtils.getUniqueTokenFromDOPWebUrl(link);</span></span>
<span id="L157"><span class="lineNum">     157</span>              :     bool isNFCNotSupported = false;</span>
<span id="L158"><span class="lineNum">     158</span>              :     try {</span>
<span id="L159"><span class="lineNum">     159</span>              :       /// We cannot use checkNfcSupport() of TvSDK for checking NFC support</span>
<span id="L160"><span class="lineNum">     160</span>              :       /// because We need init TvSDK with client_setting</span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaGNC">           2 :       final NfcAvailabilityType result = await nfcAvailabilityWrapper.checkNFCSupport();</span></span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaGNC">           1 :       isNFCNotSupported = result == NfcAvailabilityType.unsupported;</span></span>
<span id="L163"><span class="lineNum">     163</span>              : </span>
<span id="L164"><span class="lineNum">     164</span>              :       /// log event check success</span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaGNC">           2 :       evoEventTrackingUtils.sendEvoSpecialEvent(</span></span>
<span id="L166"><span class="lineNum">     166</span>              :         eventActionId: DOPNativeSpecialActionEvent.checkNfcSupport,</span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaGNC">           1 :         metaData: &lt;String, dynamic&gt;{</span></span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaGNC">           1 :           DOPNativeEventMetadataKey.checkResult: result.name,</span></span>
<span id="L169"><span class="lineNum">     169</span>              :           DOPNativeEventMetadataKey.uniqueToken: uniqueToken,</span>
<span id="L170"><span class="lineNum">     170</span>              :         },</span>
<span id="L171"><span class="lineNum">     171</span>              :       );</span>
<span id="L172"><span class="lineNum">     172</span>              :       // ignore: avoid_catches_without_on_clauses</span>
<span id="L173"><span class="lineNum">     173</span>              :     } catch (exception) {</span>
<span id="L174"><span class="lineNum">     174</span>              :       /// log event error</span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaGNC">           2 :       evoEventTrackingUtils.sendEvoSpecialEvent(</span></span>
<span id="L176"><span class="lineNum">     176</span>              :         eventActionId: DOPNativeSpecialActionEvent.checkNfcSupport,</span>
<span id="L177"><span class="lineNum">     177</span> <span class="tlaGNC">           1 :         metaData: &lt;String, dynamic&gt;{</span></span>
<span id="L178"><span class="lineNum">     178</span>              :           DOPNativeEventMetadataKey.checkResult: DOPNativeEventMetadataValue.failed,</span>
<span id="L179"><span class="lineNum">     179</span>              :           DOPNativeEventMetadataKey.errorCode: DOPNativeEventMetadataValue.exception,</span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaGNC">           1 :           DOPNativeEventMetadataKey.verdict: exception.toString(),</span></span>
<span id="L181"><span class="lineNum">     181</span>              :           DOPNativeEventMetadataKey.uniqueToken: uniqueToken,</span>
<span id="L182"><span class="lineNum">     182</span>              :         },</span>
<span id="L183"><span class="lineNum">     183</span>              :       );</span>
<span id="L184"><span class="lineNum">     184</span>              :       return false;</span>
<span id="L185"><span class="lineNum">     185</span>              :     }</span>
<span id="L186"><span class="lineNum">     186</span>              : </span>
<span id="L187"><span class="lineNum">     187</span>              :     if (!isNFCNotSupported) {</span>
<span id="L188"><span class="lineNum">     188</span>              :       return false;</span>
<span id="L189"><span class="lineNum">     189</span>              :     }</span>
<span id="L190"><span class="lineNum">     190</span>              : </span>
<span id="L191"><span class="lineNum">     191</span>              :     /// Save weblink to generate QR-code in NFC Unsupported screen</span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaGNC">           3 :     final DOPNativeState dopNativeState = getIt.get&lt;AppState&gt;().dopNativeState;</span></span>
<span id="L193"><span class="lineNum">     193</span> <span class="tlaGNC">           2 :     dopNativeState.nfcSharedModel = DOPNativeNFCSharedModel(</span></span>
<span id="L194"><span class="lineNum">     194</span>              :       dopWebViewURL: link,</span>
<span id="L195"><span class="lineNum">     195</span>              :     );</span>
<span id="L196"><span class="lineNum">     196</span>              : </span>
<span id="L197"><span class="lineNum">     197</span> <span class="tlaGNC">           1 :     DOPNativeNFCDeviceUnsupportedScreen.pushNamed(</span></span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaGNC">           1 :       DOPNativeNFCDeviceUnsupportedScreenArg(entryPoint: NFCEntryPoint.dopWebView),</span></span>
<span id="L199"><span class="lineNum">     199</span>              :     );</span>
<span id="L200"><span class="lineNum">     200</span>              :     return true;</span>
<span id="L201"><span class="lineNum">     201</span>              :   }</span>
<span id="L202"><span class="lineNum">     202</span>              : </span>
<span id="L203"><span class="lineNum">     203</span>              :   /// Building next action model based on params which are passed from deep link</span>
<span id="L204"><span class="lineNum">     204</span>              :   /// return NULL if the params are invalid or missing [next_action_type] key</span>
<span id="L205"><span class="lineNum">     205</span>              :   /// references: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3433234592/EVO+App+Deep+Links</span>
<span id="L206"><span class="lineNum">     206</span>              :   /// Section: Navigate user to a Web View in the EVO App with a given URL With a CTA at the bottom of the screen</span>
<span id="L207"><span class="lineNum">     207</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L208"><span class="lineNum">     208</span>              :   EvoActionModel? createNextActionModelIfNeed(Map&lt;String, String&gt;? params) {</span>
<span id="L209"><span class="lineNum">     209</span>              :     if (params == null) {</span>
<span id="L210"><span class="lineNum">     210</span>              :       return null;</span>
<span id="L211"><span class="lineNum">     211</span>              :     }</span>
<span id="L212"><span class="lineNum">     212</span>              : </span>
<span id="L213"><span class="lineNum">     213</span>              :     /// [next_action_type] is considered as a mandatory key.</span>
<span id="L214"><span class="lineNum">     214</span>              :     /// If it is missing, there is no need to create next action model</span>
<span id="L215"><span class="lineNum">     215</span> <span class="tlaGNC">           3 :     if (params.containsKey(EVODeeplinkKey.nextActionTypeKey.value) == false) {</span></span>
<span id="L216"><span class="lineNum">     216</span>              :       return null;</span>
<span id="L217"><span class="lineNum">     217</span>              :     }</span>
<span id="L218"><span class="lineNum">     218</span>              : </span>
<span id="L219"><span class="lineNum">     219</span> <span class="tlaGNC">           1 :     return EvoActionModel(</span></span>
<span id="L220"><span class="lineNum">     220</span> <span class="tlaGNC">           2 :       type: params[EVODeeplinkKey.nextActionTypeKey.value],</span></span>
<span id="L221"><span class="lineNum">     221</span> <span class="tlaGNC">           1 :       args: EvoArgs(</span></span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaGNC">           2 :         link: params[EVODeeplinkKey.nextActionLinkKey.value],</span></span>
<span id="L223"><span class="lineNum">     223</span> <span class="tlaGNC">           2 :         actionLabel: params[EVODeeplinkKey.nextActionLabelKey.value],</span></span>
<span id="L224"><span class="lineNum">     224</span> <span class="tlaGNC">           2 :         screenName: params[EVODeeplinkKey.nextActionScreenNameKey.value],</span></span>
<span id="L225"><span class="lineNum">     225</span> <span class="tlaGNC">           2 :         additionalParams: deepLinkUtils.removeEvoDeepLinkKeysFromMap(params),</span></span>
<span id="L226"><span class="lineNum">     226</span> <span class="tlaGNC">           1 :         parameters: EvoParameters(</span></span>
<span id="L227"><span class="lineNum">     227</span> <span class="tlaGNC">           2 :           code: params[EVODeeplinkKey.nextActionCodeKey.value],</span></span>
<span id="L228"><span class="lineNum">     228</span> <span class="tlaGNC">           1 :           id: extractIdForNextActionModel(params),</span></span>
<span id="L229"><span class="lineNum">     229</span>              :         ),</span>
<span id="L230"><span class="lineNum">     230</span>              :       ),</span>
<span id="L231"><span class="lineNum">     231</span>              :     );</span>
<span id="L232"><span class="lineNum">     232</span>              :   }</span>
<span id="L233"><span class="lineNum">     233</span>              : </span>
<span id="L234"><span class="lineNum">     234</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L235"><span class="lineNum">     235</span>              :   String? extractIdForNextActionModel(Map&lt;String, String&gt; params) {</span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaGNC">           2 :     if (params.containsKey(EVODeeplinkKey.nextActionIdKey.value)) {</span></span>
<span id="L237"><span class="lineNum">     237</span> <span class="tlaGNC">           2 :       return params[EVODeeplinkKey.nextActionIdKey.value];</span></span>
<span id="L238"><span class="lineNum">     238</span>              :     }</span>
<span id="L239"><span class="lineNum">     239</span>              : </span>
<span id="L240"><span class="lineNum">     240</span> <span class="tlaGNC">           2 :     if (params.containsKey(EVODeeplinkKey.nextActionCampaignIdKey.value)) {</span></span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaGNC">           2 :       return params[EVODeeplinkKey.nextActionCampaignIdKey.value];</span></span>
<span id="L242"><span class="lineNum">     242</span>              :     }</span>
<span id="L243"><span class="lineNum">     243</span>              : </span>
<span id="L244"><span class="lineNum">     244</span>              :     return null;</span>
<span id="L245"><span class="lineNum">     245</span>              :   }</span>
<span id="L246"><span class="lineNum">     246</span>              : </span>
<span id="L247"><span class="lineNum">     247</span>              :   /// Open DOP Native Introduction screen: https://trustingsocial1.atlassian.net/browse/EMA-1891</span>
<span id="L248"><span class="lineNum">     248</span>              :   /// Open QR Scan screen for non-login user: https://trustingsocial1.atlassian.net/browse/EMA-4087</span>
<span id="L249"><span class="lineNum">     249</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; navigateToEvoScreen(DeepLinkModel? deepLink) async {</span></span>
<span id="L250"><span class="lineNum">     250</span> <span class="tlaGNC">           1 :     final EvoActionModel evoActionModel = EvoActionModel(</span></span>
<span id="L251"><span class="lineNum">     251</span>              :       type: EvoActionModel.openAppScreen,</span>
<span id="L252"><span class="lineNum">     252</span> <span class="tlaGNC">           1 :       args: EvoArgs(</span></span>
<span id="L253"><span class="lineNum">     253</span> <span class="tlaGNC">           1 :         screenName: deepLink?.screenName,</span></span>
<span id="L254"><span class="lineNum">     254</span> <span class="tlaGNC">           3 :         additionalParams: deepLinkUtils.removeEvoDeepLinkKeysFromMap(deepLink?.params),</span></span>
<span id="L255"><span class="lineNum">     255</span>              :       ),</span>
<span id="L256"><span class="lineNum">     256</span>              :     );</span>
<span id="L257"><span class="lineNum">     257</span>              : </span>
<span id="L258"><span class="lineNum">     258</span> <span class="tlaGNC">           2 :     return await EvoActionHandler().handle(evoActionModel);</span></span>
<span id="L259"><span class="lineNum">     259</span>              :   }</span>
<span id="L260"><span class="lineNum">     260</span>              : </span>
<span id="L261"><span class="lineNum">     261</span> <span class="tlaGNC">           1 :   bool isHandlingDOPWebNfcDeepLink() {</span></span>
<span id="L262"><span class="lineNum">     262</span> <span class="tlaGNC">           1 :     if (deepLinkData == null) {</span></span>
<span id="L263"><span class="lineNum">     263</span>              :       return false;</span>
<span id="L264"><span class="lineNum">     264</span>              :     }</span>
<span id="L265"><span class="lineNum">     265</span> <span class="tlaGNC">           4 :     final String? webLink = deepLinkData?.params?[EVODeeplinkKey.webViewLinkKey.value];</span></span>
<span id="L266"><span class="lineNum">     266</span> <span class="tlaGNC">           2 :     return deepLinkUtils.isDOPWebLinkNfcStep(webLink);</span></span>
<span id="L267"><span class="lineNum">     267</span>              :   }</span>
<span id="L268"><span class="lineNum">     268</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
