<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/promotion_list/my_voucher_view/my_voucher_item_widget.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/promotion_list/my_voucher_view">lib/feature/promotion_list/my_voucher_view</a> - my_voucher_item_widget.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">47</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../../data/response/voucher_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../../model/promotion_status_ui_model.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import '../../../resources/resources.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../../util/promotion/status_ui_data_creator_factory/promotion_source_data.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../../util/promotion/status_ui_data_creator_factory/promotion_status_ui_creator_factory.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../../util/ui_utils/change_colors.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../widget/evo_image_provider_widget.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../other_widgets/voucher_expired_time_widget.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span>              : class MyVoucherItemWidget extends StatelessWidget {</span>
<span id="L13"><span class="lineNum">      13</span>              :   final VoucherEntity item;</span>
<span id="L14"><span class="lineNum">      14</span>              :   final double heightItem;</span>
<span id="L15"><span class="lineNum">      15</span>              :   final double cornerRadiusImage;</span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaUNC">           0 :   const MyVoucherItemWidget(</span></span>
<span id="L18"><span class="lineNum">      18</span>              :       {required this.item, required this.heightItem, required this.cornerRadiusImage, super.key});</span>
<span id="L19"><span class="lineNum">      19</span>              : </span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L21"><span class="lineNum">      21</span>              :   Widget build(BuildContext context) {</span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaUNC">           0 :     final PromotionStatusUIModel? voucherStatusUIModel = PromotionStatusUICreatorFactory()</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaUNC">           0 :         .create(</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaUNC">           0 :           VoucherSourceData(</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaUNC">           0 :             validToDate: item.validToDateTime,</span></span>
<span id="L26"><span class="lineNum">      26</span>              :           ),</span>
<span id="L27"><span class="lineNum">      27</span>              :         )</span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :         .createPromotionStatusUIData();</span></span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :     final bool? isUsed = item.isUsed;</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :     return isUsed == true</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :         ? _contentUsedVoucherWidget()</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :         : _contentUnusedVoucherWidget(voucherStatusUIModel);</span></span>
<span id="L34"><span class="lineNum">      34</span>              :   }</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 :   Widget _contentUsedVoucherWidget() {</span></span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :     return Row(crossAxisAlignment: CrossAxisAlignment.start, children: &lt;Widget&gt;[</span></span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :       _thumbnailImage(hasUseGrayScale: true),</span></span>
<span id="L39"><span class="lineNum">      39</span>              :       const SizedBox(width: 16),</span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :       Expanded(</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :           child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: &lt;Widget&gt;[</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :         _titlePromotionItem(title: item.title, color: evoColors.promotionIsUsed),</span></span>
<span id="L43"><span class="lineNum">      43</span>              :         const SizedBox(height: 6),</span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :         _promotionButton(</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :             title: EvoStrings.promotionIsUsed, color: evoColors.secondaryButtonBgDisable)</span></span>
<span id="L46"><span class="lineNum">      46</span>              :       ]))</span>
<span id="L47"><span class="lineNum">      47</span>              :     ]);</span>
<span id="L48"><span class="lineNum">      48</span>              :   }</span>
<span id="L49"><span class="lineNum">      49</span>              : </span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :   Widget _contentUnusedVoucherWidget(PromotionStatusUIModel? voucherStatusUIData) {</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :     return Row(crossAxisAlignment: CrossAxisAlignment.start, children: &lt;Widget&gt;[</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :       _thumbnailImage(hasUseGrayScale: voucherStatusUIData?.hasOpacityTitle),</span></span>
<span id="L53"><span class="lineNum">      53</span>              :       const SizedBox(width: 16),</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :       Expanded(</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :           child: Column(</span></span>
<span id="L56"><span class="lineNum">      56</span>              :               crossAxisAlignment: CrossAxisAlignment.start,</span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :               children: _contentUnusedVoucherItemsWidget(voucherStatusUIData)))</span></span>
<span id="L58"><span class="lineNum">      58</span>              :     ]);</span>
<span id="L59"><span class="lineNum">      59</span>              :   }</span>
<span id="L60"><span class="lineNum">      60</span>              : </span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :   List&lt;Widget&gt; _contentUnusedVoucherItemsWidget(PromotionStatusUIModel? voucherStatusUIData) {</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :     final List&lt;Widget&gt; widgets = &lt;Widget&gt;[];</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :     widgets.addAll(&lt;Widget&gt;[</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :       _titlePromotionItem(</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :           title: item.title,</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :           color: voucherStatusUIData?.hasOpacityTitle == true ? evoColors.promotionIsUsed : null),</span></span>
<span id="L67"><span class="lineNum">      67</span>              :       const SizedBox(height: 4),</span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :       VoucherExpiredTimeWidget(voucherStatusUIData: voucherStatusUIData)</span></span>
<span id="L69"><span class="lineNum">      69</span>              :     ]);</span>
<span id="L70"><span class="lineNum">      70</span>              : </span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :     if (voucherStatusUIData?.hasOpacityTitle != true) {</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :       widgets.addAll(&lt;Widget&gt;[</span></span>
<span id="L73"><span class="lineNum">      73</span>              :         const SizedBox(height: 6),</span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :         _promotionButton(title: EvoStrings.promotionUse, color: evoColors.foreground)</span></span>
<span id="L75"><span class="lineNum">      75</span>              :       ]);</span>
<span id="L76"><span class="lineNum">      76</span>              :     }</span>
<span id="L77"><span class="lineNum">      77</span>              : </span>
<span id="L78"><span class="lineNum">      78</span>              :     return widgets;</span>
<span id="L79"><span class="lineNum">      79</span>              :   }</span>
<span id="L80"><span class="lineNum">      80</span>              : </span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :   Widget _thumbnailImage({bool? hasUseGrayScale}) {</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :     final Widget widget = EvoNetworkImageProviderWidget(item.thumbnail,</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :         height: heightItem, width: heightItem, cornerRadius: cornerRadiusImage);</span></span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :     if (hasUseGrayScale == true) {</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :       return ChangeColors(saturation: -1, hue: 0.3, brightness: 0.3, child: widget);</span></span>
<span id="L87"><span class="lineNum">      87</span>              :     }</span>
<span id="L88"><span class="lineNum">      88</span>              : </span>
<span id="L89"><span class="lineNum">      89</span>              :     return widget;</span>
<span id="L90"><span class="lineNum">      90</span>              :   }</span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :   Widget _titlePromotionItem({String? title, Color? color}) {</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :     return Text(title ?? '',</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :         style: evoTextStyles.h300(color: color), maxLines: 2, overflow: TextOverflow.ellipsis);</span></span>
<span id="L95"><span class="lineNum">      95</span>              :   }</span>
<span id="L96"><span class="lineNum">      96</span>              : </span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :   Widget _promotionButton({required String title, required Color color}) {</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :     return Container(</span></span>
<span id="L99"><span class="lineNum">      99</span>              :         padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),</span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :         decoration: BoxDecoration(color: color, borderRadius: BorderRadius.circular(1000)),</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :         child: Text(title, style: evoTextStyles.bodySmall(color: evoColors.background)));</span></span>
<span id="L102"><span class="lineNum">     102</span>              :   }</span>
<span id="L103"><span class="lineNum">     103</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
