<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/alice/v2/evo_alice_chatwoot_config_values.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/alice/v2">lib/feature/alice/v2</a> - evo_alice_chatwoot_config_values.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">6</td>
            <td class="headerCovTableEntry">6</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/flavors/flavor_config.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../../flavors/flavors_type.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : class EvoAliceChatwootConfigValues {</span>
<span id="L6"><span class="lineNum">       6</span>              :   static const String nonEvoUserChatWootNamePrefix = 'EVO App user';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              :   /// Alice Chatwoot inbox - EVO App Staging</span>
<span id="L9"><span class="lineNum">       9</span>              :   /// Dashboard link: https://evo-crm-staging.trustingsocial.com/app/accounts/1/settings/inboxes/56</span>
<span id="L10"><span class="lineNum">      10</span>              :   /// Note: if you don't access the link above, please contact Admin or Leader to get permission</span>
<span id="L11"><span class="lineNum">      11</span>              :   static const String chatwootBaseUrlStag = 'https://evo-crm-staging.trustingsocial.com';</span>
<span id="L12"><span class="lineNum">      12</span>              :   static const String chatwootWebsiteTokenStag = 'uAXFb9aAe3mHg3YJSofKejka';</span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span>              :   /// Alice Chatwoot inbox - EVO App Uat</span>
<span id="L15"><span class="lineNum">      15</span>              :   /// Dashboard link: https://evo-crm-staging.trustingsocial.com/app/accounts/1/settings/inboxes/57</span>
<span id="L16"><span class="lineNum">      16</span>              :   /// Note: if you don't access the link above, please contact Admin or Leader to get permission</span>
<span id="L17"><span class="lineNum">      17</span>              :   static const String chatwootBaseUrlUat = 'https://evo-crm-uat.trustingsocial.com';</span>
<span id="L18"><span class="lineNum">      18</span>              :   static const String chatwootWebsiteTokenUat = '9Bw4zPUpef8bn3RdHf8cqoG6';</span>
<span id="L19"><span class="lineNum">      19</span>              : </span>
<span id="L20"><span class="lineNum">      20</span>              :   /// Alice Chatwoot inbox - EVO App Prod</span>
<span id="L21"><span class="lineNum">      21</span>              :   static const String chatwootBaseUrlProd = 'https://crm.goevo.vn';</span>
<span id="L22"><span class="lineNum">      22</span>              :   static const String chatwootWebsiteTokenProd = '9BR4CMMa1PN1SaaHjMwCCuTv';</span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">           2 :   static String getChatwootBaseUrlBasedOnEnvironment() {</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           8 :     if (FlavorConfig.instance.flavor == FlavorType.prod.name) {</span></span>
<span id="L26"><span class="lineNum">      26</span>              :       return chatwootBaseUrlProd;</span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           8 :     } else if (FlavorConfig.instance.flavor == FlavorType.uat.name) {</span></span>
<span id="L28"><span class="lineNum">      28</span>              :       return chatwootBaseUrlUat;</span>
<span id="L29"><span class="lineNum">      29</span>              :     } else {</span>
<span id="L30"><span class="lineNum">      30</span>              :       /// Chatwoot Website Token - EVO App Staging</span>
<span id="L31"><span class="lineNum">      31</span>              :       return chatwootBaseUrlStag;</span>
<span id="L32"><span class="lineNum">      32</span>              :     }</span>
<span id="L33"><span class="lineNum">      33</span>              :   }</span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           2 :   static String getChatwootWebsiteTokenBasedOnEnvironment() {</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaGNC">           8 :     if (FlavorConfig.instance.flavor == FlavorType.prod.name) {</span></span>
<span id="L37"><span class="lineNum">      37</span>              :       return chatwootWebsiteTokenProd;</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           8 :     } else if (FlavorConfig.instance.flavor == FlavorType.uat.name) {</span></span>
<span id="L39"><span class="lineNum">      39</span>              :       return chatwootWebsiteTokenUat;</span>
<span id="L40"><span class="lineNum">      40</span>              :     } else {</span>
<span id="L41"><span class="lineNum">      41</span>              :       /// Chatwoot Website Token - EVO App Staging</span>
<span id="L42"><span class="lineNum">      42</span>              :       return chatwootWebsiteTokenStag;</span>
<span id="L43"><span class="lineNum">      43</span>              :     }</span>
<span id="L44"><span class="lineNum">      44</span>              :   }</span>
<span id="L45"><span class="lineNum">      45</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
