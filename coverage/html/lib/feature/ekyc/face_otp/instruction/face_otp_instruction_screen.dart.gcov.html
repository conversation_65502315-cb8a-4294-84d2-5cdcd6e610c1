<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/ekyc/face_otp/instruction/face_otp_instruction_screen.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/ekyc/face_otp/instruction">lib/feature/ekyc/face_otp/instruction</a> - face_otp_instruction_screen.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">1.8&nbsp;%</td>
            <td class="headerCovTableEntry">112</td>
            <td class="headerCovTableEntry">2</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/base/page_base.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/common_package/common_package.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/global_key_provider.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/init_common_package.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import 'package:flutter_common_package/util/extension.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : </span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../../base/evo_page_state_base.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../../model/evo_dialog_id.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../../resources/resources.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../../util/ui_utils/evo_dialog_helper.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../../../util/ui_utils/evo_ui_utils.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../../../widget/evo_appbar.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../../../widget/evo_appbar_leading_button.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../../camera_permission/camera_permission_cubit.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../../../mock_test/mock_test_feature_type.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../../ekyc_bridge/ekyc_bridge.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../../ekyc_error_screen/ekyc_error_screen.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../../ekyc_flow_failed_reason.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import '../../ekyc_flow_mixin.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import '../../model/ekyc_for_flow_type.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import '../../model/ekyc_result_model.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import '../face_matching/face_otp_matching_process_screen.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : import '../selfie_capture/selfie_capturing_cubit.dart';</span>
<span id="L27"><span class="lineNum">      27</span>              : import 'face_otp_instruction_cubit.dart';</span>
<span id="L28"><span class="lineNum">      28</span>              : import 'face_otp_screen_mock_testing.dart';</span>
<span id="L29"><span class="lineNum">      29</span>              : import 'widgets/face_otp_instruction_content_widget.dart';</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span>              : class FaceOTPInstructionScreenArg extends PageBaseArg {</span>
<span id="L32"><span class="lineNum">      32</span>              :   final EkycFlowType flowType;</span>
<span id="L33"><span class="lineNum">      33</span>              :   final bool forceCreateNewEKYCSession;</span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           1 :   FaceOTPInstructionScreenArg({</span></span>
<span id="L36"><span class="lineNum">      36</span>              :     required this.flowType,</span>
<span id="L37"><span class="lineNum">      37</span>              :     required this.forceCreateNewEKYCSession,</span>
<span id="L38"><span class="lineNum">      38</span>              :   });</span>
<span id="L39"><span class="lineNum">      39</span>              : }</span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span>              : class FaceOTPInstructionScreen extends PageBase {</span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :   static void pushReplacementNamed({</span></span>
<span id="L43"><span class="lineNum">      43</span>              :     required EkycFlowType flowType,</span>
<span id="L44"><span class="lineNum">      44</span>              :     required bool forceCreateNewEKYCSession,</span>
<span id="L45"><span class="lineNum">      45</span>              :   }) {</span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :     return navigatorContext?.pushReplacementNamed(</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :       Screen.faceOTPInstructionScreen.name,</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :       extra: FaceOTPInstructionScreenArg(</span></span>
<span id="L49"><span class="lineNum">      49</span>              :         flowType: flowType,</span>
<span id="L50"><span class="lineNum">      50</span>              :         forceCreateNewEKYCSession: forceCreateNewEKYCSession,</span>
<span id="L51"><span class="lineNum">      51</span>              :       ),</span>
<span id="L52"><span class="lineNum">      52</span>              :     );</span>
<span id="L53"><span class="lineNum">      53</span>              :   }</span>
<span id="L54"><span class="lineNum">      54</span>              : </span>
<span id="L55"><span class="lineNum">      55</span>              :   final FaceOTPInstructionScreenArg arg;</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           1 :   const FaceOTPInstructionScreen({required this.arg, super.key});</span></span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L60"><span class="lineNum">      60</span>              :   EventTrackingScreenId get eventTrackingScreenId =&gt; EventTrackingScreenId.undefined;</span>
<span id="L61"><span class="lineNum">      61</span>              : </span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :   RouteSettings get routeSettings =&gt; RouteSettings(name: Screen.faceOTPInstructionScreen.routeName);</span></span>
<span id="L64"><span class="lineNum">      64</span>              : </span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :   State&lt;FaceOTPInstructionScreen&gt; createState() =&gt; _FaceOTPInstructionScreenState();</span></span>
<span id="L67"><span class="lineNum">      67</span>              : }</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span>              : class _FaceOTPInstructionScreenState extends EvoPageStateBase&lt;FaceOTPInstructionScreen&gt;</span>
<span id="L70"><span class="lineNum">      70</span>              :     with EkycFlowMixin, FaceOtpInstructionScreenMockTesting {</span>
<span id="L71"><span class="lineNum">      71</span>              :   final FaceOTPInstructionCubit _faceOTPInstructionCubit =</span>
<span id="L72"><span class="lineNum">      72</span>              :       FaceOTPInstructionCubit(getIt.get&lt;EkycBridge&gt;());</span>
<span id="L73"><span class="lineNum">      73</span>              :   final CameraPermissionCubit _permissionCubit = CameraPermissionCubit();</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span>              :   final SelfieCapturingCubit _selfieCapturingCubit =</span>
<span id="L76"><span class="lineNum">      76</span>              :       SelfieCapturingCubit(ekycBridge: getIt.get&lt;EkycBridge&gt;());</span>
<span id="L77"><span class="lineNum">      77</span>              : </span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :   MockTestFeatureType get mockTestFeatureType =&gt; switch (widget.arg.flowType) {</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :         EkycFlowType.linkCard =&gt; MockTestFeatureType.manualLinkCard,</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :         EkycFlowType.faceOtpSignIn =&gt; MockTestFeatureType.signIn,</span></span>
<span id="L82"><span class="lineNum">      82</span>              :       };</span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L85"><span class="lineNum">      85</span>              :   void dispose() {</span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :     _permissionCubit.close();</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :     _faceOTPInstructionCubit.close();</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :     _selfieCapturingCubit.close();</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :     super.dispose();</span></span>
<span id="L90"><span class="lineNum">      90</span>              :   }</span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L93"><span class="lineNum">      93</span>              :   Widget getContentWidget(BuildContext context) {</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :     return MultiBlocProvider(</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :       providers: &lt;BlocProvider&lt;dynamic&gt;&gt;[</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :         BlocProvider&lt;FaceOTPInstructionCubit&gt;(</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :           create: (_) =&gt; _faceOTPInstructionCubit,</span></span>
<span id="L98"><span class="lineNum">      98</span>              :         ),</span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :         BlocProvider&lt;CameraPermissionCubit&gt;(</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :           create: (_) =&gt; _permissionCubit,</span></span>
<span id="L101"><span class="lineNum">     101</span>              :         ),</span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :         BlocProvider&lt;SelfieCapturingCubit&gt;(</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :           create: (_) =&gt; _selfieCapturingCubit,</span></span>
<span id="L104"><span class="lineNum">     104</span>              :         ),</span>
<span id="L105"><span class="lineNum">     105</span>              :       ],</span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :       child: MultiBlocListener(</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :         listeners: &lt;BlocListener&lt;dynamic, dynamic&gt;&gt;[</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :           BlocListener&lt;FaceOTPInstructionCubit, FaceOTPInstructionState&gt;(</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :             listener: _listenNoticeFaceOTPScreen,</span></span>
<span id="L110"><span class="lineNum">     110</span>              :           ),</span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :           BlocListener&lt;CameraPermissionCubit, CameraPermissionState&gt;(</span></span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :             listener: _listenPermissionState,</span></span>
<span id="L113"><span class="lineNum">     113</span>              :           ),</span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :           BlocListener&lt;SelfieCapturingCubit, SelfieCapturingState&gt;(</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :             listener: _listenSelfieCapturingCubit,</span></span>
<span id="L116"><span class="lineNum">     116</span>              :           ),</span>
<span id="L117"><span class="lineNum">     117</span>              :         ],</span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :         child: BlocBuilder&lt;FaceOTPInstructionCubit, FaceOTPInstructionState&gt;(</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :           builder: (_, FaceOTPInstructionState state) {</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :             return PopScope(</span></span>
<span id="L121"><span class="lineNum">     121</span>              :               canPop: false,</span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :               onPopInvokedWithResult: (bool didPop, _) {</span></span>
<span id="L123"><span class="lineNum">     123</span>              :                 if (didPop) {</span>
<span id="L124"><span class="lineNum">     124</span>              :                   return;</span>
<span id="L125"><span class="lineNum">     125</span>              :                 }</span>
<span id="L126"><span class="lineNum">     126</span>              : </span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :                 _backToEntryPoint(reason: EkycFlowFailedReason.userCancelled);</span></span>
<span id="L128"><span class="lineNum">     128</span>              :               },</span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :               child: Scaffold(</span></span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :                 backgroundColor: evoColors.background,</span></span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :                 appBar: EvoAppBar(</span></span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaUNC">           0 :                   leading: EvoAppBarLeadingButton(</span></span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :                     onPressed: () {</span></span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :                       _backToEntryPoint(reason: EkycFlowFailedReason.userCancelled);</span></span>
<span id="L135"><span class="lineNum">     135</span>              :                     },</span>
<span id="L136"><span class="lineNum">     136</span>              :                   ),</span>
<span id="L137"><span class="lineNum">     137</span>              :                 ),</span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :                 body: SafeArea(</span></span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaUNC">           0 :                   child: _buildInstructionContent(state),</span></span>
<span id="L140"><span class="lineNum">     140</span>              :                 ),</span>
<span id="L141"><span class="lineNum">     141</span>              :               ),</span>
<span id="L142"><span class="lineNum">     142</span>              :             );</span>
<span id="L143"><span class="lineNum">     143</span>              :           },</span>
<span id="L144"><span class="lineNum">     144</span>              :         ),</span>
<span id="L145"><span class="lineNum">     145</span>              :       ),</span>
<span id="L146"><span class="lineNum">     146</span>              :     );</span>
<span id="L147"><span class="lineNum">     147</span>              :   }</span>
<span id="L148"><span class="lineNum">     148</span>              : </span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :   String get instructionDescription {</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :     return switch (widget.arg.flowType) {</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :       EkycFlowType.linkCard =&gt; EvoStrings.verifyFaceOtpLinkCardDescription,</span></span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :       EkycFlowType.faceOtpSignIn =&gt; EvoStrings.verifyFaceOtpSignInDescription,</span></span>
<span id="L153"><span class="lineNum">     153</span>              :     };</span>
<span id="L154"><span class="lineNum">     154</span>              :   }</span>
<span id="L155"><span class="lineNum">     155</span>              : </span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaUNC">           0 :   Widget _buildInstructionContent(FaceOTPInstructionState state) {</span></span>
<span id="L157"><span class="lineNum">     157</span>              :     final VoidCallback? onStartCallBack =</span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :         state is FaceOTPInstructionLoadingState ? null : _startFaceOTPCapturing;</span></span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :     return FaceOtpInstructionContentWidget(</span></span>
<span id="L160"><span class="lineNum">     160</span>              :       onPressStart: onStartCallBack,</span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaUNC">           0 :       description: instructionDescription,</span></span>
<span id="L162"><span class="lineNum">     162</span>              :     );</span>
<span id="L163"><span class="lineNum">     163</span>              :   }</span>
<span id="L164"><span class="lineNum">     164</span>              : </span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaUNC">           0 :   void _listenNoticeFaceOTPScreen(BuildContext context, FaceOTPInstructionState state) {</span></span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaUNC">           0 :     if (state is FaceOTPInstructionLoadingState) {</span></span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaUNC">           0 :       EvoUiUtils().showHudLoading();</span></span>
<span id="L168"><span class="lineNum">     168</span>              :       return;</span>
<span id="L169"><span class="lineNum">     169</span>              :     }</span>
<span id="L170"><span class="lineNum">     170</span>              : </span>
<span id="L171"><span class="lineNum">     171</span> <span class="tlaUNC">           0 :     EvoUiUtils().hideHudLoading();</span></span>
<span id="L172"><span class="lineNum">     172</span>              : </span>
<span id="L173"><span class="lineNum">     173</span> <span class="tlaUNC">           0 :     if (state is FaceOTPInstructionFailState) {</span></span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaUNC">           0 :       handleEvoApiError(state.errorUIModel);</span></span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :       _backToEntryPoint(error: state.errorUIModel);</span></span>
<span id="L176"><span class="lineNum">     176</span>              :       return;</span>
<span id="L177"><span class="lineNum">     177</span>              :     }</span>
<span id="L178"><span class="lineNum">     178</span>              : </span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaUNC">           0 :     if (state is FaceOTPInstructionStartNow) {</span></span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaUNC">           0 :       _selfieCapturingCubit.startSelfieCapturing();</span></span>
<span id="L181"><span class="lineNum">     181</span>              :       return;</span>
<span id="L182"><span class="lineNum">     182</span>              :     }</span>
<span id="L183"><span class="lineNum">     183</span>              :   }</span>
<span id="L184"><span class="lineNum">     184</span>              : </span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :   void _backToEntryPoint({ErrorUIModel? error, EkycFlowFailedReason? reason}) {</span></span>
<span id="L186"><span class="lineNum">     186</span> <span class="tlaUNC">           0 :     onEkycFailed(</span></span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaUNC">           0 :       context: context,</span></span>
<span id="L188"><span class="lineNum">     188</span>              :       reason: reason ?? EkycFlowFailedReason.unknown,</span>
<span id="L189"><span class="lineNum">     189</span> <span class="tlaUNC">           0 :       userMessage: error?.userMessage,</span></span>
<span id="L190"><span class="lineNum">     190</span>              :     );</span>
<span id="L191"><span class="lineNum">     191</span>              :   }</span>
<span id="L192"><span class="lineNum">     192</span>              : </span>
<span id="L193"><span class="lineNum">     193</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _listenPermissionState(BuildContext context, CameraPermissionState state) async {</span></span>
<span id="L194"><span class="lineNum">     194</span> <span class="tlaUNC">           0 :     if (state is CameraPermissionGrantedState) {</span></span>
<span id="L195"><span class="lineNum">     195</span> <span class="tlaUNC">           0 :       _faceOTPInstructionCubit.initTrustVisionSDK();</span></span>
<span id="L196"><span class="lineNum">     196</span>              :       return;</span>
<span id="L197"><span class="lineNum">     197</span>              :     }</span>
<span id="L198"><span class="lineNum">     198</span>              : </span>
<span id="L199"><span class="lineNum">     199</span> <span class="tlaUNC">           0 :     if (state is CameraPermissionDeniedState) {</span></span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaUNC">           0 :       await _showAppSettingDialog();</span></span>
<span id="L201"><span class="lineNum">     201</span>              :       return;</span>
<span id="L202"><span class="lineNum">     202</span>              :     }</span>
<span id="L203"><span class="lineNum">     203</span>              :   }</span>
<span id="L204"><span class="lineNum">     204</span>              : </span>
<span id="L205"><span class="lineNum">     205</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _showAppSettingDialog() {</span></span>
<span id="L206"><span class="lineNum">     206</span> <span class="tlaUNC">           0 :     return EvoDialogHelper().showDialogBottomSheet(</span></span>
<span id="L207"><span class="lineNum">     207</span>              :       dialogId: EvoDialogId.appSettingCameraBottomSheet,</span>
<span id="L208"><span class="lineNum">     208</span>              :       isDismissible: false,</span>
<span id="L209"><span class="lineNum">     209</span>              :       title: EvoStrings.cameraPermissionTitle,</span>
<span id="L210"><span class="lineNum">     210</span>              :       content: EvoStrings.cameraFaceOtpPermissionDescription,</span>
<span id="L211"><span class="lineNum">     211</span>              :       textPositive: EvoStrings.settingTitle,</span>
<span id="L212"><span class="lineNum">     212</span>              :       textNegative: EvoStrings.ignoreTitle,</span>
<span id="L213"><span class="lineNum">     213</span> <span class="tlaUNC">           0 :       onClickPositive: () {</span></span>
<span id="L214"><span class="lineNum">     214</span> <span class="tlaUNC">           0 :         _permissionCubit.resetState();</span></span>
<span id="L215"><span class="lineNum">     215</span>              : </span>
<span id="L216"><span class="lineNum">     216</span>              :         /// dismiss popup</span>
<span id="L217"><span class="lineNum">     217</span> <span class="tlaUNC">           0 :         navigatorContext?.pop();</span></span>
<span id="L218"><span class="lineNum">     218</span>              : </span>
<span id="L219"><span class="lineNum">     219</span> <span class="tlaUNC">           0 :         openAppSettings();</span></span>
<span id="L220"><span class="lineNum">     220</span>              :       },</span>
<span id="L221"><span class="lineNum">     221</span> <span class="tlaUNC">           0 :       onClickNegative: () {</span></span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaUNC">           0 :         _permissionCubit.resetState();</span></span>
<span id="L223"><span class="lineNum">     223</span>              : </span>
<span id="L224"><span class="lineNum">     224</span>              :         /// dismiss popup</span>
<span id="L225"><span class="lineNum">     225</span> <span class="tlaUNC">           0 :         navigatorContext?.pop();</span></span>
<span id="L226"><span class="lineNum">     226</span>              :       },</span>
<span id="L227"><span class="lineNum">     227</span>              :     );</span>
<span id="L228"><span class="lineNum">     228</span>              :   }</span>
<span id="L229"><span class="lineNum">     229</span>              : </span>
<span id="L230"><span class="lineNum">     230</span> <span class="tlaUNC">           0 :   void _listenSelfieCapturingCubit(BuildContext context, SelfieCapturingState state) {</span></span>
<span id="L231"><span class="lineNum">     231</span> <span class="tlaUNC">           0 :     if (state is SelfieCapturingSuccessState) {</span></span>
<span id="L232"><span class="lineNum">     232</span> <span class="tlaUNC">           0 :       final String? imageId = state.imageId;</span></span>
<span id="L233"><span class="lineNum">     233</span> <span class="tlaUNC">           0 :       commonLog('imageId: $imageId');</span></span>
<span id="L234"><span class="lineNum">     234</span> <span class="tlaUNC">           0 :       FaceOtpMatchingProcessScreen.pushReplacementNamed(</span></span>
<span id="L235"><span class="lineNum">     235</span>              :         selfieImageId: imageId,</span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaUNC">           0 :         flowType: widget.arg.flowType,</span></span>
<span id="L237"><span class="lineNum">     237</span>              :       );</span>
<span id="L238"><span class="lineNum">     238</span>              :       return;</span>
<span id="L239"><span class="lineNum">     239</span>              :     }</span>
<span id="L240"><span class="lineNum">     240</span>              : </span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaUNC">           0 :     if (state is SelfieCapturingFailedState) {</span></span>
<span id="L242"><span class="lineNum">     242</span> <span class="tlaUNC">           0 :       _handleSelfieCapturingFail(state.reason);</span></span>
<span id="L243"><span class="lineNum">     243</span>              :       return;</span>
<span id="L244"><span class="lineNum">     244</span>              :     }</span>
<span id="L245"><span class="lineNum">     245</span>              :   }</span>
<span id="L246"><span class="lineNum">     246</span>              : </span>
<span id="L247"><span class="lineNum">     247</span> <span class="tlaUNC">           0 :   void _handleSelfieCapturingFail(TVSDKFailReason? reason) {</span></span>
<span id="L248"><span class="lineNum">     248</span> <span class="tlaUNC">           0 :     if (reason == TVSDKFailReason.userCancelled) {</span></span>
<span id="L249"><span class="lineNum">     249</span>              :       // do nothing if user cancel capturing by back button</span>
<span id="L250"><span class="lineNum">     250</span>              :       // user can press CTA button to capture again</span>
<span id="L251"><span class="lineNum">     251</span>              :       // ref: https://trustingsocial1.atlassian.net/browse/EMA-4835</span>
<span id="L252"><span class="lineNum">     252</span>              :       return;</span>
<span id="L253"><span class="lineNum">     253</span>              :     }</span>
<span id="L254"><span class="lineNum">     254</span>              : </span>
<span id="L255"><span class="lineNum">     255</span> <span class="tlaUNC">           0 :     EkycErrorScreen.pushNamed(</span></span>
<span id="L256"><span class="lineNum">     256</span>              :       errorReason: reason,</span>
<span id="L257"><span class="lineNum">     257</span> <span class="tlaUNC">           0 :       onActionButtonTap: (BuildContext context) {</span></span>
<span id="L258"><span class="lineNum">     258</span>              :         //close error screen</span>
<span id="L259"><span class="lineNum">     259</span> <span class="tlaUNC">           0 :         navigatorContext?.pop();</span></span>
<span id="L260"><span class="lineNum">     260</span>              : </span>
<span id="L261"><span class="lineNum">     261</span> <span class="tlaUNC">           0 :         handleRetrySelfieCapturingIfEkycHasError(</span></span>
<span id="L262"><span class="lineNum">     262</span>              :           context: context,</span>
<span id="L263"><span class="lineNum">     263</span>              :           reason: reason,</span>
<span id="L264"><span class="lineNum">     264</span> <span class="tlaUNC">           0 :           cubit: _selfieCapturingCubit,</span></span>
<span id="L265"><span class="lineNum">     265</span>              :         );</span>
<span id="L266"><span class="lineNum">     266</span>              :       },</span>
<span id="L267"><span class="lineNum">     267</span>              :     );</span>
<span id="L268"><span class="lineNum">     268</span>              :   }</span>
<span id="L269"><span class="lineNum">     269</span>              : </span>
<span id="L270"><span class="lineNum">     270</span> <span class="tlaUNC">           0 :   void _startFaceOTPCapturing() {</span></span>
<span id="L271"><span class="lineNum">     271</span>              :     // handle Mock Test Face OTP</span>
<span id="L272"><span class="lineNum">     272</span> <span class="tlaUNC">           0 :     if (isEnableMockFaceOtpTestFlow) {</span></span>
<span id="L273"><span class="lineNum">     273</span> <span class="tlaUNC">           0 :       requestStoragePermissionForMockTest();</span></span>
<span id="L274"><span class="lineNum">     274</span>              :       return;</span>
<span id="L275"><span class="lineNum">     275</span>              :     }</span>
<span id="L276"><span class="lineNum">     276</span>              :     // the SDK already initial and start but cancel it previously (back button)</span>
<span id="L277"><span class="lineNum">     277</span>              :     // now we can start it again without request permission or init SDK</span>
<span id="L278"><span class="lineNum">     278</span> <span class="tlaUNC">           0 :     if (_faceOTPInstructionCubit.state is FaceOTPInstructionStartNow) {</span></span>
<span id="L279"><span class="lineNum">     279</span> <span class="tlaUNC">           0 :       _selfieCapturingCubit.startSelfieCapturing();</span></span>
<span id="L280"><span class="lineNum">     280</span>              :       return;</span>
<span id="L281"><span class="lineNum">     281</span>              :     }</span>
<span id="L282"><span class="lineNum">     282</span>              :     // request permission to init SDK</span>
<span id="L283"><span class="lineNum">     283</span> <span class="tlaUNC">           0 :     _permissionCubit.requestPermission();</span></span>
<span id="L284"><span class="lineNum">     284</span>              :   }</span>
<span id="L285"><span class="lineNum">     285</span>              : </span>
<span id="L286"><span class="lineNum">     286</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L287"><span class="lineNum">     287</span>              :   void onIgnoreMockTest() {</span>
<span id="L288"><span class="lineNum">     288</span> <span class="tlaUNC">           0 :     _startFaceOTPCapturing();</span></span>
<span id="L289"><span class="lineNum">     289</span>              :   }</span>
<span id="L290"><span class="lineNum">     290</span>              : </span>
<span id="L291"><span class="lineNum">     291</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L292"><span class="lineNum">     292</span>              :   void onMockDataIsReady() {</span>
<span id="L293"><span class="lineNum">     293</span>              :     /// get image id from mock data</span>
<span id="L294"><span class="lineNum">     294</span> <span class="tlaUNC">           0 :     FaceOtpMatchingProcessScreen.pushReplacementNamed(</span></span>
<span id="L295"><span class="lineNum">     295</span> <span class="tlaUNC">           0 :       selfieImageId: mockFaceOTPImageId,</span></span>
<span id="L296"><span class="lineNum">     296</span> <span class="tlaUNC">           0 :       flowType: widget.arg.flowType,</span></span>
<span id="L297"><span class="lineNum">     297</span>              :     );</span>
<span id="L298"><span class="lineNum">     298</span>              :   }</span>
<span id="L299"><span class="lineNum">     299</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
