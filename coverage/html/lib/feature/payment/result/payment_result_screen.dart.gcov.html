<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/payment/result/payment_result_screen.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/payment/result">lib/feature/payment/result</a> - payment_result_screen.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">3.8&nbsp;%</td>
            <td class="headerCovTableEntry">160</td>
            <td class="headerCovTableEntry">6</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:async';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter/material.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/base/page_base.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/common_package/common_package.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'package:flutter_common_package/global_key_provider.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import 'package:flutter_common_package/resources/ui_strings.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import 'package:flutter_common_package/util/extension.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../base/evo_page_state_base.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../data/repository/checkout_repo.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../data/response/payment_result_transaction_entity.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../../data/response/promotion_info_entity.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../../model/transaction_status_model.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../../prepare_for_app_initiation.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../../resources/resources.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../../../util/task_polling_handler/polling_task.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../../../widget/cta_with_powered_by_widget.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../../../widget/evo_loading_widget.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../../emi_management/detail_screen/emi_management_detail_screen.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import '../../feature_toggle.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import '../../payment/payment_config.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import '../../payment/result/bloc/payment_result_cubit.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import '../../payment/result/bloc/payment_result_state.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : import '../../payment/utils/dialog_enable_pos_limit_handler.dart';</span>
<span id="L27"><span class="lineNum">      27</span>              : import '../../transaction_detail/transaction_detail_screen.dart';</span>
<span id="L28"><span class="lineNum">      28</span>              : import '../../../util/task_polling_handler/task_polling_handler.dart';</span>
<span id="L29"><span class="lineNum">      29</span>              : import '../utils/payment_navigate_helper_mixin.dart';</span>
<span id="L30"><span class="lineNum">      30</span>              : import '../utils/payment_with_emi_utils.dart';</span>
<span id="L31"><span class="lineNum">      31</span>              : import '../widget/order_summary_widget/order_summary_widget.dart';</span>
<span id="L32"><span class="lineNum">      32</span>              : import '../widget/payment_result_and_amount_title_widget.dart';</span>
<span id="L33"><span class="lineNum">      33</span>              : import '../widget/payment_result_container_widget.dart';</span>
<span id="L34"><span class="lineNum">      34</span>              : import '../widget/payment_result_note_widget.dart';</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span>              : class PaymentResultArg extends PageBaseArg {</span>
<span id="L37"><span class="lineNum">      37</span>              :   final bool needPollingProcessingStatus;</span>
<span id="L38"><span class="lineNum">      38</span>              :   final String? transactionId;</span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           2 :   PaymentResultArg({</span></span>
<span id="L41"><span class="lineNum">      41</span>              :     required this.transactionId,</span>
<span id="L42"><span class="lineNum">      42</span>              :     this.needPollingProcessingStatus = false,</span>
<span id="L43"><span class="lineNum">      43</span>              :   });</span>
<span id="L44"><span class="lineNum">      44</span>              : }</span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span>              : class PaymentResultScreen extends PageBase {</span>
<span id="L47"><span class="lineNum">      47</span>              :   final PaymentResultArg? arg;</span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           1 :   const PaymentResultScreen({super.key, this.arg});</span></span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           1 :   static void pushReplacementNamed({</span></span>
<span id="L52"><span class="lineNum">      52</span>              :     required String? transactionId,</span>
<span id="L53"><span class="lineNum">      53</span>              :     bool needPollingProcessingStatus = false,</span>
<span id="L54"><span class="lineNum">      54</span>              :   }) {</span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaGNC">           2 :     return navigatorContext?.pushReplacementNamed(</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           1 :       Screen.paymentResultScreen.name,</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           1 :       extra: PaymentResultArg(</span></span>
<span id="L58"><span class="lineNum">      58</span>              :         transactionId: transactionId,</span>
<span id="L59"><span class="lineNum">      59</span>              :         needPollingProcessingStatus: needPollingProcessingStatus,</span>
<span id="L60"><span class="lineNum">      60</span>              :       ),</span>
<span id="L61"><span class="lineNum">      61</span>              :     );</span>
<span id="L62"><span class="lineNum">      62</span>              :   }</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :   State&lt;PaymentResultScreen&gt; createState() =&gt; _PaymentResultScreenState();</span></span>
<span id="L66"><span class="lineNum">      66</span>              : </span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L68"><span class="lineNum">      68</span>              :   EventTrackingScreenId get eventTrackingScreenId =&gt; EventTrackingScreenId.undefined;</span>
<span id="L69"><span class="lineNum">      69</span>              : </span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :   RouteSettings get routeSettings =&gt; RouteSettings(name: Screen.paymentResultScreen.routeName);</span></span>
<span id="L72"><span class="lineNum">      72</span>              : }</span>
<span id="L73"><span class="lineNum">      73</span>              : </span>
<span id="L74"><span class="lineNum">      74</span>              : class _PaymentResultScreenState extends EvoPageStateBase&lt;PaymentResultScreen&gt;</span>
<span id="L75"><span class="lineNum">      75</span>              :     with PaymentNavigationHelperMixin {</span>
<span id="L76"><span class="lineNum">      76</span>              :   TaskPollingHandler? _cashBackResultPollingHandler;</span>
<span id="L77"><span class="lineNum">      77</span>              :   final PaymentResultCubit _paymentResultCubit = PaymentResultCubit(</span>
<span id="L78"><span class="lineNum">      78</span>              :     getIt.get&lt;CheckOutRepo&gt;(),</span>
<span id="L79"><span class="lineNum">      79</span>              :     getIt.get&lt;AppState&gt;(),</span>
<span id="L80"><span class="lineNum">      80</span>              :   );</span>
<span id="L81"><span class="lineNum">      81</span>              : </span>
<span id="L82"><span class="lineNum">      82</span>              :   Timer? _pollingTimeOutTimer;</span>
<span id="L83"><span class="lineNum">      83</span>              :   bool _needPollingProcessingStatus = false;</span>
<span id="L84"><span class="lineNum">      84</span>              :   PollingTask? _cashBackPollingTask;</span>
<span id="L85"><span class="lineNum">      85</span>              :   bool _shouldPollForCashBackResult = true;</span>
<span id="L86"><span class="lineNum">      86</span>              :   bool _isAppOnForeGround = true;</span>
<span id="L87"><span class="lineNum">      87</span>              :   final RefreshController _refreshController = RefreshController();</span>
<span id="L88"><span class="lineNum">      88</span>              :   final DialogEnablePosLimitHandler _dialogEnablePosLimitHandler = DialogEnablePosLimitHandler();</span>
<span id="L89"><span class="lineNum">      89</span>              : </span>
<span id="L90"><span class="lineNum">      90</span>              :   //wait at least 5s before polling for cashback result, to make sure user has read the payment info</span>
<span id="L91"><span class="lineNum">      91</span>              :   static const int _waitTimeBeforePollingForCashBackResultInSec = 5;</span>
<span id="L92"><span class="lineNum">      92</span>              : </span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L94"><span class="lineNum">      94</span>              :   void initState() {</span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :     super.initState();</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :     initData();</span></span>
<span id="L97"><span class="lineNum">      97</span>              : </span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :     WidgetsBinding.instance.addPostFrameCallback((_) async {</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :       final PaymentWithEMIUtils paymentWithEMIUtils = PaymentWithEMIUtils();</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :       await paymentWithEMIUtils.updateFlagPostLimitWarning();</span></span>
<span id="L101"><span class="lineNum">     101</span>              :     });</span>
<span id="L102"><span class="lineNum">     102</span>              :   }</span>
<span id="L103"><span class="lineNum">     103</span>              : </span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L105"><span class="lineNum">     105</span>              :   Widget getContentWidget(BuildContext context) {</span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :     return Scaffold(</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :       backgroundColor: evoColors.background,</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :       body: SafeArea(</span></span>
<span id="L109"><span class="lineNum">     109</span>              :         top: false,</span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :         child: PopScope(</span></span>
<span id="L111"><span class="lineNum">     111</span>              :           canPop: false,</span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :           child: BlocProvider&lt;PaymentResultCubit&gt;(</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :             create: (_) =&gt; _paymentResultCubit,</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :             child: BlocConsumer&lt;PaymentResultCubit, PaymentResultState&gt;(</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :               listener: (_, PaymentResultState state) {</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :                 _handlePaymentResultListener(state);</span></span>
<span id="L117"><span class="lineNum">     117</span>              :               },</span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :               builder: (_, PaymentResultState state) {</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :                 if (state is PaymentResultLoadingState) {</span></span>
<span id="L120"><span class="lineNum">     120</span>              :                   return const EvoLoadingWidget();</span>
<span id="L121"><span class="lineNum">     121</span>              :                 }</span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :                 if (state is PaymentResultLoadedState) {</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :                   return _itemBody(state.transaction);</span></span>
<span id="L124"><span class="lineNum">     124</span>              :                 }</span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :                 if (state is PaymentResultPosLimitState) {</span></span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :                   return _itemBody(state.transaction);</span></span>
<span id="L127"><span class="lineNum">     127</span>              :                 }</span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :                 if (state is PaymentResultErrorState) {</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :                   return _itemBody(state.savedTransaction);</span></span>
<span id="L130"><span class="lineNum">     130</span>              :                 }</span>
<span id="L131"><span class="lineNum">     131</span>              :                 return const SizedBox.shrink();</span>
<span id="L132"><span class="lineNum">     132</span>              :               },</span>
<span id="L133"><span class="lineNum">     133</span>              :             ),</span>
<span id="L134"><span class="lineNum">     134</span>              :           ),</span>
<span id="L135"><span class="lineNum">     135</span>              :         ),</span>
<span id="L136"><span class="lineNum">     136</span>              :       ),</span>
<span id="L137"><span class="lineNum">     137</span>              :     );</span>
<span id="L138"><span class="lineNum">     138</span>              :   }</span>
<span id="L139"><span class="lineNum">     139</span>              : </span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaUNC">           0 :   Widget _itemBody(PaymentResultTransactionEntity? transaction) {</span></span>
<span id="L141"><span class="lineNum">     141</span>              :     final TransactionStatusModel? status =</span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaUNC">           0 :         TransactionStatusModel.formatStatusString(transaction?.status);</span></span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaUNC">           0 :     final bool isEmiTransaction = transaction?.isEmiTransaction ?? false;</span></span>
<span id="L144"><span class="lineNum">     144</span>              : </span>
<span id="L145"><span class="lineNum">     145</span> <span class="tlaUNC">           0 :     return Column(</span></span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaUNC">           0 :       children: &lt;Widget&gt;[</span></span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :         Expanded(</span></span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaUNC">           0 :           child: PaymentResultContainerWidget(</span></span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :               controller: _refreshController,</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :               onRefresh: () {</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :                 _getTransactionDetail(isRefresh: true);</span></span>
<span id="L152"><span class="lineNum">     152</span>              :               },</span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :               child: Column(</span></span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaUNC">           0 :                 children: &lt;Widget&gt;[</span></span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaUNC">           0 :                   PaymentResultTitleAndAmountWidget(</span></span>
<span id="L156"><span class="lineNum">     156</span>              :                     status: status ?? TransactionStatusModel.processing,</span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :                     amount: transaction?.userChargeAmount ?? 0,</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :                     merchantName: transaction?.storeInfo?.merchantName ?? '',</span></span>
<span id="L159"><span class="lineNum">     159</span>              :                     isEmiTransaction: isEmiTransaction,</span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :                     onMoreDetail: () {</span></span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaUNC">           0 :                       onTapPaymentResultDetail(transaction?.id);</span></span>
<span id="L162"><span class="lineNum">     162</span>              :                     },</span>
<span id="L163"><span class="lineNum">     163</span>              :                   ),</span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :                   PaymentResultNoteWidget(</span></span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaUNC">           0 :                     args: PaymentResultNoteWidgetArgs.fromTransaction(</span></span>
<span id="L166"><span class="lineNum">     166</span>              :                       transaction,</span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaUNC">           0 :                       onTapHeaderInfo: () {</span></span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaUNC">           0 :                         _goEmiManagementScreen(transaction);</span></span>
<span id="L169"><span class="lineNum">     169</span>              :                       },</span>
<span id="L170"><span class="lineNum">     170</span>              :                     ),</span>
<span id="L171"><span class="lineNum">     171</span>              :                   ),</span>
<span id="L172"><span class="lineNum">     172</span> <span class="tlaUNC">           0 :                   _buildPaymentResultContent(transaction),</span></span>
<span id="L173"><span class="lineNum">     173</span>              :                 ],</span>
<span id="L174"><span class="lineNum">     174</span>              :               )),</span>
<span id="L175"><span class="lineNum">     175</span>              :         ),</span>
<span id="L176"><span class="lineNum">     176</span> <span class="tlaUNC">           0 :         _itemCtaAndPoweredBy(),</span></span>
<span id="L177"><span class="lineNum">     177</span>              :       ],</span>
<span id="L178"><span class="lineNum">     178</span>              :     );</span>
<span id="L179"><span class="lineNum">     179</span>              :   }</span>
<span id="L180"><span class="lineNum">     180</span>              : </span>
<span id="L181"><span class="lineNum">     181</span> <span class="tlaUNC">           0 :   void _goEmiManagementScreen(PaymentResultTransactionEntity? transaction) {</span></span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :     final FeatureToggle featureToggle = getIt.get&lt;FeatureToggle&gt;();</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaUNC">           0 :     if (!featureToggle.enableEmiManagementFeature) {</span></span>
<span id="L184"><span class="lineNum">     184</span>              :       // temporary redirect to Transaction Detail by https://trustingsocial1.atlassian.net/browse/EMA-2427</span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :       onTapPaymentResultDetail(transaction?.id);</span></span>
<span id="L186"><span class="lineNum">     186</span>              :       return;</span>
<span id="L187"><span class="lineNum">     187</span>              :     }</span>
<span id="L188"><span class="lineNum">     188</span>              : </span>
<span id="L189"><span class="lineNum">     189</span> <span class="tlaUNC">           0 :     EmiManagementDetailScreen.pushNamed(id: transaction?.emiInfo?.id);</span></span>
<span id="L190"><span class="lineNum">     190</span>              :   }</span>
<span id="L191"><span class="lineNum">     191</span>              : </span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaUNC">           0 :   Widget _buildPaymentResultContent(PaymentResultTransactionEntity? transaction) {</span></span>
<span id="L193"><span class="lineNum">     193</span> <span class="tlaUNC">           0 :     return Padding(</span></span>
<span id="L194"><span class="lineNum">     194</span>              :       padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),</span>
<span id="L195"><span class="lineNum">     195</span> <span class="tlaUNC">           0 :       child: OrderSummaryWidget(</span></span>
<span id="L196"><span class="lineNum">     196</span>              :         transactionEntity: transaction,</span>
<span id="L197"><span class="lineNum">     197</span>              :       ),</span>
<span id="L198"><span class="lineNum">     198</span>              :     );</span>
<span id="L199"><span class="lineNum">     199</span>              :   }</span>
<span id="L200"><span class="lineNum">     200</span>              : </span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaUNC">           0 :   Widget _itemCtaAndPoweredBy() {</span></span>
<span id="L202"><span class="lineNum">     202</span> <span class="tlaUNC">           0 :     return Padding(</span></span>
<span id="L203"><span class="lineNum">     203</span>              :       padding: const EdgeInsets.symmetric(horizontal: 20),</span>
<span id="L204"><span class="lineNum">     204</span> <span class="tlaUNC">           0 :       child: CTAWithPoweredByWidget(</span></span>
<span id="L205"><span class="lineNum">     205</span>              :         text: EvoStrings.moveToHome,</span>
<span id="L206"><span class="lineNum">     206</span> <span class="tlaUNC">           0 :         onPressed: () {</span></span>
<span id="L207"><span class="lineNum">     207</span>              :           final bool isHandlePosLimitShowing =</span>
<span id="L208"><span class="lineNum">     208</span> <span class="tlaUNC">           0 :               _paymentResultCubit.state is PaymentResultPosLimitState &amp;&amp;</span></span>
<span id="L209"><span class="lineNum">     209</span> <span class="tlaUNC">           0 :                   !_dialogEnablePosLimitHandler.isDialogEnablePosLimitShown;</span></span>
<span id="L210"><span class="lineNum">     210</span>              : </span>
<span id="L211"><span class="lineNum">     211</span>              :           if (isHandlePosLimitShowing) {</span>
<span id="L212"><span class="lineNum">     212</span> <span class="tlaUNC">           0 :             _dialogEnablePosLimitHandler.handleDialogEnablePosLimitShowing(</span></span>
<span id="L213"><span class="lineNum">     213</span> <span class="tlaUNC">           0 :               onClickNegative: handlePaymentFlowComplete,</span></span>
<span id="L214"><span class="lineNum">     214</span> <span class="tlaUNC">           0 :               onClickPositive: openPosSetupUserGuild,</span></span>
<span id="L215"><span class="lineNum">     215</span>              :             );</span>
<span id="L216"><span class="lineNum">     216</span>              :             return;</span>
<span id="L217"><span class="lineNum">     217</span>              :           }</span>
<span id="L218"><span class="lineNum">     218</span>              : </span>
<span id="L219"><span class="lineNum">     219</span> <span class="tlaUNC">           0 :           handlePaymentFlowComplete();</span></span>
<span id="L220"><span class="lineNum">     220</span>              :         },</span>
<span id="L221"><span class="lineNum">     221</span>              :       ),</span>
<span id="L222"><span class="lineNum">     222</span>              :     );</span>
<span id="L223"><span class="lineNum">     223</span>              :   }</span>
<span id="L224"><span class="lineNum">     224</span>              : </span>
<span id="L225"><span class="lineNum">     225</span> <span class="tlaUNC">           0 :   void onTapPaymentResultDetail(String? transactionId) {</span></span>
<span id="L226"><span class="lineNum">     226</span> <span class="tlaUNC">           0 :     TransactionDetailScreen.pushNamed(transactionId: transactionId);</span></span>
<span id="L227"><span class="lineNum">     227</span>              :   }</span>
<span id="L228"><span class="lineNum">     228</span>              : </span>
<span id="L229"><span class="lineNum">     229</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L230"><span class="lineNum">     230</span>              :   void initData() {</span>
<span id="L231"><span class="lineNum">     231</span> <span class="tlaUNC">           0 :     _clearPaymentSharedData();</span></span>
<span id="L232"><span class="lineNum">     232</span> <span class="tlaUNC">           0 :     _getTransactionDetail();</span></span>
<span id="L233"><span class="lineNum">     233</span> <span class="tlaUNC">           0 :     _setUpPollingTimeOut();</span></span>
<span id="L234"><span class="lineNum">     234</span>              :   }</span>
<span id="L235"><span class="lineNum">     235</span>              : </span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaUNC">           0 :   void _clearPaymentSharedData() {</span></span>
<span id="L237"><span class="lineNum">     237</span> <span class="tlaUNC">           0 :     _paymentResultCubit.clearPaymentSharedData();</span></span>
<span id="L238"><span class="lineNum">     238</span>              :   }</span>
<span id="L239"><span class="lineNum">     239</span>              : </span>
<span id="L240"><span class="lineNum">     240</span> <span class="tlaUNC">           0 :   void _getTransactionDetail({bool isRefresh = false}) {</span></span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaUNC">           0 :     _paymentResultCubit.getPaymentResult(</span></span>
<span id="L242"><span class="lineNum">     242</span> <span class="tlaUNC">           0 :       transactionId: widget.arg?.transactionId,</span></span>
<span id="L243"><span class="lineNum">     243</span>              :       isRefresh: isRefresh,</span>
<span id="L244"><span class="lineNum">     244</span>              :     );</span>
<span id="L245"><span class="lineNum">     245</span>              :   }</span>
<span id="L246"><span class="lineNum">     246</span>              : </span>
<span id="L247"><span class="lineNum">     247</span> <span class="tlaUNC">           0 :   void _setUpPollingTimeOut() {</span></span>
<span id="L248"><span class="lineNum">     248</span> <span class="tlaUNC">           0 :     _needPollingProcessingStatus = widget.arg?.needPollingProcessingStatus ?? false;</span></span>
<span id="L249"><span class="lineNum">     249</span> <span class="tlaUNC">           0 :     if (_needPollingProcessingStatus) {</span></span>
<span id="L250"><span class="lineNum">     250</span> <span class="tlaUNC">           0 :       _pollingTimeOutTimer = Timer(</span></span>
<span id="L251"><span class="lineNum">     251</span>              :         const Duration(seconds: PaymentConfig.timeOutPollingGetCheckOutInSec),</span>
<span id="L252"><span class="lineNum">     252</span> <span class="tlaUNC">           0 :         () {</span></span>
<span id="L253"><span class="lineNum">     253</span> <span class="tlaUNC">           0 :           _markDoNotNeedPollingProcessingStatus();</span></span>
<span id="L254"><span class="lineNum">     254</span>              :         },</span>
<span id="L255"><span class="lineNum">     255</span>              :       );</span>
<span id="L256"><span class="lineNum">     256</span>              :     }</span>
<span id="L257"><span class="lineNum">     257</span>              :   }</span>
<span id="L258"><span class="lineNum">     258</span>              : </span>
<span id="L259"><span class="lineNum">     259</span> <span class="tlaUNC">           0 :   void _markDoNotNeedPollingProcessingStatus() {</span></span>
<span id="L260"><span class="lineNum">     260</span> <span class="tlaUNC">           0 :     _needPollingProcessingStatus = false;</span></span>
<span id="L261"><span class="lineNum">     261</span>              :   }</span>
<span id="L262"><span class="lineNum">     262</span>              : </span>
<span id="L263"><span class="lineNum">     263</span> <span class="tlaUNC">           0 :   void _cancelPollingTimeOut() {</span></span>
<span id="L264"><span class="lineNum">     264</span> <span class="tlaUNC">           0 :     _pollingTimeOutTimer?.cancel();</span></span>
<span id="L265"><span class="lineNum">     265</span>              :   }</span>
<span id="L266"><span class="lineNum">     266</span>              : </span>
<span id="L267"><span class="lineNum">     267</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _handlePaymentResultListener(PaymentResultState state) async {</span></span>
<span id="L268"><span class="lineNum">     268</span> <span class="tlaUNC">           0 :     if (_refreshController.isRefresh) {</span></span>
<span id="L269"><span class="lineNum">     269</span> <span class="tlaUNC">           0 :       _refreshController.refreshCompleted();</span></span>
<span id="L270"><span class="lineNum">     270</span>              :     }</span>
<span id="L271"><span class="lineNum">     271</span>              : </span>
<span id="L272"><span class="lineNum">     272</span> <span class="tlaUNC">           0 :     if (state is PaymentResultLoadedState) {</span></span>
<span id="L273"><span class="lineNum">     273</span>              :       final TransactionStatusModel? status =</span>
<span id="L274"><span class="lineNum">     274</span> <span class="tlaUNC">           0 :           TransactionStatusModel.formatStatusString(state.transaction?.status);</span></span>
<span id="L275"><span class="lineNum">     275</span> <span class="tlaUNC">           0 :       if (status == TransactionStatusModel.processing) {</span></span>
<span id="L276"><span class="lineNum">     276</span> <span class="tlaUNC">           0 :         _handlePollingGetTransactionDetailIfNeeded();</span></span>
<span id="L277"><span class="lineNum">     277</span>              :       } else {</span>
<span id="L278"><span class="lineNum">     278</span> <span class="tlaUNC">           0 :         _markDoNotNeedPollingProcessingStatus();</span></span>
<span id="L279"><span class="lineNum">     279</span> <span class="tlaUNC">           0 :         _cancelPollingTimeOut();</span></span>
<span id="L280"><span class="lineNum">     280</span>              :       }</span>
<span id="L281"><span class="lineNum">     281</span> <span class="tlaUNC">           0 :       await _startPollingCashBackStatusIfNeeded(</span></span>
<span id="L282"><span class="lineNum">     282</span> <span class="tlaUNC">           0 :           state.transaction?.id, state.transaction?.promotionInfo, status);</span></span>
<span id="L283"><span class="lineNum">     283</span> <span class="tlaUNC">           0 :       await _stopPollingCashBackStatusIfNeeded(status);</span></span>
<span id="L284"><span class="lineNum">     284</span>              :       return;</span>
<span id="L285"><span class="lineNum">     285</span>              :     }</span>
<span id="L286"><span class="lineNum">     286</span>              : </span>
<span id="L287"><span class="lineNum">     287</span> <span class="tlaUNC">           0 :     if (state is PaymentResultErrorState) {</span></span>
<span id="L288"><span class="lineNum">     288</span> <span class="tlaUNC">           0 :       showSnackBarError(state.errorUIModel.userMessage ?? CommonStrings.otherGenericErrorMessage);</span></span>
<span id="L289"><span class="lineNum">     289</span>              :       return;</span>
<span id="L290"><span class="lineNum">     290</span>              :     }</span>
<span id="L291"><span class="lineNum">     291</span>              : </span>
<span id="L292"><span class="lineNum">     292</span> <span class="tlaUNC">           0 :     if (state is PaymentResultPosLimitState) {</span></span>
<span id="L293"><span class="lineNum">     293</span> <span class="tlaUNC">           0 :       _dialogEnablePosLimitHandler.delayToHandleDialogEnablePosLimitShowing(</span></span>
<span id="L294"><span class="lineNum">     294</span> <span class="tlaUNC">           0 :         onClickNegative: handlePaymentFlowComplete,</span></span>
<span id="L295"><span class="lineNum">     295</span> <span class="tlaUNC">           0 :         onClickPositive: openPosSetupUserGuild,</span></span>
<span id="L296"><span class="lineNum">     296</span>              :       );</span>
<span id="L297"><span class="lineNum">     297</span>              :       return;</span>
<span id="L298"><span class="lineNum">     298</span>              :     }</span>
<span id="L299"><span class="lineNum">     299</span>              :   }</span>
<span id="L300"><span class="lineNum">     300</span>              : </span>
<span id="L301"><span class="lineNum">     301</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _handlePollingGetTransactionDetailIfNeeded() async {</span></span>
<span id="L302"><span class="lineNum">     302</span> <span class="tlaUNC">           0 :     await Future&lt;void&gt;.delayed(</span></span>
<span id="L303"><span class="lineNum">     303</span>              :       const Duration(seconds: PaymentConfig.intervalPollingGetCheckOutDetailInSec),</span>
<span id="L304"><span class="lineNum">     304</span>              :     );</span>
<span id="L305"><span class="lineNum">     305</span> <span class="tlaUNC">           0 :     if (_needPollingProcessingStatus &amp;&amp; _paymentResultCubit.state is! PaymentResultLoadingState) {</span></span>
<span id="L306"><span class="lineNum">     306</span> <span class="tlaUNC">           0 :       _getTransactionDetail(isRefresh: true);</span></span>
<span id="L307"><span class="lineNum">     307</span>              :     }</span>
<span id="L308"><span class="lineNum">     308</span>              :   }</span>
<span id="L309"><span class="lineNum">     309</span>              : </span>
<span id="L310"><span class="lineNum">     310</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L311"><span class="lineNum">     311</span>              :   void listenNetworkHandler(bool hasInternet) {</span>
<span id="L312"><span class="lineNum">     312</span>              :     if (hasInternet) {</span>
<span id="L313"><span class="lineNum">     313</span>              :       // after re-connect, check condition to continue polling if needed</span>
<span id="L314"><span class="lineNum">     314</span> <span class="tlaUNC">           0 :       _handlePollingGetTransactionDetailIfNeeded();</span></span>
<span id="L315"><span class="lineNum">     315</span>              :     }</span>
<span id="L316"><span class="lineNum">     316</span>              :   }</span>
<span id="L317"><span class="lineNum">     317</span>              : </span>
<span id="L318"><span class="lineNum">     318</span>              :   //refer comment https://trustingsocial1.atlassian.net/browse/EMA-6201?focusedCommentId=175844</span>
<span id="L319"><span class="lineNum">     319</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _startPollingCashBackStatusIfNeeded(</span></span>
<span id="L320"><span class="lineNum">     320</span>              :       String? transactionId, PromotionInfoEntity? promotionInfoEntity, TransactionStatusModel? status) async {</span>
<span id="L321"><span class="lineNum">     321</span> <span class="tlaUNC">           0 :     if (!getIt&lt;FeatureToggle&gt;().enableInstantCashbackFeature) {</span></span>
<span id="L322"><span class="lineNum">     322</span>              :       return;</span>
<span id="L323"><span class="lineNum">     323</span>              :     }</span>
<span id="L324"><span class="lineNum">     324</span> <span class="tlaUNC">           0 :     final bool isPaymentWithCashBackVoucher = (promotionInfoEntity?.cashbackAmount ?? 0) &gt; 0;</span></span>
<span id="L325"><span class="lineNum">     325</span>              :     if (transactionId != null &amp;&amp;</span>
<span id="L326"><span class="lineNum">     326</span>              :         isPaymentWithCashBackVoucher &amp;&amp;</span>
<span id="L327"><span class="lineNum">     327</span> <span class="tlaUNC">           0 :         _cashBackResultPollingHandler == null &amp;&amp;</span></span>
<span id="L328"><span class="lineNum">     328</span> <span class="tlaUNC">           0 :         (status == TransactionStatusModel.processing || status == TransactionStatusModel.success)) {</span></span>
<span id="L329"><span class="lineNum">     329</span> <span class="tlaUNC">           0 :       _cashBackResultPollingHandler = getIt&lt;TaskPollingHandler&gt;();</span></span>
<span id="L330"><span class="lineNum">     330</span> <span class="tlaUNC">           0 :       final DateTime taskCreatedTIme = DateTime.now();</span></span>
<span id="L331"><span class="lineNum">     331</span>              :       //save task to storage to poll later if needed</span>
<span id="L332"><span class="lineNum">     332</span> <span class="tlaUNC">           0 :       await _cashBackResultPollingHandler?.saveNewTask(</span></span>
<span id="L333"><span class="lineNum">     333</span>              :         transactionId,</span>
<span id="L334"><span class="lineNum">     334</span>              :         PollingTaskType.cashBackResult,</span>
<span id="L335"><span class="lineNum">     335</span>              :         taskCreatedTIme,</span>
<span id="L336"><span class="lineNum">     336</span>              :       );</span>
<span id="L337"><span class="lineNum">     337</span>              :       //create and add task to polling handler</span>
<span id="L338"><span class="lineNum">     338</span> <span class="tlaUNC">           0 :       _cashBackPollingTask = _cashBackResultPollingHandler?.createPollingTask(</span></span>
<span id="L339"><span class="lineNum">     339</span>              :         taskId: transactionId,</span>
<span id="L340"><span class="lineNum">     340</span>              :         type: PollingTaskType.cashBackResult,</span>
<span id="L341"><span class="lineNum">     341</span>              :         createdTime: taskCreatedTIme,</span>
<span id="L342"><span class="lineNum">     342</span>              :       );</span>
<span id="L343"><span class="lineNum">     343</span> <span class="tlaUNC">           0 :       Future&lt;void&gt;.delayed(Duration(seconds: _waitTimeBeforePollingForCashBackResultInSec), () {</span></span>
<span id="L344"><span class="lineNum">     344</span> <span class="tlaUNC">           0 :         if (mounted &amp;&amp; _shouldPollForCashBackResult == true &amp;&amp; isTopVisible() &amp;&amp; _isAppOnForeGround) {</span></span>
<span id="L345"><span class="lineNum">     345</span>              :           //only start polling in case of app is in foreground and top visible, and the transaction is not failed</span>
<span id="L346"><span class="lineNum">     346</span> <span class="tlaUNC">           0 :           _cashBackResultPollingHandler?.startPollingAllTasks();</span></span>
<span id="L347"><span class="lineNum">     347</span>              :         }</span>
<span id="L348"><span class="lineNum">     348</span>              :       });</span>
<span id="L349"><span class="lineNum">     349</span>              :     }</span>
<span id="L350"><span class="lineNum">     350</span>              :   }</span>
<span id="L351"><span class="lineNum">     351</span>              : </span>
<span id="L352"><span class="lineNum">     352</span>              :   //refer comment https://trustingsocial1.atlassian.net/browse/EMA-6201?focusedCommentId=175844</span>
<span id="L353"><span class="lineNum">     353</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _stopPollingCashBackStatusIfNeeded(TransactionStatusModel? status) async {</span></span>
<span id="L354"><span class="lineNum">     354</span>              :     //to handle the case that has transaction result failure when user is still in the screen,</span>
<span id="L355"><span class="lineNum">     355</span>              :     //so stop polling for cashback result to avoid unnecessary polling api call</span>
<span id="L356"><span class="lineNum">     356</span> <span class="tlaUNC">           0 :     if (!getIt&lt;FeatureToggle&gt;().enableInstantCashbackFeature) {</span></span>
<span id="L357"><span class="lineNum">     357</span>              :       return;</span>
<span id="L358"><span class="lineNum">     358</span>              :     }</span>
<span id="L359"><span class="lineNum">     359</span> <span class="tlaUNC">           0 :     if(status != TransactionStatusModel.processing</span></span>
<span id="L360"><span class="lineNum">     360</span> <span class="tlaUNC">           0 :         &amp;&amp; status != TransactionStatusModel.success) {</span></span>
<span id="L361"><span class="lineNum">     361</span>              :       //set this flag to handle the case that the polling is not yet started</span>
<span id="L362"><span class="lineNum">     362</span> <span class="tlaUNC">           0 :       _shouldPollForCashBackResult = false;</span></span>
<span id="L363"><span class="lineNum">     363</span>              :       //in case the polling started, just stop it and delete task</span>
<span id="L364"><span class="lineNum">     364</span> <span class="tlaUNC">           0 :       _cashBackResultPollingHandler?.stopPollingAllTasks();</span></span>
<span id="L365"><span class="lineNum">     365</span> <span class="tlaUNC">           0 :       final PollingTask? cashBackPollingTask = _cashBackPollingTask;</span></span>
<span id="L366"><span class="lineNum">     366</span>              :       if(cashBackPollingTask != null) {</span>
<span id="L367"><span class="lineNum">     367</span> <span class="tlaUNC">           0 :         await _cashBackResultPollingHandler?.deleteTask(cashBackPollingTask);</span></span>
<span id="L368"><span class="lineNum">     368</span>              :       }</span>
<span id="L369"><span class="lineNum">     369</span>              :     }</span>
<span id="L370"><span class="lineNum">     370</span>              :   }</span>
<span id="L371"><span class="lineNum">     371</span>              : </span>
<span id="L372"><span class="lineNum">     372</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L373"><span class="lineNum">     373</span>              :   void didPopNext() {</span>
<span id="L374"><span class="lineNum">     374</span> <span class="tlaUNC">           0 :     super.didPopNext();</span></span>
<span id="L375"><span class="lineNum">     375</span> <span class="tlaUNC">           0 :     _cashBackResultPollingHandler?.startPollingAllTasks();</span></span>
<span id="L376"><span class="lineNum">     376</span>              :   }</span>
<span id="L377"><span class="lineNum">     377</span>              : </span>
<span id="L378"><span class="lineNum">     378</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L379"><span class="lineNum">     379</span>              :   void didPushNext() {</span>
<span id="L380"><span class="lineNum">     380</span> <span class="tlaUNC">           0 :     super.didPushNext();</span></span>
<span id="L381"><span class="lineNum">     381</span> <span class="tlaUNC">           0 :     _cashBackResultPollingHandler?.pausePollingAllTasks();</span></span>
<span id="L382"><span class="lineNum">     382</span>              :   }</span>
<span id="L383"><span class="lineNum">     383</span>              : </span>
<span id="L384"><span class="lineNum">     384</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L385"><span class="lineNum">     385</span>              :   void dispose() {</span>
<span id="L386"><span class="lineNum">     386</span> <span class="tlaUNC">           0 :     _cashBackResultPollingHandler?.stopPollingAllTasks();</span></span>
<span id="L387"><span class="lineNum">     387</span> <span class="tlaUNC">           0 :     super.dispose();</span></span>
<span id="L388"><span class="lineNum">     388</span>              :   }</span>
<span id="L389"><span class="lineNum">     389</span>              : </span>
<span id="L390"><span class="lineNum">     390</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L391"><span class="lineNum">     391</span>              :   void onPaused() {</span>
<span id="L392"><span class="lineNum">     392</span> <span class="tlaUNC">           0 :     _cashBackResultPollingHandler?.pausePollingAllTasks();</span></span>
<span id="L393"><span class="lineNum">     393</span> <span class="tlaUNC">           0 :     _isAppOnForeGround = false;</span></span>
<span id="L394"><span class="lineNum">     394</span> <span class="tlaUNC">           0 :     super.onPaused();</span></span>
<span id="L395"><span class="lineNum">     395</span>              :   }</span>
<span id="L396"><span class="lineNum">     396</span>              : </span>
<span id="L397"><span class="lineNum">     397</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L398"><span class="lineNum">     398</span>              :   Future&lt;void&gt; onResumed() async {</span>
<span id="L399"><span class="lineNum">     399</span> <span class="tlaUNC">           0 :     _isAppOnForeGround = true;</span></span>
<span id="L400"><span class="lineNum">     400</span> <span class="tlaUNC">           0 :     if (!getIt&lt;FeatureToggle&gt;().enableInstantCashbackFeature) {</span></span>
<span id="L401"><span class="lineNum">     401</span>              :       return;</span>
<span id="L402"><span class="lineNum">     402</span>              :     }</span>
<span id="L403"><span class="lineNum">     403</span> <span class="tlaUNC">           0 :     if (isTopVisible()) {</span></span>
<span id="L404"><span class="lineNum">     404</span> <span class="tlaUNC">           0 :       _cashBackResultPollingHandler?.startPollingAllTasks();</span></span>
<span id="L405"><span class="lineNum">     405</span>              :     }</span>
<span id="L406"><span class="lineNum">     406</span> <span class="tlaUNC">           0 :     super.onResumed();</span></span>
<span id="L407"><span class="lineNum">     407</span>              :   }</span>
<span id="L408"><span class="lineNum">     408</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
