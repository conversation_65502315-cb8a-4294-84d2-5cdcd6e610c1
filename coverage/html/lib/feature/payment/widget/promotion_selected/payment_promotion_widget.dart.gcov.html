<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/payment/widget/promotion_selected/payment_promotion_widget.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/payment/widget/promotion_selected">lib/feature/payment/widget/promotion_selected</a> - payment_promotion_widget.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">39</td>
            <td class="headerCovTableEntry">39</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../../../data/response/voucher_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../../../resources/resources.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import '../../../../util/extension.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../../payment/confirm_payment/model/order_info_ui_model.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'promotion_selected_widget.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span>              : typedef GlobalOffsetReadyCallback = void Function(Offset? offset);</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : class PaymentPromotionWidget extends StatefulWidget {</span>
<span id="L12"><span class="lineNum">      12</span>              :   final VoucherEntity? selectedPromotion;</span>
<span id="L13"><span class="lineNum">      13</span>              :   final VoidCallback? onOpenPromotionList;</span>
<span id="L14"><span class="lineNum">      14</span>              :   final VoucherSelectionState voucherSelectionState;</span>
<span id="L15"><span class="lineNum">      15</span>              :   final GlobalOffsetReadyCallback? onGlobalOffsetReady;</span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaGNC">           3 :   const PaymentPromotionWidget({</span></span>
<span id="L18"><span class="lineNum">      18</span>              :     super.key,</span>
<span id="L19"><span class="lineNum">      19</span>              :     this.selectedPromotion,</span>
<span id="L20"><span class="lineNum">      20</span>              :     this.onOpenPromotionList,</span>
<span id="L21"><span class="lineNum">      21</span>              :     this.voucherSelectionState = VoucherSelectionState.noSelect,</span>
<span id="L22"><span class="lineNum">      22</span>              :     this.onGlobalOffsetReady,</span>
<span id="L23"><span class="lineNum">      23</span>              :   });</span>
<span id="L24"><span class="lineNum">      24</span>              : </span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC">           2 :   State&lt;PaymentPromotionWidget&gt; createState() =&gt; _PaymentPromotionWidgetState();</span></span>
<span id="L27"><span class="lineNum">      27</span>              : }</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span>              : class _PaymentPromotionWidgetState extends State&lt;PaymentPromotionWidget&gt; {</span>
<span id="L30"><span class="lineNum">      30</span>              :   final GlobalKey _paymentPromotionWidgetKey = GlobalKey();</span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L33"><span class="lineNum">      33</span>              :   void initState() {</span>
<span id="L34"><span class="lineNum">      34</span>              :     /// Get the widget's global offset of widget after the widget is rendered</span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           6 :     WidgetsBinding.instance.addPostFrameCallback((_) {</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaGNC">          10 :       widget.onGlobalOffsetReady?.call(_paymentPromotionWidgetKey.globalOffset);</span></span>
<span id="L37"><span class="lineNum">      37</span>              :     });</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           2 :     super.initState();</span></span>
<span id="L39"><span class="lineNum">      39</span>              :   }</span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L42"><span class="lineNum">      42</span>              :   Widget build(BuildContext context) {</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           4 :     final VoucherSelectionState voucherSelectionState = widget.voucherSelectionState;</span></span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           2 :     return GestureDetector(</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           4 :       onTap: widget.onOpenPromotionList,</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           2 :       child: Padding(</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           2 :         key: _paymentPromotionWidgetKey,</span></span>
<span id="L49"><span class="lineNum">      49</span>              :         padding: const EdgeInsets.symmetric(vertical: 16),</span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           2 :         child: PromotionSelectedWidget(</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           2 :           title: _getPromotionText(voucherSelectionState),</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaGNC">           2 :           iconPath: _getPromotionIcon(voucherSelectionState),</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           2 :           backgroundColor: _getBackgroundColor(voucherSelectionState),</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           2 :           borderColor: _getBorderColor(voucherSelectionState),</span></span>
<span id="L55"><span class="lineNum">      55</span>              :         ),</span>
<span id="L56"><span class="lineNum">      56</span>              :       ),</span>
<span id="L57"><span class="lineNum">      57</span>              :     );</span>
<span id="L58"><span class="lineNum">      58</span>              :   }</span>
<span id="L59"><span class="lineNum">      59</span>              : </span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaGNC">           2 :   String _getPromotionText(VoucherSelectionState voucherSelectionState) {</span></span>
<span id="L61"><span class="lineNum">      61</span>              :     switch (voucherSelectionState) {</span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaGNC">           2 :       case VoucherSelectionState.noSelect:</span></span>
<span id="L63"><span class="lineNum">      63</span>              :         return EvoStrings.paymentSelectPromotion;</span>
<span id="L64"><span class="lineNum">      64</span>              : </span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaGNC">           2 :       case VoucherSelectionState.invalidVoucher:</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaGNC">           2 :       case VoucherSelectionState.validVoucher:</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           6 :         return widget.selectedPromotion?.title ?? '';</span></span>
<span id="L68"><span class="lineNum">      68</span>              :     }</span>
<span id="L69"><span class="lineNum">      69</span>              :   }</span>
<span id="L70"><span class="lineNum">      70</span>              : </span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">           2 :   String _getPromotionIcon(VoucherSelectionState voucherSelectionState) {</span></span>
<span id="L72"><span class="lineNum">      72</span>              :     switch (voucherSelectionState) {</span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaGNC">           2 :       case VoucherSelectionState.invalidVoucher:</span></span>
<span id="L74"><span class="lineNum">      74</span>              :         return EvoImages.icEmiPaymentInvalidPromotion;</span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           2 :       case VoucherSelectionState.validVoucher:</span></span>
<span id="L77"><span class="lineNum">      77</span>              :         return EvoImages.icEmiPaymentHasPromotion;</span>
<span id="L78"><span class="lineNum">      78</span>              : </span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaGNC">           2 :       case VoucherSelectionState.noSelect:</span></span>
<span id="L80"><span class="lineNum">      80</span>              :         return EvoImages.icEmiPaymentNoPromotion;</span>
<span id="L81"><span class="lineNum">      81</span>              :     }</span>
<span id="L82"><span class="lineNum">      82</span>              :   }</span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">           2 :   Color _getBackgroundColor(VoucherSelectionState voucherSelectionState) {</span></span>
<span id="L85"><span class="lineNum">      85</span>              :     switch (voucherSelectionState) {</span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaGNC">           2 :       case VoucherSelectionState.validVoucher:</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           4 :         return evoColors.emiVoucherSelectedBackground;</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           2 :       case VoucherSelectionState.invalidVoucher:</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">           2 :       case VoucherSelectionState.noSelect:</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           4 :         return evoColors.background;</span></span>
<span id="L91"><span class="lineNum">      91</span>              :     }</span>
<span id="L92"><span class="lineNum">      92</span>              :   }</span>
<span id="L93"><span class="lineNum">      93</span>              : </span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           2 :   Color? _getBorderColor(VoucherSelectionState voucherSelectionState) {</span></span>
<span id="L95"><span class="lineNum">      95</span>              :     switch (voucherSelectionState) {</span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaGNC">           2 :       case VoucherSelectionState.validVoucher:</span></span>
<span id="L97"><span class="lineNum">      97</span>              :         return null;</span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaGNC">           2 :       case VoucherSelectionState.invalidVoucher:</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaGNC">           2 :         return evoColors.emiInvalidVoucherSelectedBorder;</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaGNC">           2 :       case VoucherSelectionState.noSelect:</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaGNC">           4 :         return evoColors.primary;</span></span>
<span id="L102"><span class="lineNum">     102</span>              :     }</span>
<span id="L103"><span class="lineNum">     103</span>              :   }</span>
<span id="L104"><span class="lineNum">     104</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
