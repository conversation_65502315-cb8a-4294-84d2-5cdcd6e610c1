<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/payment/emi_not_support_screen/emi_not_support_screen.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/payment/emi_not_support_screen">lib/feature/payment/emi_not_support_screen</a> - emi_not_support_screen.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">3.2&nbsp;%</td>
            <td class="headerCovTableEntry">62</td>
            <td class="headerCovTableEntry">2</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/base/page_base.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/common_package/common_package.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/global_key_provider.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../../base/evo_page_state_base.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../data/repository/checkout_repo.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../data/response/order_session_entity.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../data/response/qr_code_parse_entity.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../prepare_for_app_initiation.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../resources/resources.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../../util/ui_utils/evo_ui_utils.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../../widget/evo_appbar.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../confirm_payment/update_confirm_payment_screen/update_confirm_payment_screen.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../switch_order_to_outright_purchase/switch_order_to_outright_purchase_cubit.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../utils/active_pos_limit_handler/active_pos_limit_handler.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../utils/payment_navigate_helper_mixin.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../utils/payment_with_emi_utils.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import 'emi_not_support_body_widget.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : </span>
<span id="L23"><span class="lineNum">      23</span>              : class EmiNotSupportScreenArg extends PageBaseArg {</span>
<span id="L24"><span class="lineNum">      24</span>              :   final QrCodeParseEntity qrCodeParseEntity;</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC">           1 :   EmiNotSupportScreenArg({required this.qrCodeParseEntity});</span></span>
<span id="L27"><span class="lineNum">      27</span>              : }</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span>              : class EmiNotSupportScreen extends PageBase {</span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :   static void pushNamed({required QrCodeParseEntity qrCodeParseEntity}) {</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :     return navigatorContext?.pushNamed(</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :       Screen.emiNotSupportScreen.name,</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :       extra: EmiNotSupportScreenArg(</span></span>
<span id="L34"><span class="lineNum">      34</span>              :         qrCodeParseEntity: qrCodeParseEntity,</span>
<span id="L35"><span class="lineNum">      35</span>              :       ),</span>
<span id="L36"><span class="lineNum">      36</span>              :     );</span>
<span id="L37"><span class="lineNum">      37</span>              :   }</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span>              :   final EmiNotSupportScreenArg arg;</span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           1 :   const EmiNotSupportScreen({</span></span>
<span id="L42"><span class="lineNum">      42</span>              :     required this.arg,</span>
<span id="L43"><span class="lineNum">      43</span>              :     super.key,</span>
<span id="L44"><span class="lineNum">      44</span>              :   });</span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :   State&lt;EmiNotSupportScreen&gt; createState() =&gt; _EmiNotSupportScreenState();</span></span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L50"><span class="lineNum">      50</span>              :   EventTrackingScreenId get eventTrackingScreenId =&gt; EventTrackingScreenId.undefined;</span>
<span id="L51"><span class="lineNum">      51</span>              : </span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :   RouteSettings get routeSettings =&gt; RouteSettings(name: Screen.emiNotSupportScreen.routeName);</span></span>
<span id="L54"><span class="lineNum">      54</span>              : }</span>
<span id="L55"><span class="lineNum">      55</span>              : </span>
<span id="L56"><span class="lineNum">      56</span>              : class _EmiNotSupportScreenState extends EvoPageStateBase&lt;EmiNotSupportScreen&gt;</span>
<span id="L57"><span class="lineNum">      57</span>              :     with PaymentNavigationHelperMixin {</span>
<span id="L58"><span class="lineNum">      58</span>              :   final SwitchOrderToOutRightPurchaseCubit _switchToOutRightPaymentCubit =</span>
<span id="L59"><span class="lineNum">      59</span>              :       SwitchOrderToOutRightPurchaseCubit(</span>
<span id="L60"><span class="lineNum">      60</span>              :     checkOutRepo: getIt.get&lt;CheckOutRepo&gt;(),</span>
<span id="L61"><span class="lineNum">      61</span>              :     paymentForEmiUtils: PaymentWithEMIUtils(),</span>
<span id="L62"><span class="lineNum">      62</span>              :   );</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span>              :   final ActivePosLimitHandler _activePosLimitHandler = ActivePosLimitHandlerImpl();</span>
<span id="L65"><span class="lineNum">      65</span>              : </span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L67"><span class="lineNum">      67</span>              :   Widget getContentWidget(BuildContext context) {</span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :     return BlocProvider&lt;SwitchOrderToOutRightPurchaseCubit&gt;(</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :       create: (_) =&gt; _switchToOutRightPaymentCubit,</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :       child: BlocListener&lt;SwitchOrderToOutRightPurchaseCubit, SwitchToOutrightPaymentState&gt;(</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :         listener: (BuildContext context, SwitchToOutrightPaymentState state) {</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :           _handleSwitchToOutPaymentState(state);</span></span>
<span id="L73"><span class="lineNum">      73</span>              :         },</span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :         child: Scaffold(</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :           backgroundColor: evoColors.background,</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :           appBar: EvoAppBar(</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :             leading: CloseButton(</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :               color: evoColors.icon,</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :               onPressed: () {</span></span>
<span id="L80"><span class="lineNum">      80</span>              :                 /// back to previous screen</span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :                 navigatorContext?.pop();</span></span>
<span id="L82"><span class="lineNum">      82</span>              :               },</span>
<span id="L83"><span class="lineNum">      83</span>              :             ),</span>
<span id="L84"><span class="lineNum">      84</span>              :           ),</span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :           body: SafeArea(</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :             child: Padding(</span></span>
<span id="L87"><span class="lineNum">      87</span>              :               padding: const EdgeInsets.symmetric(horizontal: 20),</span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :               child: EmiNotSupportBodyWidget(</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :                 onClickPayInAll: _onClickPayInAll,</span></span>
<span id="L90"><span class="lineNum">      90</span>              :               ),</span>
<span id="L91"><span class="lineNum">      91</span>              :             ),</span>
<span id="L92"><span class="lineNum">      92</span>              :           ),</span>
<span id="L93"><span class="lineNum">      93</span>              :         ),</span>
<span id="L94"><span class="lineNum">      94</span>              :       ),</span>
<span id="L95"><span class="lineNum">      95</span>              :     );</span>
<span id="L96"><span class="lineNum">      96</span>              :   }</span>
<span id="L97"><span class="lineNum">      97</span>              : </span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :   void _onClickPayInAll() {</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :     _switchToOutRightPaymentCubit.convertOrderToOutRightPayment(widget.arg.qrCodeParseEntity);</span></span>
<span id="L100"><span class="lineNum">     100</span>              :   }</span>
<span id="L101"><span class="lineNum">     101</span>              : </span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :   void _handleSwitchToOutPaymentState(SwitchToOutrightPaymentState state) {</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :     if (state is SwitchToOutrightPaymentApiLoadingState) {</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :       EvoUiUtils().showHudLoading();</span></span>
<span id="L105"><span class="lineNum">     105</span>              :       return;</span>
<span id="L106"><span class="lineNum">     106</span>              :     }</span>
<span id="L107"><span class="lineNum">     107</span>              : </span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :     EvoUiUtils().hideHudLoading();</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :     if (state is CreateDynamicOrderSuccessState) {</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :       _handleCreatePaymentSuccessState(state);</span></span>
<span id="L111"><span class="lineNum">     111</span>              :       return;</span>
<span id="L112"><span class="lineNum">     112</span>              :     }</span>
<span id="L113"><span class="lineNum">     113</span>              : </span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :     if (state is CreateOrderActivatePosLimitErrorState) {</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :       _activePosLimitHandler.handleActivatedPOSLimitErrorState(</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :         orderActivatePosLimitEntity: OrderActivatePosLimitEntity(</span></span>
<span id="L117"><span class="lineNum">     117</span>              :           paymentService: PaymentService.outrightPurchase,</span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :           orderSession: state.entity?.session,</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :           emiPackages: state.entity?.emiPackages,</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :           prerequisitesAction: state.entity?.prerequisitesAction,</span></span>
<span id="L121"><span class="lineNum">     121</span>              :         ),</span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :         entity: state.entity,</span></span>
<span id="L123"><span class="lineNum">     123</span>              :         entryPoint: Screen.qrCodeScannerScreen,</span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaUNC">           0 :         onActivatedPOSLimitFlowSucceed: (OrderActivatePosLimitEntity entity) {</span></span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :           UpdateConfirmPaymentScreen.pushReplacementNamed(</span></span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :             orderSession: entity.orderSession,</span></span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :             selectedVoucher: appState.paymentSharedData.selectedVoucher,</span></span>
<span id="L128"><span class="lineNum">     128</span>              :           );</span>
<span id="L129"><span class="lineNum">     129</span>              :         },</span>
<span id="L130"><span class="lineNum">     130</span>              :       );</span>
<span id="L131"><span class="lineNum">     131</span>              :     }</span>
<span id="L132"><span class="lineNum">     132</span>              : </span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :     if (state is SwitchToOutrightPaymentCommonErrorState) {</span></span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :       handleEvoApiError(state.errorUIModel);</span></span>
<span id="L135"><span class="lineNum">     135</span>              :       return;</span>
<span id="L136"><span class="lineNum">     136</span>              :     }</span>
<span id="L137"><span class="lineNum">     137</span>              : </span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :     if (state is SwitchToOutrightPaymentOrderExpiredState) {</span></span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaUNC">           0 :       showExpiredOrderBottomSheet();</span></span>
<span id="L140"><span class="lineNum">     140</span>              :       return;</span>
<span id="L141"><span class="lineNum">     141</span>              :     }</span>
<span id="L142"><span class="lineNum">     142</span>              :   }</span>
<span id="L143"><span class="lineNum">     143</span>              : </span>
<span id="L144"><span class="lineNum">     144</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _handleCreatePaymentSuccessState(CreateDynamicOrderSuccessState state) async {</span></span>
<span id="L145"><span class="lineNum">     145</span>              :     final PaymentService paymentService = PaymentService.outrightPurchase;</span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaUNC">           0 :     final OrderSessionEntity? orderSession = state.entity?.session;</span></span>
<span id="L147"><span class="lineNum">     147</span>              : </span>
<span id="L148"><span class="lineNum">     148</span>              :     final bool isActivateCardOrPosLimit =</span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :         await _activePosLimitHandler.handleActivateCardOrPosLimitIfNeed(</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :       orderActivatePosLimitEntity: OrderActivatePosLimitEntity(</span></span>
<span id="L151"><span class="lineNum">     151</span>              :         paymentService: paymentService,</span>
<span id="L152"><span class="lineNum">     152</span>              :         orderSession: orderSession,</span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :         prerequisitesAction: state.entity?.prerequisitesAction,</span></span>
<span id="L154"><span class="lineNum">     154</span>              :       ),</span>
<span id="L155"><span class="lineNum">     155</span>              :       entryPoint: Screen.paymentInputAmount,</span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaUNC">           0 :       onActivatedPOSLimitFlowSucceed: (OrderActivatePosLimitEntity entity) {</span></span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :         final CreateDynamicOrderSuccessState newState = CreateDynamicOrderSuccessState(</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :           entity: state.entity,</span></span>
<span id="L159"><span class="lineNum">     159</span>              :         );</span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :         _handleCreatePaymentSuccessState(newState);</span></span>
<span id="L161"><span class="lineNum">     161</span>              :       },</span>
<span id="L162"><span class="lineNum">     162</span>              :     );</span>
<span id="L163"><span class="lineNum">     163</span>              : </span>
<span id="L164"><span class="lineNum">     164</span>              :     if (isActivateCardOrPosLimit) {</span>
<span id="L165"><span class="lineNum">     165</span>              :       return;</span>
<span id="L166"><span class="lineNum">     166</span>              :     }</span>
<span id="L167"><span class="lineNum">     167</span>              : </span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaUNC">           0 :     UpdateConfirmPaymentScreen.pushReplacementNamed(</span></span>
<span id="L169"><span class="lineNum">     169</span>              :       orderSession: orderSession,</span>
<span id="L170"><span class="lineNum">     170</span> <span class="tlaUNC">           0 :       selectedVoucher: appState.paymentSharedData.selectedVoucher,</span></span>
<span id="L171"><span class="lineNum">     171</span>              :     );</span>
<span id="L172"><span class="lineNum">     172</span>              :   }</span>
<span id="L173"><span class="lineNum">     173</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
