<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/payment/emi_option_screen/emi_option_screen.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/payment/emi_option_screen">lib/feature/payment/emi_option_screen</a> - emi_option_screen.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryMed">81.9&nbsp;%</td>
            <td class="headerCovTableEntry">215</td>
            <td class="headerCovTableEntry">176</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/base/page_base.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/common_package/common_package.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/global_key_provider.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../base/evo_page_state_base.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../data/response/emi_package_entity.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../data/response/order_session_entity.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../data/response/voucher_entity.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../prepare_for_app_initiation.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../../resources/resources.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../../util/ui_utils/evo_ui_utils.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../../widget/cta_with_powered_by_widget.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../../widget/evo_appbar.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../base_page_payment/cubit/update_order_cubit.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../confirm_payment/update_confirm_payment_screen/update_confirm_payment_screen.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../models/emi_option_cache_data_model.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../models/emi_option_ui_model.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import '../utils/invalid_payment_popup_handler_mixin.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import '../utils/payment_navigate_helper_mixin.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import '../utils/payment_with_emi_utils.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import '../widget/emi_tenor_list_widget/emi_tenor_list_widget.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : import '../widget/payment_detail_info/emi_summary_info_widget.dart';</span>
<span id="L27"><span class="lineNum">      27</span>              : import '../widget/payment_title_widget/emi_payment_title_widget.dart';</span>
<span id="L28"><span class="lineNum">      28</span>              : import 'bloc/emi_option_screen_cubit.dart';</span>
<span id="L29"><span class="lineNum">      29</span>              : import 'bloc/emi_option_screen_state.dart';</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span>              : class EmiOptionScreenArg extends PageBaseArg {</span>
<span id="L32"><span class="lineNum">      32</span>              :   final OrderSessionEntity orderSession;</span>
<span id="L33"><span class="lineNum">      33</span>              :   final List&lt;EmiPackageEntity&gt; emiPackages;</span>
<span id="L34"><span class="lineNum">      34</span>              :   final VoucherEntity? selectedVoucher;</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaGNC">           2 :   EmiOptionScreenArg({</span></span>
<span id="L37"><span class="lineNum">      37</span>              :     required this.orderSession,</span>
<span id="L38"><span class="lineNum">      38</span>              :     required this.emiPackages,</span>
<span id="L39"><span class="lineNum">      39</span>              :     this.selectedVoucher,</span>
<span id="L40"><span class="lineNum">      40</span>              :   });</span>
<span id="L41"><span class="lineNum">      41</span>              : }</span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span>              : class EmiOptionScreen extends PageBase {</span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           1 :   static void pushNamed({</span></span>
<span id="L45"><span class="lineNum">      45</span>              :     required OrderSessionEntity orderSession,</span>
<span id="L46"><span class="lineNum">      46</span>              :     required List&lt;EmiPackageEntity&gt; emiPackages,</span>
<span id="L47"><span class="lineNum">      47</span>              :     VoucherEntity? selectedVoucher,</span>
<span id="L48"><span class="lineNum">      48</span>              :   }) {</span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           2 :     return navigatorContext?.pushNamed(</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           1 :       Screen.emiOptionScreen.name,</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           1 :       extra: EmiOptionScreenArg(</span></span>
<span id="L52"><span class="lineNum">      52</span>              :         orderSession: orderSession,</span>
<span id="L53"><span class="lineNum">      53</span>              :         emiPackages: emiPackages,</span>
<span id="L54"><span class="lineNum">      54</span>              :         selectedVoucher: selectedVoucher,</span>
<span id="L55"><span class="lineNum">      55</span>              :       ),</span>
<span id="L56"><span class="lineNum">      56</span>              :     );</span>
<span id="L57"><span class="lineNum">      57</span>              :   }</span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span>              :   final EmiOptionScreenArg arg;</span>
<span id="L60"><span class="lineNum">      60</span>              : </span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaGNC">           2 :   const EmiOptionScreen({</span></span>
<span id="L62"><span class="lineNum">      62</span>              :     required this.arg,</span>
<span id="L63"><span class="lineNum">      63</span>              :     super.key,</span>
<span id="L64"><span class="lineNum">      64</span>              :   });</span>
<span id="L65"><span class="lineNum">      65</span>              : </span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           1 :   State&lt;EmiOptionScreen&gt; createState() =&gt; EmiOptionsScreenState();</span></span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L70"><span class="lineNum">      70</span>              :   EventTrackingScreenId get eventTrackingScreenId =&gt; EventTrackingScreenId.undefined;</span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaGNC">           2 :   RouteSettings get routeSettings =&gt; RouteSettings(name: Screen.emiOptionScreen.routeName);</span></span>
<span id="L74"><span class="lineNum">      74</span>              : }</span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span>              : @visibleForTesting</span>
<span id="L77"><span class="lineNum">      77</span>              : class EmiOptionsScreenState extends EvoPageStateBase&lt;EmiOptionScreen&gt;</span>
<span id="L78"><span class="lineNum">      78</span>              :     with PaymentNavigationHelperMixin, InvalidPaymentPopupHandlerMixin {</span>
<span id="L79"><span class="lineNum">      79</span>              :   @visibleForTesting</span>
<span id="L80"><span class="lineNum">      80</span>              :   final EmiOptionScreenCubit emiOptionScreenCubit = getIt&lt;EmiOptionScreenCubit&gt;();</span>
<span id="L81"><span class="lineNum">      81</span>              : </span>
<span id="L82"><span class="lineNum">      82</span>              :   @visibleForTesting</span>
<span id="L83"><span class="lineNum">      83</span>              :   final UpdateOrderCubit updateOrderCubit = getIt&lt;UpdateOrderCubit&gt;();</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span>              :   @visibleForTesting</span>
<span id="L86"><span class="lineNum">      86</span>              :   final PaymentWithEMIUtils paymentWithEMIUtils = getIt&lt;PaymentWithEMIUtils&gt;();</span>
<span id="L87"><span class="lineNum">      87</span>              : </span>
<span id="L88"><span class="lineNum">      88</span>              :   // flag to distinguish when user selects Tenor or presses Continue CTA</span>
<span id="L89"><span class="lineNum">      89</span>              :   // The behavior will be different</span>
<span id="L90"><span class="lineNum">      90</span>              :   @visibleForTesting</span>
<span id="L91"><span class="lineNum">      91</span>              :   bool isContinueCTAPressed = false;</span>
<span id="L92"><span class="lineNum">      92</span>              : </span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L94"><span class="lineNum">      94</span>              :   void initState() {</span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaGNC">           1 :     super.initState();</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaGNC">           2 :     WidgetsBinding.instance.addPostFrameCallback(</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaGNC">           1 :       (_) {</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaGNC">           2 :         emiOptionScreenCubit.initialize(</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaGNC">           3 :           orderSessionEntity: widget.arg.orderSession,</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaGNC">           3 :           emiPackages: widget.arg.emiPackages,</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaGNC">           3 :           selectedVoucher: widget.arg.selectedVoucher,</span></span>
<span id="L102"><span class="lineNum">     102</span>              :         );</span>
<span id="L103"><span class="lineNum">     103</span>              :       },</span>
<span id="L104"><span class="lineNum">     104</span>              :     );</span>
<span id="L105"><span class="lineNum">     105</span>              :   }</span>
<span id="L106"><span class="lineNum">     106</span>              : </span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L108"><span class="lineNum">     108</span>              :   Widget getContentWidget(BuildContext context) {</span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaGNC">           1 :     return MultiBlocProvider(</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaGNC">           1 :       providers: &lt;BlocProvider&lt;dynamic&gt;&gt;[</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaGNC">           1 :         BlocProvider&lt;UpdateOrderCubit&gt;(</span></span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaGNC">           2 :           create: (_) =&gt; updateOrderCubit,</span></span>
<span id="L113"><span class="lineNum">     113</span>              :         ),</span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaGNC">           1 :         BlocProvider&lt;EmiOptionScreenCubit&gt;(</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaGNC">           2 :           create: (_) =&gt; emiOptionScreenCubit,</span></span>
<span id="L116"><span class="lineNum">     116</span>              :         ),</span>
<span id="L117"><span class="lineNum">     117</span>              :       ],</span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaGNC">           1 :       child: MultiBlocListener(</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaGNC">           1 :         listeners: &lt;BlocListener&lt;dynamic, dynamic&gt;&gt;[</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaGNC">           1 :           BlocListener&lt;UpdateOrderCubit, UpdateOrderState&gt;(</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :             listener: (BuildContext context, UpdateOrderState state) {</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :               handleUpdateOrderState(state);</span></span>
<span id="L123"><span class="lineNum">     123</span>              :             },</span>
<span id="L124"><span class="lineNum">     124</span>              :           ),</span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaGNC">           1 :           BlocListener&lt;EmiOptionScreenCubit, EmiOptionScreenState&gt;(</span></span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :             listener: (BuildContext context, EmiOptionScreenState state) {</span></span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :               handleEmiOptionScreenStateChanged(state);</span></span>
<span id="L128"><span class="lineNum">     128</span>              :             },</span>
<span id="L129"><span class="lineNum">     129</span>              :           ),</span>
<span id="L130"><span class="lineNum">     130</span>              :         ],</span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaGNC">           1 :         child: Scaffold(</span></span>
<span id="L132"><span class="lineNum">     132</span>              :           backgroundColor: Colors.white,</span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaGNC">           1 :           appBar: EvoAppBar(),</span></span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaGNC">           1 :           body: SafeArea(</span></span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaGNC">           1 :             child: BlocBuilder&lt;EmiOptionScreenCubit, EmiOptionScreenState&gt;(</span></span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :               buildWhen: (EmiOptionScreenState previous, EmiOptionScreenState current) {</span></span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaUNC">           0 :                 return current is EmiOrderInfoLoadedState;</span></span>
<span id="L138"><span class="lineNum">     138</span>              :               },</span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaGNC">           1 :               builder: (BuildContext context, EmiOptionScreenState state) {</span></span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaGNC">           1 :                 if (state is EmiOrderInfoLoadedState) {</span></span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaGNC">           2 :                   return buildBody(state.orderUiOptionModel);</span></span>
<span id="L142"><span class="lineNum">     142</span>              :                 }</span>
<span id="L143"><span class="lineNum">     143</span>              :                 return const SizedBox.shrink();</span>
<span id="L144"><span class="lineNum">     144</span>              :               },</span>
<span id="L145"><span class="lineNum">     145</span>              :             ),</span>
<span id="L146"><span class="lineNum">     146</span>              :           ),</span>
<span id="L147"><span class="lineNum">     147</span>              :         ),</span>
<span id="L148"><span class="lineNum">     148</span>              :       ),</span>
<span id="L149"><span class="lineNum">     149</span>              :     );</span>
<span id="L150"><span class="lineNum">     150</span>              :   }</span>
<span id="L151"><span class="lineNum">     151</span>              : </span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L153"><span class="lineNum">     153</span>              :   Widget buildBody(EmiOptionUiModel emiOrder) {</span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaGNC">           1 :     return Column(</span></span>
<span id="L155"><span class="lineNum">     155</span>              :       mainAxisSize: MainAxisSize.min,</span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaGNC">           1 :       children: &lt;Widget&gt;[</span></span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaGNC">           1 :         Expanded(</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaGNC">           1 :           child: SingleChildScrollView(</span></span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaGNC">           1 :             child: Column(</span></span>
<span id="L160"><span class="lineNum">     160</span>              :               crossAxisAlignment: CrossAxisAlignment.stretch,</span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaGNC">           1 :               children: &lt;Widget&gt;[</span></span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaGNC">           1 :                 Padding(</span></span>
<span id="L163"><span class="lineNum">     163</span>              :                   padding: const EdgeInsets.symmetric(horizontal: 20),</span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaGNC">           1 :                   child: EmiPaymentTitle(</span></span>
<span id="L165"><span class="lineNum">     165</span>              :                     title: EvoStrings.emiOptionUWantEMIPaymentTitle,</span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaGNC">           1 :                     merchantStoreName: emiOrder.merchantName,</span></span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaGNC">           1 :                     amount: emiOrder.orderAmount,</span></span>
<span id="L168"><span class="lineNum">     168</span>              :                   ),</span>
<span id="L169"><span class="lineNum">     169</span>              :                 ),</span>
<span id="L170"><span class="lineNum">     170</span> <span class="tlaGNC">           1 :                 Padding(</span></span>
<span id="L171"><span class="lineNum">     171</span>              :                   padding: const EdgeInsets.symmetric(vertical: 20.0),</span>
<span id="L172"><span class="lineNum">     172</span> <span class="tlaGNC">           1 :                   child: EmiTenorListWidget(</span></span>
<span id="L173"><span class="lineNum">     173</span> <span class="tlaGNC">           1 :                     listEmiPackageEntity: emiOrder.emiPackages,</span></span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaGNC">           1 :                     onTapTenor: (EmiPackageEntity? emiPackage) {</span></span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaGNC">           1 :                       handleOnTenorTap(emiOrder: emiOrder, emiPackage: emiPackage);</span></span>
<span id="L176"><span class="lineNum">     176</span>              :                     },</span>
<span id="L177"><span class="lineNum">     177</span> <span class="tlaGNC">           3 :                     selectedTenorIdDefault: emiOrder.selectedEmiPackage?.offer?.id ?? '',</span></span>
<span id="L178"><span class="lineNum">     178</span>              :                   ),</span>
<span id="L179"><span class="lineNum">     179</span>              :                 ),</span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaGNC">           1 :                 buildTenorSummaryInfo(),</span></span>
<span id="L181"><span class="lineNum">     181</span> <span class="tlaGNC">           1 :                 Center(</span></span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaGNC">           1 :                   child: InkWell(</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaUNC">           0 :                     onTap: () =&gt; paymentWithEMIUtils.onShowTenorInfo(getTenorInfo()),</span></span>
<span id="L184"><span class="lineNum">     184</span> <span class="tlaGNC">           1 :                     child: Padding(</span></span>
<span id="L185"><span class="lineNum">     185</span>              :                       padding: const EdgeInsets.symmetric(vertical: 16.0),</span>
<span id="L186"><span class="lineNum">     186</span> <span class="tlaGNC">           1 :                       child: Text(</span></span>
<span id="L187"><span class="lineNum">     187</span>              :                         EvoStrings.emiOptionSeeMoreTenorInfoTitle,</span>
<span id="L188"><span class="lineNum">     188</span> <span class="tlaGNC">           1 :                         style: evoTextStyles</span></span>
<span id="L189"><span class="lineNum">     189</span> <span class="tlaGNC">           3 :                             .bodyMedium(evoColors.emiRegularText)</span></span>
<span id="L190"><span class="lineNum">     190</span> <span class="tlaGNC">           1 :                             .copyWith(fontWeight: FontWeight.w700),</span></span>
<span id="L191"><span class="lineNum">     191</span>              :                       ),</span>
<span id="L192"><span class="lineNum">     192</span>              :                     ),</span>
<span id="L193"><span class="lineNum">     193</span>              :                   ),</span>
<span id="L194"><span class="lineNum">     194</span>              :                 ),</span>
<span id="L195"><span class="lineNum">     195</span>              :               ],</span>
<span id="L196"><span class="lineNum">     196</span>              :             ),</span>
<span id="L197"><span class="lineNum">     197</span>              :           ),</span>
<span id="L198"><span class="lineNum">     198</span>              :         ),</span>
<span id="L199"><span class="lineNum">     199</span> <span class="tlaGNC">           1 :         Padding(</span></span>
<span id="L200"><span class="lineNum">     200</span>              :           padding: const EdgeInsets.symmetric(horizontal: 20),</span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaGNC">           1 :           child: CTAWithPoweredByWidget(</span></span>
<span id="L202"><span class="lineNum">     202</span>              :             text: EvoStrings.continueBtn,</span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaGNC">           1 :             onPressed: () {</span></span>
<span id="L204"><span class="lineNum">     204</span> <span class="tlaGNC">           1 :               isContinueCTAPressed = true;</span></span>
<span id="L205"><span class="lineNum">     205</span> <span class="tlaGNC">           1 :               onApplyEmiPackageClick();</span></span>
<span id="L206"><span class="lineNum">     206</span>              :             },</span>
<span id="L207"><span class="lineNum">     207</span>              :           ),</span>
<span id="L208"><span class="lineNum">     208</span>              :         ),</span>
<span id="L209"><span class="lineNum">     209</span>              :       ],</span>
<span id="L210"><span class="lineNum">     210</span>              :     );</span>
<span id="L211"><span class="lineNum">     211</span>              :   }</span>
<span id="L212"><span class="lineNum">     212</span>              : </span>
<span id="L213"><span class="lineNum">     213</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L214"><span class="lineNum">     214</span>              :   Widget buildTenorSummaryInfo() {</span>
<span id="L215"><span class="lineNum">     215</span> <span class="tlaGNC">           1 :     return BlocBuilder&lt;EmiOptionScreenCubit, EmiOptionScreenState&gt;(</span></span>
<span id="L216"><span class="lineNum">     216</span> <span class="tlaUNC">           0 :       buildWhen: (EmiOptionScreenState previous, EmiOptionScreenState current) {</span></span>
<span id="L217"><span class="lineNum">     217</span> <span class="tlaUNC">           0 :         return current is EmiOrderInfoLoadedState || current is UpdateSelectedTenorState;</span></span>
<span id="L218"><span class="lineNum">     218</span>              :       },</span>
<span id="L219"><span class="lineNum">     219</span> <span class="tlaGNC">           1 :       builder: (BuildContext context, EmiOptionScreenState state) {</span></span>
<span id="L220"><span class="lineNum">     220</span>              :         EmiPackageEntity? emiPackage;</span>
<span id="L221"><span class="lineNum">     221</span>              :         OrderSessionEntity? orderSessionEntity;</span>
<span id="L222"><span class="lineNum">     222</span>              : </span>
<span id="L223"><span class="lineNum">     223</span> <span class="tlaGNC">           1 :         if (state is EmiOrderInfoLoadedState) {</span></span>
<span id="L224"><span class="lineNum">     224</span> <span class="tlaGNC">           2 :           emiPackage = state.orderUiOptionModel.selectedEmiPackage;</span></span>
<span id="L225"><span class="lineNum">     225</span> <span class="tlaGNC">           2 :           orderSessionEntity = state.orderUiOptionModel.orderSessionEntity;</span></span>
<span id="L226"><span class="lineNum">     226</span>              :         }</span>
<span id="L227"><span class="lineNum">     227</span>              : </span>
<span id="L228"><span class="lineNum">     228</span> <span class="tlaGNC">           1 :         if (state is UpdateSelectedTenorState) {</span></span>
<span id="L229"><span class="lineNum">     229</span> <span class="tlaUNC">           0 :           emiPackage = state.selectedEmiPackage;</span></span>
<span id="L230"><span class="lineNum">     230</span> <span class="tlaUNC">           0 :           orderSessionEntity = state.orderSessionEntity;</span></span>
<span id="L231"><span class="lineNum">     231</span>              :         }</span>
<span id="L232"><span class="lineNum">     232</span>              : </span>
<span id="L233"><span class="lineNum">     233</span>              :         if (emiPackage == null) {</span>
<span id="L234"><span class="lineNum">     234</span>              :           return const SizedBox.shrink();</span>
<span id="L235"><span class="lineNum">     235</span>              :         }</span>
<span id="L236"><span class="lineNum">     236</span>              : </span>
<span id="L237"><span class="lineNum">     237</span> <span class="tlaGNC">           1 :         return Padding(</span></span>
<span id="L238"><span class="lineNum">     238</span>              :           padding: const EdgeInsets.symmetric(horizontal: 20.0),</span>
<span id="L239"><span class="lineNum">     239</span> <span class="tlaGNC">           1 :           child: SummaryEmiInfoWidget(</span></span>
<span id="L240"><span class="lineNum">     240</span> <span class="tlaGNC">           1 :             monthlyInstallmentAmount: emiPackage.monthlyInstallmentAmount,</span></span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaGNC">           1 :             conversionFee: emiPackage.conversionFee,</span></span>
<span id="L242"><span class="lineNum">     242</span> <span class="tlaGNC">           2 :             interestRate: emiPackage.offer?.interestRate,</span></span>
<span id="L243"><span class="lineNum">     243</span> <span class="tlaGNC">           1 :             outrightPurchaseDiff: emiPackage.outrightPurchaseDiff,</span></span>
<span id="L244"><span class="lineNum">     244</span>              :             hasShowPaymentSource: false,</span>
<span id="L245"><span class="lineNum">     245</span> <span class="tlaGNC">           1 :             promotionAmount: orderSessionEntity?.promotionAmount,</span></span>
<span id="L246"><span class="lineNum">     246</span>              :           ),</span>
<span id="L247"><span class="lineNum">     247</span>              :         );</span>
<span id="L248"><span class="lineNum">     248</span>              :       },</span>
<span id="L249"><span class="lineNum">     249</span>              :     );</span>
<span id="L250"><span class="lineNum">     250</span>              :   }</span>
<span id="L251"><span class="lineNum">     251</span>              : </span>
<span id="L252"><span class="lineNum">     252</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L253"><span class="lineNum">     253</span>              :   void onApplyEmiPackageClick() {</span>
<span id="L254"><span class="lineNum">     254</span>              :     EmiPackageEntity? selectedEmiPackage;</span>
<span id="L255"><span class="lineNum">     255</span> <span class="tlaGNC">           2 :     final EmiOptionScreenState state = emiOptionScreenCubit.state;</span></span>
<span id="L256"><span class="lineNum">     256</span>              :     VoucherEntity? selectedVoucher;</span>
<span id="L257"><span class="lineNum">     257</span> <span class="tlaGNC">           1 :     if (state is EmiOrderInfoLoadedState) {</span></span>
<span id="L258"><span class="lineNum">     258</span> <span class="tlaGNC">           2 :       selectedEmiPackage = state.orderUiOptionModel.selectedEmiPackage;</span></span>
<span id="L259"><span class="lineNum">     259</span> <span class="tlaGNC">           2 :       selectedVoucher = state.orderUiOptionModel.selectedVoucher;</span></span>
<span id="L260"><span class="lineNum">     260</span>              :     }</span>
<span id="L261"><span class="lineNum">     261</span> <span class="tlaGNC">           1 :     if (state is UpdateSelectedTenorState) {</span></span>
<span id="L262"><span class="lineNum">     262</span> <span class="tlaGNC">           1 :       selectedEmiPackage = state.selectedEmiPackage;</span></span>
<span id="L263"><span class="lineNum">     263</span> <span class="tlaGNC">           1 :       selectedVoucher = state.selectedVoucher;</span></span>
<span id="L264"><span class="lineNum">     264</span>              :     }</span>
<span id="L265"><span class="lineNum">     265</span>              :     if (selectedEmiPackage != null) {</span>
<span id="L266"><span class="lineNum">     266</span>              :       final EmiOptionCacheDataModel? cachedData =</span>
<span id="L267"><span class="lineNum">     267</span> <span class="tlaGNC">           2 :           emiOptionScreenCubit.getCachedData(selectedEmiPackage);</span></span>
<span id="L268"><span class="lineNum">     268</span> <span class="tlaGNC">           1 :       final OrderSessionEntity? cachedOrderSession = cachedData?.orderSession;</span></span>
<span id="L269"><span class="lineNum">     269</span> <span class="tlaGNC">           1 :       final EmiPackageEntity? cachedEmiPackage = cachedData?.selectedEmiPackage;</span></span>
<span id="L270"><span class="lineNum">     270</span>              : </span>
<span id="L271"><span class="lineNum">     271</span> <span class="tlaGNC">           2 :       updateOrderCubit.updateOrderPackage(</span></span>
<span id="L272"><span class="lineNum">     272</span>              :         orderSession: cachedOrderSession,</span>
<span id="L273"><span class="lineNum">     273</span>              : </span>
<span id="L274"><span class="lineNum">     274</span>              :         /// [cachedEmiPackage] from cached will always be updated</span>
<span id="L275"><span class="lineNum">     275</span>              :         /// when the state is `UpdateOrderInvalidVoucher` and `UpdateOrderSuccess`</span>
<span id="L276"><span class="lineNum">     276</span>              :         /// So, we need to make sure [selectedEmiPackage] is cached when the order is updated.</span>
<span id="L277"><span class="lineNum">     277</span>              :         selectedEmiPackage: cachedEmiPackage,</span>
<span id="L278"><span class="lineNum">     278</span>              :         selectedVoucher: selectedVoucher,</span>
<span id="L279"><span class="lineNum">     279</span>              :       );</span>
<span id="L280"><span class="lineNum">     280</span>              :     }</span>
<span id="L281"><span class="lineNum">     281</span>              :   }</span>
<span id="L282"><span class="lineNum">     282</span>              : </span>
<span id="L283"><span class="lineNum">     283</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L284"><span class="lineNum">     284</span>              :   void handleUpdateOrderState(UpdateOrderState state) {</span>
<span id="L285"><span class="lineNum">     285</span> <span class="tlaGNC">           1 :     if (state is UpdateOrderLoading) {</span></span>
<span id="L286"><span class="lineNum">     286</span> <span class="tlaGNC">           2 :       EvoUiUtils().showHudLoading();</span></span>
<span id="L287"><span class="lineNum">     287</span>              :       return;</span>
<span id="L288"><span class="lineNum">     288</span>              :     }</span>
<span id="L289"><span class="lineNum">     289</span>              : </span>
<span id="L290"><span class="lineNum">     290</span> <span class="tlaGNC">           2 :     EvoUiUtils().hideHudLoading();</span></span>
<span id="L291"><span class="lineNum">     291</span>              : </span>
<span id="L292"><span class="lineNum">     292</span>              :     /// Handle error cases after calling API failed</span>
<span id="L293"><span class="lineNum">     293</span> <span class="tlaGNC">           1 :     if (state is UpdateOrderExpired) {</span></span>
<span id="L294"><span class="lineNum">     294</span> <span class="tlaGNC">           1 :       showExpiredOrderBottomSheet();</span></span>
<span id="L295"><span class="lineNum">     295</span>              :       return;</span>
<span id="L296"><span class="lineNum">     296</span>              :     }</span>
<span id="L297"><span class="lineNum">     297</span>              : </span>
<span id="L298"><span class="lineNum">     298</span> <span class="tlaGNC">           1 :     if (state is UpdateOrderUnqualifiedAfterApplyVoucher) {</span></span>
<span id="L299"><span class="lineNum">     299</span> <span class="tlaGNC">           1 :       updateEmiPackageUI(</span></span>
<span id="L300"><span class="lineNum">     300</span> <span class="tlaGNC">           1 :         orderSession: state.orderSession,</span></span>
<span id="L301"><span class="lineNum">     301</span> <span class="tlaGNC">           1 :         emiPackage: state.emiPackage,</span></span>
<span id="L302"><span class="lineNum">     302</span> <span class="tlaGNC">           1 :         selectedVoucher: state.selectedVoucher,</span></span>
<span id="L303"><span class="lineNum">     303</span>              :       );</span>
<span id="L304"><span class="lineNum">     304</span> <span class="tlaGNC">           1 :       showUnqualifiedEmiErrorDialog(state);</span></span>
<span id="L305"><span class="lineNum">     305</span>              :       return;</span>
<span id="L306"><span class="lineNum">     306</span>              :     }</span>
<span id="L307"><span class="lineNum">     307</span>              : </span>
<span id="L308"><span class="lineNum">     308</span> <span class="tlaGNC">           1 :     if (state is UpdateOrderError) {</span></span>
<span id="L309"><span class="lineNum">     309</span> <span class="tlaGNC">           2 :       handleEvoApiError(state.error);</span></span>
<span id="L310"><span class="lineNum">     310</span>              :       return;</span>
<span id="L311"><span class="lineNum">     311</span>              :     }</span>
<span id="L312"><span class="lineNum">     312</span>              : </span>
<span id="L313"><span class="lineNum">     313</span> <span class="tlaGNC">           1 :     if (state is UpdateOrderInvalidVoucher) {</span></span>
<span id="L314"><span class="lineNum">     314</span> <span class="tlaGNC">           1 :       if (isContinueCTAPressed) {</span></span>
<span id="L315"><span class="lineNum">     315</span> <span class="tlaGNC">           1 :         isContinueCTAPressed = false;</span></span>
<span id="L316"><span class="lineNum">     316</span> <span class="tlaGNC">           1 :         showInvalidVoucherDialog(state);</span></span>
<span id="L317"><span class="lineNum">     317</span>              :         return;</span>
<span id="L318"><span class="lineNum">     318</span>              :       }</span>
<span id="L319"><span class="lineNum">     319</span>              : </span>
<span id="L320"><span class="lineNum">     320</span> <span class="tlaGNC">           1 :       updateCachingData(</span></span>
<span id="L321"><span class="lineNum">     321</span> <span class="tlaGNC">           1 :         orderSession: state.orderSession,</span></span>
<span id="L322"><span class="lineNum">     322</span> <span class="tlaGNC">           1 :         emiPackage: state.emiPackage,</span></span>
<span id="L323"><span class="lineNum">     323</span>              :       );</span>
<span id="L324"><span class="lineNum">     324</span>              : </span>
<span id="L325"><span class="lineNum">     325</span> <span class="tlaGNC">           1 :       updateEmiPackageUI(</span></span>
<span id="L326"><span class="lineNum">     326</span> <span class="tlaGNC">           1 :         orderSession: state.orderSession,</span></span>
<span id="L327"><span class="lineNum">     327</span> <span class="tlaGNC">           1 :         emiPackage: state.emiPackage,</span></span>
<span id="L328"><span class="lineNum">     328</span> <span class="tlaGNC">           1 :         selectedVoucher: state.selectedVoucher,</span></span>
<span id="L329"><span class="lineNum">     329</span>              :       );</span>
<span id="L330"><span class="lineNum">     330</span> <span class="tlaGNC">           1 :       final ErrorUIModel errorUIModel = state.error;</span></span>
<span id="L331"><span class="lineNum">     331</span> <span class="tlaGNC">           1 :       showSnackBarWarning(</span></span>
<span id="L332"><span class="lineNum">     332</span> <span class="tlaGNC">           1 :         errorUIModel.userMessage ?? getMessageByErrorCode(errorUIModel.statusCode),</span></span>
<span id="L333"><span class="lineNum">     333</span>              :       );</span>
<span id="L334"><span class="lineNum">     334</span>              :       return;</span>
<span id="L335"><span class="lineNum">     335</span>              :     }</span>
<span id="L336"><span class="lineNum">     336</span>              : </span>
<span id="L337"><span class="lineNum">     337</span>              :     /// Handle success cases after calling API succeed</span>
<span id="L338"><span class="lineNum">     338</span>              :     /// Update UI after update emi package success</span>
<span id="L339"><span class="lineNum">     339</span> <span class="tlaGNC">           1 :     if (state is UpdateOrderSuccess) {</span></span>
<span id="L340"><span class="lineNum">     340</span> <span class="tlaGNC">           1 :       if (isContinueCTAPressed) {</span></span>
<span id="L341"><span class="lineNum">     341</span> <span class="tlaGNC">           1 :         isContinueCTAPressed = false;</span></span>
<span id="L342"><span class="lineNum">     342</span> <span class="tlaGNC">           1 :         gotoCheckoutScreen(</span></span>
<span id="L343"><span class="lineNum">     343</span> <span class="tlaGNC">           1 :           orderSession: state.orderSession,</span></span>
<span id="L344"><span class="lineNum">     344</span> <span class="tlaGNC">           1 :           emiPackage: state.emiPackage,</span></span>
<span id="L345"><span class="lineNum">     345</span> <span class="tlaGNC">           1 :           selectedVoucher: state.selectedVoucher,</span></span>
<span id="L346"><span class="lineNum">     346</span>              :         );</span>
<span id="L347"><span class="lineNum">     347</span>              :         return;</span>
<span id="L348"><span class="lineNum">     348</span>              :       }</span>
<span id="L349"><span class="lineNum">     349</span>              : </span>
<span id="L350"><span class="lineNum">     350</span> <span class="tlaUNC">           0 :       updateCachingData(</span></span>
<span id="L351"><span class="lineNum">     351</span> <span class="tlaUNC">           0 :         orderSession: state.orderSession,</span></span>
<span id="L352"><span class="lineNum">     352</span> <span class="tlaUNC">           0 :         emiPackage: state.emiPackage,</span></span>
<span id="L353"><span class="lineNum">     353</span>              :       );</span>
<span id="L354"><span class="lineNum">     354</span>              : </span>
<span id="L355"><span class="lineNum">     355</span> <span class="tlaUNC">           0 :       updateEmiPackageUI(</span></span>
<span id="L356"><span class="lineNum">     356</span> <span class="tlaUNC">           0 :         orderSession: state.orderSession,</span></span>
<span id="L357"><span class="lineNum">     357</span> <span class="tlaUNC">           0 :         emiPackage: state.emiPackage,</span></span>
<span id="L358"><span class="lineNum">     358</span> <span class="tlaUNC">           0 :         selectedVoucher: state.selectedVoucher,</span></span>
<span id="L359"><span class="lineNum">     359</span>              :       );</span>
<span id="L360"><span class="lineNum">     360</span>              :       return;</span>
<span id="L361"><span class="lineNum">     361</span>              :     }</span>
<span id="L362"><span class="lineNum">     362</span>              : </span>
<span id="L363"><span class="lineNum">     363</span>              :     /// Go to outright checkout screen after change to outright success</span>
<span id="L364"><span class="lineNum">     364</span> <span class="tlaGNC">           1 :     if (state is ChangeOrderToOutrightPaymentSuccess) {</span></span>
<span id="L365"><span class="lineNum">     365</span> <span class="tlaGNC">           1 :       handleChangeToOutrightSuccess(</span></span>
<span id="L366"><span class="lineNum">     366</span> <span class="tlaGNC">           1 :         orderSession: state.orderSession,</span></span>
<span id="L367"><span class="lineNum">     367</span> <span class="tlaGNC">           1 :         selectedVoucher: state.selectedVoucher,</span></span>
<span id="L368"><span class="lineNum">     368</span>              :       );</span>
<span id="L369"><span class="lineNum">     369</span>              :       return;</span>
<span id="L370"><span class="lineNum">     370</span>              :     }</span>
<span id="L371"><span class="lineNum">     371</span>              : </span>
<span id="L372"><span class="lineNum">     372</span>              :     /// Go to emi checkout screen after remove voucher success</span>
<span id="L373"><span class="lineNum">     373</span> <span class="tlaUNC">           0 :     if (state is RemoveVoucherInOrderSuccess) {</span></span>
<span id="L374"><span class="lineNum">     374</span> <span class="tlaUNC">           0 :       gotoCheckoutScreen(</span></span>
<span id="L375"><span class="lineNum">     375</span> <span class="tlaUNC">           0 :         orderSession: state.orderSession,</span></span>
<span id="L376"><span class="lineNum">     376</span> <span class="tlaUNC">           0 :         emiPackage: state.emiPackage,</span></span>
<span id="L377"><span class="lineNum">     377</span>              :       );</span>
<span id="L378"><span class="lineNum">     378</span>              :       return;</span>
<span id="L379"><span class="lineNum">     379</span>              :     }</span>
<span id="L380"><span class="lineNum">     380</span>              :   }</span>
<span id="L381"><span class="lineNum">     381</span>              : </span>
<span id="L382"><span class="lineNum">     382</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L383"><span class="lineNum">     383</span>              :   void showUnqualifiedEmiErrorDialog(UpdateOrderUnqualifiedAfterApplyVoucher state) {</span>
<span id="L384"><span class="lineNum">     384</span> <span class="tlaGNC">           1 :     showEmiUnqualifiedAfterApplyPromotionPopup(</span></span>
<span id="L385"><span class="lineNum">     385</span> <span class="tlaGNC">           2 :       errorMessage: state.error.userMessage,</span></span>
<span id="L386"><span class="lineNum">     386</span>              :       isDismissible: false,</span>
<span id="L387"><span class="lineNum">     387</span>              :       textNegative: EvoStrings.emiUnqualifiedAfterApplyPromotionChangeToOutrightPayment,</span>
<span id="L388"><span class="lineNum">     388</span> <span class="tlaUNC">           0 :       onClickNegative: () {</span></span>
<span id="L389"><span class="lineNum">     389</span> <span class="tlaUNC">           0 :         onChangeToOutRightPayment(</span></span>
<span id="L390"><span class="lineNum">     390</span> <span class="tlaUNC">           0 :           orderSession: state.orderSession,</span></span>
<span id="L391"><span class="lineNum">     391</span> <span class="tlaUNC">           0 :           selectedEmiPackage: state.emiPackage,</span></span>
<span id="L392"><span class="lineNum">     392</span> <span class="tlaUNC">           0 :           selectedVoucher: state.selectedVoucher,</span></span>
<span id="L393"><span class="lineNum">     393</span>              :         );</span>
<span id="L394"><span class="lineNum">     394</span>              :       },</span>
<span id="L395"><span class="lineNum">     395</span>              :       textPositive: EvoStrings.titleEmiWithoutPromotion,</span>
<span id="L396"><span class="lineNum">     396</span> <span class="tlaUNC">           0 :       onClickPositive: () {</span></span>
<span id="L397"><span class="lineNum">     397</span> <span class="tlaUNC">           0 :         onPayEmiWithoutVoucher(</span></span>
<span id="L398"><span class="lineNum">     398</span> <span class="tlaUNC">           0 :           orderSession: state.orderSession,</span></span>
<span id="L399"><span class="lineNum">     399</span> <span class="tlaUNC">           0 :           emiPackage: state.emiPackage,</span></span>
<span id="L400"><span class="lineNum">     400</span>              :         );</span>
<span id="L401"><span class="lineNum">     401</span>              :       },</span>
<span id="L402"><span class="lineNum">     402</span>              :     );</span>
<span id="L403"><span class="lineNum">     403</span>              :   }</span>
<span id="L404"><span class="lineNum">     404</span>              : </span>
<span id="L405"><span class="lineNum">     405</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L406"><span class="lineNum">     406</span>              :   void showInvalidVoucherDialog(UpdateOrderInvalidVoucher state) {</span>
<span id="L407"><span class="lineNum">     407</span> <span class="tlaGNC">           1 :     showInvalidPromotionPopup(</span></span>
<span id="L408"><span class="lineNum">     408</span> <span class="tlaGNC">           2 :       errorMessage: state.error.userMessage,</span></span>
<span id="L409"><span class="lineNum">     409</span> <span class="tlaGNC">           2 :       imageWidth: context.screenWidth,</span></span>
<span id="L410"><span class="lineNum">     410</span>              : </span>
<span id="L411"><span class="lineNum">     411</span>              :       /// Change to outright payment WITHOUT voucher</span>
<span id="L412"><span class="lineNum">     412</span>              :       textNegative: EvoStrings.titlePayInAll,</span>
<span id="L413"><span class="lineNum">     413</span> <span class="tlaUNC">           0 :       onClickNegative: () {</span></span>
<span id="L414"><span class="lineNum">     414</span> <span class="tlaUNC">           0 :         onChangeToOutRightPayment(</span></span>
<span id="L415"><span class="lineNum">     415</span> <span class="tlaUNC">           0 :           orderSession: state.orderSession,</span></span>
<span id="L416"><span class="lineNum">     416</span> <span class="tlaUNC">           0 :           selectedEmiPackage: state.emiPackage,</span></span>
<span id="L417"><span class="lineNum">     417</span>              :         );</span>
<span id="L418"><span class="lineNum">     418</span>              :       },</span>
<span id="L419"><span class="lineNum">     419</span>              : </span>
<span id="L420"><span class="lineNum">     420</span>              :       /// Pay without promotion</span>
<span id="L421"><span class="lineNum">     421</span>              :       textPositive: EvoStrings.titleEmiWithoutPromotion,</span>
<span id="L422"><span class="lineNum">     422</span> <span class="tlaUNC">           0 :       onClickPositive: () {</span></span>
<span id="L423"><span class="lineNum">     423</span> <span class="tlaUNC">           0 :         onPayEmiWithoutVoucher(</span></span>
<span id="L424"><span class="lineNum">     424</span> <span class="tlaUNC">           0 :           orderSession: state.orderSession,</span></span>
<span id="L425"><span class="lineNum">     425</span> <span class="tlaUNC">           0 :           emiPackage: state.emiPackage,</span></span>
<span id="L426"><span class="lineNum">     426</span>              :         );</span>
<span id="L427"><span class="lineNum">     427</span>              :       },</span>
<span id="L428"><span class="lineNum">     428</span>              :     );</span>
<span id="L429"><span class="lineNum">     429</span>              :   }</span>
<span id="L430"><span class="lineNum">     430</span>              : </span>
<span id="L431"><span class="lineNum">     431</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L432"><span class="lineNum">     432</span>              :   void onChangeToOutRightPayment({</span>
<span id="L433"><span class="lineNum">     433</span>              :     required OrderSessionEntity? orderSession,</span>
<span id="L434"><span class="lineNum">     434</span>              :     required EmiPackageEntity? selectedEmiPackage,</span>
<span id="L435"><span class="lineNum">     435</span>              :     VoucherEntity? selectedVoucher,</span>
<span id="L436"><span class="lineNum">     436</span>              :   }) {</span>
<span id="L437"><span class="lineNum">     437</span> <span class="tlaGNC">           2 :     updateOrderCubit.changeToOutrightPayment(</span></span>
<span id="L438"><span class="lineNum">     438</span>              :       orderSession: orderSession,</span>
<span id="L439"><span class="lineNum">     439</span>              :       selectedEmiPackage: selectedEmiPackage,</span>
<span id="L440"><span class="lineNum">     440</span>              :       selectedVoucher: selectedVoucher,</span>
<span id="L441"><span class="lineNum">     441</span>              :     );</span>
<span id="L442"><span class="lineNum">     442</span>              :   }</span>
<span id="L443"><span class="lineNum">     443</span>              : </span>
<span id="L444"><span class="lineNum">     444</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L445"><span class="lineNum">     445</span>              :   void onPayEmiWithoutVoucher({</span>
<span id="L446"><span class="lineNum">     446</span>              :     required OrderSessionEntity? orderSession,</span>
<span id="L447"><span class="lineNum">     447</span>              :     required EmiPackageEntity? emiPackage,</span>
<span id="L448"><span class="lineNum">     448</span>              :   }) {</span>
<span id="L449"><span class="lineNum">     449</span> <span class="tlaGNC">           2 :     navigatorContext?.pop();</span></span>
<span id="L450"><span class="lineNum">     450</span> <span class="tlaGNC">           2 :     updateOrderCubit.removeVoucher(</span></span>
<span id="L451"><span class="lineNum">     451</span>              :       orderSession: orderSession,</span>
<span id="L452"><span class="lineNum">     452</span>              :       emiPackageEntity: emiPackage,</span>
<span id="L453"><span class="lineNum">     453</span>              :     );</span>
<span id="L454"><span class="lineNum">     454</span>              :   }</span>
<span id="L455"><span class="lineNum">     455</span>              : </span>
<span id="L456"><span class="lineNum">     456</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L457"><span class="lineNum">     457</span>              :   void handleChangeToOutrightSuccess({</span>
<span id="L458"><span class="lineNum">     458</span>              :     required OrderSessionEntity? orderSession,</span>
<span id="L459"><span class="lineNum">     459</span>              :     VoucherEntity? selectedVoucher,</span>
<span id="L460"><span class="lineNum">     460</span>              :   }) {</span>
<span id="L461"><span class="lineNum">     461</span> <span class="tlaGNC">           1 :     handlePopToOrderCreationScreen();</span></span>
<span id="L462"><span class="lineNum">     462</span> <span class="tlaGNC">           1 :     gotoCheckoutScreen(</span></span>
<span id="L463"><span class="lineNum">     463</span>              :       orderSession: orderSession,</span>
<span id="L464"><span class="lineNum">     464</span>              :       selectedVoucher: selectedVoucher,</span>
<span id="L465"><span class="lineNum">     465</span>              :     );</span>
<span id="L466"><span class="lineNum">     466</span>              :   }</span>
<span id="L467"><span class="lineNum">     467</span>              : </span>
<span id="L468"><span class="lineNum">     468</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L469"><span class="lineNum">     469</span>              :   void gotoCheckoutScreen({</span>
<span id="L470"><span class="lineNum">     470</span>              :     OrderSessionEntity? orderSession,</span>
<span id="L471"><span class="lineNum">     471</span>              :     VoucherEntity? selectedVoucher,</span>
<span id="L472"><span class="lineNum">     472</span>              :     EmiPackageEntity? emiPackage,</span>
<span id="L473"><span class="lineNum">     473</span>              :   }) {</span>
<span id="L474"><span class="lineNum">     474</span> <span class="tlaGNC">           1 :     UpdateConfirmPaymentScreen.pushNamed(</span></span>
<span id="L475"><span class="lineNum">     475</span>              :       orderSession: orderSession,</span>
<span id="L476"><span class="lineNum">     476</span>              :       emiPackage: emiPackage,</span>
<span id="L477"><span class="lineNum">     477</span>              :       selectedVoucher: selectedVoucher,</span>
<span id="L478"><span class="lineNum">     478</span>              :     );</span>
<span id="L479"><span class="lineNum">     479</span> <span class="tlaGNC">           2 :     emiOptionScreenCubit.removeSelectedVoucher(</span></span>
<span id="L480"><span class="lineNum">     480</span>              :       selectedPackage: emiPackage,</span>
<span id="L481"><span class="lineNum">     481</span>              :     );</span>
<span id="L482"><span class="lineNum">     482</span>              :   }</span>
<span id="L483"><span class="lineNum">     483</span>              : </span>
<span id="L484"><span class="lineNum">     484</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L485"><span class="lineNum">     485</span>              :   void updateEmiPackageUI({</span>
<span id="L486"><span class="lineNum">     486</span>              :     OrderSessionEntity? orderSession,</span>
<span id="L487"><span class="lineNum">     487</span>              :     EmiPackageEntity? emiPackage,</span>
<span id="L488"><span class="lineNum">     488</span>              :     VoucherEntity? selectedVoucher,</span>
<span id="L489"><span class="lineNum">     489</span>              :   }) {</span>
<span id="L490"><span class="lineNum">     490</span> <span class="tlaGNC">           2 :     emiOptionScreenCubit.updateSelectedEmiPackage(</span></span>
<span id="L491"><span class="lineNum">     491</span>              :       orderSessionEntity: orderSession,</span>
<span id="L492"><span class="lineNum">     492</span>              :       selectedEmiPackage: emiPackage,</span>
<span id="L493"><span class="lineNum">     493</span>              :       selectedVoucher: selectedVoucher,</span>
<span id="L494"><span class="lineNum">     494</span>              :     );</span>
<span id="L495"><span class="lineNum">     495</span>              :   }</span>
<span id="L496"><span class="lineNum">     496</span>              : </span>
<span id="L497"><span class="lineNum">     497</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L498"><span class="lineNum">     498</span>              :   void handleOnTenorTap({required EmiOptionUiModel emiOrder, EmiPackageEntity? emiPackage}) {</span>
<span id="L499"><span class="lineNum">     499</span> <span class="tlaGNC">           2 :     emiOptionScreenCubit.updateEmiPackageFromCacheIfNeeded(</span></span>
<span id="L500"><span class="lineNum">     500</span> <span class="tlaGNC">           1 :       orderSession: emiOrder.orderSessionEntity,</span></span>
<span id="L501"><span class="lineNum">     501</span>              :       selectedEmiPackage: emiPackage,</span>
<span id="L502"><span class="lineNum">     502</span> <span class="tlaGNC">           1 :       selectedVoucher: emiOrder.selectedVoucher,</span></span>
<span id="L503"><span class="lineNum">     503</span>              :     );</span>
<span id="L504"><span class="lineNum">     504</span>              :   }</span>
<span id="L505"><span class="lineNum">     505</span>              : </span>
<span id="L506"><span class="lineNum">     506</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L507"><span class="lineNum">     507</span>              :   void handleEmiOptionScreenStateChanged(EmiOptionScreenState state) {</span>
<span id="L508"><span class="lineNum">     508</span> <span class="tlaGNC">           1 :     if (state is GetEmiOrderFromCacheSuccessState) {</span></span>
<span id="L509"><span class="lineNum">     509</span> <span class="tlaGNC">           1 :       updateEmiPackageUI(</span></span>
<span id="L510"><span class="lineNum">     510</span> <span class="tlaGNC">           1 :         orderSession: state.orderSession,</span></span>
<span id="L511"><span class="lineNum">     511</span> <span class="tlaGNC">           1 :         emiPackage: state.emiPackage,</span></span>
<span id="L512"><span class="lineNum">     512</span> <span class="tlaGNC">           1 :         selectedVoucher: state.selectedVoucher,</span></span>
<span id="L513"><span class="lineNum">     513</span>              :       );</span>
<span id="L514"><span class="lineNum">     514</span>              :       return;</span>
<span id="L515"><span class="lineNum">     515</span>              :     }</span>
<span id="L516"><span class="lineNum">     516</span>              : </span>
<span id="L517"><span class="lineNum">     517</span> <span class="tlaGNC">           1 :     if (state is NeededUpdateEmiOrderState) {</span></span>
<span id="L518"><span class="lineNum">     518</span> <span class="tlaGNC">           2 :       updateOrderCubit.updateOrderPackage(</span></span>
<span id="L519"><span class="lineNum">     519</span> <span class="tlaGNC">           1 :         orderSession: state.orderSession,</span></span>
<span id="L520"><span class="lineNum">     520</span> <span class="tlaGNC">           1 :         selectedEmiPackage: state.emiPackage,</span></span>
<span id="L521"><span class="lineNum">     521</span> <span class="tlaGNC">           1 :         selectedVoucher: state.selectedVoucher,</span></span>
<span id="L522"><span class="lineNum">     522</span>              :       );</span>
<span id="L523"><span class="lineNum">     523</span>              :       return;</span>
<span id="L524"><span class="lineNum">     524</span>              :     }</span>
<span id="L525"><span class="lineNum">     525</span>              :   }</span>
<span id="L526"><span class="lineNum">     526</span>              : </span>
<span id="L527"><span class="lineNum">     527</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L528"><span class="lineNum">     528</span>              :   List&lt;String&gt;? getTenorInfo() {</span>
<span id="L529"><span class="lineNum">     529</span>              :     List&lt;String&gt;? tenorInfo;</span>
<span id="L530"><span class="lineNum">     530</span> <span class="tlaGNC">           2 :     final EmiOptionScreenState state = emiOptionScreenCubit.state;</span></span>
<span id="L531"><span class="lineNum">     531</span>              : </span>
<span id="L532"><span class="lineNum">     532</span> <span class="tlaGNC">           1 :     if (state is EmiOrderInfoLoadedState) {</span></span>
<span id="L533"><span class="lineNum">     533</span> <span class="tlaGNC">           2 :       final EmiPackageEntity? selectedEmiPackage = state.orderUiOptionModel.selectedEmiPackage;</span></span>
<span id="L534"><span class="lineNum">     534</span> <span class="tlaGNC">           2 :       return selectedEmiPackage?.offer?.information;</span></span>
<span id="L535"><span class="lineNum">     535</span>              :     }</span>
<span id="L536"><span class="lineNum">     536</span>              : </span>
<span id="L537"><span class="lineNum">     537</span> <span class="tlaGNC">           1 :     if (state is UpdateSelectedTenorState) {</span></span>
<span id="L538"><span class="lineNum">     538</span> <span class="tlaGNC">           1 :       return state.selectedEmiPackage?.offer?.information;</span></span>
<span id="L539"><span class="lineNum">     539</span>              :     }</span>
<span id="L540"><span class="lineNum">     540</span>              : </span>
<span id="L541"><span class="lineNum">     541</span>              :     return tenorInfo;</span>
<span id="L542"><span class="lineNum">     542</span>              :   }</span>
<span id="L543"><span class="lineNum">     543</span>              : </span>
<span id="L544"><span class="lineNum">     544</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L545"><span class="lineNum">     545</span>              :   void updateCachingData({</span>
<span id="L546"><span class="lineNum">     546</span>              :     OrderSessionEntity? orderSession,</span>
<span id="L547"><span class="lineNum">     547</span>              :     EmiPackageEntity? emiPackage,</span>
<span id="L548"><span class="lineNum">     548</span>              :   }) {</span>
<span id="L549"><span class="lineNum">     549</span> <span class="tlaGNC">           2 :     emiOptionScreenCubit.handleCachingData(</span></span>
<span id="L550"><span class="lineNum">     550</span>              :       orderSession: orderSession,</span>
<span id="L551"><span class="lineNum">     551</span>              :       emiPackage: emiPackage,</span>
<span id="L552"><span class="lineNum">     552</span>              :     );</span>
<span id="L553"><span class="lineNum">     553</span>              :   }</span>
<span id="L554"><span class="lineNum">     554</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
