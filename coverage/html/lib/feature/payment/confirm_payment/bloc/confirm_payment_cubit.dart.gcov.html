<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/payment/confirm_payment/bloc/confirm_payment_cubit.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/payment/confirm_payment/bloc">lib/feature/payment/confirm_payment/bloc</a> - confirm_payment_cubit.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">62</td>
            <td class="headerCovTableEntry">62</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/foundation.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/base/common_cubit.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/data/http_client/mock_config.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/util/uuid/uuid_generator.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../../../data/repository/checkout_repo.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../../data/response/confirm_and_pay_order_entity.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../../data/response/emi_package_entity.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../../data/response/order_session_entity.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../../data/response/voucher_entity.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../../prepare_for_app_initiation.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../../../util/functions.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../../../util/secure_storage_helper/secure_storage_helper.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../../activated_pos_limit/utils/activate_pos_limit_utils.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../../biometric_pin_confirm/biometric_pin_data.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../../../feature_toggle.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../../mock_file/mock_checkout_file_name.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span>              : part 'confirm_payment_state.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : </span>
<span id="L23"><span class="lineNum">      23</span>              : class ConfirmPaymentCubit extends CommonCubit&lt;ConfirmPaymentState&gt; {</span>
<span id="L24"><span class="lineNum">      24</span>              :   final CheckOutRepo checkOutRepo;</span>
<span id="L25"><span class="lineNum">      25</span>              :   final UUIDGenerator uuidGenerator;</span>
<span id="L26"><span class="lineNum">      26</span>              :   final EvoLocalStorageHelper evoLocalStorageHelper;</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           1 :   ConfirmPaymentCubit({</span></span>
<span id="L29"><span class="lineNum">      29</span>              :     required this.checkOutRepo,</span>
<span id="L30"><span class="lineNum">      30</span>              :     required this.uuidGenerator,</span>
<span id="L31"><span class="lineNum">      31</span>              :     required this.evoLocalStorageHelper,</span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           2 :   }) : super(ConfirmPaymentInitial());</span></span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; confirmAndPay({</span></span>
<span id="L35"><span class="lineNum">      35</span>              :     required AuthenticateType authenticateType,</span>
<span id="L36"><span class="lineNum">      36</span>              :     String? biometricToken,</span>
<span id="L37"><span class="lineNum">      37</span>              :     String? pin,</span>
<span id="L38"><span class="lineNum">      38</span>              :     VoucherEntity? selectedVoucher,</span>
<span id="L39"><span class="lineNum">      39</span>              :     OrderSessionEntity? orderSession,</span>
<span id="L40"><span class="lineNum">      40</span>              :     EmiPackageEntity? emiPackage,</span>
<span id="L41"><span class="lineNum">      41</span>              :   }) async {</span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           2 :     emit(ConfirmPaymentLoading());</span></span>
<span id="L43"><span class="lineNum">      43</span>              : </span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           2 :     final ConfirmAndPayOrderEntity entity = await checkOutRepo.confirmAndPay(</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           1 :       sessionId: orderSession?.id,</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           2 :       idempotencyKey: uuidGenerator.genV4(),</span></span>
<span id="L47"><span class="lineNum">      47</span>              :       authType: authenticateType,</span>
<span id="L48"><span class="lineNum">      48</span>              :       biometricToken: biometricToken,</span>
<span id="L49"><span class="lineNum">      49</span>              :       pin: pin,</span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           4 :       voucherIds: selectedVoucher?.id != null ? &lt;int?&gt;[selectedVoucher?.id] : &lt;int?&gt;[],</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           4 :       paymentMethodId: evoUtilFunction.getDefaultPaymentMethod(orderSession?.paymentInfo)?.id,</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaGNC">           1 :       userChargeAmount: orderSession?.userChargeAmount,</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           2 :       emiOfferId: emiPackage?.offer?.id,</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           1 :       mockConfig: MockConfig(</span></span>
<span id="L55"><span class="lineNum">      55</span>              :         enable: false,</span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           1 :         fileName: getConfirmAndPayMockFileName(</span></span>
<span id="L57"><span class="lineNum">      57</span>              :             mockVerdict: ConfirmAndPayMockVerdict.creditLimitInsufficient),</span>
<span id="L58"><span class="lineNum">      58</span>              :       ),</span>
<span id="L59"><span class="lineNum">      59</span>              :     );</span>
<span id="L60"><span class="lineNum">      60</span>              : </span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaGNC">           1 :     switch (entity.statusCode) {</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaGNC">           1 :       case CommonHttpClient.SUCCESS:</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaGNC">           3 :         emit(ConfirmPaymentSuccess(orderSession: entity.session));</span></span>
<span id="L64"><span class="lineNum">      64</span>              :         break;</span>
<span id="L65"><span class="lineNum">      65</span>              : </span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaGNC">           1 :       case CommonHttpClient.BAD_REQUEST:</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           1 :         handleStateIfBadRequest(entity);</span></span>
<span id="L68"><span class="lineNum">      68</span>              :         break;</span>
<span id="L69"><span class="lineNum">      69</span>              : </span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaGNC">           1 :       case CommonHttpClient.NO_INTERNET:</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">           1 :       case CommonHttpClient.SOCKET_ERRORS:</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaGNC">           3 :         emit(ConfirmPaymentNetworkError(ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L73"><span class="lineNum">      73</span>              :         break;</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaGNC">           1 :       case CommonHttpClient.LIMIT_EXCEEDED:</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           1 :         handleStateIfLimitExceeded(entity);</span></span>
<span id="L77"><span class="lineNum">      77</span>              :         break;</span>
<span id="L78"><span class="lineNum">      78</span>              : </span>
<span id="L79"><span class="lineNum">      79</span>              :       default:</span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaGNC">           3 :         emit(ConfirmAndPayCommonFailed(ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L81"><span class="lineNum">      81</span>              :         break;</span>
<span id="L82"><span class="lineNum">      82</span>              :     }</span>
<span id="L83"><span class="lineNum">      83</span>              :   }</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L86"><span class="lineNum">      86</span>              :   void handleStateIfBadRequest(ConfirmAndPayOrderEntity entity) {</span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           1 :     switch (entity.verdict) {</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictExpiredToken:</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">           2 :         emit(RequestPinPopup());</span></span>
<span id="L90"><span class="lineNum">      90</span>              :         break;</span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictSessionNotOpened:</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictSessionExpired:</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           3 :         emit(ConfirmPaymentFailedWhenOrderExpired(ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L95"><span class="lineNum">      95</span>              :         break;</span>
<span id="L96"><span class="lineNum">      96</span>              : </span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictOneLastTry:</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictInvalidCredential:</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaGNC">           3 :         emit(InvalidCredentialPin(ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L100"><span class="lineNum">     100</span>              :         break;</span>
<span id="L101"><span class="lineNum">     101</span>              : </span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictPromotionInvalid:</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictPromotionExpired:</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictPromotionUnqualified:</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictPromotionDuplicate:</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictPromotionPermissionDenied:</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaGNC">           3 :         emit(ConfirmPaymentPromotionError(ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L108"><span class="lineNum">     108</span>              :         break;</span>
<span id="L109"><span class="lineNum">     109</span>              : </span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictMissingPaymentMethod:</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaGNC">           3 :         emit(ConfirmPaymentMissingPaymentMethod(ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L112"><span class="lineNum">     112</span>              :         break;</span>
<span id="L113"><span class="lineNum">     113</span>              : </span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictCreditLimitInsufficientLimit:</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaGNC">           2 :         emit(ConfirmAndPayInsufficientCreditLimit());</span></span>
<span id="L116"><span class="lineNum">     116</span>              :         break;</span>
<span id="L117"><span class="lineNum">     117</span>              : </span>
<span id="L118"><span class="lineNum">     118</span>              :       default:</span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaGNC">           3 :         emit(ConfirmAndPayCommonFailed(ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L120"><span class="lineNum">     120</span>              :         break;</span>
<span id="L121"><span class="lineNum">     121</span>              :     }</span>
<span id="L122"><span class="lineNum">     122</span>              :   }</span>
<span id="L123"><span class="lineNum">     123</span>              : </span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L125"><span class="lineNum">     125</span>              :   void handleStateIfLimitExceeded(ConfirmAndPayOrderEntity entity) {</span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaGNC">           1 :     switch (entity.verdict) {</span></span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictLimitExceed:</span></span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaGNC">           3 :         emit(ConfirmAndPayCommonFailed(ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L129"><span class="lineNum">     129</span>              :         break;</span>
<span id="L130"><span class="lineNum">     130</span>              : </span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaGNC">           1 :       case ConfirmAndPayOrderEntity.verdictTransactionTooSoon:</span></span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaGNC">           3 :         emit(ConfirmAndPayTransactionTooSoon(ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L133"><span class="lineNum">     133</span>              :         break;</span>
<span id="L134"><span class="lineNum">     134</span>              : </span>
<span id="L135"><span class="lineNum">     135</span>              :       default:</span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaGNC">           3 :         emit(ConfirmAndPayCommonFailed(ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L137"><span class="lineNum">     137</span>              :         break;</span>
<span id="L138"><span class="lineNum">     138</span>              :     }</span>
<span id="L139"><span class="lineNum">     139</span>              :   }</span>
<span id="L140"><span class="lineNum">     140</span>              : </span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; checkShowWaitingPopUp({OrderSessionEntity? order}) async {</span></span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaGNC">           4 :     if (getIt.get&lt;FeatureToggle&gt;().enableActivatePOSLimitFeature == false) {</span></span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaGNC">           2 :       emit(NoNeedShowWaitingPopup(order: order));</span></span>
<span id="L144"><span class="lineNum">     144</span>              :       return;</span>
<span id="L145"><span class="lineNum">     145</span>              :     }</span>
<span id="L146"><span class="lineNum">     146</span>              : </span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaGNC">           2 :     final int? savedTime = await evoLocalStorageHelper.getLastTimeRequest3DSCardActivation();</span></span>
<span id="L148"><span class="lineNum">     148</span>              : </span>
<span id="L149"><span class="lineNum">     149</span>              :     if (savedTime == null) {</span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaGNC">           2 :       emit(NoNeedShowWaitingPopup(order: order));</span></span>
<span id="L151"><span class="lineNum">     151</span>              :       return;</span>
<span id="L152"><span class="lineNum">     152</span>              :     }</span>
<span id="L153"><span class="lineNum">     153</span>              : </span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaGNC">           2 :     final int remainingTime = ActivatePosLimitUtils().calculateRemainingCountdownTime(savedTime);</span></span>
<span id="L155"><span class="lineNum">     155</span>              : </span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaGNC">           1 :     if (remainingTime &gt; 0) {</span></span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaGNC">           2 :       emit(NeedShowWaitingPopup(remainingTime));</span></span>
<span id="L158"><span class="lineNum">     158</span>              :       return;</span>
<span id="L159"><span class="lineNum">     159</span>              :     }</span>
<span id="L160"><span class="lineNum">     160</span>              : </span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaGNC">           2 :     evoLocalStorageHelper.setLastTimeRequest3DSCardActivation(null);</span></span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaGNC">           2 :     emit(NoNeedShowWaitingPopup(order: order));</span></span>
<span id="L163"><span class="lineNum">     163</span>              :   }</span>
<span id="L164"><span class="lineNum">     164</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
