<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/profile/profile_screen/profile_appbar_widget.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/profile/profile_screen">lib/feature/profile/profile_screen</a> - profile_appbar_widget.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">34</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../../data/response/user_information_entity.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import '../../../resources/resources.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../../widget/evo_image_provider_widget.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import '../model/gender.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../profile_detail_screen/other_widgets/default_avatar_user_widget.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import 'card_status/widget/toggleable_masked_text_widget.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : class ProfileAppbarWidget extends StatelessWidget implements PreferredSizeWidget {</span>
<span id="L12"><span class="lineNum">      12</span>              :   final Widget child;</span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaUNC">           0 :   const ProfileAppbarWidget({required this.child, super.key});</span></span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span>              :   static const double appBarHeight = 72;</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L19"><span class="lineNum">      19</span>              :   Size get preferredSize =&gt; const Size(double.infinity, appBarHeight);</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L22"><span class="lineNum">      22</span>              :   Widget build(BuildContext context) {</span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaUNC">           0 :     return child;</span></span>
<span id="L24"><span class="lineNum">      24</span>              :   }</span>
<span id="L25"><span class="lineNum">      25</span>              : }</span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span>              : class ProfileAppbarChildWidget extends StatelessWidget {</span>
<span id="L28"><span class="lineNum">      28</span>              :   final VoidCallback? onViewProfileDetailClick;</span>
<span id="L29"><span class="lineNum">      29</span>              :   final UserInformationEntity? user;</span>
<span id="L30"><span class="lineNum">      30</span>              :   static const double _avatarSize = 50;</span>
<span id="L31"><span class="lineNum">      31</span>              :   static const double _circularAvatar = 50;</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :   const ProfileAppbarChildWidget({</span></span>
<span id="L34"><span class="lineNum">      34</span>              :     super.key,</span>
<span id="L35"><span class="lineNum">      35</span>              :     this.onViewProfileDetailClick,</span>
<span id="L36"><span class="lineNum">      36</span>              :     this.user,</span>
<span id="L37"><span class="lineNum">      37</span>              :   });</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L40"><span class="lineNum">      40</span>              :   Widget build(BuildContext context) {</span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :     final Gender? gender = Gender.formatGenderString(user?.gender);</span></span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :     final Widget defaultAvatar = DefaultAvatarUserWidget(</span></span>
<span id="L44"><span class="lineNum">      44</span>              :       gender: gender,</span>
<span id="L45"><span class="lineNum">      45</span>              :       cornerRadius: _circularAvatar,</span>
<span id="L46"><span class="lineNum">      46</span>              :       sizeAvatar: _avatarSize,</span>
<span id="L47"><span class="lineNum">      47</span>              :     );</span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :     return AppBar(</span></span>
<span id="L50"><span class="lineNum">      50</span>              :         toolbarHeight: ProfileAppbarWidget.appBarHeight,</span>
<span id="L51"><span class="lineNum">      51</span>              :         leadingWidth: 0,</span>
<span id="L52"><span class="lineNum">      52</span>              :         titleSpacing: 0,</span>
<span id="L53"><span class="lineNum">      53</span>              :         elevation: 0,</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :         backgroundColor: evoColors.background,</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :         title: InkWell(</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :             onTap: onViewProfileDetailClick,</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :             child: Padding(</span></span>
<span id="L58"><span class="lineNum">      58</span>              :                 padding: const EdgeInsets.symmetric(</span>
<span id="L59"><span class="lineNum">      59</span>              :                     horizontal: EvoDimension.profilePageHorizontalMargin, vertical: 8),</span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :                 child: Row(children: &lt;Widget&gt;[</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :                   ClipRRect(</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :                     borderRadius: BorderRadius.circular(_circularAvatar),</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :                     child: (user?.avatarUrl?.isNotEmpty == true)</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :                         ? EvoNetworkImageProviderWidget(</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :                             user?.avatarUrl,</span></span>
<span id="L66"><span class="lineNum">      66</span>              :                             width: _avatarSize,</span>
<span id="L67"><span class="lineNum">      67</span>              :                             height: _avatarSize,</span>
<span id="L68"><span class="lineNum">      68</span>              :                             fit: BoxFit.cover,</span>
<span id="L69"><span class="lineNum">      69</span>              :                             errorWidget: defaultAvatar,</span>
<span id="L70"><span class="lineNum">      70</span>              :                           )</span>
<span id="L71"><span class="lineNum">      71</span>              :                         : defaultAvatar,</span>
<span id="L72"><span class="lineNum">      72</span>              :                   ),</span>
<span id="L73"><span class="lineNum">      73</span>              :                   const SizedBox(width: 12),</span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :                   Expanded(</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :                       child: Column(</span></span>
<span id="L76"><span class="lineNum">      76</span>              :                           crossAxisAlignment: CrossAxisAlignment.start,</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :                           children: _getUserInfoContent())),</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :                   Padding(</span></span>
<span id="L79"><span class="lineNum">      79</span>              :                       padding: const EdgeInsets.only(left: 10, top: 10, bottom: 10),</span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :                       child: evoImageProvider.asset(EvoImages.icArrowRight,</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :                           width: 24, height: 24, color: evoColors.icon))</span></span>
<span id="L82"><span class="lineNum">      82</span>              :                 ]))));</span>
<span id="L83"><span class="lineNum">      83</span>              :   }</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :   List&lt;Widget&gt; _getUserInfoContent() {</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :     final List&lt;Widget&gt; content = &lt;Widget&gt;[</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :       Text(user?.getDisplayName() ?? EvoStrings.unKnowUser,</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :           style: evoTextStyles.h500().copyWith(fontSize: 22))</span></span>
<span id="L89"><span class="lineNum">      89</span>              :     ];</span>
<span id="L90"><span class="lineNum">      90</span>              : </span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :     if (user?.hasFullName() == true) {</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :       content.add(ToggleableMaskedTextWidget(</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :           unmaskedText: user?.phoneNumber,</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :           maskedText: user?.phoneNumber</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :               ?.hiddenByFormat((user?.phoneNumber?.length ?? 0) - phoneNumberFormatNumOfLastShow)));</span></span>
<span id="L96"><span class="lineNum">      96</span>              :     }</span>
<span id="L97"><span class="lineNum">      97</span>              : </span>
<span id="L98"><span class="lineNum">      98</span>              :     return content;</span>
<span id="L99"><span class="lineNum">      99</span>              :   }</span>
<span id="L100"><span class="lineNum">     100</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
