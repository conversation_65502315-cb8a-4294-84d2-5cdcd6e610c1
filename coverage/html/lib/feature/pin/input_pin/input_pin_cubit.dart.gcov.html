<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/pin/input_pin/input_pin_cubit.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/pin/input_pin">lib/feature/pin/input_pin</a> - input_pin_cubit.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">27</td>
            <td class="headerCovTableEntry">27</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/base/common_cubit.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/http_client/mock_config.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../../data/repository/authentication_repo.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../../data/response/sign_in_otp_entity.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../../util/functions.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../util/mock_file_name_utils/mock_authentication_file_name.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../util/secure_storage_helper/secure_storage_helper.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../pin_state.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import 'input_pin_screen.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span>              : class InputPinCubit extends CommonCubit&lt;PinState&gt; {</span>
<span id="L15"><span class="lineNum">      15</span>              :   final AuthenticationRepo _authenticationRepo;</span>
<span id="L16"><span class="lineNum">      16</span>              :   final EvoLocalStorageHelper _storageHelper;</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaGNC">           1 :   InputPinCubit(</span></span>
<span id="L19"><span class="lineNum">      19</span>              :     this._authenticationRepo,</span>
<span id="L20"><span class="lineNum">      20</span>              :     this._storageHelper,</span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC">           2 :   ) : super(PinState());</span></span>
<span id="L22"><span class="lineNum">      22</span>              : </span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           1 :   void validateLengthPin(String value) {</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">           2 :     final bool result = evoUtilFunction.validateMaxLengthPin(value);</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           3 :     emit(result ? PinFullState() : PinNotFullState());</span></span>
<span id="L26"><span class="lineNum">      26</span>              :   }</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; verifyPin({</span></span>
<span id="L29"><span class="lineNum">      29</span>              :     required String phoneNumber,</span>
<span id="L30"><span class="lineNum">      30</span>              :     String? pinCode,</span>
<span id="L31"><span class="lineNum">      31</span>              :     String? sessionToken,</span>
<span id="L32"><span class="lineNum">      32</span>              :     InputPinEntryPoint? entryPoint = InputPinEntryPoint.normal,</span>
<span id="L33"><span class="lineNum">      33</span>              :   }) async {</span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           2 :     emit(PinLoadingState());</span></span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           1 :     final SignInOtpEntity response = entryPoint == InputPinEntryPoint.fromDOE</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaGNC">           1 :         ? await _loginWithPinFromDOE(pinCode, sessionToken)</span></span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaGNC">           1 :         : await _loginWithPin(phoneNumber, pinCode, sessionToken);</span></span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           2 :     if (response.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           3 :       evoUtilFunction.updateProcessUserStatus(response.status);</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           2 :       emit(PinLoadedState(</span></span>
<span id="L42"><span class="lineNum">      42</span>              :         pinCode: pinCode,</span>
<span id="L43"><span class="lineNum">      43</span>              :         entity: response,</span>
<span id="L44"><span class="lineNum">      44</span>              :       ));</span>
<span id="L45"><span class="lineNum">      45</span>              :     } else {</span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           3 :       emit(PinErrorState(ErrorUIModel.fromEntity(response)));</span></span>
<span id="L47"><span class="lineNum">      47</span>              :     }</span>
<span id="L48"><span class="lineNum">      48</span>              :   }</span>
<span id="L49"><span class="lineNum">      49</span>              : </span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           1 :   void setPhoneNumber(String phoneNumber) {</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           2 :     _storageHelper.setUserPhoneNumber(phoneNumber);</span></span>
<span id="L52"><span class="lineNum">      52</span>              :   }</span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; loginWithAnotherPhoneNumber() async {</span></span>
<span id="L55"><span class="lineNum">      55</span>              :     // Clear current data first.</span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           2 :     await _storageHelper.clearAllUserData();</span></span>
<span id="L57"><span class="lineNum">      57</span>              :   }</span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           1 :   Future&lt;SignInOtpEntity&gt; _loginWithPin(</span></span>
<span id="L60"><span class="lineNum">      60</span>              :       String phoneNumber, String? pinCode, String? sessionToken) async {</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaGNC">           2 :     return await _authenticationRepo.login(</span></span>
<span id="L62"><span class="lineNum">      62</span>              :       TypeLogin.pin,</span>
<span id="L63"><span class="lineNum">      63</span>              :       phoneNumber: phoneNumber,</span>
<span id="L64"><span class="lineNum">      64</span>              :       pin: pinCode,</span>
<span id="L65"><span class="lineNum">      65</span>              :       sessionToken: sessionToken,</span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaGNC">           2 :       facialVerificationVersion: evoUtilFunction.getFacialVerificationVersion(),</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           1 :       mockConfig: MockConfig(</span></span>
<span id="L68"><span class="lineNum">      68</span>              :         enable: false,</span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">           1 :         fileName: getLoginMockFileName(),</span></span>
<span id="L70"><span class="lineNum">      70</span>              :         statusCode: CommonHttpClient.BAD_REQUEST,</span>
<span id="L71"><span class="lineNum">      71</span>              :       ),</span>
<span id="L72"><span class="lineNum">      72</span>              :     );</span>
<span id="L73"><span class="lineNum">      73</span>              :   }</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaGNC">           1 :   Future&lt;SignInOtpEntity&gt; _loginWithPinFromDOE(</span></span>
<span id="L76"><span class="lineNum">      76</span>              :     String? pinCode,</span>
<span id="L77"><span class="lineNum">      77</span>              :     String? sessionToken,</span>
<span id="L78"><span class="lineNum">      78</span>              :   ) async {</span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaGNC">           2 :     return await _authenticationRepo.loginFromDOE(</span></span>
<span id="L80"><span class="lineNum">      80</span>              :       type: TypeLogin.pin,</span>
<span id="L81"><span class="lineNum">      81</span>              :       pin: pinCode,</span>
<span id="L82"><span class="lineNum">      82</span>              :       sessionToken: sessionToken,</span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaGNC">           1 :       mockConfig: MockConfig(</span></span>
<span id="L84"><span class="lineNum">      84</span>              :         enable: false,</span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaGNC">           1 :         fileName: getLoginMockFileName(),</span></span>
<span id="L86"><span class="lineNum">      86</span>              :         statusCode: CommonHttpClient.BAD_REQUEST,</span>
<span id="L87"><span class="lineNum">      87</span>              :       ),</span>
<span id="L88"><span class="lineNum">      88</span>              :     );</span>
<span id="L89"><span class="lineNum">      89</span>              :   }</span>
<span id="L90"><span class="lineNum">      90</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
