<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/pin/create_pin/input_create_pin_widget.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/pin/create_pin">lib/feature/pin/create_pin</a> - input_create_pin_widget.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">75</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../../resources/resources.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../../widget/evo_pin_code/evo_pin_code_config.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'obscuring_text_editing_controller.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : class PinTextFieldWidget extends StatefulWidget {</span>
<span id="L8"><span class="lineNum">       8</span>              :   final ObscuringTextEditingController? controller;</span>
<span id="L9"><span class="lineNum">       9</span>              :   final FocusNode? focusNode;</span>
<span id="L10"><span class="lineNum">      10</span>              :   final String? errorText;</span>
<span id="L11"><span class="lineNum">      11</span>              :   final String? labelText;</span>
<span id="L12"><span class="lineNum">      12</span>              :   final TextStyle? labelStyle;</span>
<span id="L13"><span class="lineNum">      13</span>              :   final TextStyle? textStyle;</span>
<span id="L14"><span class="lineNum">      14</span>              :   final TextStyle? errorStyle;</span>
<span id="L15"><span class="lineNum">      15</span>              :   final Color? background;</span>
<span id="L16"><span class="lineNum">      16</span>              :   final TextInputType? keyboardType;</span>
<span id="L17"><span class="lineNum">      17</span>              :   final Widget? suffixIcon;</span>
<span id="L18"><span class="lineNum">      18</span>              :   final Widget? prefixIcon;</span>
<span id="L19"><span class="lineNum">      19</span>              :   final Widget? errorIcon;</span>
<span id="L20"><span class="lineNum">      20</span>              :   final bool obscureText;</span>
<span id="L21"><span class="lineNum">      21</span>              :   final int maxLength;</span>
<span id="L22"><span class="lineNum">      22</span>              :   final int? maxLines;</span>
<span id="L23"><span class="lineNum">      23</span>              :   final double? heightSuffixIcon;</span>
<span id="L24"><span class="lineNum">      24</span>              :   final double? widthSuffixIcon;</span>
<span id="L25"><span class="lineNum">      25</span>              :   final void Function(String)? onChange;</span>
<span id="L26"><span class="lineNum">      26</span>              :   final bool autoUnFocus;</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :   const PinTextFieldWidget(</span></span>
<span id="L29"><span class="lineNum">      29</span>              :       {super.key,</span>
<span id="L30"><span class="lineNum">      30</span>              :       this.background,</span>
<span id="L31"><span class="lineNum">      31</span>              :       this.controller,</span>
<span id="L32"><span class="lineNum">      32</span>              :       this.focusNode,</span>
<span id="L33"><span class="lineNum">      33</span>              :       this.textStyle,</span>
<span id="L34"><span class="lineNum">      34</span>              :       this.labelText,</span>
<span id="L35"><span class="lineNum">      35</span>              :       this.keyboardType,</span>
<span id="L36"><span class="lineNum">      36</span>              :       this.labelStyle,</span>
<span id="L37"><span class="lineNum">      37</span>              :       this.prefixIcon,</span>
<span id="L38"><span class="lineNum">      38</span>              :       this.suffixIcon,</span>
<span id="L39"><span class="lineNum">      39</span>              :       this.errorText,</span>
<span id="L40"><span class="lineNum">      40</span>              :       this.errorStyle,</span>
<span id="L41"><span class="lineNum">      41</span>              :       this.errorIcon,</span>
<span id="L42"><span class="lineNum">      42</span>              :       this.maxLines = 1,</span>
<span id="L43"><span class="lineNum">      43</span>              :       this.maxLength = EvoPinCodeConfig.maxPinCodeLength,</span>
<span id="L44"><span class="lineNum">      44</span>              :       this.obscureText = false,</span>
<span id="L45"><span class="lineNum">      45</span>              :       this.heightSuffixIcon,</span>
<span id="L46"><span class="lineNum">      46</span>              :       this.onChange,</span>
<span id="L47"><span class="lineNum">      47</span>              :       this.autoUnFocus = true,</span>
<span id="L48"><span class="lineNum">      48</span>              :       this.widthSuffixIcon});</span>
<span id="L49"><span class="lineNum">      49</span>              : </span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :   State&lt;PinTextFieldWidget&gt; createState() =&gt; _PinTextFieldWidgetState();</span></span>
<span id="L52"><span class="lineNum">      52</span>              : }</span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span>              : class _PinTextFieldWidgetState extends State&lt;PinTextFieldWidget&gt; {</span>
<span id="L55"><span class="lineNum">      55</span>              :   bool showObscureText = true;</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L58"><span class="lineNum">      58</span>              :   void initState() {</span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :     super.initState();</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :     widget.focusNode?.addListener(clearText);</span></span>
<span id="L61"><span class="lineNum">      61</span>              :   }</span>
<span id="L62"><span class="lineNum">      62</span>              : </span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L64"><span class="lineNum">      64</span>              :   void dispose() {</span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :     widget.focusNode?.removeListener(clearText);</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :     widget.focusNode?.dispose();</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :     super.dispose();</span></span>
<span id="L68"><span class="lineNum">      68</span>              :   }</span>
<span id="L69"><span class="lineNum">      69</span>              : </span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :   void clearText() {</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :     if (widget.focusNode?.hasFocus == true) {</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :       setState(() {</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :         widget.controller?.clear();</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :         widget.onChange?.call('');</span></span>
<span id="L75"><span class="lineNum">      75</span>              :       });</span>
<span id="L76"><span class="lineNum">      76</span>              :     }</span>
<span id="L77"><span class="lineNum">      77</span>              :   }</span>
<span id="L78"><span class="lineNum">      78</span>              : </span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L80"><span class="lineNum">      80</span>              :   Widget build(BuildContext context) {</span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :     return Column(children: &lt;Widget&gt;[</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :       TextField(</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :           controller: widget.controller,</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaUNC">           0 :           focusNode: widget.focusNode,</span></span>
<span id="L85"><span class="lineNum">      85</span>              :           enableInteractiveSelection: false,</span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :           keyboardType: widget.keyboardType ?? TextInputType.number,</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :           style: widget.textStyle ??</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :               evoTextStyles.h600(evoColors.textActive).copyWith(letterSpacing: 8),</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :           obscureText: widget.obscureText,</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :           maxLength: widget.maxLength,</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :           maxLines: widget.maxLines,</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :           onChanged: (String value) {</span></span>
<span id="L93"><span class="lineNum">      93</span>              :             ///auto unFocus when input maxLength and has focus</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :             if (value.length == widget.maxLength &amp;&amp;</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :                 widget.focusNode?.hasPrimaryFocus == true &amp;&amp;</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :                 widget.autoUnFocus) {</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :               widget.focusNode?.unfocus();</span></span>
<span id="L98"><span class="lineNum">      98</span>              :             }</span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :             widget.onChange?.call(value);</span></span>
<span id="L100"><span class="lineNum">     100</span>              :           },</span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :           decoration: InputDecoration(</span></span>
<span id="L102"><span class="lineNum">     102</span>              :               contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),</span>
<span id="L103"><span class="lineNum">     103</span>              :               isCollapsed: true,</span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :               labelText: widget.labelText,</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :               labelStyle: widget.labelStyle ??</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :                   evoTextStyles</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :                       .bodyLarge(</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :                           widget.errorText == null ? evoColors.textPassive2 : evoColors.error)</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :                       .copyWith(letterSpacing: 0),</span></span>
<span id="L110"><span class="lineNum">     110</span>              :               counterText: '',</span>
<span id="L111"><span class="lineNum">     111</span>              :               errorText:</span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :                   widget.errorText == null || widget.controller?.text.isEmpty == true ? null : '',</span></span>
<span id="L113"><span class="lineNum">     113</span>              :               errorStyle: const TextStyle(fontSize: 0),</span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :               focusedBorder: OutlineInputBorder(</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :                   borderSide: BorderSide(color: evoColors.primary),</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :                   borderRadius: BorderRadius.circular(8)),</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :               enabledBorder: OutlineInputBorder(</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :                   borderSide: BorderSide(color: evoColors.textHint),</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :                   borderRadius: BorderRadius.circular(8)),</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :               errorBorder: OutlineInputBorder(</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :                   borderSide: BorderSide(color: evoColors.error),</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :                   borderRadius: BorderRadius.circular(8)),</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :               focusedErrorBorder: OutlineInputBorder(</span></span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaUNC">           0 :                   borderSide: BorderSide(color: evoColors.error),</span></span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :                   borderRadius: BorderRadius.circular(8)),</span></span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :               suffixIcon: _itemObscuring(),</span></span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :               suffixIconConstraints: BoxConstraints(</span></span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :                 maxHeight: widget.heightSuffixIcon ?? 48,</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :                 maxWidth: widget.widthSuffixIcon ?? 48,</span></span>
<span id="L130"><span class="lineNum">     130</span>              :               ))),</span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :       if (widget.errorText != null &amp;&amp; widget.controller?.text.isNotEmpty == true)</span></span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaUNC">           0 :         Row(children: &lt;Widget&gt;[</span></span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :           widget.errorIcon ??</span></span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :               evoImageProvider.asset(EvoImages.icPinCodeError, width: 16, fit: BoxFit.fitWidth),</span></span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :           Expanded(</span></span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :               child: Padding(</span></span>
<span id="L137"><span class="lineNum">     137</span>              :             padding: const EdgeInsets.symmetric(horizontal: 8),</span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :             child: Text(widget.errorText ?? '',</span></span>
<span id="L139"><span class="lineNum">     139</span>              :                 maxLines: 2,</span>
<span id="L140"><span class="lineNum">     140</span>              :                 overflow: TextOverflow.ellipsis,</span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaUNC">           0 :                 style: widget.errorStyle ?? evoTextStyles.bodySmall(color: evoColors.error)),</span></span>
<span id="L142"><span class="lineNum">     142</span>              :           ))</span>
<span id="L143"><span class="lineNum">     143</span>              :         ])</span>
<span id="L144"><span class="lineNum">     144</span>              :     ]);</span>
<span id="L145"><span class="lineNum">     145</span>              :   }</span>
<span id="L146"><span class="lineNum">     146</span>              : </span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :   Widget _itemObscuring() {</span></span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaUNC">           0 :     return GestureDetector(</span></span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :         onTap: () {</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :           setState(() {</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :             showObscureText = !showObscureText;</span></span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :             widget.controller?.updateShowObscureText = showObscureText;</span></span>
<span id="L153"><span class="lineNum">     153</span>              :           });</span>
<span id="L154"><span class="lineNum">     154</span>              :         },</span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaUNC">           0 :         child: Container(</span></span>
<span id="L156"><span class="lineNum">     156</span>              :           padding: const EdgeInsets.only(right: 16, top: 8, bottom: 8, left: 8),</span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :           child: widget.suffixIcon ??</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :               evoImageProvider.asset(</span></span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :                   showObscureText ? EvoImages.icShowOffPin : EvoImages.icShowOnPin,</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :                   color: evoColors.textPassive2),</span></span>
<span id="L161"><span class="lineNum">     161</span>              :         ));</span>
<span id="L162"><span class="lineNum">     162</span>              :   }</span>
<span id="L163"><span class="lineNum">     163</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
