<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/manual_link_card/model/linked_card_status_model.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/manual_link_card/model">lib/feature/manual_link_card/model</a> - linked_card_status_model.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">19</td>
            <td class="headerCovTableEntry">19</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import '../../../data/response/action_entity.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import '../../../data/response/linked_card_status_checking_entity.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : enum LinkedCardStatusVerdict {</span>
<span id="L5"><span class="lineNum">       5</span>              :   verdictSuccess(LinkedCardStatusCheckingEntity.verdictSuccess),</span>
<span id="L6"><span class="lineNum">       6</span>              :   verdictUnfulfilledCard(LinkedCardStatusCheckingEntity.verdictUnfulfilledCard),</span>
<span id="L7"><span class="lineNum">       7</span>              :   verdictWaitingForCardIssuing(LinkedCardStatusCheckingEntity.verdictWaitingForCardIssuing),</span>
<span id="L8"><span class="lineNum">       8</span>              :   verdictUnqualifiedCard(LinkedCardStatusCheckingEntity.verdictUnqualifiedCard),</span>
<span id="L9"><span class="lineNum">       9</span>              :   verdictDuplicatedLinkRequest(LinkedCardStatusCheckingEntity.verdictDuplicatedLinkRequest),</span>
<span id="L10"><span class="lineNum">      10</span>              :   verdictUnqualifiedUserInformation(</span>
<span id="L11"><span class="lineNum">      11</span>              :       LinkedCardStatusCheckingEntity.verdictUnqualifiedUserInformation),</span>
<span id="L12"><span class="lineNum">      12</span>              : </span>
<span id="L13"><span class="lineNum">      13</span>              :   // PREPARE LINK CARD</span>
<span id="L14"><span class="lineNum">      14</span>              :   verdictLinkCardInvalidPhoneNumber(</span>
<span id="L15"><span class="lineNum">      15</span>              :       LinkedCardStatusCheckingEntity.verdictLinkCardInvalidPhoneNumber),</span>
<span id="L16"><span class="lineNum">      16</span>              :   verdictLinkCardAlreadyLinked(LinkedCardStatusCheckingEntity.verdictLinkCardAlreadyLinked),</span>
<span id="L17"><span class="lineNum">      17</span>              :   verdictLinkCardUnqualifiedUserInformation(</span>
<span id="L18"><span class="lineNum">      18</span>              :       LinkedCardStatusCheckingEntity.verdictLinkCardUnqualifiedUserInformation),</span>
<span id="L19"><span class="lineNum">      19</span>              :   verdictLinkCardInvalidParameters(LinkedCardStatusCheckingEntity.verdictLinkCardInvalidParameters),</span>
<span id="L20"><span class="lineNum">      20</span>              :   verdictLinkCardLinkRequestIsProcessing(</span>
<span id="L21"><span class="lineNum">      21</span>              :       LinkedCardStatusCheckingEntity.verdictLinkCardLinkRequestIsProcessing),</span>
<span id="L22"><span class="lineNum">      22</span>              :   verdictLinkCardInvalidBankCode(LinkedCardStatusCheckingEntity.verdictLinkCardInvalidBankCode),</span>
<span id="L23"><span class="lineNum">      23</span>              :   verdictLinkCardNotFoundLinkInfo(LinkedCardStatusCheckingEntity.verdictLinkCardNotFoundLinkInfo),</span>
<span id="L24"><span class="lineNum">      24</span>              :   verdictLinkCardBankProductNotSupported(</span>
<span id="L25"><span class="lineNum">      25</span>              :       LinkedCardStatusCheckingEntity.verdictLinkCardBankProductNotSupported),</span>
<span id="L26"><span class="lineNum">      26</span>              :   verdictLinkCardFailure(LinkedCardStatusCheckingEntity.verdictLinkCardFailure),</span>
<span id="L27"><span class="lineNum">      27</span>              :   verdictFailureAll(LinkedCardStatusCheckingEntity.verdictFailureAll),</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span>              :   /// it is not verdict from BE returned. it is defined if BE verdict is not in known values:</span>
<span id="L30"><span class="lineNum">      30</span>              :   /// 'success' | 'unfulfilled_card' | 'waiting_for_card_issuing' | 'unqualified_card' | 'duplicated_link_request' | 'unqualified_user_information'</span>
<span id="L31"><span class="lineNum">      31</span>              :   unknown('unknown');</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span>              :   final String value;</span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span>              :   const LinkedCardStatusVerdict(this.value);</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaGNC">           1 :   static LinkedCardStatusVerdict formatStatusString(String? value) {</span></span>
<span id="L38"><span class="lineNum">      38</span>              :     switch (value) {</span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictSuccess:</span></span>
<span id="L40"><span class="lineNum">      40</span>              :         return LinkedCardStatusVerdict.verdictSuccess;</span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictUnfulfilledCard:</span></span>
<span id="L42"><span class="lineNum">      42</span>              :         return LinkedCardStatusVerdict.verdictUnfulfilledCard;</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictWaitingForCardIssuing:</span></span>
<span id="L44"><span class="lineNum">      44</span>              :         return LinkedCardStatusVerdict.verdictWaitingForCardIssuing;</span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictUnqualifiedCard:</span></span>
<span id="L46"><span class="lineNum">      46</span>              :         return LinkedCardStatusVerdict.verdictUnqualifiedCard;</span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictDuplicatedLinkRequest:</span></span>
<span id="L48"><span class="lineNum">      48</span>              :         return LinkedCardStatusVerdict.verdictDuplicatedLinkRequest;</span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictUnqualifiedUserInformation:</span></span>
<span id="L50"><span class="lineNum">      50</span>              :         return LinkedCardStatusVerdict.verdictUnqualifiedUserInformation;</span>
<span id="L51"><span class="lineNum">      51</span>              : </span>
<span id="L52"><span class="lineNum">      52</span>              :       // PREPARE LINK CARD</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictLinkCardInvalidPhoneNumber:</span></span>
<span id="L54"><span class="lineNum">      54</span>              :         return LinkedCardStatusVerdict.verdictLinkCardInvalidPhoneNumber;</span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictLinkCardAlreadyLinked:</span></span>
<span id="L56"><span class="lineNum">      56</span>              :         return LinkedCardStatusVerdict.verdictLinkCardAlreadyLinked;</span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictLinkCardInvalidParameters:</span></span>
<span id="L58"><span class="lineNum">      58</span>              :         return LinkedCardStatusVerdict.verdictLinkCardInvalidParameters;</span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictLinkCardLinkRequestIsProcessing:</span></span>
<span id="L60"><span class="lineNum">      60</span>              :         return LinkedCardStatusVerdict.verdictLinkCardLinkRequestIsProcessing;</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictLinkCardInvalidBankCode:</span></span>
<span id="L62"><span class="lineNum">      62</span>              :         return LinkedCardStatusVerdict.verdictLinkCardInvalidBankCode;</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictLinkCardNotFoundLinkInfo:</span></span>
<span id="L64"><span class="lineNum">      64</span>              :         return LinkedCardStatusVerdict.verdictLinkCardNotFoundLinkInfo;</span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictLinkCardBankProductNotSupported:</span></span>
<span id="L66"><span class="lineNum">      66</span>              :         return LinkedCardStatusVerdict.verdictLinkCardBankProductNotSupported;</span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictLinkCardFailure:</span></span>
<span id="L68"><span class="lineNum">      68</span>              :         return LinkedCardStatusVerdict.verdictLinkCardFailure;</span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">           1 :       case LinkedCardStatusCheckingEntity.verdictFailureAll:</span></span>
<span id="L70"><span class="lineNum">      70</span>              :         return LinkedCardStatusVerdict.verdictFailureAll;</span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span>              :       default:</span>
<span id="L73"><span class="lineNum">      73</span>              :         return unknown;</span>
<span id="L74"><span class="lineNum">      74</span>              :     }</span>
<span id="L75"><span class="lineNum">      75</span>              :   }</span>
<span id="L76"><span class="lineNum">      76</span>              : }</span>
<span id="L77"><span class="lineNum">      77</span>              : </span>
<span id="L78"><span class="lineNum">      78</span>              : class LinkedCardStatusModel {</span>
<span id="L79"><span class="lineNum">      79</span>              :   final String? userMessage;</span>
<span id="L80"><span class="lineNum">      80</span>              :   final String? verdict;</span>
<span id="L81"><span class="lineNum">      81</span>              :   final ActionEntity? action;</span>
<span id="L82"><span class="lineNum">      82</span>              : </span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaGNC">           5 :   LinkedCardStatusModel({required this.verdict, this.userMessage, this.action});</span></span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaGNC">           1 :   LinkedCardStatusVerdict getVerdictLinkedCardStatusModel() {</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaGNC">           2 :     return LinkedCardStatusVerdict.formatStatusString(verdict);</span></span>
<span id="L87"><span class="lineNum">      87</span>              :   }</span>
<span id="L88"><span class="lineNum">      88</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
