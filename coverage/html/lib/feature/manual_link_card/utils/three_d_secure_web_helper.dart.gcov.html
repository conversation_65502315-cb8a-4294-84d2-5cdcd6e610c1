<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/manual_link_card/utils/three_d_secure_web_helper.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/manual_link_card/utils">lib/feature/manual_link_card/utils</a> - three_d_secure_web_helper.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">14</td>
            <td class="headerCovTableEntry">14</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/feature/webview/common_webview_controller.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : import '../../../widget/evo_appbar.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../../widget/evo_appbar_leading_button.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../../widget/evo_error_web_view_widget.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../webview/models/evo_webview_arg.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : </span>
<span id="L10"><span class="lineNum">      10</span>              : class ThreeDSecureWebHelper {</span>
<span id="L11"><span class="lineNum">      11</span> <span class="tlaGNC">           1 :   EvoWebViewArg createEvoWebViewArg({</span></span>
<span id="L12"><span class="lineNum">      12</span>              :     String? url,</span>
<span id="L13"><span class="lineNum">      13</span>              :     String? title,</span>
<span id="L14"><span class="lineNum">      14</span>              :     Widget? leadingImage,</span>
<span id="L15"><span class="lineNum">      15</span>              :     VoidCallback? onBackPressed,</span>
<span id="L16"><span class="lineNum">      16</span>              :     CommonWebViewController? controller,</span>
<span id="L17"><span class="lineNum">      17</span>              :   }) {</span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaGNC">           1 :     return EvoWebViewArg(</span></span>
<span id="L19"><span class="lineNum">      19</span>              :       title: '',</span>
<span id="L20"><span class="lineNum">      20</span>              :       url: url ?? '',</span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC">           2 :       errorWidget: (String error, VoidCallback onReload) =&gt; errorWebViewWidget(onReload: onReload),</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           1 :       appBar: evoAppBar(title: title, leadingImage: leadingImage, onBackPressed: onBackPressed),</span></span>
<span id="L23"><span class="lineNum">      23</span>              :       controller: controller,</span>
<span id="L24"><span class="lineNum">      24</span>              :     );</span>
<span id="L25"><span class="lineNum">      25</span>              :   }</span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           1 :   Widget errorWebViewWidget({VoidCallback? onReload}) =&gt; EvoErrorWebViewWidget(</span></span>
<span id="L29"><span class="lineNum">      29</span>              :         onReloadWebView: onReload,</span>
<span id="L30"><span class="lineNum">      30</span>              :       );</span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L33"><span class="lineNum">      33</span>              :   EvoAppBar evoAppBar({</span>
<span id="L34"><span class="lineNum">      34</span>              :     String? title,</span>
<span id="L35"><span class="lineNum">      35</span>              :     Widget? leadingImage,</span>
<span id="L36"><span class="lineNum">      36</span>              :     VoidCallback? onBackPressed,</span>
<span id="L37"><span class="lineNum">      37</span>              :   }) =&gt;</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           1 :       EvoAppBar(</span></span>
<span id="L39"><span class="lineNum">      39</span>              :         title: title ?? '',</span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           1 :         leading: EvoAppBarLeadingButton(image: leadingImage, onPressed: onBackPressed),</span></span>
<span id="L41"><span class="lineNum">      41</span>              :       );</span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           1 :   CommonWebViewController createWebViewController({</span></span>
<span id="L44"><span class="lineNum">      44</span>              :     void Function(Uri?)? onRedirectUrl,</span>
<span id="L45"><span class="lineNum">      45</span>              :     VoidCallback? onWebLoaded,</span>
<span id="L46"><span class="lineNum">      46</span>              :   }) =&gt;</span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           1 :       CommonWebViewController(</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           1 :         onRedirectUrl: (Uri? uri, _) {</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           2 :           commonLog('CommonWebViewController load $uri');</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           1 :           onRedirectUrl?.call(uri);</span></span>
<span id="L51"><span class="lineNum">      51</span>              :         },</span>
<span id="L52"><span class="lineNum">      52</span>              :         onLoaded: onWebLoaded,</span>
<span id="L53"><span class="lineNum">      53</span>              :       );</span>
<span id="L54"><span class="lineNum">      54</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
