<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/logging/evo_event_tracking_screen_id.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/logging">lib/feature/logging</a> - evo_event_tracking_screen_id.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">1</td>
            <td class="headerCovTableEntry">1</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : class EvoEventTrackingScreenId extends EventTrackingScreenId {</span>
<span id="L4"><span class="lineNum">       4</span>              :   // Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3380117684/Event+Tracking+Framework#Special-events</span>
<span id="L5"><span class="lineNum">       5</span>              :   static const EvoEventTrackingScreenId special = EvoEventTrackingScreenId('0000');</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              :   /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/3403317396/Event+Tracking+-+Home+page</span>
<span id="L8"><span class="lineNum">       8</span>              :   static const EvoEventTrackingScreenId nonUserHomeScreen = EvoEventTrackingScreenId('1006');</span>
<span id="L9"><span class="lineNum">       9</span>              : </span>
<span id="L10"><span class="lineNum">      10</span>              :   static const EvoEventTrackingScreenId policyAgreementScreen = EvoEventTrackingScreenId('1007');</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span>              :   /// Payment: https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/3403220152/Event+Tracking+-+Payment+flow</span>
<span id="L13"><span class="lineNum">      13</span>              :   static const EvoEventTrackingScreenId scanQrScreen = EvoEventTrackingScreenId('3000');</span>
<span id="L14"><span class="lineNum">      14</span>              :   static const EvoEventTrackingScreenId orderAmountInputScreen = EvoEventTrackingScreenId('3001');</span>
<span id="L15"><span class="lineNum">      15</span>              :   static const EvoEventTrackingScreenId checkOutScreen = EvoEventTrackingScreenId('3002');</span>
<span id="L16"><span class="lineNum">      16</span>              :   static const EvoEventTrackingScreenId threeDSecureScreen = EvoEventTrackingScreenId('3003');</span>
<span id="L17"><span class="lineNum">      17</span>              :   static const EvoEventTrackingScreenId paymentResultScreen = EvoEventTrackingScreenId('3004');</span>
<span id="L18"><span class="lineNum">      18</span>              :   static const EvoEventTrackingScreenId emiNotSupportScreen = EvoEventTrackingScreenId('3005');</span>
<span id="L19"><span class="lineNum">      19</span>              :   static const EvoEventTrackingScreenId emiOptionsScreen = EvoEventTrackingScreenId('3006');</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span>              :   /// Promotion/Campaign: https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/3403220176/Event+Tracking+-+Promotion+pages</span>
<span id="L22"><span class="lineNum">      22</span>              :   static const EvoEventTrackingScreenId campaignListingScreen = EvoEventTrackingScreenId('5000');</span>
<span id="L23"><span class="lineNum">      23</span>              :   static const EvoEventTrackingScreenId campaignDetailScreen = EvoEventTrackingScreenId('5001');</span>
<span id="L24"><span class="lineNum">      24</span>              :   static const EvoEventTrackingScreenId offerListingScreen = EvoEventTrackingScreenId('5002');</span>
<span id="L25"><span class="lineNum">      25</span>              :   static const EvoEventTrackingScreenId offerDetailScreen = EvoEventTrackingScreenId('5003');</span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span>              :   /// User: https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/3403382859/Event+Tracking+-+User+Management+pages</span>
<span id="L28"><span class="lineNum">      28</span>              :   static const EvoEventTrackingScreenId userProfileScreen = EvoEventTrackingScreenId('2000');</span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaGNC">         950 :   const EvoEventTrackingScreenId(super.name);</span></span>
<span id="L31"><span class="lineNum">      31</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
