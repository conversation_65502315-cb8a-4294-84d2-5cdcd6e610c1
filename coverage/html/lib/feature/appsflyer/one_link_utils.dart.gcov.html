<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/appsflyer/one_link_utils.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/appsflyer">lib/feature/appsflyer</a> - one_link_utils.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">15</td>
            <td class="headerCovTableEntry">15</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/flavors/flavor_config.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../flavors/flavors_type.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../prepare_for_app_initiation.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'model/one_link_model.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'one_link_constants.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span> <span class="tlaGNC">          84 : OneLinkUtils get oneLinkUtils =&gt; getIt.get&lt;OneLinkUtils&gt;();</span></span>
<span id="L9"><span class="lineNum">       9</span>              : </span>
<span id="L10"><span class="lineNum">      10</span>              : class OneLinkUtils {</span>
<span id="L11"><span class="lineNum">      11</span>              :   ///Refer format from</span>
<span id="L12"><span class="lineNum">      12</span>              :   /// https://trustingsocial1.atlassian.net/browse/EMA-3010</span>
<span id="L13"><span class="lineNum">      13</span>              :   ///</span>
<span id="L14"><span class="lineNum">      14</span>              :   /// Why append string to use long link instead use SDK generated short link?</span>
<span id="L15"><span class="lineNum">      15</span>              :   ///</span>
<span id="L16"><span class="lineNum">      16</span>              :   /// * The Appsflyer SDK has provided the function [generateInviteLink]</span>
<span id="L17"><span class="lineNum">      17</span>              :   ///   It will return short link, eg. https://evoappvn-stag.onelink.me/Blki/hhnnlh08</span>
<span id="L18"><span class="lineNum">      18</span>              :   ///   If want to verify that link contain enough and right format,</span>
<span id="L19"><span class="lineNum">      19</span>              :   ///   We need call API: https://onelink.appsflyer.com/shortlink/v1/{one-link-id}, Refer: https://dev.appsflyer.com/hc/reference/get-onelink-attribution-link</span>
<span id="L20"><span class="lineNum">      20</span>              :   ///</span>
<span id="L21"><span class="lineNum">      21</span>              :   ///   Evo Appsflyer plan is a custom plan, and not contain that API</span>
<span id="L22"><span class="lineNum">      22</span>              :   ///   So it difficult to verify the link generated have right format</span>
<span id="L23"><span class="lineNum">      23</span>              :   ///</span>
<span id="L24"><span class="lineNum">      24</span>              :   /// * In the other hand: when use [generateInviteLink], we also need define most of parameters:</span>
<span id="L25"><span class="lineNum">      25</span>              :   ///   Eg. final inviteLinkParams = AppsFlyerInviteLinkParams(</span>
<span id="L26"><span class="lineNum">      26</span>              :   ///       campaign: 'campaign',</span>
<span id="L27"><span class="lineNum">      27</span>              :   ///       baseDeepLink: 'our_deeplink',</span>
<span id="L28"><span class="lineNum">      28</span>              :   ///       customParams: {</span>
<span id="L29"><span class="lineNum">      29</span>              :   ///         'pid': 'evo_dop_nfc',</span>
<span id="L30"><span class="lineNum">      30</span>              :   ///         'af_force_deeplink': 'true',</span>
<span id="L31"><span class="lineNum">      31</span>              :   ///         'deep_link_value':'our_deeplink?params1&amp;params2'</span>
<span id="L32"><span class="lineNum">      32</span>              :   ///       },</span>
<span id="L33"><span class="lineNum">      33</span>              :   ///     );</span>
<span id="L34"><span class="lineNum">      34</span>              :   ///</span>
<span id="L35"><span class="lineNum">      35</span>              :   /// * Because above reason, so at this time, we use long-link by append String</span>
<span id="L36"><span class="lineNum">      36</span>              :   ///</span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaGNC">           1 :   String generateOneLink(OneLinkModel oneLinkModel) {</span></span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           1 :     final String baseURL = OneLinkURLConstants.getOneLinkURLByEnv();</span></span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           2 :     final String encodedFallbackUrl = Uri.encodeComponent(oneLinkModel.afDP);</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           2 :     final String encodedDeeplinkValue = Uri.encodeComponent(oneLinkModel.deepLinkValue);</span></span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           3 :     return '$baseURL?${OneLinkKey.afXp.value}=${oneLinkModel.afXp}'</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           2 :         '&amp;${OneLinkKey.pid.value}=${oneLinkModel.pid}'</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           2 :         '&amp;${OneLinkKey.c.value}=${oneLinkModel.c}'</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           1 :         '&amp;${OneLinkKey.afDP.value}=$encodedFallbackUrl'</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           1 :         '&amp;${OneLinkKey.deepLinkValue.value}=$encodedDeeplinkValue'</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           2 :         '&amp;${OneLinkKey.afForceDeeplink.value}=${oneLinkModel.afForceDeeplink}';</span></span>
<span id="L48"><span class="lineNum">      48</span>              :   }</span>
<span id="L49"><span class="lineNum">      49</span>              : </span>
<span id="L50"><span class="lineNum">      50</span>              :   /// Regex of EVO onelink</span>
<span id="L51"><span class="lineNum">      51</span>              :   /// - evo one link stag: https://evoappvn-stag.onelink.me</span>
<span id="L52"><span class="lineNum">      52</span>              :   /// - evo one link uat: https://evoappvn-uat.onelink.me</span>
<span id="L53"><span class="lineNum">      53</span>              :   /// - evo one link prod: https://evoappvn.onelink.me</span>
<span id="L54"><span class="lineNum">      54</span>              :   /// Refer ticket: https://trustingsocial1.atlassian.net/browse/EMA-4087</span>
<span id="L55"><span class="lineNum">      55</span>              :   ///</span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           2 :   RegExp getRegExpOfOneLink() {</span></span>
<span id="L57"><span class="lineNum">      57</span>              :     String pattern;</span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           8 :     if (FlavorConfig.instance.flavor == FlavorType.prod.name) {</span></span>
<span id="L60"><span class="lineNum">      60</span>              :       pattern = r'^https:\/\/(www\.)?evoappvn\.onelink\.me(?:\/.*)?(?:\?.*)?$';</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaGNC">           8 :     } else if (FlavorConfig.instance.flavor == FlavorType.uat.name) {</span></span>
<span id="L62"><span class="lineNum">      62</span>              :       pattern = r'^https:\/\/(www\.)?evoappvn-uat\.onelink\.me(?:\/.*)?(?:\?.*)?$';</span>
<span id="L63"><span class="lineNum">      63</span>              :     } else {</span>
<span id="L64"><span class="lineNum">      64</span>              :       pattern = r'^https:\/\/(www\.)?evoappvn-stag\.onelink\.me(?:\/.*)?(?:\?.*)?$';</span>
<span id="L65"><span class="lineNum">      65</span>              :     }</span>
<span id="L66"><span class="lineNum">      66</span>              : </span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           2 :     return RegExp(pattern);</span></span>
<span id="L68"><span class="lineNum">      68</span>              :   }</span>
<span id="L69"><span class="lineNum">      69</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
