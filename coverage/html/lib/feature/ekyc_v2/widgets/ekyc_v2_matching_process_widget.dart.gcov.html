<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/ekyc_v2/widgets/ekyc_v2_matching_process_widget.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/ekyc_v2/widgets">lib/feature/ekyc_v2/widgets</a> - ekyc_v2_matching_process_widget.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">17</td>
            <td class="headerCovTableEntry">17</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../../resources/resources.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../../util/ui_utils/evo_ui_utils.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import '../../../widget/animation/lottie_animation_widget.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : /// this widget is used to show loading of eKYC process when matching process</span>
<span id="L8"><span class="lineNum">       8</span>              : /// such as:</span>
<span id="L9"><span class="lineNum">       9</span>              : ///   - Matching face OTP</span>
<span id="L10"><span class="lineNum">      10</span>              : ///   - ....</span>
<span id="L11"><span class="lineNum">      11</span>              : class EkycV2MatchingProcessWidget extends StatelessWidget {</span>
<span id="L12"><span class="lineNum">      12</span>              :   @visibleForTesting</span>
<span id="L13"><span class="lineNum">      13</span>              :   final double loadingHeightPercentage = 0.122;</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span>              :   @visibleForTesting</span>
<span id="L16"><span class="lineNum">      16</span>              :   final double loadingPaddingTopPercentage = 0.25;</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span>              :   @visibleForTesting</span>
<span id="L19"><span class="lineNum">      19</span>              :   final double loadingPaddingBottomPercentage = 0.068;</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC">         950 :   const EkycV2MatchingProcessWidget({</span></span>
<span id="L22"><span class="lineNum">      22</span>              :     super.key,</span>
<span id="L23"><span class="lineNum">      23</span>              :   });</span>
<span id="L24"><span class="lineNum">      24</span>              : </span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L26"><span class="lineNum">      26</span>              :   Widget build(BuildContext context) {</span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           1 :     return Center(</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           1 :       child: Column(</span></span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaGNC">           1 :         children: &lt;Widget&gt;[</span></span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaGNC">           1 :           SizedBox(</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaGNC">           2 :             height: EvoUiUtils().calculateVerticalSpace(</span></span>
<span id="L32"><span class="lineNum">      32</span>              :               context: context,</span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaGNC">           1 :               heightPercentage: loadingPaddingTopPercentage,</span></span>
<span id="L34"><span class="lineNum">      34</span>              :             ),</span>
<span id="L35"><span class="lineNum">      35</span>              :           ),</span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaGNC">           1 :           LottieAnimationWidget(</span></span>
<span id="L37"><span class="lineNum">      37</span>              :             EvoAnimation.animationHubLoading,</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           2 :             size: EvoUiUtils().calculateVerticalSpace(</span></span>
<span id="L39"><span class="lineNum">      39</span>              :               context: context,</span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           1 :               heightPercentage: loadingHeightPercentage,</span></span>
<span id="L41"><span class="lineNum">      41</span>              :             ),</span>
<span id="L42"><span class="lineNum">      42</span>              :           ),</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           1 :           SizedBox(</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           2 :             height: EvoUiUtils().calculateVerticalSpace(</span></span>
<span id="L45"><span class="lineNum">      45</span>              :               context: context,</span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           1 :               heightPercentage: loadingPaddingBottomPercentage,</span></span>
<span id="L47"><span class="lineNum">      47</span>              :             ),</span>
<span id="L48"><span class="lineNum">      48</span>              :           ),</span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           1 :           Padding(</span></span>
<span id="L50"><span class="lineNum">      50</span>              :             padding: const EdgeInsets.symmetric(horizontal: 45),</span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           1 :             child: Text(</span></span>
<span id="L52"><span class="lineNum">      52</span>              :               EvoStrings.faceOtpMatchingProcessing,</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           2 :               style: evoTextStyles.h300(),</span></span>
<span id="L54"><span class="lineNum">      54</span>              :               textAlign: TextAlign.center,</span>
<span id="L55"><span class="lineNum">      55</span>              :             ),</span>
<span id="L56"><span class="lineNum">      56</span>              :           ),</span>
<span id="L57"><span class="lineNum">      57</span>              :         ],</span>
<span id="L58"><span class="lineNum">      58</span>              :       ),</span>
<span id="L59"><span class="lineNum">      59</span>              :     );</span>
<span id="L60"><span class="lineNum">      60</span>              :   }</span>
<span id="L61"><span class="lineNum">      61</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
