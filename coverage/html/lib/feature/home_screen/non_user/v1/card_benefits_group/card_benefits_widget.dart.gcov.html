<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/home_screen/non_user/v1/card_benefits_group/card_benefits_widget.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/home_screen/non_user/v1/card_benefits_group">lib/feature/home_screen/non_user/v1/card_benefits_group</a> - card_benefits_widget.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">68</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/common_package/common_package.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/resources/resources.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/ui_component/ui_component.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/widget/shimmer/shimmer.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'package:flutter_common_package/widget/widgets.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../../../data/repository/home_repo.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../../../data/response/card_benefits_button_item_entity.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../../../data/response/card_benefits_entity.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../../../data/response/card_benefits_item_entity.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../../../model/evo_action_model.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../../../../prepare_for_app_initiation.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../../../../resources/resources.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../../../../util/evo_action_handler.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../../../../util/mapper.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../../../../../widget/evo_image_provider_widget.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../../../../../widget/evo_inkwell_network_image.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import 'card_benefits_cubit.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import 'skeleton_card_benefits_widget.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : </span>
<span id="L23"><span class="lineNum">      23</span>              : /// Card Benefits</span>
<span id="L24"><span class="lineNum">      24</span>              : /// LinK: https://trustingsocial1.atlassian.net/browse/EMA-267</span>
<span id="L25"><span class="lineNum">      25</span>              : class CardBenefitWidget extends UIComponentWidget {</span>
<span id="L26"><span class="lineNum">      26</span>              :   final double heightCardImage;</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :   const CardBenefitWidget({super.key, super.controller, this.heightCardImage = 127});</span></span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :   State&lt;CardBenefitWidget&gt; createState() =&gt; _CardBenefitWidgetState();</span></span>
<span id="L32"><span class="lineNum">      32</span>              : }</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span>              : class _CardBenefitWidgetState extends UIComponentWidgetState&lt;CardBenefitWidget&gt; {</span>
<span id="L35"><span class="lineNum">      35</span>              :   final CardBenefitsCubit _cardBenefitsCubit = CardBenefitsCubit(homeRepo: getIt.get&lt;HomeRepo&gt;());</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L38"><span class="lineNum">      38</span>              :   void loadData() {</span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :     _cardBenefitsCubit.getHomeCardBenefits();</span></span>
<span id="L40"><span class="lineNum">      40</span>              :   }</span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L43"><span class="lineNum">      43</span>              :   void dispose() {</span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :     _cardBenefitsCubit.close();</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :     super.dispose();</span></span>
<span id="L46"><span class="lineNum">      46</span>              :   }</span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L49"><span class="lineNum">      49</span>              :   Widget build(BuildContext context) {</span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :     return BlocProvider&lt;CardBenefitsCubit&gt;.value(</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :         value: _cardBenefitsCubit,</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :         child: BlocConsumer&lt;CardBenefitsCubit, UiComponentState&gt;(</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :             listener: (BuildContext context, UiComponentState state) {</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :           handleCommonEvent(state);</span></span>
<span id="L55"><span class="lineNum">      55</span>              : </span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :           if (state is UiComponentDataLoaded&lt;CardBenefitsEntity&gt;) {</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :             if (state.data?.cardImages?.isEmpty ?? true) {</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :               widget.controller?.onEmptyData?.call();</span></span>
<span id="L59"><span class="lineNum">      59</span>              :             }</span>
<span id="L60"><span class="lineNum">      60</span>              :           }</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :         }, builder: (_, UiComponentState state) {</span></span>
<span id="L62"><span class="lineNum">      62</span>              :           CardBenefitsEntity? cardBenefitsEntity;</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :           if (state is UiComponentDataLoaded&lt;CardBenefitsEntity&gt;) {</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :             cardBenefitsEntity = state.data;</span></span>
<span id="L65"><span class="lineNum">      65</span>              :           }</span>
<span id="L66"><span class="lineNum">      66</span>              : </span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :           final bool isLoading = state is UiComponentLoading;</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :           return ShimmerWidget(</span></span>
<span id="L69"><span class="lineNum">      69</span>              :               isLoading: isLoading,</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :               skeletonWidget: SkeletonCardBenefitsWidget(heightCard: widget.heightCardImage),</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :               child: isLoading ? const SizedBox.shrink() : _content(cardBenefitsEntity));</span></span>
<span id="L72"><span class="lineNum">      72</span>              :         }));</span>
<span id="L73"><span class="lineNum">      73</span>              :   }</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :   Widget _content(CardBenefitsEntity? entity) {</span></span>
<span id="L76"><span class="lineNum">      76</span>              :     if (entity == null) {</span>
<span id="L77"><span class="lineNum">      77</span>              :       return const SizedBox.shrink();</span>
<span id="L78"><span class="lineNum">      78</span>              :     }</span>
<span id="L79"><span class="lineNum">      79</span>              : </span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :     final List&lt;Widget&gt; widgets = &lt;Widget&gt;[];</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :     if (entity.cardImages?.isNotEmpty == true) {</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :       widgets.addAll(&lt;Widget&gt;[</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :         SizedBox(height: widget.heightCardImage, child: _contentCardBenefit(entity.cardImages!)),</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaUNC">           0 :         SizedBox(height: entity.buttons?.isNotEmpty == true ? 20 : 30),</span></span>
<span id="L85"><span class="lineNum">      85</span>              :       ]);</span>
<span id="L86"><span class="lineNum">      86</span>              :     }</span>
<span id="L87"><span class="lineNum">      87</span>              : </span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :     if (entity.buttons?.isNotEmpty == true) {</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :       widgets.add(Padding(</span></span>
<span id="L90"><span class="lineNum">      90</span>              :           padding: const EdgeInsets.only(left: 20, right: 20, bottom: 30),</span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :           child: _buildContentButtons(entity.buttons!)));</span></span>
<span id="L92"><span class="lineNum">      92</span>              :     }</span>
<span id="L93"><span class="lineNum">      93</span>              : </span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :     return Column(crossAxisAlignment: CrossAxisAlignment.start, children: widgets);</span></span>
<span id="L95"><span class="lineNum">      95</span>              :   }</span>
<span id="L96"><span class="lineNum">      96</span>              : </span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :   Widget _buildContentButtons(List&lt;CardBenefitsButtonItemEntity&gt; buttons) {</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :     final List&lt;Widget&gt; widgets = &lt;Widget&gt;[];</span></span>
<span id="L99"><span class="lineNum">      99</span>              : </span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :     for (int index = 0; index &lt; buttons.length; index++) {</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :       final CardBenefitsButtonItemEntity item = buttons.elementAt(index);</span></span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :       widgets.addAll(&lt;Widget&gt;[</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :         index == 0 ? const SizedBox.shrink() : const SizedBox(width: 8),</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :         _buttonWidget(</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :             item: item, style: index % 2 == 0 ? evoButtonStyles.tertiary(ButtonSize.xLarge) : null)</span></span>
<span id="L106"><span class="lineNum">     106</span>              :       ]);</span>
<span id="L107"><span class="lineNum">     107</span>              :     }</span>
<span id="L108"><span class="lineNum">     108</span>              : </span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :     return Row(children: widgets);</span></span>
<span id="L110"><span class="lineNum">     110</span>              :   }</span>
<span id="L111"><span class="lineNum">     111</span>              : </span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :   Widget _contentCardBenefit(List&lt;CardBenefitsItemEntity&gt; benefits) {</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :     return Padding(</span></span>
<span id="L114"><span class="lineNum">     114</span>              :         padding: const EdgeInsets.only(left: 20),</span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :         child: PageView.builder(</span></span>
<span id="L116"><span class="lineNum">     116</span>              :             controller:</span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :                 PageController(viewportFraction: _isMultiCardBenefit(benefits) ? 0.75 : 1.0),</span></span>
<span id="L118"><span class="lineNum">     118</span>              :             padEnds: false,</span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :             itemCount: benefits.length,</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :             itemBuilder: (BuildContext context, int index) {</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :               final CardBenefitsItemEntity currentItem = benefits.elementAt(index);</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :               return _itemBuilder(currentItem);</span></span>
<span id="L123"><span class="lineNum">     123</span>              :             }));</span>
<span id="L124"><span class="lineNum">     124</span>              :   }</span>
<span id="L125"><span class="lineNum">     125</span>              : </span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :   bool _isMultiCardBenefit(List&lt;CardBenefitsItemEntity&gt; benefits) {</span></span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :     if (benefits.length &lt;= 1) {</span></span>
<span id="L128"><span class="lineNum">     128</span>              :       return false;</span>
<span id="L129"><span class="lineNum">     129</span>              :     }</span>
<span id="L130"><span class="lineNum">     130</span>              : </span>
<span id="L131"><span class="lineNum">     131</span>              :     return true;</span>
<span id="L132"><span class="lineNum">     132</span>              :   }</span>
<span id="L133"><span class="lineNum">     133</span>              : </span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :   Widget _itemBuilder(CardBenefitsItemEntity currentItem) {</span></span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :     return Padding(</span></span>
<span id="L136"><span class="lineNum">     136</span>              :       padding: const EdgeInsets.only(right: 12),</span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaUNC">           0 :       child: EvoInkWellNetworkImage(currentItem.imageUrl,</span></span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :           height: widget.heightCardImage,</span></span>
<span id="L139"><span class="lineNum">     139</span>              :           width: double.infinity,</span>
<span id="L140"><span class="lineNum">     140</span>              :           fit: BoxFit.fitWidth,</span>
<span id="L141"><span class="lineNum">     141</span>              :           cornerRadius: 16,</span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaUNC">           0 :           typeImage: TypeImage.banner, onTap: () {</span></span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaUNC">           0 :         currentItem.action?.toEvoActionModel().let((EvoActionModel it) async {</span></span>
<span id="L144"><span class="lineNum">     144</span> <span class="tlaUNC">           0 :           EvoActionHandler().handle(it);</span></span>
<span id="L145"><span class="lineNum">     145</span>              :         });</span>
<span id="L146"><span class="lineNum">     146</span>              :       }),</span>
<span id="L147"><span class="lineNum">     147</span>              :     );</span>
<span id="L148"><span class="lineNum">     148</span>              :   }</span>
<span id="L149"><span class="lineNum">     149</span>              : </span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :   Widget _buttonWidget({required CardBenefitsButtonItemEntity item, ButtonStyle? style}) {</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :     return Expanded(</span></span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :       child: CommonButton(</span></span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :         onPressed: () {</span></span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaUNC">           0 :           item.action?.toEvoActionModel().let((EvoActionModel it) async {</span></span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaUNC">           0 :             EvoActionHandler().handle(it);</span></span>
<span id="L156"><span class="lineNum">     156</span>              :           });</span>
<span id="L157"><span class="lineNum">     157</span>              :         },</span>
<span id="L158"><span class="lineNum">     158</span>              :         isWrapContent: false,</span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :         style: style ?? evoButtonStyles.primary(ButtonSize.xLarge),</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :         child: Text(item.title ?? '', overflow: TextOverflow.ellipsis),</span></span>
<span id="L161"><span class="lineNum">     161</span>              :       ),</span>
<span id="L162"><span class="lineNum">     162</span>              :     );</span>
<span id="L163"><span class="lineNum">     163</span>              :   }</span>
<span id="L164"><span class="lineNum">     164</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
