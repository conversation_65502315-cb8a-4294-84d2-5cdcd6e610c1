<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/home_screen/non_user/v2/story/widgets/story_indicator/story_indicator_widget.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/home_screen/non_user/v2/story/widgets/story_indicator">lib/feature/home_screen/non_user/v2/story/widgets/story_indicator</a> - story_indicator_widget.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">51.7&nbsp;%</td>
            <td class="headerCovTableEntry">89</td>
            <td class="headerCovTableEntry">46</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:collection/collection.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter/material.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/common_package/common_package.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : import '../../model/story_view_model.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../story_config.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import '../story_progress_bar_widget.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import 'story_indicator_cubit.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import 'story_indicator_state.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : class StoryIndicatorController {</span>
<span id="L12"><span class="lineNum">      12</span>              :   VoidCallback? play;</span>
<span id="L13"><span class="lineNum">      13</span>              :   VoidCallback? pause;</span>
<span id="L14"><span class="lineNum">      14</span>              :   VoidCallback? next;</span>
<span id="L15"><span class="lineNum">      15</span>              :   VoidCallback? previous;</span>
<span id="L16"><span class="lineNum">      16</span>              :   VoidCallback? reset;</span>
<span id="L17"><span class="lineNum">      17</span>              : }</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              : class StoryIndicatorWidget extends StatefulWidget {</span>
<span id="L20"><span class="lineNum">      20</span>              :   final List&lt;StoryViewModel&gt; stories;</span>
<span id="L21"><span class="lineNum">      21</span>              :   final Color? backgroundColor;</span>
<span id="L22"><span class="lineNum">      22</span>              :   final Color? activeColor;</span>
<span id="L23"><span class="lineNum">      23</span>              :   final StoryIndicatorController? controller;</span>
<span id="L24"><span class="lineNum">      24</span>              :   final VoidCallback? animateToNextPage;</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC">           1 :   const StoryIndicatorWidget({</span></span>
<span id="L27"><span class="lineNum">      27</span>              :     required this.stories,</span>
<span id="L28"><span class="lineNum">      28</span>              :     this.backgroundColor,</span>
<span id="L29"><span class="lineNum">      29</span>              :     this.activeColor,</span>
<span id="L30"><span class="lineNum">      30</span>              :     this.controller,</span>
<span id="L31"><span class="lineNum">      31</span>              :     this.animateToNextPage,</span>
<span id="L32"><span class="lineNum">      32</span>              :     super.key,</span>
<span id="L33"><span class="lineNum">      33</span>              :   });</span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaGNC">           1 :   State&lt;StatefulWidget&gt; createState() =&gt; StoryIndicatorWidgetState();</span></span>
<span id="L37"><span class="lineNum">      37</span>              : }</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span>              : @visibleForTesting</span>
<span id="L40"><span class="lineNum">      40</span>              : class StoryIndicatorWidgetState extends State&lt;StoryIndicatorWidget&gt; with TickerProviderStateMixin {</span>
<span id="L41"><span class="lineNum">      41</span>              :   late AnimationController _storyIndicatorController;</span>
<span id="L42"><span class="lineNum">      42</span>              :   final StoryIndicatorCubit _storyIndicatorCubit = StoryIndicatorCubit();</span>
<span id="L43"><span class="lineNum">      43</span>              :   final List&lt;StoryViewModel&gt; _stories = &lt;StoryViewModel&gt;[];</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L46"><span class="lineNum">      46</span>              :   void initState() {</span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           1 :     super.initState();</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           6 :     _stories.addAll(_storyIndicatorCubit.initStoryView(widget.stories));</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           1 :     _handleInitAnimation();</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           1 :     _initController();</span></span>
<span id="L51"><span class="lineNum">      51</span>              :   }</span>
<span id="L52"><span class="lineNum">      52</span>              : </span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L54"><span class="lineNum">      54</span>              :   void dispose() {</span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaGNC">           3 :     _storyIndicatorController.removeStatusListener(_handleAnimationStatus);</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           2 :     _storyIndicatorController.dispose();</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           1 :     super.dispose();</span></span>
<span id="L58"><span class="lineNum">      58</span>              :   }</span>
<span id="L59"><span class="lineNum">      59</span>              : </span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L61"><span class="lineNum">      61</span>              :   void setState(VoidCallback fn) {</span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :     if (!mounted) {</span></span>
<span id="L63"><span class="lineNum">      63</span>              :       return;</span>
<span id="L64"><span class="lineNum">      64</span>              :     }</span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :     super.setState(fn);</span></span>
<span id="L66"><span class="lineNum">      66</span>              :   }</span>
<span id="L67"><span class="lineNum">      67</span>              : </span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L69"><span class="lineNum">      69</span>              :   Widget build(BuildContext context) {</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaGNC">           1 :     return BlocProvider&lt;StoryIndicatorCubit&gt;(</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">           2 :       create: (_) =&gt; _storyIndicatorCubit,</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaGNC">           1 :       child: BlocConsumer&lt;StoryIndicatorCubit, StoryIndicatorState&gt;(</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :         listener: (_, StoryIndicatorState state) {</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :           _handleListenerState(state);</span></span>
<span id="L75"><span class="lineNum">      75</span>              :         },</span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           1 :         builder: (_, StoryIndicatorState state) {</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaGNC">           2 :           return Row(children: _buildItemPage(state));</span></span>
<span id="L78"><span class="lineNum">      78</span>              :         },</span>
<span id="L79"><span class="lineNum">      79</span>              :       ),</span>
<span id="L80"><span class="lineNum">      80</span>              :     );</span>
<span id="L81"><span class="lineNum">      81</span>              :   }</span>
<span id="L82"><span class="lineNum">      82</span>              : </span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaGNC">           1 :   List&lt;Widget&gt; _buildItemPage(StoryIndicatorState state) {</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">           1 :     final List&lt;Widget&gt; listWidget = &lt;Widget&gt;[];</span></span>
<span id="L85"><span class="lineNum">      85</span>              : </span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaGNC">           2 :     _stories.forEachIndexed(</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           1 :       (int index, StoryViewModel? model) {</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           3 :         final int lastIndex = _stories.length - 1;</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">           1 :         final double padding = index == lastIndex ? 0 : StoryConfig.progressBarPadding;</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           1 :         final double progressValue = handleProgressValue(model);</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaGNC">           1 :         final Widget item = Expanded(</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaGNC">           1 :           child: Padding(</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaGNC">           1 :             padding: EdgeInsets.only(right: padding),</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           1 :             child: StoryProgressBarWidget(</span></span>
<span id="L95"><span class="lineNum">      95</span>              :               value: progressValue,</span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaGNC">           2 :               backgroundColor: widget.backgroundColor,</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaGNC">           2 :               activeColor: widget.activeColor,</span></span>
<span id="L98"><span class="lineNum">      98</span>              :             ),</span>
<span id="L99"><span class="lineNum">      99</span>              :           ),</span>
<span id="L100"><span class="lineNum">     100</span>              :         );</span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaGNC">           1 :         listWidget.add(item);</span></span>
<span id="L102"><span class="lineNum">     102</span>              :       },</span>
<span id="L103"><span class="lineNum">     103</span>              :     );</span>
<span id="L104"><span class="lineNum">     104</span>              :     return listWidget;</span>
<span id="L105"><span class="lineNum">     105</span>              :   }</span>
<span id="L106"><span class="lineNum">     106</span>              : </span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L108"><span class="lineNum">     108</span>              :   double handleProgressValue(StoryViewModel? model) {</span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaGNC">           2 :     if (model?.type == StoryViewType.isPlaying) {</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaGNC">           2 :       final double currentValue = _storyIndicatorController.value;</span></span>
<span id="L111"><span class="lineNum">     111</span>              :       return currentValue;</span>
<span id="L112"><span class="lineNum">     112</span>              :     }</span>
<span id="L113"><span class="lineNum">     113</span>              : </span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :     final double valuePageNotShow = model?.type == StoryViewType.isShown ? 1 : 0;</span></span>
<span id="L115"><span class="lineNum">     115</span>              :     return valuePageNotShow;</span>
<span id="L116"><span class="lineNum">     116</span>              :   }</span>
<span id="L117"><span class="lineNum">     117</span>              : </span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaGNC">           1 :   void _handleInitAnimation() {</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaGNC">           2 :     _storyIndicatorController = AnimationController(</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaGNC">           4 :         duration: Duration(milliseconds: _stories.first.durationInMs), vsync: this);</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaGNC">           3 :     _storyIndicatorController.addStatusListener(_handleAnimationStatus);</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaGNC">           2 :     _storyIndicatorController.addListener(() {</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :       setState(() {});</span></span>
<span id="L124"><span class="lineNum">     124</span>              :     });</span>
<span id="L125"><span class="lineNum">     125</span>              :   }</span>
<span id="L126"><span class="lineNum">     126</span>              : </span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaGNC">           1 :   void _initController() {</span></span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaGNC">           4 :     widget.controller?.play = _playController;</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaGNC">           4 :     widget.controller?.pause = _pauseController;</span></span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaGNC">           4 :     widget.controller?.next = _nextController;</span></span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaGNC">           4 :     widget.controller?.previous = _previousController;</span></span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaGNC">           4 :     widget.controller?.reset = _resetController;</span></span>
<span id="L133"><span class="lineNum">     133</span>              :   }</span>
<span id="L134"><span class="lineNum">     134</span>              : </span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :   void _playController() {</span></span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :     _storyIndicatorController.forward();</span></span>
<span id="L137"><span class="lineNum">     137</span>              :   }</span>
<span id="L138"><span class="lineNum">     138</span>              : </span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaUNC">           0 :   void _pauseController() {</span></span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaUNC">           0 :     _storyIndicatorController.stop(canceled: false);</span></span>
<span id="L141"><span class="lineNum">     141</span>              :   }</span>
<span id="L142"><span class="lineNum">     142</span>              : </span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaUNC">           0 :   void _nextController() {</span></span>
<span id="L144"><span class="lineNum">     144</span> <span class="tlaUNC">           0 :     _storyIndicatorCubit.nextStoryView();</span></span>
<span id="L145"><span class="lineNum">     145</span>              :   }</span>
<span id="L146"><span class="lineNum">     146</span>              : </span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :   void _previousController() {</span></span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaUNC">           0 :     _storyIndicatorCubit.previousStoryView();</span></span>
<span id="L149"><span class="lineNum">     149</span>              :   }</span>
<span id="L150"><span class="lineNum">     150</span>              : </span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :   void _resetController() {</span></span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :     _storyIndicatorController.reset();</span></span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :     _storyIndicatorController.forward();</span></span>
<span id="L154"><span class="lineNum">     154</span>              :   }</span>
<span id="L155"><span class="lineNum">     155</span>              : </span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaUNC">           0 :   void _handleAnimationStatus(AnimationStatus status) {</span></span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :     if (status == AnimationStatus.completed) {</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :       widget.animateToNextPage?.call();</span></span>
<span id="L159"><span class="lineNum">     159</span>              :     }</span>
<span id="L160"><span class="lineNum">     160</span>              :   }</span>
<span id="L161"><span class="lineNum">     161</span>              : </span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaUNC">           0 :   void _handleListenerState(StoryIndicatorState state) {</span></span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaUNC">           0 :     if (state is IndicatorNextState) {</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :       _goNextStoryView(state);</span></span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaUNC">           0 :       _handleUpdateDataStoryView(state.storyItems);</span></span>
<span id="L166"><span class="lineNum">     166</span>              :       return;</span>
<span id="L167"><span class="lineNum">     167</span>              :     }</span>
<span id="L168"><span class="lineNum">     168</span>              : </span>
<span id="L169"><span class="lineNum">     169</span> <span class="tlaUNC">           0 :     if (state is IndicatorCompleteState) {</span></span>
<span id="L170"><span class="lineNum">     170</span> <span class="tlaUNC">           0 :       _completeStoryView();</span></span>
<span id="L171"><span class="lineNum">     171</span>              :     }</span>
<span id="L172"><span class="lineNum">     172</span>              :   }</span>
<span id="L173"><span class="lineNum">     173</span>              : </span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaUNC">           0 :   void _goNextStoryView(IndicatorNextState state) {</span></span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :     _storyIndicatorController.stop();</span></span>
<span id="L176"><span class="lineNum">     176</span> <span class="tlaUNC">           0 :     _replayStoryView(currentStoryIndex: state.currentStoryIndex);</span></span>
<span id="L177"><span class="lineNum">     177</span>              :   }</span>
<span id="L178"><span class="lineNum">     178</span>              : </span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaUNC">           0 :   void _completeStoryView() {</span></span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaUNC">           0 :     _storyIndicatorController.stop();</span></span>
<span id="L181"><span class="lineNum">     181</span> <span class="tlaUNC">           0 :     _replayStoryView();</span></span>
<span id="L182"><span class="lineNum">     182</span>              :   }</span>
<span id="L183"><span class="lineNum">     183</span>              : </span>
<span id="L184"><span class="lineNum">     184</span> <span class="tlaUNC">           0 :   void _replayStoryView({int? currentStoryIndex}) {</span></span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :     _storyIndicatorController.reset();</span></span>
<span id="L186"><span class="lineNum">     186</span> <span class="tlaUNC">           0 :     _changeDurationAnimation();</span></span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaUNC">           0 :     _playController();</span></span>
<span id="L188"><span class="lineNum">     188</span>              :   }</span>
<span id="L189"><span class="lineNum">     189</span>              : </span>
<span id="L190"><span class="lineNum">     190</span> <span class="tlaUNC">           0 :   void _changeDurationAnimation() {</span></span>
<span id="L191"><span class="lineNum">     191</span> <span class="tlaUNC">           0 :     final Duration duration = Duration(milliseconds: _storyIndicatorCubit.currentDurationInMs);</span></span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaUNC">           0 :     _storyIndicatorController.duration = duration;</span></span>
<span id="L193"><span class="lineNum">     193</span>              :   }</span>
<span id="L194"><span class="lineNum">     194</span>              : </span>
<span id="L195"><span class="lineNum">     195</span> <span class="tlaUNC">           0 :   void _handleUpdateDataStoryView(List&lt;StoryViewModel&gt; stories) {</span></span>
<span id="L196"><span class="lineNum">     196</span> <span class="tlaUNC">           0 :     _stories.clear();</span></span>
<span id="L197"><span class="lineNum">     197</span> <span class="tlaUNC">           0 :     _stories.addAll(stories);</span></span>
<span id="L198"><span class="lineNum">     198</span>              :   }</span>
<span id="L199"><span class="lineNum">     199</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
