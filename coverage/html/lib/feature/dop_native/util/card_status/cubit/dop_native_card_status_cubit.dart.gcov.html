<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/dop_native/util/card_status/cubit/dop_native_card_status_cubit.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/dop_native/util/card_status/cubit">lib/feature/dop_native/util/card_status/cubit</a> - dop_native_card_status_cubit.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">85</td>
            <td class="headerCovTableEntry">85</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/cupertino.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/base/common_cubit.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/data/http_client/mock_config.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../../../../data/response/dop_native/dop_native_card_status_entity.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../../../data/response/dop_native/dop_native_pos_limit_entity.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../../../prepare_for_app_initiation.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../mock/mock_dop_native_card_status_use_case.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import 'dop_native_card_status_state.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span>              : enum CardStatusUseCase {</span>
<span id="L15"><span class="lineNum">      15</span>              :   underwritingInProgress,</span>
<span id="L16"><span class="lineNum">      16</span>              :   underwritingCardIssued,</span>
<span id="L17"><span class="lineNum">      17</span>              :   cardInformation,</span>
<span id="L18"><span class="lineNum">      18</span>              :   underwritingCardStatus,</span>
<span id="L19"><span class="lineNum">      19</span>              : }</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span>              : class DOPNativeCardStatusCubit extends CommonCubit&lt;DOPNativeCardStatusState&gt; {</span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           2 :   DOPNativeCardStatusCubit({</span></span>
<span id="L23"><span class="lineNum">      23</span>              :     required this.dopNativeRepo,</span>
<span id="L24"><span class="lineNum">      24</span>              :     required this.appState,</span>
<span id="L25"><span class="lineNum">      25</span>              :     required this.cardStatusUseCase,</span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC">           4 :   }) : super(GetCardStatusInitial());</span></span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span>              :   final DOPNativeRepo dopNativeRepo;</span>
<span id="L29"><span class="lineNum">      29</span>              :   final AppState appState;</span>
<span id="L30"><span class="lineNum">      30</span>              :   final CardStatusUseCase cardStatusUseCase;</span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span>              :   /// [needShowLoading] is used to hide loading when pooling.</span>
<span id="L33"><span class="lineNum">      33</span>              :   /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-5779</span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; getCardStatus({bool needShowLoading = true}) async {</span></span>
<span id="L35"><span class="lineNum">      35</span>              :     if (needShowLoading) {</span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaGNC">           2 :       emit(GetCardStatusLoading());</span></span>
<span id="L37"><span class="lineNum">      37</span>              :     }</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           2 :     final DOPNativeCardStatusEntity entity = await dopNativeRepo.getCardStatus(</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           1 :       mockConfig: MockConfig(</span></span>
<span id="L41"><span class="lineNum">      41</span>              :         enable: false,</span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           1 :         fileName: getMockDOPNativeCardStatus(</span></span>
<span id="L43"><span class="lineNum">      43</span>              :           MockDOPNativeCardStatus.getCardStatusOfflineMerchantSuccess,</span>
<span id="L44"><span class="lineNum">      44</span>              :         ),</span>
<span id="L45"><span class="lineNum">      45</span>              :       ),</span>
<span id="L46"><span class="lineNum">      46</span>              :     );</span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           2 :     if (entity.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           2 :       savePosLimit(entity.posLimit);</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           1 :       handleSuccessCase(</span></span>
<span id="L51"><span class="lineNum">      51</span>              :         cardStatus: entity,</span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaGNC">           1 :         useCase: cardStatusUseCase,</span></span>
<span id="L53"><span class="lineNum">      53</span>              :       );</span>
<span id="L54"><span class="lineNum">      54</span>              :       return;</span>
<span id="L55"><span class="lineNum">      55</span>              :     }</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           1 :     emit(</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaGNC">           1 :       GetCardStatusFailure(</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           1 :         ErrorUIModel.fromEntity(entity),</span></span>
<span id="L60"><span class="lineNum">      60</span>              :       ),</span>
<span id="L61"><span class="lineNum">      61</span>              :     );</span>
<span id="L62"><span class="lineNum">      62</span>              :   }</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L65"><span class="lineNum">      65</span>              :   void handleSuccessCase({</span>
<span id="L66"><span class="lineNum">      66</span>              :     required DOPNativeCardStatusEntity cardStatus,</span>
<span id="L67"><span class="lineNum">      67</span>              :     required CardStatusUseCase useCase,</span>
<span id="L68"><span class="lineNum">      68</span>              :   }) {</span>
<span id="L69"><span class="lineNum">      69</span>              :     switch (useCase) {</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaGNC">           1 :       case CardStatusUseCase.underwritingInProgress:</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">           1 :         handleUnderwritingInProgress(cardStatus);</span></span>
<span id="L72"><span class="lineNum">      72</span>              :         return;</span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaGNC">           1 :       case CardStatusUseCase.underwritingCardIssued:</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaGNC">           1 :         handleUnderwritingCardIssued(cardStatus);</span></span>
<span id="L75"><span class="lineNum">      75</span>              :         return;</span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           1 :       case CardStatusUseCase.cardInformation:</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaGNC">           1 :         final DOPNativeCardActivationStatus? activationStatus = cardStatus.activationStatus;</span></span>
<span id="L78"><span class="lineNum">      78</span>              :         final DOPNativePOSLimitActivationStatus? posLimitActivationStatus =</span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaGNC">           2 :             cardStatus.posLimit?.posLimitActivationStatus;</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaGNC">           1 :         final bool? canActivateCard = cardStatus.canActivateCard;</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaGNC">           1 :         handleCardStatusInformation(</span></span>
<span id="L82"><span class="lineNum">      82</span>              :           activationStatus: activationStatus,</span>
<span id="L83"><span class="lineNum">      83</span>              :           canActivateCard: canActivateCard,</span>
<span id="L84"><span class="lineNum">      84</span>              :           posLimitActivationStatus: posLimitActivationStatus,</span>
<span id="L85"><span class="lineNum">      85</span>              :         );</span>
<span id="L86"><span class="lineNum">      86</span>              :         return;</span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           1 :       case CardStatusUseCase.underwritingCardStatus:</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           1 :         handleUnderwritingCardStatus(cardStatus);</span></span>
<span id="L89"><span class="lineNum">      89</span>              :         return;</span>
<span id="L90"><span class="lineNum">      90</span>              :     }</span>
<span id="L91"><span class="lineNum">      91</span>              :   }</span>
<span id="L92"><span class="lineNum">      92</span>              : </span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L94"><span class="lineNum">      94</span>              :   bool isLockCard(DOPNativeCardActivationStatus? activationStatus) {</span>
<span id="L95"><span class="lineNum">      95</span>              :     /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3583082497/Underwriting+sub+flow+Card+activation#Underwriting-sub-flow</span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaGNC">           1 :     if (activationStatus == DOPNativeCardActivationStatus.permanentBlocked ||</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaGNC">           1 :         activationStatus == DOPNativeCardActivationStatus.temporaryBlocked ||</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaGNC">           1 :         activationStatus == DOPNativeCardActivationStatus.lockCard) {</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaGNC">           2 :       emit(GetCardStatusBlocked());</span></span>
<span id="L100"><span class="lineNum">     100</span>              :       return true;</span>
<span id="L101"><span class="lineNum">     101</span>              :     }</span>
<span id="L102"><span class="lineNum">     102</span>              :     return false;</span>
<span id="L103"><span class="lineNum">     103</span>              :   }</span>
<span id="L104"><span class="lineNum">     104</span>              : </span>
<span id="L105"><span class="lineNum">     105</span>              :   /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3583082497/Underwriting+sub+flow+Card+activation#Underwriting-sub-flow</span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L107"><span class="lineNum">     107</span>              :   void handleUnderwritingInProgress(DOPNativeCardStatusEntity cardStatus) {</span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaGNC">           2 :     final bool isLocked = isLockCard(cardStatus.activationStatus);</span></span>
<span id="L109"><span class="lineNum">     109</span>              :     if (isLocked) {</span>
<span id="L110"><span class="lineNum">     110</span>              :       return;</span>
<span id="L111"><span class="lineNum">     111</span>              :     }</span>
<span id="L112"><span class="lineNum">     112</span>              : </span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaGNC">           1 :     final bool isValid = validateUnderwritingRequiredField(cardStatus);</span></span>
<span id="L114"><span class="lineNum">     114</span>              : </span>
<span id="L115"><span class="lineNum">     115</span>              :     if (!isValid) {</span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaGNC">           3 :       emit(GetCardStatusFailure(ErrorUIModel()));</span></span>
<span id="L117"><span class="lineNum">     117</span>              :       return;</span>
<span id="L118"><span class="lineNum">     118</span>              :     }</span>
<span id="L119"><span class="lineNum">     119</span>              : </span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaGNC">           4 :     if (cardStatus.isOfflineMerchant == true &amp;&amp; cardStatus.consentLinkCard == true) {</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaGNC">           2 :       if (cardStatus.canActivateCard == true) {</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaGNC">           3 :         emit(GetCardStatusFailure(ErrorUIModel()));</span></span>
<span id="L123"><span class="lineNum">     123</span>              :       } else {</span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaGNC">           2 :         emit(UnderwritingOfflineMerchantAndLinkCard(cardStatus));</span></span>
<span id="L125"><span class="lineNum">     125</span>              :       }</span>
<span id="L126"><span class="lineNum">     126</span>              :       return;</span>
<span id="L127"><span class="lineNum">     127</span>              :     }</span>
<span id="L128"><span class="lineNum">     128</span>              : </span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaGNC">           2 :     emit(UnderwritingNoneOfflineMerchantOrNoneLinkCard());</span></span>
<span id="L130"><span class="lineNum">     130</span>              :   }</span>
<span id="L131"><span class="lineNum">     131</span>              : </span>
<span id="L132"><span class="lineNum">     132</span>              :   /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3583082497/Underwriting+sub+flow+Card+activation#Underwriting-sub-flow</span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L134"><span class="lineNum">     134</span>              :   void handleUnderwritingCardIssued(DOPNativeCardStatusEntity cardStatus) {</span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaGNC">           2 :     final bool isLocked = isLockCard(cardStatus.activationStatus);</span></span>
<span id="L136"><span class="lineNum">     136</span>              :     if (isLocked) {</span>
<span id="L137"><span class="lineNum">     137</span>              :       return;</span>
<span id="L138"><span class="lineNum">     138</span>              :     }</span>
<span id="L139"><span class="lineNum">     139</span>              : </span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaGNC">           1 :     final bool isValid = validateUnderwritingRequiredField(cardStatus);</span></span>
<span id="L141"><span class="lineNum">     141</span>              : </span>
<span id="L142"><span class="lineNum">     142</span>              :     if (!isValid) {</span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaGNC">           3 :       emit(GetCardStatusFailure(ErrorUIModel()));</span></span>
<span id="L144"><span class="lineNum">     144</span>              :       return;</span>
<span id="L145"><span class="lineNum">     145</span>              :     }</span>
<span id="L146"><span class="lineNum">     146</span>              : </span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaGNC">           4 :     if (cardStatus.isOfflineMerchant == true &amp;&amp; cardStatus.consentLinkCard == true) {</span></span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaGNC">           2 :       final bool canActivateCard = cardStatus.canActivateCard == true;</span></span>
<span id="L149"><span class="lineNum">     149</span>              : </span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaGNC">           2 :       if (cardStatus.activationStatus == DOPNativeCardActivationStatus.activated) {</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaGNC">           2 :         emit(UnderwritingCardIssuedCardActivated());</span></span>
<span id="L152"><span class="lineNum">     152</span>              :         return;</span>
<span id="L153"><span class="lineNum">     153</span>              :       }</span>
<span id="L154"><span class="lineNum">     154</span>              : </span>
<span id="L155"><span class="lineNum">     155</span>              :       if (canActivateCard) {</span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaGNC">           2 :         emit(UnderwritingCardIssuedCanActivateCard());</span></span>
<span id="L157"><span class="lineNum">     157</span>              :         return;</span>
<span id="L158"><span class="lineNum">     158</span>              :       }</span>
<span id="L159"><span class="lineNum">     159</span>              : </span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaGNC">           2 :       emit(UnderwritingCardIssuedCannotActivateCard());</span></span>
<span id="L161"><span class="lineNum">     161</span>              :       return;</span>
<span id="L162"><span class="lineNum">     162</span>              :     }</span>
<span id="L163"><span class="lineNum">     163</span>              : </span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaGNC">           2 :     emit(UnderwritingNoneOfflineMerchantOrNoneLinkCard());</span></span>
<span id="L165"><span class="lineNum">     165</span>              :     return;</span>
<span id="L166"><span class="lineNum">     166</span>              :   }</span>
<span id="L167"><span class="lineNum">     167</span>              : </span>
<span id="L168"><span class="lineNum">     168</span>              :   /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3583082497/Underwriting+sub+flow+Card+activation</span>
<span id="L169"><span class="lineNum">     169</span> <span class="tlaGNC">           1 :   void handleCardStatusInformation({</span></span>
<span id="L170"><span class="lineNum">     170</span>              :     required DOPNativeCardActivationStatus? activationStatus,</span>
<span id="L171"><span class="lineNum">     171</span>              :     required bool? canActivateCard,</span>
<span id="L172"><span class="lineNum">     172</span>              :     DOPNativePOSLimitActivationStatus? posLimitActivationStatus,</span>
<span id="L173"><span class="lineNum">     173</span>              :   }) {</span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaGNC">           1 :     final bool isLocked = isLockCard(activationStatus);</span></span>
<span id="L175"><span class="lineNum">     175</span>              :     if (isLocked) {</span>
<span id="L176"><span class="lineNum">     176</span>              :       return;</span>
<span id="L177"><span class="lineNum">     177</span>              :     }</span>
<span id="L178"><span class="lineNum">     178</span>              : </span>
<span id="L179"><span class="lineNum">     179</span>              :     final bool isInValid = activationStatus == null ||</span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaGNC">           1 :         activationStatus == DOPNativeCardActivationStatus.unknown ||</span></span>
<span id="L181"><span class="lineNum">     181</span> <span class="tlaGNC">           1 :         (activationStatus != DOPNativeCardActivationStatus.activated &amp;&amp; canActivateCard == null);</span></span>
<span id="L182"><span class="lineNum">     182</span>              : </span>
<span id="L183"><span class="lineNum">     183</span>              :     if (isInValid) {</span>
<span id="L184"><span class="lineNum">     184</span> <span class="tlaGNC">           3 :       emit(GetCardStatusFailure(ErrorUIModel()));</span></span>
<span id="L185"><span class="lineNum">     185</span>              :       return;</span>
<span id="L186"><span class="lineNum">     186</span>              :     }</span>
<span id="L187"><span class="lineNum">     187</span>              : </span>
<span id="L188"><span class="lineNum">     188</span> <span class="tlaGNC">           1 :     if (activationStatus == DOPNativeCardActivationStatus.activated) {</span></span>
<span id="L189"><span class="lineNum">     189</span> <span class="tlaGNC">           1 :       handlePOSLimitStatus(posLimitActivationStatus);</span></span>
<span id="L190"><span class="lineNum">     190</span>              :       return;</span>
<span id="L191"><span class="lineNum">     191</span>              :     }</span>
<span id="L192"><span class="lineNum">     192</span>              : </span>
<span id="L193"><span class="lineNum">     193</span> <span class="tlaGNC">           1 :     if (canActivateCard == true) {</span></span>
<span id="L194"><span class="lineNum">     194</span> <span class="tlaGNC">           2 :       emit(CardStatusInformationRetry());</span></span>
<span id="L195"><span class="lineNum">     195</span>              :       return;</span>
<span id="L196"><span class="lineNum">     196</span>              :     }</span>
<span id="L197"><span class="lineNum">     197</span>              : </span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaGNC">           2 :     emit(CardStatusInformationFail());</span></span>
<span id="L199"><span class="lineNum">     199</span>              :   }</span>
<span id="L200"><span class="lineNum">     200</span>              : </span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L202"><span class="lineNum">     202</span>              :   void handlePOSLimitStatus(DOPNativePOSLimitActivationStatus? status) {</span>
<span id="L203"><span class="lineNum">     203</span>              :     switch (status) {</span>
<span id="L204"><span class="lineNum">     204</span> <span class="tlaGNC">           1 :       case DOPNativePOSLimitActivationStatus.success:</span></span>
<span id="L205"><span class="lineNum">     205</span> <span class="tlaGNC">           1 :       case DOPNativePOSLimitActivationStatus.empty:</span></span>
<span id="L206"><span class="lineNum">     206</span> <span class="tlaGNC">           2 :         emit(CardStatusInformationActivated());</span></span>
<span id="L207"><span class="lineNum">     207</span>              :         return;</span>
<span id="L208"><span class="lineNum">     208</span> <span class="tlaGNC">           1 :       case DOPNativePOSLimitActivationStatus.pending:</span></span>
<span id="L209"><span class="lineNum">     209</span> <span class="tlaGNC">           2 :         emit(CardStatusInformationActivatedRetryPosLimit());</span></span>
<span id="L210"><span class="lineNum">     210</span>              :         return;</span>
<span id="L211"><span class="lineNum">     211</span> <span class="tlaGNC">           1 :       case DOPNativePOSLimitActivationStatus.timeout:</span></span>
<span id="L212"><span class="lineNum">     212</span> <span class="tlaGNC">           1 :       case DOPNativePOSLimitActivationStatus.failure:</span></span>
<span id="L213"><span class="lineNum">     213</span> <span class="tlaGNC">           2 :         emit(CardStatusInformationActivatedPosFailed());</span></span>
<span id="L214"><span class="lineNum">     214</span>              :         return;</span>
<span id="L215"><span class="lineNum">     215</span>              :       default:</span>
<span id="L216"><span class="lineNum">     216</span> <span class="tlaGNC">           3 :         emit(GetCardStatusFailure(ErrorUIModel()));</span></span>
<span id="L217"><span class="lineNum">     217</span>              :         return;</span>
<span id="L218"><span class="lineNum">     218</span>              :     }</span>
<span id="L219"><span class="lineNum">     219</span>              :   }</span>
<span id="L220"><span class="lineNum">     220</span>              : </span>
<span id="L221"><span class="lineNum">     221</span>              :   /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3583082497/Underwriting+sub+flow+Card+activation</span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L223"><span class="lineNum">     223</span>              :   void handleUnderwritingCardStatus(DOPNativeCardStatusEntity cardStatus) {</span>
<span id="L224"><span class="lineNum">     224</span> <span class="tlaGNC">           2 :     final bool isLocked = isLockCard(cardStatus.activationStatus);</span></span>
<span id="L225"><span class="lineNum">     225</span>              :     if (isLocked) {</span>
<span id="L226"><span class="lineNum">     226</span>              :       return;</span>
<span id="L227"><span class="lineNum">     227</span>              :     }</span>
<span id="L228"><span class="lineNum">     228</span>              : </span>
<span id="L229"><span class="lineNum">     229</span> <span class="tlaGNC">           2 :     emit(UnderwritingCardStatusCardActivated());</span></span>
<span id="L230"><span class="lineNum">     230</span>              :   }</span>
<span id="L231"><span class="lineNum">     231</span>              : </span>
<span id="L232"><span class="lineNum">     232</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L233"><span class="lineNum">     233</span>              :   bool validateUnderwritingRequiredField(DOPNativeCardStatusEntity cardStatus) {</span>
<span id="L234"><span class="lineNum">     234</span> <span class="tlaGNC">           1 :     return &lt;dynamic&gt;[</span></span>
<span id="L235"><span class="lineNum">     235</span> <span class="tlaGNC">           1 :       cardStatus.activationStatus,</span></span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaGNC">           1 :       cardStatus.canActivateCard,</span></span>
<span id="L237"><span class="lineNum">     237</span> <span class="tlaGNC">           2 :       cardStatus.posLimit?.maxPosLimitAllow,</span></span>
<span id="L238"><span class="lineNum">     238</span> <span class="tlaGNC">           1 :       cardStatus.consentLinkCard,</span></span>
<span id="L239"><span class="lineNum">     239</span> <span class="tlaGNC">           1 :       cardStatus.isOfflineMerchant</span></span>
<span id="L240"><span class="lineNum">     240</span> <span class="tlaGNC">           2 :     ].every((dynamic field) =&gt; field != null);</span></span>
<span id="L241"><span class="lineNum">     241</span>              :   }</span>
<span id="L242"><span class="lineNum">     242</span>              : </span>
<span id="L243"><span class="lineNum">     243</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L244"><span class="lineNum">     244</span>              :   void savePosLimit(DOPNativePosLimitEntity? posLimit) {</span>
<span id="L245"><span class="lineNum">     245</span> <span class="tlaGNC">           1 :     if (posLimit == null || posLimit.maxPosLimitAllow == null) {</span></span>
<span id="L246"><span class="lineNum">     246</span>              :       return;</span>
<span id="L247"><span class="lineNum">     247</span>              :     }</span>
<span id="L248"><span class="lineNum">     248</span>              : </span>
<span id="L249"><span class="lineNum">     249</span> <span class="tlaGNC">           4 :     appState.dopNativeState.posLimitNumber = posLimit.maxPosLimitAllow;</span></span>
<span id="L250"><span class="lineNum">     250</span>              :   }</span>
<span id="L251"><span class="lineNum">     251</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
