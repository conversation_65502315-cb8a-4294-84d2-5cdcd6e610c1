<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/dop_native/resources/dop_native_button_styles.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/dop_native/resources">lib/feature/dop_native/resources</a> - dop_native_button_styles.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">73.3&nbsp;%</td>
            <td class="headerCovTableEntry">30</td>
            <td class="headerCovTableEntry">22</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/resources/resources.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : import 'dop_native_resources.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : class DopNativeButtonStyles extends CommonButtonStyles {</span>
<span id="L7"><span class="lineNum">       7</span> <span class="tlaGNC">          14 :   @override</span></span>
<span id="L8"><span class="lineNum">       8</span>              :   ButtonStyle primary(</span>
<span id="L9"><span class="lineNum">       9</span>              :     ButtonSize size, {</span>
<span id="L10"><span class="lineNum">      10</span>              :     bool isHasShadow = true,</span>
<span id="L11"><span class="lineNum">      11</span>              :     MaterialTapTargetSize tapTargetSize = MaterialTapTargetSize.padded,</span>
<span id="L12"><span class="lineNum">      12</span>              :   }) {</span>
<span id="L13"><span class="lineNum">      13</span> <span class="tlaGNC">          14 :     final WidgetStateProperty&lt;Color?&gt; foreground = WidgetStateProperty.resolveWith(</span></span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaGNC">           9 :       (Set&lt;WidgetState&gt; states) {</span></span>
<span id="L15"><span class="lineNum">      15</span> <span class="tlaGNC">           9 :         return states.contains(WidgetState.disabled)</span></span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaGNC">           4 :             ? dopNativeColors.primaryButtonForegroundDisable</span></span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaGNC">          18 :             : dopNativeColors.primaryButtonForeground;</span></span>
<span id="L18"><span class="lineNum">      18</span>              :       },</span>
<span id="L19"><span class="lineNum">      19</span>              :     );</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC">          14 :     final WidgetStateProperty&lt;Color?&gt; background = WidgetStateProperty.resolveWith(</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           8 :       (Set&lt;WidgetState&gt; states) {</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           8 :         return states.contains(WidgetState.disabled)</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">           4 :             ? dopNativeColors.primaryButtonBgDisable</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">          16 :             : dopNativeColors.primaryButtonBg;</span></span>
<span id="L26"><span class="lineNum">      26</span>              :       },</span>
<span id="L27"><span class="lineNum">      27</span>              :     );</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span>              :     return super</span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaGNC">          14 :         .primary(</span></span>
<span id="L31"><span class="lineNum">      31</span>              :           size,</span>
<span id="L32"><span class="lineNum">      32</span>              :           tapTargetSize: tapTargetSize,</span>
<span id="L33"><span class="lineNum">      33</span>              :         )</span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">          14 :         .copyWith(</span></span>
<span id="L35"><span class="lineNum">      35</span>              :           foregroundColor: foreground,</span>
<span id="L36"><span class="lineNum">      36</span>              :           backgroundColor: background,</span>
<span id="L37"><span class="lineNum">      37</span>              :         );</span>
<span id="L38"><span class="lineNum">      38</span>              :   }</span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L41"><span class="lineNum">      41</span>              :   ButtonStyle tertiary(</span>
<span id="L42"><span class="lineNum">      42</span>              :     ButtonSize size, {</span>
<span id="L43"><span class="lineNum">      43</span>              :     bool isHasShadow = true,</span>
<span id="L44"><span class="lineNum">      44</span>              :     MaterialTapTargetSize tapTargetSize = MaterialTapTargetSize.padded,</span>
<span id="L45"><span class="lineNum">      45</span>              :   }) {</span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           1 :     final WidgetStateProperty&lt;RoundedRectangleBorder&gt; shape = WidgetStateProperty.resolveWith(</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :       (Set&lt;WidgetState&gt; states) {</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :         return RoundedRectangleBorder(</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :           borderRadius: BorderRadius.circular(</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :             buttonDimensions.getCornerRadius(size),</span></span>
<span id="L51"><span class="lineNum">      51</span>              :           ),</span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :           side: BorderSide(</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :             color: states.contains(WidgetState.disabled)</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :                 ? dopNativeColors.tertiaryButtonForegroundDisable</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :                 : dopNativeColors.tertiaryButtonForeground,</span></span>
<span id="L56"><span class="lineNum">      56</span>              :           ),</span>
<span id="L57"><span class="lineNum">      57</span>              :         );</span>
<span id="L58"><span class="lineNum">      58</span>              :       },</span>
<span id="L59"><span class="lineNum">      59</span>              :     );</span>
<span id="L60"><span class="lineNum">      60</span>              : </span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaGNC">           1 :     final WidgetStateProperty&lt;Color&gt; backgroundColor = WidgetStateProperty.resolveWith(</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaGNC">           1 :       (Set&lt;WidgetState&gt; states) {</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaGNC">           1 :         return states.contains(WidgetState.disabled)</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaGNC">           2 :             ? dopNativeColors.tertiaryButtonBgDisable</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaGNC">           2 :             : dopNativeColors.tertiaryButtonBg;</span></span>
<span id="L66"><span class="lineNum">      66</span>              :       },</span>
<span id="L67"><span class="lineNum">      67</span>              :     );</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span>              :     return super</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaGNC">           1 :         .tertiary(</span></span>
<span id="L71"><span class="lineNum">      71</span>              :           size,</span>
<span id="L72"><span class="lineNum">      72</span>              :           isHasShadow: isHasShadow,</span>
<span id="L73"><span class="lineNum">      73</span>              :           tapTargetSize: tapTargetSize,</span>
<span id="L74"><span class="lineNum">      74</span>              :         )</span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaGNC">           1 :         .copyWith(</span></span>
<span id="L76"><span class="lineNum">      76</span>              :           shape: shape,</span>
<span id="L77"><span class="lineNum">      77</span>              :           backgroundColor: backgroundColor,</span>
<span id="L78"><span class="lineNum">      78</span>              :         );</span>
<span id="L79"><span class="lineNum">      79</span>              :   }</span>
<span id="L80"><span class="lineNum">      80</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
