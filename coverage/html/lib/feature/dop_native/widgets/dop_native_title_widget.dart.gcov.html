<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/dop_native/widgets/dop_native_title_widget.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/dop_native/widgets">lib/feature/dop_native/widgets</a> - dop_native_title_widget.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">72.7&nbsp;%</td>
            <td class="headerCovTableEntry">11</td>
            <td class="headerCovTableEntry">8</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../../resources/resources.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../alice/alice_utils.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import '../resources/dop_native_images.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'help_chat_widget.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : class DOPNativeTitleWidget extends StatelessWidget {</span>
<span id="L9"><span class="lineNum">       9</span>              :   final VoidCallback? onLogEventAliceChatButton;</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span> <span class="tlaGNC">        6644 :   const DOPNativeTitleWidget({</span></span>
<span id="L12"><span class="lineNum">      12</span>              :     super.key,</span>
<span id="L13"><span class="lineNum">      13</span>              :     this.onLogEventAliceChatButton,</span>
<span id="L14"><span class="lineNum">      14</span>              :   });</span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaGNC">           3 :   @override</span></span>
<span id="L17"><span class="lineNum">      17</span>              :   Widget build(BuildContext context) {</span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaGNC">           3 :     return Padding(</span></span>
<span id="L19"><span class="lineNum">      19</span>              :       padding: const EdgeInsets.symmetric(vertical: 16),</span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaGNC">           3 :       child: Row(</span></span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC">           3 :         children: &lt;Widget&gt;[</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           6 :           evoImageProvider.asset(</span></span>
<span id="L23"><span class="lineNum">      23</span>              :             DOPNativeImages.icTpBankLogo,</span>
<span id="L24"><span class="lineNum">      24</span>              :             height: 23,</span>
<span id="L25"><span class="lineNum">      25</span>              :           ),</span>
<span id="L26"><span class="lineNum">      26</span>              :           const SizedBox(width: 10),</span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           6 :           evoImageProvider.asset(</span></span>
<span id="L28"><span class="lineNum">      28</span>              :             DOPNativeImages.icEvoLogo,</span>
<span id="L29"><span class="lineNum">      29</span>              :             height: 21,</span>
<span id="L30"><span class="lineNum">      30</span>              :           ),</span>
<span id="L31"><span class="lineNum">      31</span>              :           const Spacer(),</span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           3 :           DopAliceChatEntryPointWidget(</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :             onTap: () {</span></span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :               onLogEventAliceChatButton?.call();</span></span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaUNC">           0 :               AliceUtils().showAliceEvoChatBox();</span></span>
<span id="L36"><span class="lineNum">      36</span>              :             },</span>
<span id="L37"><span class="lineNum">      37</span>              :           ),</span>
<span id="L38"><span class="lineNum">      38</span>              :         ],</span>
<span id="L39"><span class="lineNum">      39</span>              :       ),</span>
<span id="L40"><span class="lineNum">      40</span>              :     );</span>
<span id="L41"><span class="lineNum">      41</span>              :   }</span>
<span id="L42"><span class="lineNum">      42</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
