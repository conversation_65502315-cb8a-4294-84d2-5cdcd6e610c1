<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/dop_native/features/verify_otp/cubit/dop_native_verify_otp_cubit.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/dop_native/features/verify_otp/cubit">lib/feature/dop_native/features/verify_otp/cubit</a> - dop_native_verify_otp_cubit.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">33</td>
            <td class="headerCovTableEntry">33</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/foundation.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/base/bloc_state.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/base/common_cubit.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/data/http_client/mock_config.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../../../data/response/dop_native/dop_native_request_otp_entity.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../../../data/response/dop_native/dop_verify_otp_entity.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../../../prepare_for_app_initiation.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../util/dop_functions.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../dop_native_verify_otp_screen.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../mock/mock_dop_native_verify_otp_case.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span>              : part 'dop_native_verify_otp_state.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span>              : class DOPNativeVerifyOtpCubit extends CommonCubit&lt;DOPNativeVerifyOtpState&gt; {</span>
<span id="L19"><span class="lineNum">      19</span>              :   final DOPNativeRepo dopNativeRepo;</span>
<span id="L20"><span class="lineNum">      20</span>              :   final AppState appState;</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           1 :   DOPNativeVerifyOtpCubit({</span></span>
<span id="L23"><span class="lineNum">      23</span>              :     required this.dopNativeRepo,</span>
<span id="L24"><span class="lineNum">      24</span>              :     required this.appState,</span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           2 :   }) : super(VerifyOtpInitial());</span></span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           1 :   void requestOTPIfNeeded({</span></span>
<span id="L28"><span class="lineNum">      28</span>              :     DOPNativeVerifyOtpType? verifyOtpType,</span>
<span id="L29"><span class="lineNum">      29</span>              :     int? validSeconds,</span>
<span id="L30"><span class="lineNum">      30</span>              :     int? retries,</span>
<span id="L31"><span class="lineNum">      31</span>              :   }) {</span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           1 :     if (verifyOtpType == DOPNativeVerifyOtpType.eSign &amp;&amp; validSeconds != null &amp;&amp; retries != null) {</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaGNC">           1 :       handleESignOTP(validSeconds, retries);</span></span>
<span id="L34"><span class="lineNum">      34</span>              :     } else {</span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           1 :       requestOTP();</span></span>
<span id="L36"><span class="lineNum">      36</span>              :     }</span>
<span id="L37"><span class="lineNum">      37</span>              :   }</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L40"><span class="lineNum">      40</span>              :   void handleESignOTP(int? validSeconds, int? retries) {</span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           1 :     final DOPNativeRequestOTPEntity entity = DOPNativeRequestOTPEntity(</span></span>
<span id="L42"><span class="lineNum">      42</span>              :       validSeconds: validSeconds,</span>
<span id="L43"><span class="lineNum">      43</span>              :       retries: retries,</span>
<span id="L44"><span class="lineNum">      44</span>              :     );</span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           2 :     emit(RequestOTPLoaded(entity));</span></span>
<span id="L46"><span class="lineNum">      46</span>              :   }</span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; requestOTP() async {</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           2 :     emit(ScreenLoading());</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           3 :     final String? uniqueToken = appState.dopNativeState.uniqueToken;</span></span>
<span id="L51"><span class="lineNum">      51</span>              : </span>
<span id="L52"><span class="lineNum">      52</span>              :     // Clear the accessToken in header to avoid sending the previous token</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           2 :     dopUtilFunction.clearDOPNativeAccessTokenHeader();</span></span>
<span id="L54"><span class="lineNum">      54</span>              : </span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaGNC">           2 :     final DOPNativeRequestOTPEntity dopAuthenticateEntity = await dopNativeRepo.requestOTP(</span></span>
<span id="L56"><span class="lineNum">      56</span>              :       token: uniqueToken,</span>
<span id="L57"><span class="lineNum">      57</span>              :       mockConfig: const MockConfig(</span>
<span id="L58"><span class="lineNum">      58</span>              :         enable: false,</span>
<span id="L59"><span class="lineNum">      59</span>              :         fileName: 'dop_native_request_otp_success.json',</span>
<span id="L60"><span class="lineNum">      60</span>              :       ),</span>
<span id="L61"><span class="lineNum">      61</span>              :     );</span>
<span id="L62"><span class="lineNum">      62</span>              : </span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaGNC">           2 :     if (dopAuthenticateEntity.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaGNC">           2 :       emit(RequestOTPLoaded(dopAuthenticateEntity));</span></span>
<span id="L65"><span class="lineNum">      65</span>              :       return;</span>
<span id="L66"><span class="lineNum">      66</span>              :     }</span>
<span id="L67"><span class="lineNum">      67</span>              : </span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaGNC">           3 :     emit(RequestOtpError(ErrorUIModel.fromEntity(dopAuthenticateEntity)));</span></span>
<span id="L69"><span class="lineNum">      69</span>              :   }</span>
<span id="L70"><span class="lineNum">      70</span>              : </span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; verifyOTP(String otp) async {</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaGNC">           2 :     emit(ScreenLoading());</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaGNC">           3 :     final String? uniqueToken = appState.dopNativeState.uniqueToken;</span></span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaGNC">           2 :     final DOPVerifyOTPEntity entity = await dopNativeRepo.verifyOTP(</span></span>
<span id="L76"><span class="lineNum">      76</span>              :       token: uniqueToken,</span>
<span id="L77"><span class="lineNum">      77</span>              :       otp: otp,</span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaGNC">           1 :       mockConfig: MockConfig(</span></span>
<span id="L79"><span class="lineNum">      79</span>              :         enable: false,</span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaGNC">           1 :         fileName: getMockDOPNativeVerifyOTPFileNameByCase(MockDOPNativeVerifyOTPCase.success),</span></span>
<span id="L81"><span class="lineNum">      81</span>              :       ),</span>
<span id="L82"><span class="lineNum">      82</span>              :     );</span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">           2 :     if (entity.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaGNC">           1 :       _handleVerifyOTPSuccess(entity);</span></span>
<span id="L86"><span class="lineNum">      86</span>              :       return;</span>
<span id="L87"><span class="lineNum">      87</span>              :     }</span>
<span id="L88"><span class="lineNum">      88</span>              : </span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">           3 :     emit(VerifyOtpError(ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L90"><span class="lineNum">      90</span>              :   }</span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; _handleVerifyOTPSuccess(DOPVerifyOTPEntity entity) async {</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaGNC">           2 :     if (entity.verdict == DOPVerifyOTPEntity.verdictIncorrectOTP) {</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           3 :       emit(VerifyOtpIncorrect(ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L95"><span class="lineNum">      95</span>              :       return;</span>
<span id="L96"><span class="lineNum">      96</span>              :     }</span>
<span id="L97"><span class="lineNum">      97</span>              : </span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaGNC">           1 :     final String? accessToken = entity.accessToken;</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaGNC">           3 :     appState.dopNativeState.dopNativeAccessToken = accessToken;</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaGNC">           2 :     dopUtilFunction.setDOPNativeAccessTokenHeader(accessToken);</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaGNC">           2 :     emit(VerifyOtpCompleted(entity));</span></span>
<span id="L102"><span class="lineNum">     102</span>              :   }</span>
<span id="L103"><span class="lineNum">     103</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
