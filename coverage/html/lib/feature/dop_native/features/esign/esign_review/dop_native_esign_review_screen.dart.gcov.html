<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/dop_native/features/esign/esign_review/dop_native_esign_review_screen.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/dop_native/features/esign/esign_review">lib/feature/dop_native/features/esign/esign_review</a> - dop_native_esign_review_screen.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.6&nbsp;%</td>
            <td class="headerCovTableEntry">180</td>
            <td class="headerCovTableEntry">1</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/gestures.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter/material.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/base/page_base.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/common_package/common_package.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/global_key_provider.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'package:flutter_common_package/resources/dimensions.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import 'package:flutter_common_package/widget/common_button.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../../../../base/evo_page_state_base.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../../../../resources/resources.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../../../../prepare_for_app_initiation.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../../../../util/ui_utils/evo_ui_utils.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../../base/cubit/dop_native_application_state.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../../base/dop_native_page_state_base.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../../../dop_native_constants.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../../../resources/dop_native_images.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../../../resources/dop_native_pdf_url.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../../../resources/dop_native_resources.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import '../../../resources/dop_native_ui_strings.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import '../../../util/dop_functions.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import '../../../util/dop_native_navigation_utils.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import '../../../util/dop_native_submit_status_polling/dop_native_submit_status_polling_impl.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : import '../../../widgets/appbar/dop_native_appbar_widget.dart';</span>
<span id="L27"><span class="lineNum">      27</span>              : import '../../../widgets/dop_native_form_header_widget.dart';</span>
<span id="L28"><span class="lineNum">      28</span>              : import '../../../widgets/dop_native_labeled_check_box_widget.dart';</span>
<span id="L29"><span class="lineNum">      29</span>              : import '../../../widgets/dop_native_pdf_review/dop_native_pdf_review_widget.dart';</span>
<span id="L30"><span class="lineNum">      30</span>              : import '../../pdf_view/dop_native_pdf_screen.dart';</span>
<span id="L31"><span class="lineNum">      31</span>              : import '../../verify_otp/dop_native_verify_otp_screen.dart';</span>
<span id="L32"><span class="lineNum">      32</span>              : import 'america_citizen/dop_native_america_citizen_screen.dart';</span>
<span id="L33"><span class="lineNum">      33</span>              : import 'america_citizen/dop_native_america_elements_constants.dart';</span>
<span id="L34"><span class="lineNum">      34</span>              : import 'cubit/dop_native_esign_review_cubit.dart';</span>
<span id="L35"><span class="lineNum">      35</span>              : import 'dop_native_link_card_popup.dart';</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span>              : class DOPNativeESignReviewScreen extends PageBase {</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">         949 :   const DOPNativeESignReviewScreen({super.key});</span></span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :   EvoPageStateBase&lt;DOPNativeESignReviewScreen&gt; createState() =&gt; _DOPNativeESignReviewScreenState();</span></span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L44"><span class="lineNum">      44</span>              :   EventTrackingScreenId get eventTrackingScreenId =&gt; EventTrackingScreenId.undefined;</span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :   RouteSettings get routeSettings =&gt; RouteSettings(name: Screen.dopNativeESignReviewScreen.name);</span></span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :   static void pushNamed() {</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :     return navigatorContext?.pushNamed(Screen.dopNativeESignReviewScreen.name);</span></span>
<span id="L51"><span class="lineNum">      51</span>              :   }</span>
<span id="L52"><span class="lineNum">      52</span>              : </span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :   static void pushReplacementNamed() {</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :     return navigatorContext?.pushReplacementNamed(Screen.dopNativeESignReviewScreen.name);</span></span>
<span id="L55"><span class="lineNum">      55</span>              :   }</span>
<span id="L56"><span class="lineNum">      56</span>              : }</span>
<span id="L57"><span class="lineNum">      57</span>              : </span>
<span id="L58"><span class="lineNum">      58</span>              : class _DOPNativeESignReviewScreenState extends DOPNativePageStateBase&lt;DOPNativeESignReviewScreen&gt; {</span>
<span id="L59"><span class="lineNum">      59</span>              :   final DOPNativeESignReviewCubit _eSignReviewCubit = DOPNativeESignReviewCubit(</span>
<span id="L60"><span class="lineNum">      60</span>              :     dopNativeRepo: getIt.get&lt;DOPNativeRepo&gt;(),</span>
<span id="L61"><span class="lineNum">      61</span>              :     appState: getIt.get&lt;AppState&gt;(),</span>
<span id="L62"><span class="lineNum">      62</span>              :     dopNativeSubmitStatusPolling: DOPNativeSubmitStatusPollingImpl(</span>
<span id="L63"><span class="lineNum">      63</span>              :       intervalDuration: const Duration(</span>
<span id="L64"><span class="lineNum">      64</span>              :         milliseconds: DOPNativeConstants.defaultPollingIntervalTimeInMs,</span>
<span id="L65"><span class="lineNum">      65</span>              :       ),</span>
<span id="L66"><span class="lineNum">      66</span>              :     ),</span>
<span id="L67"><span class="lineNum">      67</span>              :   );</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span>              :   final GlobalKey&lt;TooltipState&gt; americaTooltipKey = GlobalKey&lt;TooltipState&gt;();</span>
<span id="L70"><span class="lineNum">      70</span>              :   final GlobalKey&lt;TooltipState&gt; linkCardTooltipKey = GlobalKey&lt;TooltipState&gt;();</span>
<span id="L71"><span class="lineNum">      71</span>              :   final double _pdfHeightPercent = 456 / DOPNativeConstants.figmaScreenHeight;</span>
<span id="L72"><span class="lineNum">      72</span>              :   final DOPNativePdfReviewWidgetController pdfPreviewController =</span>
<span id="L73"><span class="lineNum">      73</span>              :       DOPNativePdfReviewWidgetController();</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L76"><span class="lineNum">      76</span>              :   void initState() {</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :     super.initState();</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :     WidgetsBinding.instance.addPostFrameCallback((_) {</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :       _eSignReviewCubit.getESignState();</span></span>
<span id="L80"><span class="lineNum">      80</span>              :     });</span>
<span id="L81"><span class="lineNum">      81</span>              :   }</span>
<span id="L82"><span class="lineNum">      82</span>              : </span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :   void _handleESignStateChange(DOPNativeESignReviewState state) {</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaUNC">           0 :     if (state is ESignSubmitLoading || state is ESignPrepareLoading) {</span></span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :       showDOPLoading();</span></span>
<span id="L86"><span class="lineNum">      86</span>              :       return;</span>
<span id="L87"><span class="lineNum">      87</span>              :     }</span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :     hideDOPLoading();</span></span>
<span id="L89"><span class="lineNum">      89</span>              : </span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :     if (state is ESignSubmitSuccess) {</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :       _eSignReviewCubit.prepareESign();</span></span>
<span id="L92"><span class="lineNum">      92</span>              :       return;</span>
<span id="L93"><span class="lineNum">      93</span>              :     }</span>
<span id="L94"><span class="lineNum">      94</span>              : </span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :     if (state is ESignPrepareSuccess || state is ESignPrepareInvalidState) {</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :       dopNativeApplicationStateCubit.getApplicationState();</span></span>
<span id="L97"><span class="lineNum">      97</span>              :       return;</span>
<span id="L98"><span class="lineNum">      98</span>              :     }</span>
<span id="L99"><span class="lineNum">      99</span>              : </span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :     if (state is GetESignStateError) {</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :       handleEvoApiError(state.error);</span></span>
<span id="L102"><span class="lineNum">     102</span>              :       return;</span>
<span id="L103"><span class="lineNum">     103</span>              :     }</span>
<span id="L104"><span class="lineNum">     104</span>              : </span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :     if (state is ESignPrepareError) {</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :       handleEvoApiError(state.error);</span></span>
<span id="L107"><span class="lineNum">     107</span>              :       return;</span>
<span id="L108"><span class="lineNum">     108</span>              :     }</span>
<span id="L109"><span class="lineNum">     109</span>              : </span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :     if (state is ESignSubmitError) {</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :       handleEvoApiError(state.error);</span></span>
<span id="L112"><span class="lineNum">     112</span>              :       return;</span>
<span id="L113"><span class="lineNum">     113</span>              :     }</span>
<span id="L114"><span class="lineNum">     114</span>              : </span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :     if (state is GetESignStateSuccess) {</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :       pdfPreviewController.loadData?.call(state.url);</span></span>
<span id="L117"><span class="lineNum">     117</span>              :       return;</span>
<span id="L118"><span class="lineNum">     118</span>              :     }</span>
<span id="L119"><span class="lineNum">     119</span>              :   }</span>
<span id="L120"><span class="lineNum">     120</span>              : </span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L122"><span class="lineNum">     122</span>              :   void handleDOPNativeApplicationStateChanged(DOPNativeApplicationState state) {</span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :     if (state is DOPNativeApplicationStateLoaded) {</span></span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaUNC">           0 :       final String? currentScreen = state.entity.currentStep;</span></span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :       if (currentScreen == DOPNativeNavigationStep.esignOtp.value) {</span></span>
<span id="L126"><span class="lineNum">     126</span>              :         final ESignPrepareSuccess eSignPrepareSuccess =</span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :             _eSignReviewCubit.state as ESignPrepareSuccess;</span></span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :         DOPNativeVerifyOtpScreen.pushReplacementNamed(</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :           DOPNativeVerifyOtpScreenArg(</span></span>
<span id="L130"><span class="lineNum">     130</span>              :             verifyOtpType: DOPNativeVerifyOtpType.eSign,</span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :             validSeconds: eSignPrepareSuccess.entity?.validSeconds,</span></span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaUNC">           0 :             retries: eSignPrepareSuccess.entity?.retries,</span></span>
<span id="L133"><span class="lineNum">     133</span>              :           ),</span>
<span id="L134"><span class="lineNum">     134</span>              :         );</span>
<span id="L135"><span class="lineNum">     135</span>              :         return;</span>
<span id="L136"><span class="lineNum">     136</span>              :       }</span>
<span id="L137"><span class="lineNum">     137</span>              :     }</span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :     super.handleDOPNativeApplicationStateChanged(state);</span></span>
<span id="L139"><span class="lineNum">     139</span>              :   }</span>
<span id="L140"><span class="lineNum">     140</span>              : </span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L142"><span class="lineNum">     142</span>              :   Widget getContentWidget(BuildContext context) {</span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaUNC">           0 :     return Scaffold(</span></span>
<span id="L144"><span class="lineNum">     144</span>              :       appBar: const DOPNativeAppBar(),</span>
<span id="L145"><span class="lineNum">     145</span> <span class="tlaUNC">           0 :       backgroundColor: dopNativeColors.screenBackground,</span></span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaUNC">           0 :       body: BlocProvider&lt;DOPNativeESignReviewCubit&gt;(</span></span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :         create: (_) =&gt; _eSignReviewCubit,</span></span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaUNC">           0 :         child: BlocListener&lt;DOPNativeESignReviewCubit, DOPNativeESignReviewState&gt;(</span></span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :           listener: (BuildContext context, DOPNativeESignReviewState state) {</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :             _handleESignStateChange(state);</span></span>
<span id="L151"><span class="lineNum">     151</span>              :           },</span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :           child: SafeArea(</span></span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :             child: Padding(</span></span>
<span id="L154"><span class="lineNum">     154</span>              :               padding: const EdgeInsets.symmetric(horizontal: 20),</span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaUNC">           0 :               child: Column(</span></span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaUNC">           0 :                 children: &lt;Widget&gt;[</span></span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :                   Expanded(</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :                     child: SingleChildScrollView(</span></span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :                       child: Column(</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :                         children: &lt;Widget&gt;[</span></span>
<span id="L161"><span class="lineNum">     161</span>              :                           const DOPNativeFormHeaderWidget(</span>
<span id="L162"><span class="lineNum">     162</span>              :                             currentStep: 3,</span>
<span id="L163"><span class="lineNum">     163</span>              :                             titleStep: DOPNativeStrings.dopNativeSignEContract,</span>
<span id="L164"><span class="lineNum">     164</span>              :                           ),</span>
<span id="L165"><span class="lineNum">     165</span>              :                           const SizedBox(height: 20),</span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaUNC">           0 :                           _buildEContract(),</span></span>
<span id="L167"><span class="lineNum">     167</span>              :                           const SizedBox(height: 20),</span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaUNC">           0 :                           ..._buildUserProclamation(),</span></span>
<span id="L169"><span class="lineNum">     169</span> <span class="tlaUNC">           0 :                           ..._buildActions(),</span></span>
<span id="L170"><span class="lineNum">     170</span>              :                         ],</span>
<span id="L171"><span class="lineNum">     171</span>              :                       ),</span>
<span id="L172"><span class="lineNum">     172</span>              :                     ),</span>
<span id="L173"><span class="lineNum">     173</span>              :                   ),</span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaUNC">           0 :                   _buildCTA(),</span></span>
<span id="L175"><span class="lineNum">     175</span>              :                 ],</span>
<span id="L176"><span class="lineNum">     176</span>              :               ),</span>
<span id="L177"><span class="lineNum">     177</span>              :             ),</span>
<span id="L178"><span class="lineNum">     178</span>              :           ),</span>
<span id="L179"><span class="lineNum">     179</span>              :         ),</span>
<span id="L180"><span class="lineNum">     180</span>              :       ),</span>
<span id="L181"><span class="lineNum">     181</span>              :     );</span>
<span id="L182"><span class="lineNum">     182</span>              :   }</span>
<span id="L183"><span class="lineNum">     183</span>              : </span>
<span id="L184"><span class="lineNum">     184</span> <span class="tlaUNC">           0 :   Widget _buildEContract() {</span></span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :     return DOPNativePdfReviewWidget(</span></span>
<span id="L186"><span class="lineNum">     186</span> <span class="tlaUNC">           0 :       height: EvoUiUtils().calculateVerticalSpace(</span></span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaUNC">           0 :         context: context,</span></span>
<span id="L188"><span class="lineNum">     188</span> <span class="tlaUNC">           0 :         heightPercentage: _pdfHeightPercent,</span></span>
<span id="L189"><span class="lineNum">     189</span>              :       ),</span>
<span id="L190"><span class="lineNum">     190</span>              :       width: double.infinity,</span>
<span id="L191"><span class="lineNum">     191</span> <span class="tlaUNC">           0 :       controller: pdfPreviewController,</span></span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaUNC">           0 :       callback: DOPNativePdfReviewWidgetCallback(</span></span>
<span id="L193"><span class="lineNum">     193</span> <span class="tlaUNC">           0 :         onRetry: () {</span></span>
<span id="L194"><span class="lineNum">     194</span> <span class="tlaUNC">           0 :           pdfPreviewController.resetPdf?.call();</span></span>
<span id="L195"><span class="lineNum">     195</span> <span class="tlaUNC">           0 :           _eSignReviewCubit.getESignState();</span></span>
<span id="L196"><span class="lineNum">     196</span>              :         },</span>
<span id="L197"><span class="lineNum">     197</span> <span class="tlaUNC">           0 :         onLoading: () {</span></span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaUNC">           0 :           _eSignReviewCubit.updatePreviewStatus(false);</span></span>
<span id="L199"><span class="lineNum">     199</span>              :         },</span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaUNC">           0 :         onLoaded: () {</span></span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaUNC">           0 :           _eSignReviewCubit.updatePreviewStatus(true);</span></span>
<span id="L202"><span class="lineNum">     202</span>              :         },</span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaUNC">           0 :         onError: () {</span></span>
<span id="L204"><span class="lineNum">     204</span> <span class="tlaUNC">           0 :           _eSignReviewCubit.updatePreviewStatus(false);</span></span>
<span id="L205"><span class="lineNum">     205</span>              :         },</span>
<span id="L206"><span class="lineNum">     206</span>              :       ),</span>
<span id="L207"><span class="lineNum">     207</span>              :     );</span>
<span id="L208"><span class="lineNum">     208</span>              :   }</span>
<span id="L209"><span class="lineNum">     209</span>              : </span>
<span id="L210"><span class="lineNum">     210</span> <span class="tlaUNC">           0 :   List&lt;Widget&gt; _buildUserProclamation() {</span></span>
<span id="L211"><span class="lineNum">     211</span> <span class="tlaUNC">           0 :     return &lt;Widget&gt;[</span></span>
<span id="L212"><span class="lineNum">     212</span> <span class="tlaUNC">           0 :       Text(</span></span>
<span id="L213"><span class="lineNum">     213</span>              :         DOPNativeStrings.dopNativeESignUserProclamation,</span>
<span id="L214"><span class="lineNum">     214</span> <span class="tlaUNC">           0 :         style: dopNativeTextStyles.bodyLarge(dopNativeColors.dopNativeESignProclamationText),</span></span>
<span id="L215"><span class="lineNum">     215</span>              :       ),</span>
<span id="L216"><span class="lineNum">     216</span>              :       const SizedBox(height: 16),</span>
<span id="L217"><span class="lineNum">     217</span> <span class="tlaUNC">           0 :       _buildTextWithBullet(textWidget: _buildFPTCA()),</span></span>
<span id="L218"><span class="lineNum">     218</span> <span class="tlaUNC">           0 :       _buildTextWithBullet(text: DOPNativeStrings.dopNativeESignAgreeFPTCASign),</span></span>
<span id="L219"><span class="lineNum">     219</span> <span class="tlaUNC">           0 :       _buildTextWithBullet(text: DOPNativeStrings.dopNativeESignResponsibility),</span></span>
<span id="L220"><span class="lineNum">     220</span>              :       const SizedBox(height: 20),</span>
<span id="L221"><span class="lineNum">     221</span> <span class="tlaUNC">           0 :       _buildTextWithRoundedBox(text: DOPNativeStrings.dopNativeESignNotificationForSMSOTP),</span></span>
<span id="L222"><span class="lineNum">     222</span>              :     ];</span>
<span id="L223"><span class="lineNum">     223</span>              :   }</span>
<span id="L224"><span class="lineNum">     224</span>              : </span>
<span id="L225"><span class="lineNum">     225</span> <span class="tlaUNC">           0 :   RichText _buildFPTCA() {</span></span>
<span id="L226"><span class="lineNum">     226</span> <span class="tlaUNC">           0 :     return RichText(</span></span>
<span id="L227"><span class="lineNum">     227</span> <span class="tlaUNC">           0 :         text: TextSpan(</span></span>
<span id="L228"><span class="lineNum">     228</span> <span class="tlaUNC">           0 :       style: dopNativeTextStyles.bodyLarge(dopNativeColors.dopNativeESignProclamationText),</span></span>
<span id="L229"><span class="lineNum">     229</span> <span class="tlaUNC">           0 :       children: &lt;InlineSpan&gt;[</span></span>
<span id="L230"><span class="lineNum">     230</span>              :         const TextSpan(text: DOPNativeStrings.dopNativeESignHasReadFPTCA1),</span>
<span id="L231"><span class="lineNum">     231</span> <span class="tlaUNC">           0 :         TextSpan(</span></span>
<span id="L232"><span class="lineNum">     232</span> <span class="tlaUNC">           0 :           recognizer: TapGestureRecognizer()</span></span>
<span id="L233"><span class="lineNum">     233</span> <span class="tlaUNC">           0 :             ..onTap = () {</span></span>
<span id="L234"><span class="lineNum">     234</span> <span class="tlaUNC">           0 :               dopUtilFunction.openDOPPDFView(DOPNativePDFViewArg(</span></span>
<span id="L235"><span class="lineNum">     235</span> <span class="tlaUNC">           0 :                   url: getDOPNativePDFURLByFlavor(DOPNativePDFUrl.fptCATermCondition)));</span></span>
<span id="L236"><span class="lineNum">     236</span>              :             },</span>
<span id="L237"><span class="lineNum">     237</span>              :           text: DOPNativeStrings.dopNativeESignHasReadFPTCA2,</span>
<span id="L238"><span class="lineNum">     238</span> <span class="tlaUNC">           0 :           style: dopNativeTextStyles</span></span>
<span id="L239"><span class="lineNum">     239</span> <span class="tlaUNC">           0 :               .bodyLarge(dopNativeColors.dopNativeESignProclamationHighlightText)</span></span>
<span id="L240"><span class="lineNum">     240</span> <span class="tlaUNC">           0 :               .copyWith(decoration: TextDecoration.underline),</span></span>
<span id="L241"><span class="lineNum">     241</span>              :         ),</span>
<span id="L242"><span class="lineNum">     242</span>              :         const TextSpan(text: DOPNativeStrings.dopNativeESignHasReadFPTCA3),</span>
<span id="L243"><span class="lineNum">     243</span>              :       ],</span>
<span id="L244"><span class="lineNum">     244</span>              :     ));</span>
<span id="L245"><span class="lineNum">     245</span>              :   }</span>
<span id="L246"><span class="lineNum">     246</span>              : </span>
<span id="L247"><span class="lineNum">     247</span> <span class="tlaUNC">           0 :   Widget _buildTextWithRoundedBox({</span></span>
<span id="L248"><span class="lineNum">     248</span>              :     required String text,</span>
<span id="L249"><span class="lineNum">     249</span>              :   }) {</span>
<span id="L250"><span class="lineNum">     250</span> <span class="tlaUNC">           0 :     return Container(</span></span>
<span id="L251"><span class="lineNum">     251</span> <span class="tlaUNC">           0 :       padding: EdgeInsets.all(16),</span></span>
<span id="L252"><span class="lineNum">     252</span> <span class="tlaUNC">           0 :       decoration: BoxDecoration(</span></span>
<span id="L253"><span class="lineNum">     253</span> <span class="tlaUNC">           0 :         borderRadius: BorderRadius.circular(12),</span></span>
<span id="L254"><span class="lineNum">     254</span> <span class="tlaUNC">           0 :         color: dopNativeColors.background,</span></span>
<span id="L255"><span class="lineNum">     255</span>              :       ),</span>
<span id="L256"><span class="lineNum">     256</span> <span class="tlaUNC">           0 :       child: Text(</span></span>
<span id="L257"><span class="lineNum">     257</span>              :         text,</span>
<span id="L258"><span class="lineNum">     258</span> <span class="tlaUNC">           0 :         style: dopNativeTextStyles.bodyMedium(dopNativeColors.dopNativeESignRoundedBoxText),</span></span>
<span id="L259"><span class="lineNum">     259</span>              :       ),</span>
<span id="L260"><span class="lineNum">     260</span>              :     );</span>
<span id="L261"><span class="lineNum">     261</span>              :   }</span>
<span id="L262"><span class="lineNum">     262</span>              : </span>
<span id="L263"><span class="lineNum">     263</span> <span class="tlaUNC">           0 :   Widget _buildTextWithBullet({String? text, RichText? textWidget}) {</span></span>
<span id="L264"><span class="lineNum">     264</span> <span class="tlaUNC">           0 :     return Row(</span></span>
<span id="L265"><span class="lineNum">     265</span>              :       crossAxisAlignment: CrossAxisAlignment.start,</span>
<span id="L266"><span class="lineNum">     266</span> <span class="tlaUNC">           0 :       children: &lt;Widget&gt;[</span></span>
<span id="L267"><span class="lineNum">     267</span>              :         const SizedBox(width: 4),</span>
<span id="L268"><span class="lineNum">     268</span> <span class="tlaUNC">           0 :         Text(</span></span>
<span id="L269"><span class="lineNum">     269</span>              :           '\u2022',</span>
<span id="L270"><span class="lineNum">     270</span> <span class="tlaUNC">           0 :           style: dopNativeTextStyles.bodyLarge(dopNativeColors.textActive),</span></span>
<span id="L271"><span class="lineNum">     271</span>              :         ),</span>
<span id="L272"><span class="lineNum">     272</span>              :         const SizedBox(width: 5),</span>
<span id="L273"><span class="lineNum">     273</span> <span class="tlaUNC">           0 :         Expanded(</span></span>
<span id="L274"><span class="lineNum">     274</span>              :           child: textWidget ??</span>
<span id="L275"><span class="lineNum">     275</span> <span class="tlaUNC">           0 :               Text(</span></span>
<span id="L276"><span class="lineNum">     276</span>              :                 text ?? '',</span>
<span id="L277"><span class="lineNum">     277</span>              :                 textAlign: TextAlign.left,</span>
<span id="L278"><span class="lineNum">     278</span>              :                 softWrap: true,</span>
<span id="L279"><span class="lineNum">     279</span>              :                 style:</span>
<span id="L280"><span class="lineNum">     280</span> <span class="tlaUNC">           0 :                     dopNativeTextStyles.bodyLarge(dopNativeColors.dopNativeESignProclamationText),</span></span>
<span id="L281"><span class="lineNum">     281</span>              :               ),</span>
<span id="L282"><span class="lineNum">     282</span>              :         ),</span>
<span id="L283"><span class="lineNum">     283</span>              :       ],</span>
<span id="L284"><span class="lineNum">     284</span>              :     );</span>
<span id="L285"><span class="lineNum">     285</span>              :   }</span>
<span id="L286"><span class="lineNum">     286</span>              : </span>
<span id="L287"><span class="lineNum">     287</span> <span class="tlaUNC">           0 :   List&lt;Widget&gt; _buildActions() {</span></span>
<span id="L288"><span class="lineNum">     288</span> <span class="tlaUNC">           0 :     return &lt;Widget&gt;[</span></span>
<span id="L289"><span class="lineNum">     289</span>              :       const SizedBox(height: 20),</span>
<span id="L290"><span class="lineNum">     290</span> <span class="tlaUNC">           0 :       _buildAmericaElement(),</span></span>
<span id="L291"><span class="lineNum">     291</span>              :       const SizedBox(height: 16),</span>
<span id="L292"><span class="lineNum">     292</span> <span class="tlaUNC">           0 :       _buildLinkEvoCard(),</span></span>
<span id="L293"><span class="lineNum">     293</span>              :       const SizedBox(height: 16),</span>
<span id="L294"><span class="lineNum">     294</span>              :     ];</span>
<span id="L295"><span class="lineNum">     295</span>              :   }</span>
<span id="L296"><span class="lineNum">     296</span>              : </span>
<span id="L297"><span class="lineNum">     297</span> <span class="tlaUNC">           0 :   Widget _buildAmericaElement() {</span></span>
<span id="L298"><span class="lineNum">     298</span> <span class="tlaUNC">           0 :     return BlocBuilder&lt;DOPNativeESignReviewCubit, DOPNativeESignReviewState&gt;(</span></span>
<span id="L299"><span class="lineNum">     299</span> <span class="tlaUNC">           0 :       buildWhen: (_, DOPNativeESignReviewState state) {</span></span>
<span id="L300"><span class="lineNum">     300</span> <span class="tlaUNC">           0 :         return state is AmericaCitizenChanged;</span></span>
<span id="L301"><span class="lineNum">     301</span>              :       },</span>
<span id="L302"><span class="lineNum">     302</span> <span class="tlaUNC">           0 :       builder: (BuildContext context, DOPNativeESignReviewState state) {</span></span>
<span id="L303"><span class="lineNum">     303</span>              :         List&lt;DOPNativeAmericaElementsConstants&gt; selectedList =</span>
<span id="L304"><span class="lineNum">     304</span> <span class="tlaUNC">           0 :             &lt;DOPNativeAmericaElementsConstants&gt;[];</span></span>
<span id="L305"><span class="lineNum">     305</span> <span class="tlaUNC">           0 :         if (state is AmericaCitizenChanged) {</span></span>
<span id="L306"><span class="lineNum">     306</span> <span class="tlaUNC">           0 :           selectedList = state.selected;</span></span>
<span id="L307"><span class="lineNum">     307</span>              :         }</span>
<span id="L308"><span class="lineNum">     308</span>              : </span>
<span id="L309"><span class="lineNum">     309</span> <span class="tlaUNC">           0 :         return Row(</span></span>
<span id="L310"><span class="lineNum">     310</span> <span class="tlaUNC">           0 :           children: &lt;Widget&gt;[</span></span>
<span id="L311"><span class="lineNum">     311</span> <span class="tlaUNC">           0 :             Expanded(</span></span>
<span id="L312"><span class="lineNum">     312</span> <span class="tlaUNC">           0 :               child: DOPNativeLabeledCheckboxWidget(</span></span>
<span id="L313"><span class="lineNum">     313</span>              :                 label: DOPNativeStrings.dopNativeUserDoNotHaveAmericaElement,</span>
<span id="L314"><span class="lineNum">     314</span> <span class="tlaUNC">           0 :                 value: selectedList.isEmpty,</span></span>
<span id="L315"><span class="lineNum">     315</span> <span class="tlaUNC">           0 :                 onChanged: (bool isSelect) =&gt; _handleSelectAmerica(isSelect, selectedList),</span></span>
<span id="L316"><span class="lineNum">     316</span>              :               ),</span>
<span id="L317"><span class="lineNum">     317</span>              :             ),</span>
<span id="L318"><span class="lineNum">     318</span> <span class="tlaUNC">           0 :             GestureDetector(</span></span>
<span id="L319"><span class="lineNum">     319</span> <span class="tlaUNC">           0 :               key: americaTooltipKey,</span></span>
<span id="L320"><span class="lineNum">     320</span> <span class="tlaUNC">           0 :               onTap: () {</span></span>
<span id="L321"><span class="lineNum">     321</span> <span class="tlaUNC">           0 :                 dopUtilFunction.showBlurredTooltip(</span></span>
<span id="L322"><span class="lineNum">     322</span>              :                   context: context,</span>
<span id="L323"><span class="lineNum">     323</span>              :                   message: DOPNativeStrings.dopNativeFATCATooltip,</span>
<span id="L324"><span class="lineNum">     324</span> <span class="tlaUNC">           0 :                   targetKey: americaTooltipKey,</span></span>
<span id="L325"><span class="lineNum">     325</span>              :                 );</span>
<span id="L326"><span class="lineNum">     326</span>              :               },</span>
<span id="L327"><span class="lineNum">     327</span> <span class="tlaUNC">           0 :               child: evoImageProvider.asset(DOPNativeImages.icInfo, width: 20),</span></span>
<span id="L328"><span class="lineNum">     328</span>              :             ),</span>
<span id="L329"><span class="lineNum">     329</span>              :           ],</span>
<span id="L330"><span class="lineNum">     330</span>              :         );</span>
<span id="L331"><span class="lineNum">     331</span>              :       },</span>
<span id="L332"><span class="lineNum">     332</span>              :     );</span>
<span id="L333"><span class="lineNum">     333</span>              :   }</span>
<span id="L334"><span class="lineNum">     334</span>              : </span>
<span id="L335"><span class="lineNum">     335</span> <span class="tlaUNC">           0 :   void _handleSelectAmerica(bool isSelect, List&lt;DOPNativeAmericaElementsConstants&gt; selectedList) {</span></span>
<span id="L336"><span class="lineNum">     336</span>              :     if (isSelect) {</span>
<span id="L337"><span class="lineNum">     337</span> <span class="tlaUNC">           0 :       _eSignReviewCubit.onAmericaChanged(List&lt;DOPNativeAmericaElementsConstants&gt;.empty());</span></span>
<span id="L338"><span class="lineNum">     338</span>              :     } else {</span>
<span id="L339"><span class="lineNum">     339</span> <span class="tlaUNC">           0 :       DOPNativeAmericaCitizenScreen.pushNamed(</span></span>
<span id="L340"><span class="lineNum">     340</span> <span class="tlaUNC">           0 :         arg: DOPNativeAmericaCitizenArg(</span></span>
<span id="L341"><span class="lineNum">     341</span> <span class="tlaUNC">           0 :           americaElements: &lt;DOPNativeAmericaElementsConstants&gt;[],</span></span>
<span id="L342"><span class="lineNum">     342</span> <span class="tlaUNC">           0 :           onChanged: _eSignReviewCubit.onAmericaChanged,</span></span>
<span id="L343"><span class="lineNum">     343</span>              :         ),</span>
<span id="L344"><span class="lineNum">     344</span>              :       );</span>
<span id="L345"><span class="lineNum">     345</span>              :     }</span>
<span id="L346"><span class="lineNum">     346</span>              :   }</span>
<span id="L347"><span class="lineNum">     347</span>              : </span>
<span id="L348"><span class="lineNum">     348</span> <span class="tlaUNC">           0 :   Widget _buildLinkEvoCard() {</span></span>
<span id="L349"><span class="lineNum">     349</span> <span class="tlaUNC">           0 :     return BlocBuilder&lt;DOPNativeESignReviewCubit, DOPNativeESignReviewState&gt;(</span></span>
<span id="L350"><span class="lineNum">     350</span> <span class="tlaUNC">           0 :       buildWhen: (_, DOPNativeESignReviewState state) {</span></span>
<span id="L351"><span class="lineNum">     351</span> <span class="tlaUNC">           0 :         return state is LinkCardChanged;</span></span>
<span id="L352"><span class="lineNum">     352</span>              :       },</span>
<span id="L353"><span class="lineNum">     353</span> <span class="tlaUNC">           0 :       builder: (BuildContext context, DOPNativeESignReviewState state) {</span></span>
<span id="L354"><span class="lineNum">     354</span>              :         bool isChecked = true;</span>
<span id="L355"><span class="lineNum">     355</span> <span class="tlaUNC">           0 :         if (state is LinkCardChanged) {</span></span>
<span id="L356"><span class="lineNum">     356</span> <span class="tlaUNC">           0 :           isChecked = state.isChecked;</span></span>
<span id="L357"><span class="lineNum">     357</span>              :         }</span>
<span id="L358"><span class="lineNum">     358</span> <span class="tlaUNC">           0 :         return Row(</span></span>
<span id="L359"><span class="lineNum">     359</span> <span class="tlaUNC">           0 :           children: &lt;Widget&gt;[</span></span>
<span id="L360"><span class="lineNum">     360</span> <span class="tlaUNC">           0 :             Expanded(</span></span>
<span id="L361"><span class="lineNum">     361</span> <span class="tlaUNC">           0 :               child: DOPNativeLabeledCheckboxWidget(</span></span>
<span id="L362"><span class="lineNum">     362</span>              :                 label: DOPNativeStrings.dopNativeUserAgreeLinkCardToEVO,</span>
<span id="L363"><span class="lineNum">     363</span>              :                 value: isChecked,</span>
<span id="L364"><span class="lineNum">     364</span> <span class="tlaUNC">           0 :                 onChanged: _handleEvoLinkCard,</span></span>
<span id="L365"><span class="lineNum">     365</span>              :               ),</span>
<span id="L366"><span class="lineNum">     366</span>              :             ),</span>
<span id="L367"><span class="lineNum">     367</span> <span class="tlaUNC">           0 :             GestureDetector(</span></span>
<span id="L368"><span class="lineNum">     368</span> <span class="tlaUNC">           0 :               key: linkCardTooltipKey,</span></span>
<span id="L369"><span class="lineNum">     369</span> <span class="tlaUNC">           0 :               onTap: () {</span></span>
<span id="L370"><span class="lineNum">     370</span> <span class="tlaUNC">           0 :                 dopUtilFunction.showBlurredTooltip(</span></span>
<span id="L371"><span class="lineNum">     371</span>              :                   context: context,</span>
<span id="L372"><span class="lineNum">     372</span>              :                   message: DOPNativeStrings.dopNativeLinkCardToolTip,</span>
<span id="L373"><span class="lineNum">     373</span> <span class="tlaUNC">           0 :                   targetKey: linkCardTooltipKey,</span></span>
<span id="L374"><span class="lineNum">     374</span>              :                 );</span>
<span id="L375"><span class="lineNum">     375</span>              :               },</span>
<span id="L376"><span class="lineNum">     376</span> <span class="tlaUNC">           0 :               child: evoImageProvider.asset(DOPNativeImages.icInfo, width: 20),</span></span>
<span id="L377"><span class="lineNum">     377</span>              :             ),</span>
<span id="L378"><span class="lineNum">     378</span>              :           ],</span>
<span id="L379"><span class="lineNum">     379</span>              :         );</span>
<span id="L380"><span class="lineNum">     380</span>              :       },</span>
<span id="L381"><span class="lineNum">     381</span>              :     );</span>
<span id="L382"><span class="lineNum">     382</span>              :   }</span>
<span id="L383"><span class="lineNum">     383</span>              : </span>
<span id="L384"><span class="lineNum">     384</span> <span class="tlaUNC">           0 :   void _handleEvoLinkCard(bool isChecked) {</span></span>
<span id="L385"><span class="lineNum">     385</span>              :     if (isChecked) {</span>
<span id="L386"><span class="lineNum">     386</span> <span class="tlaUNC">           0 :       _eSignReviewCubit.onLinkCardChanged(true);</span></span>
<span id="L387"><span class="lineNum">     387</span>              :       return;</span>
<span id="L388"><span class="lineNum">     388</span>              :     }</span>
<span id="L389"><span class="lineNum">     389</span>              : </span>
<span id="L390"><span class="lineNum">     390</span> <span class="tlaUNC">           0 :     DOPNativeLinkCardPopUp.show(_eSignReviewCubit.onLinkCardChanged);</span></span>
<span id="L391"><span class="lineNum">     391</span>              :   }</span>
<span id="L392"><span class="lineNum">     392</span>              : </span>
<span id="L393"><span class="lineNum">     393</span> <span class="tlaUNC">           0 :   Widget _buildCTA() {</span></span>
<span id="L394"><span class="lineNum">     394</span> <span class="tlaUNC">           0 :     return BlocBuilder&lt;DOPNativeESignReviewCubit, DOPNativeESignReviewState&gt;(</span></span>
<span id="L395"><span class="lineNum">     395</span> <span class="tlaUNC">           0 :       buildWhen: (DOPNativeESignReviewState previous, DOPNativeESignReviewState current) {</span></span>
<span id="L396"><span class="lineNum">     396</span> <span class="tlaUNC">           0 :         return current is DOPNativeESignReviewPreviewChanged;</span></span>
<span id="L397"><span class="lineNum">     397</span>              :       },</span>
<span id="L398"><span class="lineNum">     398</span> <span class="tlaUNC">           0 :       builder: (BuildContext context, DOPNativeESignReviewState state) {</span></span>
<span id="L399"><span class="lineNum">     399</span>              :         bool enable = false;</span>
<span id="L400"><span class="lineNum">     400</span>              : </span>
<span id="L401"><span class="lineNum">     401</span> <span class="tlaUNC">           0 :         if (state is DOPNativeESignReviewPreviewChanged) {</span></span>
<span id="L402"><span class="lineNum">     402</span> <span class="tlaUNC">           0 :           enable = state.canPreview;</span></span>
<span id="L403"><span class="lineNum">     403</span>              :         }</span>
<span id="L404"><span class="lineNum">     404</span>              : </span>
<span id="L405"><span class="lineNum">     405</span> <span class="tlaUNC">           0 :         return Padding(</span></span>
<span id="L406"><span class="lineNum">     406</span> <span class="tlaUNC">           0 :           padding: EdgeInsets.only(</span></span>
<span id="L407"><span class="lineNum">     407</span>              :             top: 20,</span>
<span id="L408"><span class="lineNum">     408</span> <span class="tlaUNC">           0 :             bottom: dopUtilFunction.getPaddingBottom(context),</span></span>
<span id="L409"><span class="lineNum">     409</span>              :           ),</span>
<span id="L410"><span class="lineNum">     410</span> <span class="tlaUNC">           0 :           child: CommonButton(</span></span>
<span id="L411"><span class="lineNum">     411</span> <span class="tlaUNC">           0 :             onPressed: enable ? _eSignReviewCubit.submitESignForm : null,</span></span>
<span id="L412"><span class="lineNum">     412</span>              :             isWrapContent: false,</span>
<span id="L413"><span class="lineNum">     413</span> <span class="tlaUNC">           0 :             style: dopNativeButtonStyles.primary(ButtonSize.medium),</span></span>
<span id="L414"><span class="lineNum">     414</span>              :             child: const Text(DOPNativeStrings.dopNativeSignESign),</span>
<span id="L415"><span class="lineNum">     415</span>              :           ),</span>
<span id="L416"><span class="lineNum">     416</span>              :         );</span>
<span id="L417"><span class="lineNum">     417</span>              :       },</span>
<span id="L418"><span class="lineNum">     418</span>              :     );</span>
<span id="L419"><span class="lineNum">     419</span>              :   }</span>
<span id="L420"><span class="lineNum">     420</span>              : </span>
<span id="L421"><span class="lineNum">     421</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L422"><span class="lineNum">     422</span>              :   void dispose() {</span>
<span id="L423"><span class="lineNum">     423</span> <span class="tlaUNC">           0 :     _eSignReviewCubit.cancelPollingESignState();</span></span>
<span id="L424"><span class="lineNum">     424</span> <span class="tlaUNC">           0 :     super.dispose();</span></span>
<span id="L425"><span class="lineNum">     425</span>              :   }</span>
<span id="L426"><span class="lineNum">     426</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
