<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/dop_native/features/additional_form/widgets/address_additional_info/dop_native_address_additional_info_widget.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/dop_native/features/additional_form/widgets/address_additional_info">lib/feature/dop_native/features/additional_form/widgets/address_additional_info</a> - dop_native_address_additional_info_widget.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">273</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:collection/collection.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter/material.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/common_package/common_package.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/util/cancelable_task_controller.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../../../../../data/repository/dop_native_repo/dop_native_repo.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../../../../../data/response/dop_native/dop_native_contact_info_entity.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../../../../data/response/dop_native/dop_native_metadata_item_entity.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../../../../prepare_for_app_initiation.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../../../../resources/resources.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../../resources/dop_native_images.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../../resources/dop_native_resources.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../../../resources/dop_native_ui_strings.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../../../util/metadata/dop_native_metadata_utils_impl.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../../../util/validation/cubit/dop_native_validation_utils_cubit.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../../../widgets/radio_button/dop_native_labeled_radio_box_widget.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../../../../widgets/text_field/dop_native_text_field_widget.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../../../ekyc_ui_only/id_verification_confirm/dialogs/address_dialog/dop_native_address_dialog.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../../models/additional_form_data_model.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../dop_native_additional_form_controller.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import 'cubit/dop_native_address_additional_info_cubit.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import 'cubit/dop_native_search_company_cubit.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import 'widgets/dop_native_search_company_listview_widget.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import 'widgets/dop_native_search_company_text_field_widget.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span>              : class DOPNativeAddressAdditionalInfoWidget extends StatefulWidget {</span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :   const DOPNativeAddressAdditionalInfoWidget({</span></span>
<span id="L29"><span class="lineNum">      29</span>              :     required this.controller,</span>
<span id="L30"><span class="lineNum">      30</span>              :     required this.model,</span>
<span id="L31"><span class="lineNum">      31</span>              :     super.key,</span>
<span id="L32"><span class="lineNum">      32</span>              :   });</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span>              :   final DOPNativeAdditionalFormDataModel model;</span>
<span id="L35"><span class="lineNum">      35</span>              :   final DOPNativeAdditionalFormController controller;</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L38"><span class="lineNum">      38</span>              :   State&lt;DOPNativeAddressAdditionalInfoWidget&gt; createState() =&gt;</span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :       _DOPNativeAddressAdditionalInfoWidgetState();</span></span>
<span id="L40"><span class="lineNum">      40</span>              : }</span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span>              : class _DOPNativeAddressAdditionalInfoWidgetState</span>
<span id="L43"><span class="lineNum">      43</span>              :     extends State&lt;DOPNativeAddressAdditionalInfoWidget&gt; {</span>
<span id="L44"><span class="lineNum">      44</span>              :   final DOPNativeAddressAdditionalInfoCubit _cubit = DOPNativeAddressAdditionalInfoCubit(</span>
<span id="L45"><span class="lineNum">      45</span>              :     dopNativeRepo: getIt.get&lt;DOPNativeRepo&gt;(),</span>
<span id="L46"><span class="lineNum">      46</span>              :     metadataUtils: DOPNativeMetadataUtilsImpl(</span>
<span id="L47"><span class="lineNum">      47</span>              :       dopNativeRepo: getIt.get&lt;DOPNativeRepo&gt;(),</span>
<span id="L48"><span class="lineNum">      48</span>              :     ),</span>
<span id="L49"><span class="lineNum">      49</span>              :     appState: getIt.get&lt;AppState&gt;(),</span>
<span id="L50"><span class="lineNum">      50</span>              :   );</span>
<span id="L51"><span class="lineNum">      51</span>              :   final DOPNativeValidationUtilsCubit _validationUtilsCubit = DOPNativeValidationUtilsCubit();</span>
<span id="L52"><span class="lineNum">      52</span>              :   final DOPNativeSearchCompanyCubit _searchCompanyCubit = DOPNativeSearchCompanyCubit(</span>
<span id="L53"><span class="lineNum">      53</span>              :     metadataUtils: DOPNativeMetadataUtilsImpl(dopNativeRepo: getIt.get&lt;DOPNativeRepo&gt;()),</span>
<span id="L54"><span class="lineNum">      54</span>              :     searchCompanyNameTaskController: CancelableTaskController(),</span>
<span id="L55"><span class="lineNum">      55</span>              :   );</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span>              :   final TextEditingController _inputCurrentAddressController = TextEditingController();</span>
<span id="L58"><span class="lineNum">      58</span>              :   final TextEditingController _inputCompanyNameController = TextEditingController();</span>
<span id="L59"><span class="lineNum">      59</span>              :   final TextEditingController _companyPermanentResidenceTextController = TextEditingController();</span>
<span id="L60"><span class="lineNum">      60</span>              :   final TextEditingController _inputCompanyStreetController = TextEditingController();</span>
<span id="L61"><span class="lineNum">      61</span>              :   final ValueNotifier&lt;DOPNativeMetadataItemEntity?&gt; _selectedCardDeliveryType =</span>
<span id="L62"><span class="lineNum">      62</span>              :       ValueNotifier&lt;DOPNativeMetadataItemEntity?&gt;(null);</span>
<span id="L63"><span class="lineNum">      63</span>              :   final FocusNode _focusSearchCompanyName = FocusNode();</span>
<span id="L64"><span class="lineNum">      64</span>              : </span>
<span id="L65"><span class="lineNum">      65</span>              :   String _residenceAddressOfCompany = '';</span>
<span id="L66"><span class="lineNum">      66</span>              :   final List&lt;DOPNativeMetadataItemEntity&gt; _companies = &lt;DOPNativeMetadataItemEntity&gt;[];</span>
<span id="L67"><span class="lineNum">      67</span>              : </span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L69"><span class="lineNum">      69</span>              :   void initState() {</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :     super.initState();</span></span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :     _focusSearchCompanyName.addListener(_listenFocusCompanySearch);</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :     WidgetsBinding.instance.addPostFrameCallback((_) {</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :       _initializeForm();</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :       _cubit.getCardDeliveryType();</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :       _getCurrentResidenceAddressName();</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :       widget.controller.onSubmitted = _submit;</span></span>
<span id="L78"><span class="lineNum">      78</span>              :     });</span>
<span id="L79"><span class="lineNum">      79</span>              :   }</span>
<span id="L80"><span class="lineNum">      80</span>              : </span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :   void _getCurrentResidenceAddressName() {</span></span>
<span id="L82"><span class="lineNum">      82</span>              :     final DOPNativeContactInfoEntity? contactInfo =</span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :         widget.model.formDataEntity?.formData?.contactInfo;</span></span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :     _cubit.getCurrentResidenceAddressName(</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :       provinceCode: contactInfo?.curAddressProvinceId,</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :       districtCode: contactInfo?.curAddressDistId,</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :       wardCode: contactInfo?.curAddressWardId,</span></span>
<span id="L89"><span class="lineNum">      89</span>              :     );</span>
<span id="L90"><span class="lineNum">      90</span>              :   }</span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L93"><span class="lineNum">      93</span>              :   void dispose() {</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :     _focusSearchCompanyName.removeListener(_listenFocusCompanySearch);</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :     _inputCurrentAddressController.dispose();</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :     super.dispose();</span></span>
<span id="L97"><span class="lineNum">      97</span>              :   }</span>
<span id="L98"><span class="lineNum">      98</span>              : </span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :   void _listenFocusCompanySearch() {</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :     commonLog('The user focus search company: ${_focusSearchCompanyName.hasFocus}');</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :     if (!_focusSearchCompanyName.hasFocus) {</span></span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :       _searchCompanyCubit.hideCompanySuggestions();</span></span>
<span id="L103"><span class="lineNum">     103</span>              :       return;</span>
<span id="L104"><span class="lineNum">     104</span>              :     }</span>
<span id="L105"><span class="lineNum">     105</span>              : </span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :     if (_companies.isNotEmpty) {</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :       _searchCompanyCubit.showCompanySuggestions();</span></span>
<span id="L108"><span class="lineNum">     108</span>              :     }</span>
<span id="L109"><span class="lineNum">     109</span>              :   }</span>
<span id="L110"><span class="lineNum">     110</span>              : </span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L112"><span class="lineNum">     112</span>              :   Widget build(BuildContext context) {</span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :     return MultiBlocProvider(</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :       providers: &lt;BlocProvider&lt;dynamic&gt;&gt;[</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :         BlocProvider&lt;DOPNativeAddressAdditionalInfoCubit&gt;(</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :           create: (_) =&gt; _cubit,</span></span>
<span id="L117"><span class="lineNum">     117</span>              :         ),</span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :         BlocProvider&lt;DOPNativeValidationUtilsCubit&gt;(</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :           create: (_) =&gt; _validationUtilsCubit,</span></span>
<span id="L120"><span class="lineNum">     120</span>              :         ),</span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :         BlocProvider&lt;DOPNativeSearchCompanyCubit&gt;(</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :           create: (_) =&gt; _searchCompanyCubit,</span></span>
<span id="L123"><span class="lineNum">     123</span>              :         ),</span>
<span id="L124"><span class="lineNum">     124</span>              :       ],</span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :       child: BlocListener&lt;DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState&gt;(</span></span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :         listener: (_, DOPNativeAddressAdditionalInfoState state) {</span></span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :           _handleStateChanged(state);</span></span>
<span id="L128"><span class="lineNum">     128</span>              :         },</span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :         child: Column(</span></span>
<span id="L130"><span class="lineNum">     130</span>              :           crossAxisAlignment: CrossAxisAlignment.start,</span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :           children: &lt;Widget&gt;[</span></span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaUNC">           0 :             Text(</span></span>
<span id="L133"><span class="lineNum">     133</span>              :               DOPNativeStrings.dopNativeCurrentAddressTitle,</span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :               style: dopNativeTextStyles.h500(),</span></span>
<span id="L135"><span class="lineNum">     135</span>              :             ),</span>
<span id="L136"><span class="lineNum">     136</span>              :             const SizedBox(height: 16),</span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaUNC">           0 :             _buildCurrentAddressWidget(),</span></span>
<span id="L138"><span class="lineNum">     138</span>              :             const SizedBox(height: 40),</span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaUNC">           0 :             _buildCompanyAndDeliveryTypeWidget(),</span></span>
<span id="L140"><span class="lineNum">     140</span>              :           ],</span>
<span id="L141"><span class="lineNum">     141</span>              :         ),</span>
<span id="L142"><span class="lineNum">     142</span>              :       ),</span>
<span id="L143"><span class="lineNum">     143</span>              :     );</span>
<span id="L144"><span class="lineNum">     144</span>              :   }</span>
<span id="L145"><span class="lineNum">     145</span>              : </span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaUNC">           0 :   Widget _buildCompanyAndDeliveryTypeWidget() {</span></span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :     return Column(</span></span>
<span id="L148"><span class="lineNum">     148</span>              :       crossAxisAlignment: CrossAxisAlignment.start,</span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :       children: &lt;Widget&gt;[</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :         Text(</span></span>
<span id="L151"><span class="lineNum">     151</span>              :           DOPNativeStrings.dopNativeCompanyAddressTitle,</span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :           style: dopNativeTextStyles.h500(),</span></span>
<span id="L153"><span class="lineNum">     153</span>              :         ),</span>
<span id="L154"><span class="lineNum">     154</span>              :         const SizedBox(height: 16),</span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaUNC">           0 :         _buildCompanyNameWidget(),</span></span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaUNC">           0 :         _buildContentOfCompanyAndDeliveryTypeWidget(),</span></span>
<span id="L157"><span class="lineNum">     157</span>              :       ],</span>
<span id="L158"><span class="lineNum">     158</span>              :     );</span>
<span id="L159"><span class="lineNum">     159</span>              :   }</span>
<span id="L160"><span class="lineNum">     160</span>              : </span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaUNC">           0 :   Widget _buildContentOfCompanyAndDeliveryTypeWidget() {</span></span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaUNC">           0 :     return Stack(</span></span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaUNC">           0 :       children: &lt;Widget&gt;[</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :         Column(</span></span>
<span id="L165"><span class="lineNum">     165</span>              :           crossAxisAlignment: CrossAxisAlignment.start,</span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaUNC">           0 :           children: &lt;Widget&gt;[</span></span>
<span id="L167"><span class="lineNum">     167</span>              :             const SizedBox(height: 16),</span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaUNC">           0 :             _buildInputCompanyPermanentResidenceAddress(),</span></span>
<span id="L169"><span class="lineNum">     169</span>              :             const SizedBox(height: 16),</span>
<span id="L170"><span class="lineNum">     170</span> <span class="tlaUNC">           0 :             _buildInputCompanyStreetTextField(),</span></span>
<span id="L171"><span class="lineNum">     171</span>              :             const SizedBox(height: 40),</span>
<span id="L172"><span class="lineNum">     172</span> <span class="tlaUNC">           0 :             _cardDeliveryTypeWidget(),</span></span>
<span id="L173"><span class="lineNum">     173</span>              :           ],</span>
<span id="L174"><span class="lineNum">     174</span>              :         ),</span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :         _buildListCompanySearch(),</span></span>
<span id="L176"><span class="lineNum">     176</span>              :       ],</span>
<span id="L177"><span class="lineNum">     177</span>              :     );</span>
<span id="L178"><span class="lineNum">     178</span>              :   }</span>
<span id="L179"><span class="lineNum">     179</span>              : </span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaUNC">           0 :   Widget _buildInputCompanyStreetTextField() {</span></span>
<span id="L181"><span class="lineNum">     181</span> <span class="tlaUNC">           0 :     return BlocBuilder&lt;DOPNativeValidationUtilsCubit, DOPNativeValidationUtilsState&gt;(</span></span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :         buildWhen: (_, DOPNativeValidationUtilsState state) {</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaUNC">           0 :       return state is DOPNativeCompanyAddressValid || state is DOPNativeCompanyAddressFailed;</span></span>
<span id="L184"><span class="lineNum">     184</span> <span class="tlaUNC">           0 :     }, builder: (_, DOPNativeValidationUtilsState state) {</span></span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :       return DOPNativeTextField(</span></span>
<span id="L186"><span class="lineNum">     186</span> <span class="tlaUNC">           0 :         controller: _inputCompanyStreetController,</span></span>
<span id="L187"><span class="lineNum">     187</span>              :         label: DOPNativeStrings.dopNativeCompanyStreetOfAddressInputTextLabel,</span>
<span id="L188"><span class="lineNum">     188</span>              :         hintText: DOPNativeStrings.dopNativeCompanyStreetOfAddressInputTextHint,</span>
<span id="L189"><span class="lineNum">     189</span> <span class="tlaUNC">           0 :         errorText: state is DOPNativeCompanyAddressFailed ? state.errorMsg : null,</span></span>
<span id="L190"><span class="lineNum">     190</span> <span class="tlaUNC">           0 :         onChanged: (String text) {</span></span>
<span id="L191"><span class="lineNum">     191</span> <span class="tlaUNC">           0 :           _validationUtilsCubit.validateDOPNativeCompanyAddress(text);</span></span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaUNC">           0 :           _validateFormToEnableConfirmButton();</span></span>
<span id="L193"><span class="lineNum">     193</span>              :         },</span>
<span id="L194"><span class="lineNum">     194</span>              :       );</span>
<span id="L195"><span class="lineNum">     195</span>              :     });</span>
<span id="L196"><span class="lineNum">     196</span>              :   }</span>
<span id="L197"><span class="lineNum">     197</span>              : </span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaUNC">           0 :   Widget _buildInputCompanyPermanentResidenceAddress() {</span></span>
<span id="L199"><span class="lineNum">     199</span> <span class="tlaUNC">           0 :     return GestureDetector(</span></span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaUNC">           0 :       onTap: () {</span></span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaUNC">           0 :         _openAddressDialog();</span></span>
<span id="L202"><span class="lineNum">     202</span>              :       },</span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaUNC">           0 :       child: BlocBuilder&lt;DOPNativeValidationUtilsCubit, DOPNativeValidationUtilsState&gt;(</span></span>
<span id="L204"><span class="lineNum">     204</span> <span class="tlaUNC">           0 :         buildWhen: (_, DOPNativeValidationUtilsState current) {</span></span>
<span id="L205"><span class="lineNum">     205</span> <span class="tlaUNC">           0 :           return current is DOPNativeResidenceAddressValid ||</span></span>
<span id="L206"><span class="lineNum">     206</span> <span class="tlaUNC">           0 :               current is DOPNativeResidenceAddressFailed;</span></span>
<span id="L207"><span class="lineNum">     207</span>              :         },</span>
<span id="L208"><span class="lineNum">     208</span> <span class="tlaUNC">           0 :         builder: (BuildContext context, DOPNativeValidationUtilsState state) {</span></span>
<span id="L209"><span class="lineNum">     209</span>              :           String? errorMsg;</span>
<span id="L210"><span class="lineNum">     210</span> <span class="tlaUNC">           0 :           if (state is DOPNativeResidenceAddressFailed) {</span></span>
<span id="L211"><span class="lineNum">     211</span> <span class="tlaUNC">           0 :             errorMsg = state.errorMsg;</span></span>
<span id="L212"><span class="lineNum">     212</span>              :           }</span>
<span id="L213"><span class="lineNum">     213</span>              : </span>
<span id="L214"><span class="lineNum">     214</span> <span class="tlaUNC">           0 :           return DOPNativeTextField(</span></span>
<span id="L215"><span class="lineNum">     215</span> <span class="tlaUNC">           0 :             backgroundColor: dopNativeColors.background,</span></span>
<span id="L216"><span class="lineNum">     216</span> <span class="tlaUNC">           0 :             controller: _companyPermanentResidenceTextController,</span></span>
<span id="L217"><span class="lineNum">     217</span>              :             isEnabled: false,</span>
<span id="L218"><span class="lineNum">     218</span>              :             label: DOPNativeStrings.dopNativeCompanyAddressInputTextLabel,</span>
<span id="L219"><span class="lineNum">     219</span>              :             hintText: DOPNativeStrings.dopNativeCompanyAddressInputTextHint,</span>
<span id="L220"><span class="lineNum">     220</span> <span class="tlaUNC">           0 :             suffixIcon: Padding(</span></span>
<span id="L221"><span class="lineNum">     221</span>              :               padding: const EdgeInsets.symmetric(horizontal: 16),</span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaUNC">           0 :               child: evoImageProvider.asset(</span></span>
<span id="L223"><span class="lineNum">     223</span>              :                 DOPNativeImages.icArrowDown,</span>
<span id="L224"><span class="lineNum">     224</span>              :                 width: 20,</span>
<span id="L225"><span class="lineNum">     225</span>              :                 height: 20,</span>
<span id="L226"><span class="lineNum">     226</span>              :               ),</span>
<span id="L227"><span class="lineNum">     227</span>              :             ),</span>
<span id="L228"><span class="lineNum">     228</span>              :             errorText: errorMsg,</span>
<span id="L229"><span class="lineNum">     229</span>              :           );</span>
<span id="L230"><span class="lineNum">     230</span>              :         },</span>
<span id="L231"><span class="lineNum">     231</span>              :       ),</span>
<span id="L232"><span class="lineNum">     232</span>              :     );</span>
<span id="L233"><span class="lineNum">     233</span>              :   }</span>
<span id="L234"><span class="lineNum">     234</span>              : </span>
<span id="L235"><span class="lineNum">     235</span> <span class="tlaUNC">           0 :   Widget _buildCompanyNameWidget() {</span></span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaUNC">           0 :     return BlocBuilder&lt;DOPNativeValidationUtilsCubit, DOPNativeValidationUtilsState&gt;(</span></span>
<span id="L237"><span class="lineNum">     237</span> <span class="tlaUNC">           0 :         buildWhen: (_, DOPNativeValidationUtilsState state) {</span></span>
<span id="L238"><span class="lineNum">     238</span> <span class="tlaUNC">           0 :       return state is DOPNativeCompanyNameValid || state is DOPNativeCompanyNameFailed;</span></span>
<span id="L239"><span class="lineNum">     239</span> <span class="tlaUNC">           0 :     }, builder: (_, DOPNativeValidationUtilsState state) {</span></span>
<span id="L240"><span class="lineNum">     240</span> <span class="tlaUNC">           0 :       return DOPNativeSearchCompanyTextFieldWidget(</span></span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaUNC">           0 :         focusNode: _focusSearchCompanyName,</span></span>
<span id="L242"><span class="lineNum">     242</span> <span class="tlaUNC">           0 :         controller: _inputCompanyNameController,</span></span>
<span id="L243"><span class="lineNum">     243</span> <span class="tlaUNC">           0 :         errorText: state is DOPNativeCompanyNameFailed ? state.errorMsg : null,</span></span>
<span id="L244"><span class="lineNum">     244</span> <span class="tlaUNC">           0 :         onChanged: (String text) {</span></span>
<span id="L245"><span class="lineNum">     245</span> <span class="tlaUNC">           0 :           _validationUtilsCubit.validateCompanyName(companyName: text);</span></span>
<span id="L246"><span class="lineNum">     246</span> <span class="tlaUNC">           0 :           _validateFormToEnableConfirmButton();</span></span>
<span id="L247"><span class="lineNum">     247</span>              :         },</span>
<span id="L248"><span class="lineNum">     248</span> <span class="tlaUNC">           0 :         onSearched: (String value) {</span></span>
<span id="L249"><span class="lineNum">     249</span> <span class="tlaUNC">           0 :           _searchCompanyCubit.handleSearchCompanySuggestions(searchPrefix: value);</span></span>
<span id="L250"><span class="lineNum">     250</span>              :         },</span>
<span id="L251"><span class="lineNum">     251</span>              :       );</span>
<span id="L252"><span class="lineNum">     252</span>              :     });</span>
<span id="L253"><span class="lineNum">     253</span>              :   }</span>
<span id="L254"><span class="lineNum">     254</span>              : </span>
<span id="L255"><span class="lineNum">     255</span> <span class="tlaUNC">           0 :   Widget _buildListCompanySearch() {</span></span>
<span id="L256"><span class="lineNum">     256</span> <span class="tlaUNC">           0 :     return BlocBuilder&lt;DOPNativeSearchCompanyCubit, DOPNativeSearchCompanyState&gt;(</span></span>
<span id="L257"><span class="lineNum">     257</span> <span class="tlaUNC">           0 :       buildWhen: (_, DOPNativeSearchCompanyState currentState) {</span></span>
<span id="L258"><span class="lineNum">     258</span> <span class="tlaUNC">           0 :         return currentState is DOPNativeSearchCompanySucceed ||</span></span>
<span id="L259"><span class="lineNum">     259</span> <span class="tlaUNC">           0 :             currentState is DOPNativeSearchCompanyFailed ||</span></span>
<span id="L260"><span class="lineNum">     260</span> <span class="tlaUNC">           0 :             currentState is DOPNativeSearchCompanyShown ||</span></span>
<span id="L261"><span class="lineNum">     261</span> <span class="tlaUNC">           0 :             currentState is DOPNativeSearchCompanyHidden;</span></span>
<span id="L262"><span class="lineNum">     262</span>              :       },</span>
<span id="L263"><span class="lineNum">     263</span> <span class="tlaUNC">           0 :       builder: (_, DOPNativeSearchCompanyState state) {</span></span>
<span id="L264"><span class="lineNum">     264</span> <span class="tlaUNC">           0 :         if (state is DOPNativeSearchCompanyHidden) {</span></span>
<span id="L265"><span class="lineNum">     265</span>              :           return const SizedBox.shrink();</span>
<span id="L266"><span class="lineNum">     266</span>              :         }</span>
<span id="L267"><span class="lineNum">     267</span>              : </span>
<span id="L268"><span class="lineNum">     268</span> <span class="tlaUNC">           0 :         if (state is DOPNativeSearchCompanySucceed) {</span></span>
<span id="L269"><span class="lineNum">     269</span> <span class="tlaUNC">           0 :           _companies.clear();</span></span>
<span id="L270"><span class="lineNum">     270</span> <span class="tlaUNC">           0 :           _companies.addAll(state.companies.items ?? &lt;DOPNativeMetadataItemEntity&gt;[]);</span></span>
<span id="L271"><span class="lineNum">     271</span>              :         }</span>
<span id="L272"><span class="lineNum">     272</span>              : </span>
<span id="L273"><span class="lineNum">     273</span> <span class="tlaUNC">           0 :         return DOPNativeSearchCompanyListView(</span></span>
<span id="L274"><span class="lineNum">     274</span> <span class="tlaUNC">           0 :           companies: _companies,</span></span>
<span id="L275"><span class="lineNum">     275</span> <span class="tlaUNC">           0 :           onTap: (DOPNativeMetadataItemEntity company) {</span></span>
<span id="L276"><span class="lineNum">     276</span> <span class="tlaUNC">           0 :             _inputCompanyNameController.text = company.name ?? '';</span></span>
<span id="L277"><span class="lineNum">     277</span> <span class="tlaUNC">           0 :             _searchCompanyCubit.hideCompanySuggestions();</span></span>
<span id="L278"><span class="lineNum">     278</span> <span class="tlaUNC">           0 :             _focusSearchCompanyName.unfocus();</span></span>
<span id="L279"><span class="lineNum">     279</span>              :           },</span>
<span id="L280"><span class="lineNum">     280</span>              :         );</span>
<span id="L281"><span class="lineNum">     281</span>              :       },</span>
<span id="L282"><span class="lineNum">     282</span>              :     );</span>
<span id="L283"><span class="lineNum">     283</span>              :   }</span>
<span id="L284"><span class="lineNum">     284</span>              : </span>
<span id="L285"><span class="lineNum">     285</span> <span class="tlaUNC">           0 :   Widget _buildCurrentAddressWidget() {</span></span>
<span id="L286"><span class="lineNum">     286</span> <span class="tlaUNC">           0 :     return Column(</span></span>
<span id="L287"><span class="lineNum">     287</span>              :       crossAxisAlignment: CrossAxisAlignment.start,</span>
<span id="L288"><span class="lineNum">     288</span> <span class="tlaUNC">           0 :       children: &lt;Widget&gt;[</span></span>
<span id="L289"><span class="lineNum">     289</span> <span class="tlaUNC">           0 :         _buildCurrentStreetTextField(),</span></span>
<span id="L290"><span class="lineNum">     290</span>              :         const SizedBox(height: 8),</span>
<span id="L291"><span class="lineNum">     291</span> <span class="tlaUNC">           0 :         _getCurrentAddressWidget(),</span></span>
<span id="L292"><span class="lineNum">     292</span>              :       ],</span>
<span id="L293"><span class="lineNum">     293</span>              :     );</span>
<span id="L294"><span class="lineNum">     294</span>              :   }</span>
<span id="L295"><span class="lineNum">     295</span>              : </span>
<span id="L296"><span class="lineNum">     296</span> <span class="tlaUNC">           0 :   Widget _buildCurrentStreetTextField() {</span></span>
<span id="L297"><span class="lineNum">     297</span> <span class="tlaUNC">           0 :     return BlocBuilder&lt;DOPNativeValidationUtilsCubit, DOPNativeValidationUtilsState&gt;(</span></span>
<span id="L298"><span class="lineNum">     298</span> <span class="tlaUNC">           0 :         buildWhen: (_, DOPNativeValidationUtilsState state) {</span></span>
<span id="L299"><span class="lineNum">     299</span> <span class="tlaUNC">           0 :       return state is DOPNativeAddressValid || state is DOPNativeAddressFailed;</span></span>
<span id="L300"><span class="lineNum">     300</span> <span class="tlaUNC">           0 :     }, builder: (_, DOPNativeValidationUtilsState state) {</span></span>
<span id="L301"><span class="lineNum">     301</span> <span class="tlaUNC">           0 :       return DOPNativeTextField(</span></span>
<span id="L302"><span class="lineNum">     302</span> <span class="tlaUNC">           0 :         controller: _inputCurrentAddressController,</span></span>
<span id="L303"><span class="lineNum">     303</span>              :         hintText: DOPNativeStrings.dopNativeStreetOfCurrentAddressInputTextHint,</span>
<span id="L304"><span class="lineNum">     304</span> <span class="tlaUNC">           0 :         errorText: state is DOPNativeAddressFailed ? state.errorMsg : null,</span></span>
<span id="L305"><span class="lineNum">     305</span> <span class="tlaUNC">           0 :         onChanged: (String text) {</span></span>
<span id="L306"><span class="lineNum">     306</span> <span class="tlaUNC">           0 :           _validationUtilsCubit.validateDOPNativeAddress(text);</span></span>
<span id="L307"><span class="lineNum">     307</span> <span class="tlaUNC">           0 :           _validateFormToEnableConfirmButton();</span></span>
<span id="L308"><span class="lineNum">     308</span>              :         },</span>
<span id="L309"><span class="lineNum">     309</span>              :       );</span>
<span id="L310"><span class="lineNum">     310</span>              :     });</span>
<span id="L311"><span class="lineNum">     311</span>              :   }</span>
<span id="L312"><span class="lineNum">     312</span>              : </span>
<span id="L313"><span class="lineNum">     313</span> <span class="tlaUNC">           0 :   Widget _getCurrentAddressWidget() {</span></span>
<span id="L314"><span class="lineNum">     314</span> <span class="tlaUNC">           0 :     return BlocBuilder&lt;DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState&gt;(</span></span>
<span id="L315"><span class="lineNum">     315</span> <span class="tlaUNC">           0 :         buildWhen: (_, DOPNativeAddressAdditionalInfoState state) {</span></span>
<span id="L316"><span class="lineNum">     316</span> <span class="tlaUNC">           0 :       return state is DOPNativeGetResidenceAddressNameSucceed;</span></span>
<span id="L317"><span class="lineNum">     317</span> <span class="tlaUNC">           0 :     }, builder: (_, DOPNativeAddressAdditionalInfoState state) {</span></span>
<span id="L318"><span class="lineNum">     318</span>              :       String residenceAddressName = '';</span>
<span id="L319"><span class="lineNum">     319</span> <span class="tlaUNC">           0 :       if (state is DOPNativeGetResidenceAddressNameSucceed) {</span></span>
<span id="L320"><span class="lineNum">     320</span> <span class="tlaUNC">           0 :         residenceAddressName = state.residenceAddressName;</span></span>
<span id="L321"><span class="lineNum">     321</span>              :       }</span>
<span id="L322"><span class="lineNum">     322</span>              : </span>
<span id="L323"><span class="lineNum">     323</span> <span class="tlaUNC">           0 :       return residenceAddressName.isNotEmpty</span></span>
<span id="L324"><span class="lineNum">     324</span> <span class="tlaUNC">           0 :           ? Text(</span></span>
<span id="L325"><span class="lineNum">     325</span>              :               residenceAddressName,</span>
<span id="L326"><span class="lineNum">     326</span> <span class="tlaUNC">           0 :               style: dopNativeTextStyles.bodySmall(color: dopNativeColors.textActive),</span></span>
<span id="L327"><span class="lineNum">     327</span>              :             )</span>
<span id="L328"><span class="lineNum">     328</span>              :           : const SizedBox.shrink();</span>
<span id="L329"><span class="lineNum">     329</span>              :     });</span>
<span id="L330"><span class="lineNum">     330</span>              :   }</span>
<span id="L331"><span class="lineNum">     331</span>              : </span>
<span id="L332"><span class="lineNum">     332</span> <span class="tlaUNC">           0 :   Widget _cardDeliveryTypeWidget() {</span></span>
<span id="L333"><span class="lineNum">     333</span> <span class="tlaUNC">           0 :     return BlocBuilder&lt;DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState&gt;(</span></span>
<span id="L334"><span class="lineNum">     334</span> <span class="tlaUNC">           0 :       buildWhen: (_, DOPNativeAddressAdditionalInfoState current) {</span></span>
<span id="L335"><span class="lineNum">     335</span> <span class="tlaUNC">           0 :         return current is DOPNativeGetCardDeliveryTypeSucceed ||</span></span>
<span id="L336"><span class="lineNum">     336</span> <span class="tlaUNC">           0 :             current is DOPNativeGetCardDeliveryTypeFailed;</span></span>
<span id="L337"><span class="lineNum">     337</span>              :       },</span>
<span id="L338"><span class="lineNum">     338</span> <span class="tlaUNC">           0 :       builder: (_, DOPNativeAddressAdditionalInfoState state) {</span></span>
<span id="L339"><span class="lineNum">     339</span> <span class="tlaUNC">           0 :         final bool isShownCardDeliveryType = state is! DOPNativeGetCardDeliveryTypeFailed &amp;&amp;</span></span>
<span id="L340"><span class="lineNum">     340</span> <span class="tlaUNC">           0 :             (state is DOPNativeGetCardDeliveryTypeSucceed &amp;&amp;</span></span>
<span id="L341"><span class="lineNum">     341</span> <span class="tlaUNC">           0 :                 state.cardDeliveryTypes.metadata?.isNotEmpty == true);</span></span>
<span id="L342"><span class="lineNum">     342</span>              :         if (!isShownCardDeliveryType) {</span>
<span id="L343"><span class="lineNum">     343</span>              :           return const SizedBox.shrink();</span>
<span id="L344"><span class="lineNum">     344</span>              :         }</span>
<span id="L345"><span class="lineNum">     345</span>              : </span>
<span id="L346"><span class="lineNum">     346</span>              :         final List&lt;DOPNativeMetadataItemEntity&gt; cardDeliveryTypes =</span>
<span id="L347"><span class="lineNum">     347</span> <span class="tlaUNC">           0 :             state.cardDeliveryTypes.metadata!;</span></span>
<span id="L348"><span class="lineNum">     348</span>              : </span>
<span id="L349"><span class="lineNum">     349</span> <span class="tlaUNC">           0 :         return Column(</span></span>
<span id="L350"><span class="lineNum">     350</span>              :           crossAxisAlignment: CrossAxisAlignment.start,</span>
<span id="L351"><span class="lineNum">     351</span> <span class="tlaUNC">           0 :           children: &lt;Widget&gt;[</span></span>
<span id="L352"><span class="lineNum">     352</span> <span class="tlaUNC">           0 :             Text(</span></span>
<span id="L353"><span class="lineNum">     353</span>              :               DOPNativeStrings.dopNativeCardDeliveryTypeTitle,</span>
<span id="L354"><span class="lineNum">     354</span> <span class="tlaUNC">           0 :               style: dopNativeTextStyles.h500(),</span></span>
<span id="L355"><span class="lineNum">     355</span>              :             ),</span>
<span id="L356"><span class="lineNum">     356</span>              :             const SizedBox(height: 16),</span>
<span id="L357"><span class="lineNum">     357</span> <span class="tlaUNC">           0 :             Padding(</span></span>
<span id="L358"><span class="lineNum">     358</span>              :               padding: const EdgeInsets.symmetric(vertical: 8),</span>
<span id="L359"><span class="lineNum">     359</span> <span class="tlaUNC">           0 :               child: Row(</span></span>
<span id="L360"><span class="lineNum">     360</span> <span class="tlaUNC">           0 :                 children: _buildCardDeliveryTypeCheckbox(cardDeliveryTypes),</span></span>
<span id="L361"><span class="lineNum">     361</span>              :               ),</span>
<span id="L362"><span class="lineNum">     362</span>              :             ),</span>
<span id="L363"><span class="lineNum">     363</span>              :             const SizedBox(height: 20),</span>
<span id="L364"><span class="lineNum">     364</span>              :           ],</span>
<span id="L365"><span class="lineNum">     365</span>              :         );</span>
<span id="L366"><span class="lineNum">     366</span>              :       },</span>
<span id="L367"><span class="lineNum">     367</span>              :     );</span>
<span id="L368"><span class="lineNum">     368</span>              :   }</span>
<span id="L369"><span class="lineNum">     369</span>              : </span>
<span id="L370"><span class="lineNum">     370</span> <span class="tlaUNC">           0 :   List&lt;Widget&gt; _buildCardDeliveryTypeCheckbox(List&lt;DOPNativeMetadataItemEntity&gt; cardDeliveryType) {</span></span>
<span id="L371"><span class="lineNum">     371</span> <span class="tlaUNC">           0 :     final List&lt;Widget&gt; widgets = &lt;Widget&gt;[];</span></span>
<span id="L372"><span class="lineNum">     372</span> <span class="tlaUNC">           0 :     for (final DOPNativeMetadataItemEntity type in cardDeliveryType) {</span></span>
<span id="L373"><span class="lineNum">     373</span> <span class="tlaUNC">           0 :       widgets.add(Expanded(</span></span>
<span id="L374"><span class="lineNum">     374</span> <span class="tlaUNC">           0 :         child: DOPNativeLabeledRadioBoxWidget&lt;DOPNativeMetadataItemEntity&gt;(</span></span>
<span id="L375"><span class="lineNum">     375</span> <span class="tlaUNC">           0 :           label: type.name ?? '',</span></span>
<span id="L376"><span class="lineNum">     376</span>              :           value: type,</span>
<span id="L377"><span class="lineNum">     377</span> <span class="tlaUNC">           0 :           selectedValue: _selectedCardDeliveryType,</span></span>
<span id="L378"><span class="lineNum">     378</span> <span class="tlaUNC">           0 :           onChanged: _onSelectCardDeliveryType,</span></span>
<span id="L379"><span class="lineNum">     379</span>              :         ),</span>
<span id="L380"><span class="lineNum">     380</span>              :       ));</span>
<span id="L381"><span class="lineNum">     381</span>              :     }</span>
<span id="L382"><span class="lineNum">     382</span>              : </span>
<span id="L383"><span class="lineNum">     383</span>              :     return widgets;</span>
<span id="L384"><span class="lineNum">     384</span>              :   }</span>
<span id="L385"><span class="lineNum">     385</span>              : </span>
<span id="L386"><span class="lineNum">     386</span> <span class="tlaUNC">           0 :   void _onSelectCardDeliveryType(DOPNativeMetadataItemEntity cardDeliveryType) {</span></span>
<span id="L387"><span class="lineNum">     387</span> <span class="tlaUNC">           0 :     _selectedCardDeliveryType.value = cardDeliveryType;</span></span>
<span id="L388"><span class="lineNum">     388</span> <span class="tlaUNC">           0 :     _cubit.setContactInfoItem(pickupCardAddress: cardDeliveryType.code);</span></span>
<span id="L389"><span class="lineNum">     389</span> <span class="tlaUNC">           0 :     _validateFormToEnableConfirmButton();</span></span>
<span id="L390"><span class="lineNum">     390</span>              :   }</span>
<span id="L391"><span class="lineNum">     391</span>              : </span>
<span id="L392"><span class="lineNum">     392</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _openAddressDialog() async {</span></span>
<span id="L393"><span class="lineNum">     393</span> <span class="tlaUNC">           0 :     await DOPNativeAddressDialog.show(</span></span>
<span id="L394"><span class="lineNum">     394</span> <span class="tlaUNC">           0 :       addressData: ({</span></span>
<span id="L395"><span class="lineNum">     395</span>              :         DOPNativeMetadataItemEntity? province,</span>
<span id="L396"><span class="lineNum">     396</span>              :         DOPNativeMetadataItemEntity? district,</span>
<span id="L397"><span class="lineNum">     397</span>              :         DOPNativeMetadataItemEntity? ward,</span>
<span id="L398"><span class="lineNum">     398</span>              :         String? residenceAddressName,</span>
<span id="L399"><span class="lineNum">     399</span>              :       }) {</span>
<span id="L400"><span class="lineNum">     400</span> <span class="tlaUNC">           0 :         if (residenceAddressName == null || residenceAddressName.isEmpty) {</span></span>
<span id="L401"><span class="lineNum">     401</span>              :           return;</span>
<span id="L402"><span class="lineNum">     402</span>              :         }</span>
<span id="L403"><span class="lineNum">     403</span>              : </span>
<span id="L404"><span class="lineNum">     404</span> <span class="tlaUNC">           0 :         _cubit.saveCompanyAddress(</span></span>
<span id="L405"><span class="lineNum">     405</span>              :           province: province,</span>
<span id="L406"><span class="lineNum">     406</span>              :           district: district,</span>
<span id="L407"><span class="lineNum">     407</span>              :           ward: ward,</span>
<span id="L408"><span class="lineNum">     408</span>              :           residenceAddressName: residenceAddressName,</span>
<span id="L409"><span class="lineNum">     409</span>              :         );</span>
<span id="L410"><span class="lineNum">     410</span>              : </span>
<span id="L411"><span class="lineNum">     411</span> <span class="tlaUNC">           0 :         _companyPermanentResidenceTextController.text = residenceAddressName;</span></span>
<span id="L412"><span class="lineNum">     412</span>              :       },</span>
<span id="L413"><span class="lineNum">     413</span>              :     );</span>
<span id="L414"><span class="lineNum">     414</span>              : </span>
<span id="L415"><span class="lineNum">     415</span> <span class="tlaUNC">           0 :     _validateFormToEnableConfirmButton();</span></span>
<span id="L416"><span class="lineNum">     416</span>              :   }</span>
<span id="L417"><span class="lineNum">     417</span>              : </span>
<span id="L418"><span class="lineNum">     418</span> <span class="tlaUNC">           0 :   void _submit() {</span></span>
<span id="L419"><span class="lineNum">     419</span> <span class="tlaUNC">           0 :     FocusManager.instance.primaryFocus?.unfocus();</span></span>
<span id="L420"><span class="lineNum">     420</span> <span class="tlaUNC">           0 :     final bool isFormValid = _validateFormBeforeSave(</span></span>
<span id="L421"><span class="lineNum">     421</span> <span class="tlaUNC">           0 :       streetOfCurrentAddress: _inputCurrentAddressController.text,</span></span>
<span id="L422"><span class="lineNum">     422</span> <span class="tlaUNC">           0 :       companyName: _inputCompanyNameController.text,</span></span>
<span id="L423"><span class="lineNum">     423</span> <span class="tlaUNC">           0 :       companyAddress: _companyPermanentResidenceTextController.text,</span></span>
<span id="L424"><span class="lineNum">     424</span> <span class="tlaUNC">           0 :       streetOfCompanyAddress: _inputCompanyStreetController.text,</span></span>
<span id="L425"><span class="lineNum">     425</span>              :     );</span>
<span id="L426"><span class="lineNum">     426</span>              : </span>
<span id="L427"><span class="lineNum">     427</span>              :     if (!isFormValid) {</span>
<span id="L428"><span class="lineNum">     428</span> <span class="tlaUNC">           0 :       _cubit.emitStateForm(isFormValid);</span></span>
<span id="L429"><span class="lineNum">     429</span>              :       return;</span>
<span id="L430"><span class="lineNum">     430</span>              :     }</span>
<span id="L431"><span class="lineNum">     431</span>              : </span>
<span id="L432"><span class="lineNum">     432</span>              :     // Save to contact info</span>
<span id="L433"><span class="lineNum">     433</span> <span class="tlaUNC">           0 :     _cubit.setContactInfoItem(</span></span>
<span id="L434"><span class="lineNum">     434</span> <span class="tlaUNC">           0 :       streetOfCurrentAddress: _inputCurrentAddressController.text,</span></span>
<span id="L435"><span class="lineNum">     435</span> <span class="tlaUNC">           0 :       companyName: _inputCompanyNameController.text,</span></span>
<span id="L436"><span class="lineNum">     436</span> <span class="tlaUNC">           0 :       streetOfCompanyAddress: _inputCompanyStreetController.text,</span></span>
<span id="L437"><span class="lineNum">     437</span> <span class="tlaUNC">           0 :       pickupCardAddress: _selectedCardDeliveryType.value?.code,</span></span>
<span id="L438"><span class="lineNum">     438</span>              :     );</span>
<span id="L439"><span class="lineNum">     439</span>              : </span>
<span id="L440"><span class="lineNum">     440</span> <span class="tlaUNC">           0 :     final bool hasChangedInfo = _checkInfoChanged();</span></span>
<span id="L441"><span class="lineNum">     441</span>              :     if (!hasChangedInfo) {</span>
<span id="L442"><span class="lineNum">     442</span> <span class="tlaUNC">           0 :       commonLog('The user has not changed information.');</span></span>
<span id="L443"><span class="lineNum">     443</span> <span class="tlaUNC">           0 :       widget.controller.onSuccess(widget.model);</span></span>
<span id="L444"><span class="lineNum">     444</span>              :       return;</span>
<span id="L445"><span class="lineNum">     445</span>              :     }</span>
<span id="L446"><span class="lineNum">     446</span>              : </span>
<span id="L447"><span class="lineNum">     447</span> <span class="tlaUNC">           0 :     _cubit.submitAddressAdditionalInfo(model: widget.model);</span></span>
<span id="L448"><span class="lineNum">     448</span>              :   }</span>
<span id="L449"><span class="lineNum">     449</span>              : </span>
<span id="L450"><span class="lineNum">     450</span>              :   // Check the user changed information</span>
<span id="L451"><span class="lineNum">     451</span> <span class="tlaUNC">           0 :   bool _checkInfoChanged() {</span></span>
<span id="L452"><span class="lineNum">     452</span>              :     final DOPNativeContactInfoEntity? contactInfo =</span>
<span id="L453"><span class="lineNum">     453</span> <span class="tlaUNC">           0 :         widget.model.formDataEntity?.formData?.contactInfo;</span></span>
<span id="L454"><span class="lineNum">     454</span>              : </span>
<span id="L455"><span class="lineNum">     455</span> <span class="tlaUNC">           0 :     return contactInfo?.curAddress != _inputCurrentAddressController.text ||</span></span>
<span id="L456"><span class="lineNum">     456</span> <span class="tlaUNC">           0 :         contactInfo?.companyName != _inputCompanyNameController.text ||</span></span>
<span id="L457"><span class="lineNum">     457</span> <span class="tlaUNC">           0 :         _residenceAddressOfCompany != _companyPermanentResidenceTextController.text ||</span></span>
<span id="L458"><span class="lineNum">     458</span> <span class="tlaUNC">           0 :         contactInfo?.companyAddress != _inputCompanyStreetController.text ||</span></span>
<span id="L459"><span class="lineNum">     459</span> <span class="tlaUNC">           0 :         contactInfo?.pickupCardAddress != _selectedCardDeliveryType.value?.code;</span></span>
<span id="L460"><span class="lineNum">     460</span>              :   }</span>
<span id="L461"><span class="lineNum">     461</span>              : </span>
<span id="L462"><span class="lineNum">     462</span> <span class="tlaUNC">           0 :   bool _validateFormBeforeSave({</span></span>
<span id="L463"><span class="lineNum">     463</span>              :     required String streetOfCurrentAddress,</span>
<span id="L464"><span class="lineNum">     464</span>              :     required String companyName,</span>
<span id="L465"><span class="lineNum">     465</span>              :     required String companyAddress,</span>
<span id="L466"><span class="lineNum">     466</span>              :     required String streetOfCompanyAddress,</span>
<span id="L467"><span class="lineNum">     467</span>              :   }) {</span>
<span id="L468"><span class="lineNum">     468</span> <span class="tlaUNC">           0 :     return &lt;bool&gt;[</span></span>
<span id="L469"><span class="lineNum">     469</span> <span class="tlaUNC">           0 :       _validationUtilsCubit.validateDOPNativeAddress(streetOfCurrentAddress),</span></span>
<span id="L470"><span class="lineNum">     470</span> <span class="tlaUNC">           0 :       _validationUtilsCubit.validateCompanyName(companyName: companyName),</span></span>
<span id="L471"><span class="lineNum">     471</span> <span class="tlaUNC">           0 :       _validationUtilsCubit.validateResidenceAddress(companyAddress),</span></span>
<span id="L472"><span class="lineNum">     472</span> <span class="tlaUNC">           0 :       _validationUtilsCubit.validateDOPNativeCompanyAddress(streetOfCompanyAddress),</span></span>
<span id="L473"><span class="lineNum">     473</span> <span class="tlaUNC">           0 :     ].every((bool isValid) =&gt; isValid);</span></span>
<span id="L474"><span class="lineNum">     474</span>              :   }</span>
<span id="L475"><span class="lineNum">     475</span>              : </span>
<span id="L476"><span class="lineNum">     476</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _initializeForm() async {</span></span>
<span id="L477"><span class="lineNum">     477</span> <span class="tlaUNC">           0 :     widget.controller.onLoading(true);</span></span>
<span id="L478"><span class="lineNum">     478</span>              : </span>
<span id="L479"><span class="lineNum">     479</span>              :     final DOPNativeContactInfoEntity? contactInfo =</span>
<span id="L480"><span class="lineNum">     480</span> <span class="tlaUNC">           0 :         widget.model.formDataEntity?.formData?.contactInfo;</span></span>
<span id="L481"><span class="lineNum">     481</span>              : </span>
<span id="L482"><span class="lineNum">     482</span>              :     if (contactInfo == null) {</span>
<span id="L483"><span class="lineNum">     483</span> <span class="tlaUNC">           0 :       widget.controller.onValid(false);</span></span>
<span id="L484"><span class="lineNum">     484</span>              :       return;</span>
<span id="L485"><span class="lineNum">     485</span>              :     }</span>
<span id="L486"><span class="lineNum">     486</span>              : </span>
<span id="L487"><span class="lineNum">     487</span> <span class="tlaUNC">           0 :     _cubit.setContactInfo = contactInfo;</span></span>
<span id="L488"><span class="lineNum">     488</span>              : </span>
<span id="L489"><span class="lineNum">     489</span> <span class="tlaUNC">           0 :     if (contactInfo.pickupCardAddress?.isNotEmpty == true) {</span></span>
<span id="L490"><span class="lineNum">     490</span> <span class="tlaUNC">           0 :       _cubit.setContactInfoItem(pickupCardAddress: contactInfo.pickupCardAddress);</span></span>
<span id="L491"><span class="lineNum">     491</span>              :     }</span>
<span id="L492"><span class="lineNum">     492</span>              : </span>
<span id="L493"><span class="lineNum">     493</span> <span class="tlaUNC">           0 :     if (contactInfo.curAddress?.isNotEmpty == true) {</span></span>
<span id="L494"><span class="lineNum">     494</span> <span class="tlaUNC">           0 :       _cubit.setContactInfoItem(streetOfCurrentAddress: contactInfo.curAddress);</span></span>
<span id="L495"><span class="lineNum">     495</span> <span class="tlaUNC">           0 :       _inputCurrentAddressController.text = contactInfo.curAddress!;</span></span>
<span id="L496"><span class="lineNum">     496</span>              :     }</span>
<span id="L497"><span class="lineNum">     497</span>              : </span>
<span id="L498"><span class="lineNum">     498</span> <span class="tlaUNC">           0 :     if (contactInfo.companyName?.isNotEmpty == true) {</span></span>
<span id="L499"><span class="lineNum">     499</span> <span class="tlaUNC">           0 :       _cubit.setContactInfoItem(companyName: contactInfo.companyName);</span></span>
<span id="L500"><span class="lineNum">     500</span> <span class="tlaUNC">           0 :       _inputCompanyNameController.text = contactInfo.companyName!;</span></span>
<span id="L501"><span class="lineNum">     501</span>              :     }</span>
<span id="L502"><span class="lineNum">     502</span>              : </span>
<span id="L503"><span class="lineNum">     503</span> <span class="tlaUNC">           0 :     if (contactInfo.companyName?.isNotEmpty == true) {</span></span>
<span id="L504"><span class="lineNum">     504</span> <span class="tlaUNC">           0 :       _cubit.setContactInfoItem(companyName: contactInfo.companyName);</span></span>
<span id="L505"><span class="lineNum">     505</span> <span class="tlaUNC">           0 :       _inputCompanyNameController.text = contactInfo.companyName!;</span></span>
<span id="L506"><span class="lineNum">     506</span>              :     }</span>
<span id="L507"><span class="lineNum">     507</span>              : </span>
<span id="L508"><span class="lineNum">     508</span> <span class="tlaUNC">           0 :     if (contactInfo.companyAddress?.isNotEmpty == true) {</span></span>
<span id="L509"><span class="lineNum">     509</span> <span class="tlaUNC">           0 :       _cubit.setContactInfoItem(streetOfCompanyAddress: contactInfo.companyAddress);</span></span>
<span id="L510"><span class="lineNum">     510</span> <span class="tlaUNC">           0 :       _inputCompanyStreetController.text = contactInfo.companyAddress!;</span></span>
<span id="L511"><span class="lineNum">     511</span>              :     }</span>
<span id="L512"><span class="lineNum">     512</span>              : </span>
<span id="L513"><span class="lineNum">     513</span> <span class="tlaUNC">           0 :     if (contactInfo.companyAddressProvinceId?.isNotEmpty == true &amp;&amp;</span></span>
<span id="L514"><span class="lineNum">     514</span> <span class="tlaUNC">           0 :         contactInfo.companyAddressDistId?.isNotEmpty == true &amp;&amp;</span></span>
<span id="L515"><span class="lineNum">     515</span> <span class="tlaUNC">           0 :         contactInfo.companyAddressWardId?.isNotEmpty == true) {</span></span>
<span id="L516"><span class="lineNum">     516</span> <span class="tlaUNC">           0 :       _cubit.setContactInfoItem(</span></span>
<span id="L517"><span class="lineNum">     517</span> <span class="tlaUNC">           0 :         provinceOfCompanyCode: contactInfo.companyAddressProvinceId,</span></span>
<span id="L518"><span class="lineNum">     518</span> <span class="tlaUNC">           0 :         districtOfCompanyCode: contactInfo.companyAddressDistId,</span></span>
<span id="L519"><span class="lineNum">     519</span> <span class="tlaUNC">           0 :         wardOfCompanyCode: contactInfo.companyAddressWardId,</span></span>
<span id="L520"><span class="lineNum">     520</span>              :       );</span>
<span id="L521"><span class="lineNum">     521</span> <span class="tlaUNC">           0 :       _residenceAddressOfCompany = await _cubit.getResidenceAddressName(</span></span>
<span id="L522"><span class="lineNum">     522</span> <span class="tlaUNC">           0 :         provinceCode: contactInfo.companyAddressProvinceId,</span></span>
<span id="L523"><span class="lineNum">     523</span> <span class="tlaUNC">           0 :         districtCode: contactInfo.companyAddressDistId,</span></span>
<span id="L524"><span class="lineNum">     524</span> <span class="tlaUNC">           0 :         wardCode: contactInfo.companyAddressWardId,</span></span>
<span id="L525"><span class="lineNum">     525</span>              :       );</span>
<span id="L526"><span class="lineNum">     526</span> <span class="tlaUNC">           0 :       _companyPermanentResidenceTextController.text = _residenceAddressOfCompany;</span></span>
<span id="L527"><span class="lineNum">     527</span>              :     }</span>
<span id="L528"><span class="lineNum">     528</span>              : </span>
<span id="L529"><span class="lineNum">     529</span> <span class="tlaUNC">           0 :     _validateFormToEnableConfirmButton();</span></span>
<span id="L530"><span class="lineNum">     530</span>              : </span>
<span id="L531"><span class="lineNum">     531</span> <span class="tlaUNC">           0 :     widget.controller.onLoading(false);</span></span>
<span id="L532"><span class="lineNum">     532</span>              :   }</span>
<span id="L533"><span class="lineNum">     533</span>              : </span>
<span id="L534"><span class="lineNum">     534</span> <span class="tlaUNC">           0 :   void _validateFormToEnableConfirmButton() {</span></span>
<span id="L535"><span class="lineNum">     535</span> <span class="tlaUNC">           0 :     _cubit.validateForm(</span></span>
<span id="L536"><span class="lineNum">     536</span> <span class="tlaUNC">           0 :       streetOfCurrentAddress: _inputCurrentAddressController.text,</span></span>
<span id="L537"><span class="lineNum">     537</span> <span class="tlaUNC">           0 :       companyName: _inputCompanyNameController.text,</span></span>
<span id="L538"><span class="lineNum">     538</span> <span class="tlaUNC">           0 :       companyAddress: _companyPermanentResidenceTextController.text,</span></span>
<span id="L539"><span class="lineNum">     539</span> <span class="tlaUNC">           0 :       streetOfCompanyAddress: _inputCompanyStreetController.text,</span></span>
<span id="L540"><span class="lineNum">     540</span> <span class="tlaUNC">           0 :       pickupCardAddress: _selectedCardDeliveryType.value?.code,</span></span>
<span id="L541"><span class="lineNum">     541</span>              :     );</span>
<span id="L542"><span class="lineNum">     542</span>              :   }</span>
<span id="L543"><span class="lineNum">     543</span>              : </span>
<span id="L544"><span class="lineNum">     544</span> <span class="tlaUNC">           0 :   void _handleStateChanged(DOPNativeAddressAdditionalInfoState state) {</span></span>
<span id="L545"><span class="lineNum">     545</span> <span class="tlaUNC">           0 :     if (state is HasValidForm || state is HasInvalidForm) {</span></span>
<span id="L546"><span class="lineNum">     546</span> <span class="tlaUNC">           0 :       widget.controller.onValid(state is HasValidForm);</span></span>
<span id="L547"><span class="lineNum">     547</span>              :       return;</span>
<span id="L548"><span class="lineNum">     548</span>              :     }</span>
<span id="L549"><span class="lineNum">     549</span>              : </span>
<span id="L550"><span class="lineNum">     550</span> <span class="tlaUNC">           0 :     if (state is DOPNativeAddressAdditionalInfoLoading) {</span></span>
<span id="L551"><span class="lineNum">     551</span> <span class="tlaUNC">           0 :       widget.controller.onLoading(true);</span></span>
<span id="L552"><span class="lineNum">     552</span>              :       return;</span>
<span id="L553"><span class="lineNum">     553</span>              :     }</span>
<span id="L554"><span class="lineNum">     554</span>              : </span>
<span id="L555"><span class="lineNum">     555</span> <span class="tlaUNC">           0 :     if (state is DOPNativeGetResidenceAddressNameFailed) {</span></span>
<span id="L556"><span class="lineNum">     556</span> <span class="tlaUNC">           0 :       widget.controller.onError(state.error);</span></span>
<span id="L557"><span class="lineNum">     557</span>              :       return;</span>
<span id="L558"><span class="lineNum">     558</span>              :     }</span>
<span id="L559"><span class="lineNum">     559</span>              : </span>
<span id="L560"><span class="lineNum">     560</span> <span class="tlaUNC">           0 :     if (state is DOPNativeGetCardDeliveryTypeFailed) {</span></span>
<span id="L561"><span class="lineNum">     561</span> <span class="tlaUNC">           0 :       widget.controller.onError(state.error);</span></span>
<span id="L562"><span class="lineNum">     562</span>              :       return;</span>
<span id="L563"><span class="lineNum">     563</span>              :     }</span>
<span id="L564"><span class="lineNum">     564</span>              : </span>
<span id="L565"><span class="lineNum">     565</span> <span class="tlaUNC">           0 :     if (state is DOPNativeAddressAdditionalInfoSucceed) {</span></span>
<span id="L566"><span class="lineNum">     566</span> <span class="tlaUNC">           0 :       widget.controller.onSuccess(state.newModel);</span></span>
<span id="L567"><span class="lineNum">     567</span>              :       return;</span>
<span id="L568"><span class="lineNum">     568</span>              :     }</span>
<span id="L569"><span class="lineNum">     569</span>              : </span>
<span id="L570"><span class="lineNum">     570</span> <span class="tlaUNC">           0 :     if (state is DOPNativeAddressAdditionalInfoFailed) {</span></span>
<span id="L571"><span class="lineNum">     571</span> <span class="tlaUNC">           0 :       widget.controller.onError(state.error);</span></span>
<span id="L572"><span class="lineNum">     572</span>              :       return;</span>
<span id="L573"><span class="lineNum">     573</span>              :     }</span>
<span id="L574"><span class="lineNum">     574</span>              : </span>
<span id="L575"><span class="lineNum">     575</span> <span class="tlaUNC">           0 :     if (state is DOPNativeGetCardDeliveryTypeSucceed) {</span></span>
<span id="L576"><span class="lineNum">     576</span> <span class="tlaUNC">           0 :       final List&lt;DOPNativeMetadataItemEntity&gt;? cardDeliveryTypes = state.cardDeliveryTypes.metadata;</span></span>
<span id="L577"><span class="lineNum">     577</span> <span class="tlaUNC">           0 :       if (cardDeliveryTypes == null || cardDeliveryTypes.isEmpty) {</span></span>
<span id="L578"><span class="lineNum">     578</span>              :         return;</span>
<span id="L579"><span class="lineNum">     579</span>              :       }</span>
<span id="L580"><span class="lineNum">     580</span>              : </span>
<span id="L581"><span class="lineNum">     581</span>              :       final DOPNativeMetadataItemEntity? resultCardDeliveryTypes =</span>
<span id="L582"><span class="lineNum">     582</span> <span class="tlaUNC">           0 :           cardDeliveryTypes.firstWhereOrNull((DOPNativeMetadataItemEntity element) =&gt;</span></span>
<span id="L583"><span class="lineNum">     583</span> <span class="tlaUNC">           0 :               element.code == _cubit.getContactInfo.pickupCardAddress);</span></span>
<span id="L584"><span class="lineNum">     584</span> <span class="tlaUNC">           0 :       _selectedCardDeliveryType.value = resultCardDeliveryTypes ?? cardDeliveryTypes.first;</span></span>
<span id="L585"><span class="lineNum">     585</span> <span class="tlaUNC">           0 :       _validateFormToEnableConfirmButton();</span></span>
<span id="L586"><span class="lineNum">     586</span>              :       return;</span>
<span id="L587"><span class="lineNum">     587</span>              :     }</span>
<span id="L588"><span class="lineNum">     588</span>              :   }</span>
<span id="L589"><span class="lineNum">     589</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
