<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/dop_native/features/additional_form/widgets/address_additional_info/cubit/dop_native_address_additional_info_cubit.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/dop_native/features/additional_form/widgets/address_additional_info/cubit">lib/feature/dop_native/features/additional_form/widgets/address_additional_info/cubit</a> - dop_native_address_additional_info_cubit.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">95.5&nbsp;%</td>
            <td class="headerCovTableEntry">67</td>
            <td class="headerCovTableEntry">64</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/cupertino.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/base/bloc_state.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/base/common_cubit.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/data/http_client/mock_config.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../../../../../data/repository/dop_native_repo/dop_native_repo.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../../../../../data/request/dop_native/dop_native_application_submit_form_request.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../../../../../data/response/dop_native/dop_native_application_form_data_entity.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../../../../../data/response/dop_native/dop_native_contact_info_entity.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../../../../../data/response/dop_native/dop_native_form_data_entity.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../../../../../../data/response/dop_native/dop_native_metadata_entity.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../../../../../../data/response/dop_native/dop_native_metadata_item_entity.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../../../../../../prepare_for_app_initiation.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../../../../util/metadata/dop_native_metadata_utils.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../../../mock/mock_dop_native_application_form_data_use_case.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../../../models/additional_form_data_model.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span>              : part 'dop_native_address_additional_info_state.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : </span>
<span id="L23"><span class="lineNum">      23</span>              : class DOPNativeAddressAdditionalInfoCubit extends CommonCubit&lt;DOPNativeAddressAdditionalInfoState&gt; {</span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">           1 :   DOPNativeAddressAdditionalInfoCubit({</span></span>
<span id="L25"><span class="lineNum">      25</span>              :     required this.dopNativeRepo,</span>
<span id="L26"><span class="lineNum">      26</span>              :     required this.metadataUtils,</span>
<span id="L27"><span class="lineNum">      27</span>              :     required this.appState,</span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           2 :   }) : super(DOPNativeAddressAdditionalInfoInitial());</span></span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span>              :   final DOPNativeRepo dopNativeRepo;</span>
<span id="L31"><span class="lineNum">      31</span>              :   final DOPNativeMetadataUtils metadataUtils;</span>
<span id="L32"><span class="lineNum">      32</span>              :   final AppState appState;</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span>              :   @visibleForTesting</span>
<span id="L35"><span class="lineNum">      35</span>              :   DOPNativeContactInfoEntity contactInfo = const DOPNativeContactInfoEntity();</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :   DOPNativeContactInfoEntity get getContactInfo =&gt; contactInfo;</span></span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :   set setContactInfo(DOPNativeContactInfoEntity contactInfoValue) {</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :     contactInfo = contactInfoValue;</span></span>
<span id="L41"><span class="lineNum">      41</span>              :   }</span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; submitAddressAdditionalInfo({</span></span>
<span id="L44"><span class="lineNum">      44</span>              :     required DOPNativeAdditionalFormDataModel model,</span>
<span id="L45"><span class="lineNum">      45</span>              :   }) async {</span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           2 :     emit(DOPNativeAddressAdditionalInfoLoading());</span></span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           1 :     final DOPNativeApplicationSubmitFormRequest formData = DOPNativeApplicationSubmitFormRequest(</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           4 :       formAppState: appState.dopNativeState.dopApplicationState?.currentStep,</span></span>
<span id="L50"><span class="lineNum">      50</span>              :       changeState: false,</span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           3 :       formStep: model.formStepModel.currentStep.value,</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaGNC">           1 :       formData: ApplicationFormData(</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           1 :         contactInfo: DOPNativeContactInfoEntity(</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           2 :           companyName: contactInfo.companyName,</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaGNC">           2 :           curAddress: contactInfo.curAddress,</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           2 :           companyAddressProvinceId: contactInfo.companyAddressProvinceId,</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           2 :           companyAddressDistId: contactInfo.companyAddressDistId,</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaGNC">           2 :           companyAddressWardId: contactInfo.companyAddressWardId,</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           2 :           companyAddress: contactInfo.companyAddress,</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaGNC">           2 :           pickupCardAddress: contactInfo.pickupCardAddress,</span></span>
<span id="L61"><span class="lineNum">      61</span>              :         ),</span>
<span id="L62"><span class="lineNum">      62</span>              :       ),</span>
<span id="L63"><span class="lineNum">      63</span>              :     );</span>
<span id="L64"><span class="lineNum">      64</span>              : </span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaGNC">           2 :     final BaseEntity response = await dopNativeRepo.submitApplicationForm(</span></span>
<span id="L66"><span class="lineNum">      66</span>              :       form: formData,</span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           1 :       mockConfig: MockConfig(</span></span>
<span id="L68"><span class="lineNum">      68</span>              :         enable: false,</span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">           1 :         fileName: getMockDOPNativeAdditionFormData(</span></span>
<span id="L70"><span class="lineNum">      70</span>              :           MockDOPNativeAdditionFormData.addressAdditionalInfoSuccess,</span>
<span id="L71"><span class="lineNum">      71</span>              :         ),</span>
<span id="L72"><span class="lineNum">      72</span>              :       ),</span>
<span id="L73"><span class="lineNum">      73</span>              :     );</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaGNC">           2 :     if (response.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           1 :       final DOPNativeAdditionalFormDataModel newModel = updateFormDataModel(</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaGNC">           1 :         contactInfo: contactInfo,</span></span>
<span id="L78"><span class="lineNum">      78</span>              :         model: model,</span>
<span id="L79"><span class="lineNum">      79</span>              :       );</span>
<span id="L80"><span class="lineNum">      80</span>              : </span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaGNC">           2 :       emit(DOPNativeAddressAdditionalInfoSucceed(newModel: newModel));</span></span>
<span id="L82"><span class="lineNum">      82</span>              :     } else {</span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaGNC">           3 :       emit(DOPNativeAddressAdditionalInfoFailed(ErrorUIModel.fromEntity(response)));</span></span>
<span id="L84"><span class="lineNum">      84</span>              :     }</span>
<span id="L85"><span class="lineNum">      85</span>              :   }</span>
<span id="L86"><span class="lineNum">      86</span>              : </span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; getCardDeliveryType() async {</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           2 :     final DOPNativeMetadataEntity entity = await metadataUtils.getCardDeliveryType();</span></span>
<span id="L89"><span class="lineNum">      89</span>              : </span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           2 :     if (entity.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaGNC">           2 :       emit(DOPNativeGetCardDeliveryTypeSucceed(cardDeliveryTypes: entity));</span></span>
<span id="L92"><span class="lineNum">      92</span>              :     } else {</span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaGNC">           2 :       emit(DOPNativeGetCardDeliveryTypeFailed(</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           1 :           error: ErrorUIModel(</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaGNC">           1 :         statusCode: entity.statusCode,</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaGNC">           1 :         userMessage: entity.message,</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaGNC">           1 :         verdict: entity.verdict,</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaGNC">           1 :         userMessageTitle: entity.verdict,</span></span>
<span id="L99"><span class="lineNum">      99</span>              :       )));</span>
<span id="L100"><span class="lineNum">     100</span>              :     }</span>
<span id="L101"><span class="lineNum">     101</span>              :   }</span>
<span id="L102"><span class="lineNum">     102</span>              : </span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; getCurrentResidenceAddressName({</span></span>
<span id="L104"><span class="lineNum">     104</span>              :     String? provinceCode,</span>
<span id="L105"><span class="lineNum">     105</span>              :     String? districtCode,</span>
<span id="L106"><span class="lineNum">     106</span>              :     String? wardCode,</span>
<span id="L107"><span class="lineNum">     107</span>              :   }) async {</span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaGNC">           1 :     final String residenceAddressName = await getResidenceAddressName(</span></span>
<span id="L109"><span class="lineNum">     109</span>              :       provinceCode: provinceCode,</span>
<span id="L110"><span class="lineNum">     110</span>              :       districtCode: districtCode,</span>
<span id="L111"><span class="lineNum">     111</span>              :       wardCode: wardCode,</span>
<span id="L112"><span class="lineNum">     112</span>              :     );</span>
<span id="L113"><span class="lineNum">     113</span>              : </span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaGNC">           1 :     if (residenceAddressName.isEmpty) {</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaGNC">           3 :       emit(DOPNativeGetResidenceAddressNameFailed(error: ErrorUIModel()));</span></span>
<span id="L116"><span class="lineNum">     116</span>              :       return;</span>
<span id="L117"><span class="lineNum">     117</span>              :     }</span>
<span id="L118"><span class="lineNum">     118</span>              : </span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaGNC">           2 :     emit(DOPNativeGetResidenceAddressNameSucceed(residenceAddressName: residenceAddressName));</span></span>
<span id="L120"><span class="lineNum">     120</span>              :   }</span>
<span id="L121"><span class="lineNum">     121</span>              : </span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaGNC">           1 :   Future&lt;String&gt; getResidenceAddressName({</span></span>
<span id="L123"><span class="lineNum">     123</span>              :     String? provinceCode,</span>
<span id="L124"><span class="lineNum">     124</span>              :     String? districtCode,</span>
<span id="L125"><span class="lineNum">     125</span>              :     String? wardCode,</span>
<span id="L126"><span class="lineNum">     126</span>              :   }) async {</span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaGNC">           2 :     return metadataUtils.getResidenceAddressName(</span></span>
<span id="L128"><span class="lineNum">     128</span>              :       provinceCode: provinceCode,</span>
<span id="L129"><span class="lineNum">     129</span>              :       districtCode: districtCode,</span>
<span id="L130"><span class="lineNum">     130</span>              :       wardCode: wardCode,</span>
<span id="L131"><span class="lineNum">     131</span>              :     );</span>
<span id="L132"><span class="lineNum">     132</span>              :   }</span>
<span id="L133"><span class="lineNum">     133</span>              : </span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaGNC">           1 :   void setContactInfoItem({</span></span>
<span id="L135"><span class="lineNum">     135</span>              :     String? streetOfCurrentAddress,</span>
<span id="L136"><span class="lineNum">     136</span>              :     String? companyName,</span>
<span id="L137"><span class="lineNum">     137</span>              :     String? streetOfCompanyAddress,</span>
<span id="L138"><span class="lineNum">     138</span>              :     String? pickupCardAddress,</span>
<span id="L139"><span class="lineNum">     139</span>              :     String? provinceOfCompanyCode,</span>
<span id="L140"><span class="lineNum">     140</span>              :     String? districtOfCompanyCode,</span>
<span id="L141"><span class="lineNum">     141</span>              :     String? wardOfCompanyCode,</span>
<span id="L142"><span class="lineNum">     142</span>              :   }) {</span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaGNC">           3 :     contactInfo = contactInfo.copyWith(</span></span>
<span id="L144"><span class="lineNum">     144</span>              :       curAddress: streetOfCurrentAddress,</span>
<span id="L145"><span class="lineNum">     145</span>              :       companyName: companyName,</span>
<span id="L146"><span class="lineNum">     146</span>              :       companyAddress: streetOfCompanyAddress,</span>
<span id="L147"><span class="lineNum">     147</span>              :       pickupCardAddress: pickupCardAddress,</span>
<span id="L148"><span class="lineNum">     148</span>              :       companyAddressProvinceId: provinceOfCompanyCode,</span>
<span id="L149"><span class="lineNum">     149</span>              :       companyAddressDistId: districtOfCompanyCode,</span>
<span id="L150"><span class="lineNum">     150</span>              :       companyAddressWardId: wardOfCompanyCode,</span>
<span id="L151"><span class="lineNum">     151</span>              :     );</span>
<span id="L152"><span class="lineNum">     152</span>              :   }</span>
<span id="L153"><span class="lineNum">     153</span>              : </span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaGNC">           1 :   void saveCompanyAddress({</span></span>
<span id="L155"><span class="lineNum">     155</span>              :     DOPNativeMetadataItemEntity? province,</span>
<span id="L156"><span class="lineNum">     156</span>              :     DOPNativeMetadataItemEntity? district,</span>
<span id="L157"><span class="lineNum">     157</span>              :     DOPNativeMetadataItemEntity? ward,</span>
<span id="L158"><span class="lineNum">     158</span>              :     String? residenceAddressName,</span>
<span id="L159"><span class="lineNum">     159</span>              :   }) {</span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaGNC">           3 :     contactInfo = contactInfo.copyWith(</span></span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaGNC">           1 :       companyAddressDistId: district?.code,</span></span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaGNC">           1 :       companyAddressProvinceId: province?.code,</span></span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaGNC">           1 :       companyAddressWardId: ward?.code,</span></span>
<span id="L164"><span class="lineNum">     164</span>              :     );</span>
<span id="L165"><span class="lineNum">     165</span>              :   }</span>
<span id="L166"><span class="lineNum">     166</span>              : </span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; validateForm({</span></span>
<span id="L168"><span class="lineNum">     168</span>              :     required String streetOfCurrentAddress,</span>
<span id="L169"><span class="lineNum">     169</span>              :     required String companyName,</span>
<span id="L170"><span class="lineNum">     170</span>              :     required String companyAddress,</span>
<span id="L171"><span class="lineNum">     171</span>              :     required String streetOfCompanyAddress,</span>
<span id="L172"><span class="lineNum">     172</span>              :     required String? pickupCardAddress,</span>
<span id="L173"><span class="lineNum">     173</span>              :   }) async {</span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaGNC">           1 :     final bool isFormValid = streetOfCurrentAddress.isNotEmpty &amp;&amp;</span></span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaGNC">           1 :         companyName.isNotEmpty &amp;&amp;</span></span>
<span id="L176"><span class="lineNum">     176</span> <span class="tlaGNC">           1 :         companyAddress.isNotEmpty &amp;&amp;</span></span>
<span id="L177"><span class="lineNum">     177</span> <span class="tlaGNC">           1 :         streetOfCompanyAddress.isNotEmpty &amp;&amp;</span></span>
<span id="L178"><span class="lineNum">     178</span> <span class="tlaGNC">           2 :         (pickupCardAddress?.isNotEmpty == true);</span></span>
<span id="L179"><span class="lineNum">     179</span>              : </span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaGNC">           1 :     emitStateForm(isFormValid);</span></span>
<span id="L181"><span class="lineNum">     181</span>              :   }</span>
<span id="L182"><span class="lineNum">     182</span>              : </span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaGNC">           1 :   void emitStateForm(bool isFormValid) {</span></span>
<span id="L184"><span class="lineNum">     184</span>              :     if (isFormValid) {</span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaGNC">           2 :       emit(HasValidForm());</span></span>
<span id="L186"><span class="lineNum">     186</span>              :     } else {</span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaGNC">           2 :       emit(HasInvalidForm());</span></span>
<span id="L188"><span class="lineNum">     188</span>              :     }</span>
<span id="L189"><span class="lineNum">     189</span>              :   }</span>
<span id="L190"><span class="lineNum">     190</span>              : </span>
<span id="L191"><span class="lineNum">     191</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L192"><span class="lineNum">     192</span>              :   DOPNativeAdditionalFormDataModel updateFormDataModel({</span>
<span id="L193"><span class="lineNum">     193</span>              :     required DOPNativeContactInfoEntity contactInfo,</span>
<span id="L194"><span class="lineNum">     194</span>              :     required DOPNativeAdditionalFormDataModel model,</span>
<span id="L195"><span class="lineNum">     195</span>              :   }) {</span>
<span id="L196"><span class="lineNum">     196</span>              :     final DOPNativeApplicationFormDataEntity form =</span>
<span id="L197"><span class="lineNum">     197</span> <span class="tlaGNC">           2 :         model.formDataEntity ?? DOPNativeApplicationFormDataEntity();</span></span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaGNC">           2 :     final DOPNativeFormDataEntity formData = form.formData ?? DOPNativeFormDataEntity();</span></span>
<span id="L199"><span class="lineNum">     199</span>              : </span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaGNC">           1 :     return model.copyWith(</span></span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaGNC">           1 :       formDataEntityValue: form.copyWith(</span></span>
<span id="L202"><span class="lineNum">     202</span> <span class="tlaGNC">           1 :         formData: formData.copyWith(</span></span>
<span id="L203"><span class="lineNum">     203</span>              :           contactInfo: contactInfo,</span>
<span id="L204"><span class="lineNum">     204</span>              :         ),</span>
<span id="L205"><span class="lineNum">     205</span>              :       ),</span>
<span id="L206"><span class="lineNum">     206</span>              :     );</span>
<span id="L207"><span class="lineNum">     207</span>              :   }</span>
<span id="L208"><span class="lineNum">     208</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
