<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/dop_native/features/ekyc_ui_only/selfie/dop_native_selfie_introduction_screen_state.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/dop_native/features/ekyc_ui_only/selfie">lib/feature/dop_native/features/ekyc_ui_only/selfie</a> - dop_native_selfie_introduction_screen_state.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">98</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/common_package/common_package.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/global_key_provider.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/util/uuid/uuid_generator.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../../../../prepare_for_app_initiation.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../../../resources/resources.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../../../util/ui_utils/evo_ui_utils.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../../camera_permission/camera_permission_cubit.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../../ekyc/model/ekyc_result_model.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../base/cubit/dop_native_application_state.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../../dop_native_constants.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../../resources/dop_native_images.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../../resources/dop_native_resources.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../../resources/dop_native_ui_strings.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../../../util/dop_functions.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../../../util/dop_native_navigation_utils.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../../../widgets/appbar/dop_native_appbar_widget.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../../../widgets/custom_button/camera_icon_button_widget.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import '../../../widgets/dop_awareness_instruction/dop_awareness_instruction_widget.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import '../../../widgets/dop_awareness_instruction/dop_awareness_item.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import '../../../widgets/dop_native_form_header_widget.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import '../../failure_screen/dop_native_failure_screen.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : import '../../status_screen/dop_native_status_screen.dart';</span>
<span id="L27"><span class="lineNum">      27</span>              : import '../dop_native_ekyc_page_state_base.dart';</span>
<span id="L28"><span class="lineNum">      28</span>              : import '../ekyc_limit_exceed/dop_native_ekyc_limit_exceed_screen.dart';</span>
<span id="L29"><span class="lineNum">      29</span>              : import '../sdk_bridge/tv_ekyc/ekyc_ui_only_bridge.dart';</span>
<span id="L30"><span class="lineNum">      30</span>              : import '../ui_model/ekyc_error_ui_model.dart';</span>
<span id="L31"><span class="lineNum">      31</span>              : import 'cubit/dop_native_selfie_capture_cubit.dart';</span>
<span id="L32"><span class="lineNum">      32</span>              : import 'dop_native_selfie_introduction_page_base.dart';</span>
<span id="L33"><span class="lineNum">      33</span>              : import 'selfie_verification/dop_native_selfie_verification_screen.dart';</span>
<span id="L34"><span class="lineNum">      34</span>              : import 'selfie_verify_success/dop_native_selfie_verify_success_screen.dart';</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span>              : class DOPNativeSelfieIntroductionScreenState</span>
<span id="L37"><span class="lineNum">      37</span>              :     extends DOPNativeEKYCPageStateBase&lt;DOPNativeSelfieCaptureIntroductionPageBase&gt; {</span>
<span id="L38"><span class="lineNum">      38</span>              :   // this variable represents the progress of the step indicator</span>
<span id="L39"><span class="lineNum">      39</span>              :   // with selfie verification, the progress is 4/5</span>
<span id="L40"><span class="lineNum">      40</span>              :   final int _currentStep = 4;</span>
<span id="L41"><span class="lineNum">      41</span>              :   final int _totalStep = 5;</span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span>              :   final CameraPermissionCubit _cameraPermissionCubit = CameraPermissionCubit();</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span>              :   final DopNativeSelfieCaptureCubit _selfieCaptureCubit = DopNativeSelfieCaptureCubit(</span>
<span id="L46"><span class="lineNum">      46</span>              :     ekycUiOnlyBridge: getIt.get&lt;EkycUiOnlyBridge&gt;(),</span>
<span id="L47"><span class="lineNum">      47</span>              :     uuidGenerator: getIt.get&lt;UUIDGenerator&gt;(),</span>
<span id="L48"><span class="lineNum">      48</span>              :     ekycUiOnlyRepo: getIt.get&lt;DopNativeEkycUIOnlyRepo&gt;(),</span>
<span id="L49"><span class="lineNum">      49</span>              :   );</span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span>              :   final List&lt;DOPNativeAwarenessItem&gt; _awarenessItems = &lt;DOPNativeAwarenessItem&gt;[</span>
<span id="L52"><span class="lineNum">      52</span>              :     DOPNativeAwarenessItem(</span>
<span id="L53"><span class="lineNum">      53</span>              :         imgAsset: DOPNativeImages.imgSelfieCaptureAware1,</span>
<span id="L54"><span class="lineNum">      54</span>              :         descriptions: &lt;DescriptionInlineSpan&gt;[</span>
<span id="L55"><span class="lineNum">      55</span>              :           const DescriptionInlineSpan(DOPNativeStrings.dopNativeSelfieCaptureAware1),</span>
<span id="L56"><span class="lineNum">      56</span>              :         ]),</span>
<span id="L57"><span class="lineNum">      57</span>              :     DOPNativeAwarenessItem(</span>
<span id="L58"><span class="lineNum">      58</span>              :         imgAsset: DOPNativeImages.imgSelfieCaptureAware2,</span>
<span id="L59"><span class="lineNum">      59</span>              :         descriptions: &lt;DescriptionInlineSpan&gt;[</span>
<span id="L60"><span class="lineNum">      60</span>              :           const DescriptionInlineSpan('${DOPNativeStrings.dopNativeDoNot} ', negative: true),</span>
<span id="L61"><span class="lineNum">      61</span>              :           const DescriptionInlineSpan(DOPNativeStrings.dopNativeSelfieCaptureAwareSuffix2),</span>
<span id="L62"><span class="lineNum">      62</span>              :         ]),</span>
<span id="L63"><span class="lineNum">      63</span>              :     DOPNativeAwarenessItem(</span>
<span id="L64"><span class="lineNum">      64</span>              :         imgAsset: DOPNativeImages.imgSelfieCaptureAware3,</span>
<span id="L65"><span class="lineNum">      65</span>              :         descriptions: &lt;DescriptionInlineSpan&gt;[</span>
<span id="L66"><span class="lineNum">      66</span>              :           const DescriptionInlineSpan(DOPNativeStrings.dopNativeSelfieCaptureAwarePrefix3),</span>
<span id="L67"><span class="lineNum">      67</span>              :           DescriptionInlineSpan(' ${DOPNativeStrings.dopNativeDoNot.toLowerCase()} ',</span>
<span id="L68"><span class="lineNum">      68</span>              :               negative: true),</span>
<span id="L69"><span class="lineNum">      69</span>              :           const DescriptionInlineSpan(DOPNativeStrings.dopNativeSelfieCaptureAwareSuffix3),</span>
<span id="L70"><span class="lineNum">      70</span>              :         ]),</span>
<span id="L71"><span class="lineNum">      71</span>              :   ];</span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L74"><span class="lineNum">      74</span>              :   Widget getContentWidget(BuildContext context) {</span>
<span id="L75"><span class="lineNum">      75</span>              :     const SizedBox spacing12 = SizedBox(height: 12);</span>
<span id="L76"><span class="lineNum">      76</span>              :     const SizedBox spacing18 = SizedBox(height: 18);</span>
<span id="L77"><span class="lineNum">      77</span>              : </span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :     return Scaffold(</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :       backgroundColor: dopNativeColors.screenBackground,</span></span>
<span id="L80"><span class="lineNum">      80</span>              :       appBar: const DOPNativeAppBar(),</span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :       body: SafeArea(</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :         child: MultiBlocProvider(</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :           providers: &lt;BlocProvider&lt;dynamic&gt;&gt;[</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaUNC">           0 :             BlocProvider&lt;CameraPermissionCubit&gt;(</span></span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :                 create: (BuildContext context) =&gt; _cameraPermissionCubit),</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :             BlocProvider&lt;DopNativeSelfieCaptureCubit&gt;(</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :                 create: (BuildContext context) =&gt; _selfieCaptureCubit),</span></span>
<span id="L88"><span class="lineNum">      88</span>              :           ],</span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :           child: MultiBlocListener(</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :             listeners: &lt;BlocListener&lt;dynamic, dynamic&gt;&gt;[</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :               BlocListener&lt;CameraPermissionCubit, CameraPermissionState&gt;(</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :                 listener: (BuildContext context, CameraPermissionState state) async {</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :                   await _handleCameraPermission(state);</span></span>
<span id="L94"><span class="lineNum">      94</span>              :                 },</span>
<span id="L95"><span class="lineNum">      95</span>              :               ),</span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :               BlocListener&lt;DopNativeSelfieCaptureCubit, DopNativeSelfieCaptureState&gt;(</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :                 listener: (BuildContext context, DopNativeSelfieCaptureState state) {</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :                   _handleSelfieCaptureState(state);</span></span>
<span id="L99"><span class="lineNum">      99</span>              :                 },</span>
<span id="L100"><span class="lineNum">     100</span>              :               ),</span>
<span id="L101"><span class="lineNum">     101</span>              :             ],</span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :             child: ListView(</span></span>
<span id="L103"><span class="lineNum">     103</span>              :               padding: const EdgeInsets.symmetric(horizontal: 20.0),</span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :               children: &lt;Widget&gt;[</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :                 DOPNativeFormHeaderWidget(</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :                   currentStep: _currentStep,</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :                   totalStep: _totalStep,</span></span>
<span id="L108"><span class="lineNum">     108</span>              :                   titleStep: DOPNativeStrings.dopNativeSelfieCaptureIntroductionTitle,</span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :                   titleStyle: dopNativeTextStyles.h500(),</span></span>
<span id="L110"><span class="lineNum">     110</span>              :                 ),</span>
<span id="L111"><span class="lineNum">     111</span>              :                 spacing12,</span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :                 Text(</span></span>
<span id="L113"><span class="lineNum">     113</span>              :                   DOPNativeStrings.dopNativeSelfieCaptureInstructionDesc,</span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :                   style: dopNativeTextStyles.bodyMedium(dopNativeColors.textPassive),</span></span>
<span id="L115"><span class="lineNum">     115</span>              :                 ),</span>
<span id="L116"><span class="lineNum">     116</span>              :                 spacing12,</span>
<span id="L117"><span class="lineNum">     117</span>              :                 spacing18,</span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :                 Padding(</span></span>
<span id="L119"><span class="lineNum">     119</span>              :                   padding: const EdgeInsets.symmetric(vertical: 12.0),</span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :                   child: evoImageProvider.asset(DOPNativeImages.imgSelfieCapture,</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :                       height: EvoUiUtils().calculateVerticalSpace(</span></span>
<span id="L122"><span class="lineNum">     122</span>              :                         context: context,</span>
<span id="L123"><span class="lineNum">     123</span>              :                         heightPercentage: DOPNativeConstants.ekycInstructionIconHeightPercentage,</span>
<span id="L124"><span class="lineNum">     124</span>              :                       ),</span>
<span id="L125"><span class="lineNum">     125</span>              :                       width: double.infinity),</span>
<span id="L126"><span class="lineNum">     126</span>              :                 ),</span>
<span id="L127"><span class="lineNum">     127</span>              :                 const SizedBox(height: 46),</span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :                 _ctaStartSelfieButton(),</span></span>
<span id="L129"><span class="lineNum">     129</span>              :                 spacing12,</span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :                 DOPNativeAwarenessInstruction(</span></span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :                   items: _awarenessItems,</span></span>
<span id="L132"><span class="lineNum">     132</span>              :                 ),</span>
<span id="L133"><span class="lineNum">     133</span>              :               ],</span>
<span id="L134"><span class="lineNum">     134</span>              :             ),</span>
<span id="L135"><span class="lineNum">     135</span>              :           ),</span>
<span id="L136"><span class="lineNum">     136</span>              :         ),</span>
<span id="L137"><span class="lineNum">     137</span>              :       ),</span>
<span id="L138"><span class="lineNum">     138</span>              :     );</span>
<span id="L139"><span class="lineNum">     139</span>              :   }</span>
<span id="L140"><span class="lineNum">     140</span>              : </span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L142"><span class="lineNum">     142</span>              :   void onPagePaused() {</span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaUNC">           0 :     super.onPagePaused();</span></span>
<span id="L144"><span class="lineNum">     144</span>              : </span>
<span id="L145"><span class="lineNum">     145</span>              :     /// reset cameraPermission state if app move to Background,</span>
<span id="L146"><span class="lineNum">     146</span>              :     /// then resuming this page, user click CTA &amp; it will check permission again to determine the next action</span>
<span id="L147"><span class="lineNum">     147</span>              :     /// (Eg: user goto Settings -&gt; disable camera permission -&gt; back to app -&gt; click CTA)</span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaUNC">           0 :     _cameraPermissionCubit.resetState();</span></span>
<span id="L149"><span class="lineNum">     149</span>              :   }</span>
<span id="L150"><span class="lineNum">     150</span>              : </span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L152"><span class="lineNum">     152</span>              :   void onInitEKYCServerConfigsFailed(EkycErrorUIModel error) {</span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :     _onSelfieVerificationFailed(error);</span></span>
<span id="L154"><span class="lineNum">     154</span>              :   }</span>
<span id="L155"><span class="lineNum">     155</span>              : </span>
<span id="L156"><span class="lineNum">     156</span>              :   /// when user click on the CTA &quot;Retry&quot; on the [Failure Screen]. We will check the application state to determine the next action</span>
<span id="L157"><span class="lineNum">     157</span>              :   /// in case of [DOPNativeNavigationStep.ekycSelfieFlash], we will retry the id capturing</span>
<span id="L158"><span class="lineNum">     158</span>              :   /// otherwise, we will navigate to the next screen based on the application state</span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L160"><span class="lineNum">     160</span>              :   void handleDOPNativeApplicationStateChanged(DOPNativeApplicationState state) {</span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaUNC">           0 :     if (state is DOPNativeApplicationStateLoaded) {</span></span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaUNC">           0 :       if (state.entity.currentStep == DOPNativeNavigationStep.ekycSelfieFlash.value) {</span></span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaUNC">           0 :         hideDOPLoading();</span></span>
<span id="L164"><span class="lineNum">     164</span>              : </span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaUNC">           0 :         _onRetrySelfieVerification();</span></span>
<span id="L166"><span class="lineNum">     166</span>              :         return;</span>
<span id="L167"><span class="lineNum">     167</span>              :       }</span>
<span id="L168"><span class="lineNum">     168</span>              :     }</span>
<span id="L169"><span class="lineNum">     169</span>              : </span>
<span id="L170"><span class="lineNum">     170</span> <span class="tlaUNC">           0 :     super.handleDOPNativeApplicationStateChanged(state);</span></span>
<span id="L171"><span class="lineNum">     171</span>              :   }</span>
<span id="L172"><span class="lineNum">     172</span>              : </span>
<span id="L173"><span class="lineNum">     173</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _showOpenAppSettingDialog() {</span></span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaUNC">           0 :     return dopUtilFunction.showOpenAppSettingCameraDialog(</span></span>
<span id="L175"><span class="lineNum">     175</span>              :       title: DOPNativeStrings.dopNativeSelfieVerificationOpenSettingDialogTitle,</span>
<span id="L176"><span class="lineNum">     176</span>              :       content: DOPNativeStrings.dopNativeSelfieVerificationOpenSettingDialogContent,</span>
<span id="L177"><span class="lineNum">     177</span>              :       textPositive: DOPNativeStrings.dopNativeSetup,</span>
<span id="L178"><span class="lineNum">     178</span>              :       textNegative: DOPNativeStrings.dopNativeIgnore,</span>
<span id="L179"><span class="lineNum">     179</span>              :     );</span>
<span id="L180"><span class="lineNum">     180</span>              :   }</span>
<span id="L181"><span class="lineNum">     181</span>              : </span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :   Widget _ctaStartSelfieButton() {</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaUNC">           0 :     return CameraIconButtonWidget(</span></span>
<span id="L184"><span class="lineNum">     184</span>              :       title: DOPNativeStrings.dopNativeStartCapture,</span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :       onPressed: _onStartSelfieVerification,</span></span>
<span id="L186"><span class="lineNum">     186</span>              :     );</span>
<span id="L187"><span class="lineNum">     187</span>              :   }</span>
<span id="L188"><span class="lineNum">     188</span>              : </span>
<span id="L189"><span class="lineNum">     189</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _onStartSelfieVerification() async {</span></span>
<span id="L190"><span class="lineNum">     190</span> <span class="tlaUNC">           0 :     final CameraPermissionState state = _cameraPermissionCubit.state;</span></span>
<span id="L191"><span class="lineNum">     191</span> <span class="tlaUNC">           0 :     if (state is CameraPermissionInitialState) {</span></span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaUNC">           0 :       _cameraPermissionCubit.requestPermission();</span></span>
<span id="L193"><span class="lineNum">     193</span>              :     } else {</span>
<span id="L194"><span class="lineNum">     194</span> <span class="tlaUNC">           0 :       await _handleCameraPermission(state);</span></span>
<span id="L195"><span class="lineNum">     195</span>              :     }</span>
<span id="L196"><span class="lineNum">     196</span>              :   }</span>
<span id="L197"><span class="lineNum">     197</span>              : </span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaUNC">           0 :   void _handleSelfieCaptureState(DopNativeSelfieCaptureState state) {</span></span>
<span id="L199"><span class="lineNum">     199</span> <span class="tlaUNC">           0 :     if (state is DopNativeSelfieCaptureSuccess) {</span></span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaUNC">           0 :       DOPNativeSelfieVerificationScreen.pushNamed(</span></span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaUNC">           0 :         batchId: state.batchId,</span></span>
<span id="L202"><span class="lineNum">     202</span> <span class="tlaUNC">           0 :         selfieImages: state.selfieImages,</span></span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaUNC">           0 :         livenessMode: widget.livenessMode,</span></span>
<span id="L204"><span class="lineNum">     204</span> <span class="tlaUNC">           0 :         onSuccess: () {</span></span>
<span id="L205"><span class="lineNum">     205</span>              :           /// The Selfie Verification is done, navigate to the success screen</span>
<span id="L206"><span class="lineNum">     206</span>              :           /// Push and replace the [Selfie Verification Screen]</span>
<span id="L207"><span class="lineNum">     207</span> <span class="tlaUNC">           0 :           DOPNativeSelfieVerifySuccessScreen.pushReplacementNamed();</span></span>
<span id="L208"><span class="lineNum">     208</span>              :         },</span>
<span id="L209"><span class="lineNum">     209</span> <span class="tlaUNC">           0 :         onFailed: (EkycErrorUIModel ekycErrorUIModel, _) {</span></span>
<span id="L210"><span class="lineNum">     210</span>              :           /// Replace the [Selfie Verification Screen] to failure screen</span>
<span id="L211"><span class="lineNum">     211</span> <span class="tlaUNC">           0 :           _onSelfieVerificationFailed(ekycErrorUIModel, isReplaceCurrentScreen: true);</span></span>
<span id="L212"><span class="lineNum">     212</span>              :         },</span>
<span id="L213"><span class="lineNum">     213</span>              :       );</span>
<span id="L214"><span class="lineNum">     214</span>              :       return;</span>
<span id="L215"><span class="lineNum">     215</span>              :     }</span>
<span id="L216"><span class="lineNum">     216</span>              : </span>
<span id="L217"><span class="lineNum">     217</span> <span class="tlaUNC">           0 :     if (state is DopNativeSelfieCaptureFailure) {</span></span>
<span id="L218"><span class="lineNum">     218</span> <span class="tlaUNC">           0 :       _handleEkycError(failReason: state.tvSDKResult.failReason);</span></span>
<span id="L219"><span class="lineNum">     219</span>              :     }</span>
<span id="L220"><span class="lineNum">     220</span>              :   }</span>
<span id="L221"><span class="lineNum">     221</span>              : </span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _handleCameraPermission(CameraPermissionState state) async {</span></span>
<span id="L223"><span class="lineNum">     223</span>              :     switch (state) {</span>
<span id="L224"><span class="lineNum">     224</span> <span class="tlaUNC">           0 :       case CameraPermissionGrantedState():</span></span>
<span id="L225"><span class="lineNum">     225</span> <span class="tlaUNC">           0 :         _selfieCaptureCubit.startCaptureSelfie(livenessMode: widget.livenessMode);</span></span>
<span id="L226"><span class="lineNum">     226</span>              :         break;</span>
<span id="L227"><span class="lineNum">     227</span> <span class="tlaUNC">           0 :       case CameraPermissionDeniedState():</span></span>
<span id="L228"><span class="lineNum">     228</span> <span class="tlaUNC">           0 :         await _showOpenAppSettingDialog();</span></span>
<span id="L229"><span class="lineNum">     229</span>              :         break;</span>
<span id="L230"><span class="lineNum">     230</span> <span class="tlaUNC">           0 :       case CameraPermissionInitialState():</span></span>
<span id="L231"><span class="lineNum">     231</span>              :       default:</span>
<span id="L232"><span class="lineNum">     232</span>              :     }</span>
<span id="L233"><span class="lineNum">     233</span>              :   }</span>
<span id="L234"><span class="lineNum">     234</span>              : </span>
<span id="L235"><span class="lineNum">     235</span> <span class="tlaUNC">           0 :   void _handleEkycError({TVSDKFailReason? failReason}) {</span></span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaUNC">           0 :     if (failReason == TVSDKFailReason.userCancelled) {</span></span>
<span id="L237"><span class="lineNum">     237</span>              :       return;</span>
<span id="L238"><span class="lineNum">     238</span>              :     }</span>
<span id="L239"><span class="lineNum">     239</span>              : </span>
<span id="L240"><span class="lineNum">     240</span> <span class="tlaUNC">           0 :     _onSelfieVerificationFailed(const EkycErrorUIModel(</span></span>
<span id="L241"><span class="lineNum">     241</span>              :       code: EkycErrorCode.otherEkycError,</span>
<span id="L242"><span class="lineNum">     242</span>              :     ));</span>
<span id="L243"><span class="lineNum">     243</span>              :   }</span>
<span id="L244"><span class="lineNum">     244</span>              : </span>
<span id="L245"><span class="lineNum">     245</span> <span class="tlaUNC">           0 :   void _onSelfieVerificationFailed(EkycErrorUIModel error, {bool isReplaceCurrentScreen = false}) {</span></span>
<span id="L246"><span class="lineNum">     246</span> <span class="tlaUNC">           0 :     switch (error.code) {</span></span>
<span id="L247"><span class="lineNum">     247</span> <span class="tlaUNC">           0 :       case EkycErrorCode.locked:</span></span>
<span id="L248"><span class="lineNum">     248</span> <span class="tlaUNC">           0 :         DOPNativeEKYCLimitExceedScreen.pushReplacementNamed();</span></span>
<span id="L249"><span class="lineNum">     249</span>              :         break;</span>
<span id="L250"><span class="lineNum">     250</span> <span class="tlaUNC">           0 :       case EkycErrorCode.selfieFailedMatching:</span></span>
<span id="L251"><span class="lineNum">     251</span> <span class="tlaUNC">           0 :         DOPNativeFailureScreen.pushReplacementNamed();</span></span>
<span id="L252"><span class="lineNum">     252</span>              :         break;</span>
<span id="L253"><span class="lineNum">     253</span>              : </span>
<span id="L254"><span class="lineNum">     254</span>              :       /// With other error, we will show the error screen with CTA &quot;Retry&quot;</span>
<span id="L255"><span class="lineNum">     255</span>              :       /// If the application state changed to any state (Eg: locked,failure...), user retry id card steps -&gt; the upload API will return</span>
<span id="L256"><span class="lineNum">     256</span>              :       /// error `409 application state is changed`</span>
<span id="L257"><span class="lineNum">     257</span>              :       /// So to prevent this issue &amp; improve UI/UX, when user click on the CTA, we will check Application State to determine the next action</span>
<span id="L258"><span class="lineNum">     258</span>              :       /// in case of [DOPNativeNavigationStep.ekycSelfieFlash], we will retry the id capturing</span>
<span id="L259"><span class="lineNum">     259</span>              :       /// otherwise, we will navigate to the next screen based on the application state</span>
<span id="L260"><span class="lineNum">     260</span> <span class="tlaUNC">           0 :       case EkycErrorCode.otherEkycError:</span></span>
<span id="L261"><span class="lineNum">     261</span> <span class="tlaUNC">           0 :         _onEkycError(</span></span>
<span id="L262"><span class="lineNum">     262</span> <span class="tlaUNC">           0 :           errMessage: error.message,</span></span>
<span id="L263"><span class="lineNum">     263</span>              :           isReplaceCurrentScreen: isReplaceCurrentScreen,</span>
<span id="L264"><span class="lineNum">     264</span>              :         );</span>
<span id="L265"><span class="lineNum">     265</span>              :         break;</span>
<span id="L266"><span class="lineNum">     266</span>              :       // common DOP Error</span>
<span id="L267"><span class="lineNum">     267</span>              :       case EkycErrorCode.network:</span>
<span id="L268"><span class="lineNum">     268</span>              :       case EkycErrorCode.invalidToken:</span>
<span id="L269"><span class="lineNum">     269</span>              :       case EkycErrorCode.commonError:</span>
<span id="L270"><span class="lineNum">     270</span>              :       case EkycErrorCode.limitExceed:</span>
<span id="L271"><span class="lineNum">     271</span>              :       default:</span>
<span id="L272"><span class="lineNum">     272</span>              : </span>
<span id="L273"><span class="lineNum">     273</span>              :         /// Replace the current screen with the error screen</span>
<span id="L274"><span class="lineNum">     274</span> <span class="tlaUNC">           0 :         handleEvoApiError(error.toErrorUIModel(), isReplaceCurrentScreen: true);</span></span>
<span id="L275"><span class="lineNum">     275</span>              :         break;</span>
<span id="L276"><span class="lineNum">     276</span>              :     }</span>
<span id="L277"><span class="lineNum">     277</span>              :   }</span>
<span id="L278"><span class="lineNum">     278</span>              : </span>
<span id="L279"><span class="lineNum">     279</span> <span class="tlaUNC">           0 :   void _onRetrySelfieVerification() {</span></span>
<span id="L280"><span class="lineNum">     280</span>              :     // close error screen</span>
<span id="L281"><span class="lineNum">     281</span> <span class="tlaUNC">           0 :     navigatorContext?.pop();</span></span>
<span id="L282"><span class="lineNum">     282</span>              : </span>
<span id="L283"><span class="lineNum">     283</span> <span class="tlaUNC">           0 :     _selfieCaptureCubit.startCaptureSelfie(livenessMode: widget.livenessMode);</span></span>
<span id="L284"><span class="lineNum">     284</span>              :   }</span>
<span id="L285"><span class="lineNum">     285</span>              : </span>
<span id="L286"><span class="lineNum">     286</span> <span class="tlaUNC">           0 :   void _onEkycError({</span></span>
<span id="L287"><span class="lineNum">     287</span>              :     String? errMessage,</span>
<span id="L288"><span class="lineNum">     288</span>              :     bool isReplaceCurrentScreen = false,</span>
<span id="L289"><span class="lineNum">     289</span>              :   }) {</span>
<span id="L290"><span class="lineNum">     290</span>              :     final DOPNativeStatusScreenArg statusScreenArg =</span>
<span id="L291"><span class="lineNum">     291</span> <span class="tlaUNC">           0 :         _generateSelfieCaptureErrorScreenArg(errMessage: errMessage);</span></span>
<span id="L292"><span class="lineNum">     292</span>              : </span>
<span id="L293"><span class="lineNum">     293</span>              :     if (isReplaceCurrentScreen) {</span>
<span id="L294"><span class="lineNum">     294</span> <span class="tlaUNC">           0 :       DOPNativeStatusScreen.pushReplacementNamed(arg: statusScreenArg);</span></span>
<span id="L295"><span class="lineNum">     295</span>              :       return;</span>
<span id="L296"><span class="lineNum">     296</span>              :     }</span>
<span id="L297"><span class="lineNum">     297</span>              : </span>
<span id="L298"><span class="lineNum">     298</span> <span class="tlaUNC">           0 :     DOPNativeStatusScreen.pushNamed(arg: statusScreenArg);</span></span>
<span id="L299"><span class="lineNum">     299</span>              :   }</span>
<span id="L300"><span class="lineNum">     300</span>              : </span>
<span id="L301"><span class="lineNum">     301</span> <span class="tlaUNC">           0 :   DOPNativeStatusScreenArg _generateSelfieCaptureErrorScreenArg({String? errMessage}) {</span></span>
<span id="L302"><span class="lineNum">     302</span> <span class="tlaUNC">           0 :     return DOPNativeStatusScreenArg(</span></span>
<span id="L303"><span class="lineNum">     303</span>              :       icon: DOPNativeImages.imgInvalidCaptured,</span>
<span id="L304"><span class="lineNum">     304</span>              :       title: DOPNativeStrings.dopNativeEkycInvalidImageCapturedTitle,</span>
<span id="L305"><span class="lineNum">     305</span>              :       description: errMessage ?? DOPNativeStrings.dopNativeDefaultSelfieEkycErrorMessage,</span>
<span id="L306"><span class="lineNum">     306</span> <span class="tlaUNC">           0 :       iconHeight: EvoUiUtils().calculateVerticalSpace(</span></span>
<span id="L307"><span class="lineNum">     307</span> <span class="tlaUNC">           0 :         context: context,</span></span>
<span id="L308"><span class="lineNum">     308</span>              :         heightPercentage: DOPNativeConstants.statusIconHeightPercentage,</span>
<span id="L309"><span class="lineNum">     309</span>              :       ),</span>
<span id="L310"><span class="lineNum">     310</span> <span class="tlaUNC">           0 :       ctaWidget: CameraIconButtonWidget(</span></span>
<span id="L311"><span class="lineNum">     311</span> <span class="tlaUNC">           0 :         onPressed: () {</span></span>
<span id="L312"><span class="lineNum">     312</span> <span class="tlaUNC">           0 :           dopNativeApplicationStateCubit.getApplicationState();</span></span>
<span id="L313"><span class="lineNum">     313</span>              :         },</span>
<span id="L314"><span class="lineNum">     314</span>              :       ),</span>
<span id="L315"><span class="lineNum">     315</span>              :     );</span>
<span id="L316"><span class="lineNum">     316</span>              :   }</span>
<span id="L317"><span class="lineNum">     317</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
