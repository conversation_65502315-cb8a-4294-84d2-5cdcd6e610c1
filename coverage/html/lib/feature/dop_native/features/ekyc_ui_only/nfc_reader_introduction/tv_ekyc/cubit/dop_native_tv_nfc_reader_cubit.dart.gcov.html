<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/tv_ekyc/cubit/dop_native_tv_nfc_reader_cubit.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/tv_ekyc/cubit">lib/feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/tv_ekyc/cubit</a> - dop_native_tv_nfc_reader_cubit.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryMed">89.0&nbsp;%</td>
            <td class="headerCovTableEntry">73</td>
            <td class="headerCovTableEntry">65</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/widgets.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/base/bloc_state.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/base/common_cubit.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/data/http_client/mock_config.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : </span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../../../../../data/repository/dop_native_repo/dop_native_repo.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../../../../../data/request/dop_native/dop_native_verify_nfc_request.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../../../../../data/response/dop_native/dop_native_application_form_data_entity.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../../../../../../data/response/dop_native/dop_native_personal_info_entity.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../../../../../../prepare_for_app_initiation.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../../../../../logging/evo_logging_event.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../../../../util/dop_functions.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../../../../cif_confirm/mock_file/mock_dop_native_application_form_data_use_case.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../../../sdk_bridge/tv_ekyc/ekyc_ui_only_bridge.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../../constants/dop_native_nfc_entry_point.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../../models/dop_native_nfc_shared_model.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import '../mock/mock_dop_native_verify_nfc_reader_use_case.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span>              : part 'dop_native_tv_nfc_reader_state.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              : class DOPNativeTvNFCReaderCubit extends CommonCubit&lt;DOPNativeTvNFCReaderState&gt; {</span>
<span id="L27"><span class="lineNum">      27</span>              :   final DopNativeEkycUIOnlyRepo dopNativeEkycUIOnlyRepo;</span>
<span id="L28"><span class="lineNum">      28</span>              :   final DOPNativeRepo dopNativeRepo;</span>
<span id="L29"><span class="lineNum">      29</span>              :   final AppState appState;</span>
<span id="L30"><span class="lineNum">      30</span>              :   final EkycUiOnlyBridge ekycUiOnlyBridge;</span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           1 :   DOPNativeTvNFCReaderCubit({</span></span>
<span id="L33"><span class="lineNum">      33</span>              :     required this.ekycUiOnlyBridge,</span>
<span id="L34"><span class="lineNum">      34</span>              :     required this.dopNativeEkycUIOnlyRepo,</span>
<span id="L35"><span class="lineNum">      35</span>              :     required this.appState,</span>
<span id="L36"><span class="lineNum">      36</span>              :     required this.dopNativeRepo,</span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaGNC">           2 :   }) : super(DOPNativeTvNFCReaderInitial());</span></span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span>              :   /// Start: Get NFC Shared Model</span>
<span id="L40"><span class="lineNum">      40</span>              :   String? _idCardNumber;</span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           2 :   String get idCardNumber =&gt; _idCardNumber ?? '';</span></span>
<span id="L43"><span class="lineNum">      43</span>              : </span>
<span id="L44"><span class="lineNum">      44</span>              :   String? _dateOfBirth;</span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           2 :   String? get dateOfBirth =&gt; _dateOfBirth;</span></span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span>              :   String? _dateOfExpiry;</span>
<span id="L49"><span class="lineNum">      49</span>              : </span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           2 :   String? get dateOfExpiry =&gt; _dateOfExpiry;</span></span>
<span id="L51"><span class="lineNum">      51</span>              : </span>
<span id="L52"><span class="lineNum">      52</span>              :   String? _accessToken;</span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :   String? get accessToken =&gt; _accessToken;</span></span>
<span id="L55"><span class="lineNum">      55</span>              : </span>
<span id="L56"><span class="lineNum">      56</span>              :   /// End: Get NFC Shared Model</span>
<span id="L57"><span class="lineNum">      57</span>              : </span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :   void initial(NFCEntryPoint entryPoint) {</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :     if (entryPoint == NFCEntryPoint.dopWebView) {</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :       final bool hasAccessToken = receiveAccessTokenFromDOPWebView();</span></span>
<span id="L61"><span class="lineNum">      61</span>              : </span>
<span id="L62"><span class="lineNum">      62</span>              :       if (hasAccessToken) {</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :         retrieveNFCSharedModel();</span></span>
<span id="L64"><span class="lineNum">      64</span>              :       }</span>
<span id="L65"><span class="lineNum">      65</span>              : </span>
<span id="L66"><span class="lineNum">      66</span>              :       return;</span>
<span id="L67"><span class="lineNum">      67</span>              :     }</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :     if (entryPoint == NFCEntryPoint.dopNative) {</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :       getDOPNativeFormData();</span></span>
<span id="L71"><span class="lineNum">      71</span>              :       return;</span>
<span id="L72"><span class="lineNum">      72</span>              :     }</span>
<span id="L73"><span class="lineNum">      73</span>              :   }</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; verifyNFC({</span></span>
<span id="L76"><span class="lineNum">      76</span>              :     required String? sod,</span>
<span id="L77"><span class="lineNum">      77</span>              :     required String? dg1,</span>
<span id="L78"><span class="lineNum">      78</span>              :     required String? dg2,</span>
<span id="L79"><span class="lineNum">      79</span>              :     required String? dg13,</span>
<span id="L80"><span class="lineNum">      80</span>              :     required String? dg14,</span>
<span id="L81"><span class="lineNum">      81</span>              :     required String? dg15,</span>
<span id="L82"><span class="lineNum">      82</span>              :     required String? com,</span>
<span id="L83"><span class="lineNum">      83</span>              :     required String? cccd,</span>
<span id="L84"><span class="lineNum">      84</span>              :     required String? cloneStatus,</span>
<span id="L85"><span class="lineNum">      85</span>              :   }) async {</span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaGNC">           2 :     emit(DOPNativeTvNFCReaderLoading());</span></span>
<span id="L87"><span class="lineNum">      87</span>              : </span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           1 :     final DOPNativeVerifyNFCRequest request = DOPNativeVerifyNFCRequest(</span></span>
<span id="L89"><span class="lineNum">      89</span>              :       sod: sod,</span>
<span id="L90"><span class="lineNum">      90</span>              :       dg1: dg1,</span>
<span id="L91"><span class="lineNum">      91</span>              :       dg2: dg2,</span>
<span id="L92"><span class="lineNum">      92</span>              :       dg13: dg13,</span>
<span id="L93"><span class="lineNum">      93</span>              :       dg14: dg14,</span>
<span id="L94"><span class="lineNum">      94</span>              :       dg15: dg15,</span>
<span id="L95"><span class="lineNum">      95</span>              :       com: com,</span>
<span id="L96"><span class="lineNum">      96</span>              :       cccd: cccd,</span>
<span id="L97"><span class="lineNum">      97</span>              :       cloneStatus: cloneStatus,</span>
<span id="L98"><span class="lineNum">      98</span>              :     );</span>
<span id="L99"><span class="lineNum">      99</span>              : </span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaGNC">           2 :     final BaseEntity entity = await dopNativeEkycUIOnlyRepo.verifyNFC(</span></span>
<span id="L101"><span class="lineNum">     101</span>              :       request: request,</span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaGNC">           1 :       mockConfig: MockConfig(</span></span>
<span id="L103"><span class="lineNum">     103</span>              :         enable: false,</span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaGNC">           1 :         fileName: getMockDOPNativeVerifyNFC(MockDOPNativeVerifyNFC.verifyNFCSuccess),</span></span>
<span id="L105"><span class="lineNum">     105</span>              :       ),</span>
<span id="L106"><span class="lineNum">     106</span>              :     );</span>
<span id="L107"><span class="lineNum">     107</span>              : </span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaGNC">           2 :     if (entity.statusCode == CommonHttpClient.SUCCESS &amp;&amp;</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaGNC">           2 :         entity.verdict == BaseEntity.verdictSuccess) {</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaGNC">           2 :       emit(DOPNativeTvNFCReaderSuccess());</span></span>
<span id="L111"><span class="lineNum">     111</span>              :     } else {</span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaGNC">           3 :       emit(DOPNativeTvNFCReaderFailed(error: ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L113"><span class="lineNum">     113</span>              :     }</span>
<span id="L114"><span class="lineNum">     114</span>              :   }</span>
<span id="L115"><span class="lineNum">     115</span>              : </span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L117"><span class="lineNum">     117</span>              :   bool receiveAccessTokenFromDOPWebView() {</span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaGNC">           3 :     final DOPNativeNFCSharedModel? nfcSharedModel = appState.dopNativeState.nfcSharedModel;</span></span>
<span id="L119"><span class="lineNum">     119</span>              : </span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaGNC">           2 :     _accessToken = nfcSharedModel?.accessToken;</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaGNC">           4 :     if (_accessToken == null || _accessToken?.isEmpty == true) {</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaGNC">           2 :       emit(DOPNativeTvNFCReaderUnableToStart());</span></span>
<span id="L123"><span class="lineNum">     123</span>              :       return false;</span>
<span id="L124"><span class="lineNum">     124</span>              :     }</span>
<span id="L125"><span class="lineNum">     125</span>              : </span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaGNC">           3 :     dopUtilFunction.setDOPNativeAccessTokenHeader(_accessToken);</span></span>
<span id="L127"><span class="lineNum">     127</span>              : </span>
<span id="L128"><span class="lineNum">     128</span>              :     return true;</span>
<span id="L129"><span class="lineNum">     129</span>              :   }</span>
<span id="L130"><span class="lineNum">     130</span>              : </span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaGNC">           1 :   void retrieveNFCSharedModel() {</span></span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaGNC">           3 :     final DOPNativeNFCSharedModel? nfcSharedModel = appState.dopNativeState.nfcSharedModel;</span></span>
<span id="L133"><span class="lineNum">     133</span>              : </span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaGNC">           1 :     final bool isValid = checkNFCSharedModelIsValid(</span></span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaGNC">           1 :       idCardNumber: nfcSharedModel?.nationalIdNumber,</span></span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaGNC">           1 :       dateOfBirth: nfcSharedModel?.dateOfBirth,</span></span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaGNC">           1 :       dateOfExpiry: nfcSharedModel?.dateOfExpiryIdCard,</span></span>
<span id="L138"><span class="lineNum">     138</span>              :     );</span>
<span id="L139"><span class="lineNum">     139</span>              : </span>
<span id="L140"><span class="lineNum">     140</span>              :     final String? convertedDateOfBirth =</span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaGNC">           3 :         dopUtilFunction.convertDOPToEKYCDateFormat(nfcSharedModel?.dateOfBirth);</span></span>
<span id="L142"><span class="lineNum">     142</span>              :     final String? convertedDateOfExpiry =</span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaGNC">           3 :         dopUtilFunction.convertDOPToEKYCDateFormat(nfcSharedModel?.dateOfExpiryIdCard);</span></span>
<span id="L144"><span class="lineNum">     144</span>              : </span>
<span id="L145"><span class="lineNum">     145</span>              :     if (!isValid || convertedDateOfBirth == null || convertedDateOfExpiry == null) {</span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaGNC">           2 :       emit(DOPNativeTvNFCReaderUnableToStart());</span></span>
<span id="L147"><span class="lineNum">     147</span>              :       return;</span>
<span id="L148"><span class="lineNum">     148</span>              :     }</span>
<span id="L149"><span class="lineNum">     149</span>              : </span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaGNC">           2 :     _idCardNumber = nfcSharedModel?.nationalIdNumber;</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaGNC">           1 :     _dateOfBirth = convertedDateOfBirth;</span></span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaGNC">           1 :     _dateOfExpiry = convertedDateOfExpiry;</span></span>
<span id="L153"><span class="lineNum">     153</span>              : </span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaGNC">           2 :     emit(DOPNativeValidateTvNFCDataSuccess());</span></span>
<span id="L155"><span class="lineNum">     155</span>              :   }</span>
<span id="L156"><span class="lineNum">     156</span>              : </span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L158"><span class="lineNum">     158</span>              :   bool checkNFCSharedModelIsValid({</span>
<span id="L159"><span class="lineNum">     159</span>              :     String? idCardNumber,</span>
<span id="L160"><span class="lineNum">     160</span>              :     String? dateOfBirth,</span>
<span id="L161"><span class="lineNum">     161</span>              :     String? dateOfExpiry,</span>
<span id="L162"><span class="lineNum">     162</span>              :   }) {</span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaGNC">           1 :     if (!checkCCCDIsValid(idCardNumber)) {</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaGNC">           1 :       _logParamError('idCardNumber', idCardNumber);</span></span>
<span id="L165"><span class="lineNum">     165</span>              :       return false;</span>
<span id="L166"><span class="lineNum">     166</span>              :     }</span>
<span id="L167"><span class="lineNum">     167</span>              : </span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaGNC">           2 :     if (!dopUtilFunction.isCorrectDateDOPFormat(dateInputted: dateOfBirth)) {</span></span>
<span id="L169"><span class="lineNum">     169</span> <span class="tlaGNC">           1 :       _logParamError('dateOfBirth', dateOfBirth);</span></span>
<span id="L170"><span class="lineNum">     170</span>              :       return false;</span>
<span id="L171"><span class="lineNum">     171</span>              :     }</span>
<span id="L172"><span class="lineNum">     172</span>              : </span>
<span id="L173"><span class="lineNum">     173</span> <span class="tlaGNC">           2 :     if (!dopUtilFunction.isCorrectDateDOPFormat(dateInputted: dateOfExpiry)) {</span></span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaUNC">           0 :       _logParamError('dateOfExpiryIdCard', dateOfExpiry);</span></span>
<span id="L175"><span class="lineNum">     175</span>              :       return false;</span>
<span id="L176"><span class="lineNum">     176</span>              :     }</span>
<span id="L177"><span class="lineNum">     177</span>              : </span>
<span id="L178"><span class="lineNum">     178</span>              :     return true;</span>
<span id="L179"><span class="lineNum">     179</span>              :   }</span>
<span id="L180"><span class="lineNum">     180</span>              : </span>
<span id="L181"><span class="lineNum">     181</span> <span class="tlaGNC">           1 :   void _logParamError(String paramName, String? paramValue) {</span></span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaGNC">           3 :     getIt.get&lt;LoggingRepo&gt;().logErrorEvent(</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaGNC">           1 :       errorType: EvoEventType.dopNFC.name,</span></span>
<span id="L184"><span class="lineNum">     184</span> <span class="tlaGNC">           1 :       args: &lt;String, dynamic&gt;{</span></span>
<span id="L185"><span class="lineNum">     185</span>              :         'step': 'start',</span>
<span id="L186"><span class="lineNum">     186</span>              :         'error': 'invalid_params',</span>
<span id="L187"><span class="lineNum">     187</span>              :         'param_name': paramName,</span>
<span id="L188"><span class="lineNum">     188</span>              :         'param_value': paramValue ?? 'null',</span>
<span id="L189"><span class="lineNum">     189</span>              :       },</span>
<span id="L190"><span class="lineNum">     190</span>              :     );</span>
<span id="L191"><span class="lineNum">     191</span>              :   }</span>
<span id="L192"><span class="lineNum">     192</span>              : </span>
<span id="L193"><span class="lineNum">     193</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L194"><span class="lineNum">     194</span>              :   bool checkCCCDIsValid(String? idCard) {</span>
<span id="L195"><span class="lineNum">     195</span> <span class="tlaGNC">           1 :     if (idCard == null || idCard.isEmpty) {</span></span>
<span id="L196"><span class="lineNum">     196</span>              :       return false;</span>
<span id="L197"><span class="lineNum">     197</span>              :     }</span>
<span id="L198"><span class="lineNum">     198</span>              : </span>
<span id="L199"><span class="lineNum">     199</span> <span class="tlaGNC">           2 :     return dopUtilFunction.isVietnameseCitizenIdCardAfter2016(idCard);</span></span>
<span id="L200"><span class="lineNum">     200</span>              :   }</span>
<span id="L201"><span class="lineNum">     201</span>              : </span>
<span id="L202"><span class="lineNum">     202</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; checkNfcSupport() async {</span></span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaGNC">           2 :     emit(DOPNativeTvNFCReaderLoading());</span></span>
<span id="L204"><span class="lineNum">     204</span>              : </span>
<span id="L205"><span class="lineNum">     205</span> <span class="tlaGNC">           2 :     final bool isSupported = await ekycUiOnlyBridge.checkNfcSupport();</span></span>
<span id="L206"><span class="lineNum">     206</span>              : </span>
<span id="L207"><span class="lineNum">     207</span>              :     if (isSupported) {</span>
<span id="L208"><span class="lineNum">     208</span> <span class="tlaGNC">           2 :       emit(DOPNativeTvNFCSupported());</span></span>
<span id="L209"><span class="lineNum">     209</span>              :       return;</span>
<span id="L210"><span class="lineNum">     210</span>              :     }</span>
<span id="L211"><span class="lineNum">     211</span>              : </span>
<span id="L212"><span class="lineNum">     212</span> <span class="tlaGNC">           2 :     emit(DOPNativeTvNFCUnsupported());</span></span>
<span id="L213"><span class="lineNum">     213</span>              :   }</span>
<span id="L214"><span class="lineNum">     214</span>              : </span>
<span id="L215"><span class="lineNum">     215</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; getDOPNativeFormData() async {</span></span>
<span id="L216"><span class="lineNum">     216</span> <span class="tlaGNC">           2 :     emit(DOPNativeTvNFCFormDataLoading());</span></span>
<span id="L217"><span class="lineNum">     217</span>              : </span>
<span id="L218"><span class="lineNum">     218</span> <span class="tlaGNC">           2 :     final DOPNativeApplicationFormDataEntity entity = await dopNativeRepo.getApplicationFormData(</span></span>
<span id="L219"><span class="lineNum">     219</span> <span class="tlaGNC">           1 :       mockConfig: MockConfig(</span></span>
<span id="L220"><span class="lineNum">     220</span>              :         enable: false,</span>
<span id="L221"><span class="lineNum">     221</span> <span class="tlaGNC">           1 :         fileName: getMockDOPNativeApplicationFormData(</span></span>
<span id="L222"><span class="lineNum">     222</span>              :           MockDOPNativeApplicationFormData.getApplicationFormDataSuccess,</span>
<span id="L223"><span class="lineNum">     223</span>              :         ),</span>
<span id="L224"><span class="lineNum">     224</span>              :       ),</span>
<span id="L225"><span class="lineNum">     225</span>              :     );</span>
<span id="L226"><span class="lineNum">     226</span>              : </span>
<span id="L227"><span class="lineNum">     227</span> <span class="tlaGNC">           2 :     if (entity.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L228"><span class="lineNum">     228</span> <span class="tlaGNC">           2 :       final DOPNativePersonalInfoEntity? personalInfo = entity.formData?.personalInfo;</span></span>
<span id="L229"><span class="lineNum">     229</span>              : </span>
<span id="L230"><span class="lineNum">     230</span> <span class="tlaGNC">           5 :       getIt.get&lt;AppState&gt;().dopNativeState.nfcSharedModel = DOPNativeNFCSharedModel(</span></span>
<span id="L231"><span class="lineNum">     231</span> <span class="tlaGNC">           1 :         nationalIdNumber: personalInfo?.idCard,</span></span>
<span id="L232"><span class="lineNum">     232</span> <span class="tlaGNC">           1 :         dateOfBirth: personalInfo?.birthday,</span></span>
<span id="L233"><span class="lineNum">     233</span> <span class="tlaGNC">           1 :         dateOfExpiryIdCard: personalInfo?.idExpiryDate,</span></span>
<span id="L234"><span class="lineNum">     234</span>              :       );</span>
<span id="L235"><span class="lineNum">     235</span>              : </span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaGNC">           1 :       retrieveNFCSharedModel();</span></span>
<span id="L237"><span class="lineNum">     237</span>              :     } else {</span>
<span id="L238"><span class="lineNum">     238</span> <span class="tlaGNC">           3 :       emit(DOPNativeTvNFCFormDataFailed(error: ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L239"><span class="lineNum">     239</span>              :     }</span>
<span id="L240"><span class="lineNum">     240</span>              :   }</span>
<span id="L241"><span class="lineNum">     241</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
