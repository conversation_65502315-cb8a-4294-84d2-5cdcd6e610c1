<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/announcement/announcement_screen.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/announcement">lib/feature/announcement</a> - announcement_screen.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">77</td>
            <td class="headerCovTableEntry">77</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/base/page_base.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/global_key_provider.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../base/evo_page_state_base.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../prepare_for_app_initiation.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../resources/resources.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../util/ui_utils/evo_ui_utils.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../widget/evo_appbar.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../feature_toggle.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import 'reward/reward_announcement_screen.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import 'transaction/transaction_announcement_screen.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import 'utils/unread_announcement_checker.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import 'widget/no_announcement_widget.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span>              : enum AnnouncementTabType {</span>
<span id="L19"><span class="lineNum">      19</span>              :   reward(0),</span>
<span id="L20"><span class="lineNum">      20</span>              :   transaction(1);</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span>              :   final int value;</span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span>              :   const AnnouncementTabType(this.value);</span>
<span id="L25"><span class="lineNum">      25</span>              : }</span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span>              : class AnnouncementReloadController {</span>
<span id="L28"><span class="lineNum">      28</span>              :   void Function()? reloadData;</span>
<span id="L29"><span class="lineNum">      29</span>              : }</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span>              : class AnnouncementArg extends PageBaseArg {</span>
<span id="L32"><span class="lineNum">      32</span>              :   final int? id;</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           3 :   AnnouncementArg(this.id);</span></span>
<span id="L35"><span class="lineNum">      35</span>              : }</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span>              : class AnnouncementScreen extends PageBase {</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           2 :   static void pushNamed({int? id}) {</span></span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           6 :     return navigatorContext?.pushNamed(Screen.announcementListScreen.name,</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           2 :         extra: AnnouncementArg(id));</span></span>
<span id="L41"><span class="lineNum">      41</span>              :   }</span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span>              :   final int? id;</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           2 :   const AnnouncementScreen({super.key, this.id});</span></span>
<span id="L46"><span class="lineNum">      46</span>              : </span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           1 :   EvoPageStateBase&lt;AnnouncementScreen&gt; createState() =&gt; AnnouncementScreenState();</span></span>
<span id="L49"><span class="lineNum">      49</span>              : </span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L51"><span class="lineNum">      51</span>              :   EventTrackingScreenId get eventTrackingScreenId =&gt; EventTrackingScreenId.undefined;</span>
<span id="L52"><span class="lineNum">      52</span>              : </span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           2 :   RouteSettings get routeSettings =&gt; RouteSettings(name: Screen.announcementListScreen.routeName);</span></span>
<span id="L55"><span class="lineNum">      55</span>              : }</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span>              : @visibleForTesting</span>
<span id="L58"><span class="lineNum">      58</span>              : class AnnouncementScreenState extends EvoPageStateBase&lt;AnnouncementScreen&gt;</span>
<span id="L59"><span class="lineNum">      59</span>              :     with SingleTickerProviderStateMixin {</span>
<span id="L60"><span class="lineNum">      60</span>              :   final AnnouncementReloadController _rewardController = AnnouncementReloadController();</span>
<span id="L61"><span class="lineNum">      61</span>              :   final AnnouncementReloadController _transactionController = AnnouncementReloadController();</span>
<span id="L62"><span class="lineNum">      62</span>              :   late TabController _tabController;</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span>              :   final FeatureToggle _featureToggle = getIt.get&lt;FeatureToggle&gt;();</span>
<span id="L65"><span class="lineNum">      65</span>              :   final AppState _appState = getIt.get&lt;AppState&gt;();</span>
<span id="L66"><span class="lineNum">      66</span>              : </span>
<span id="L67"><span class="lineNum">      67</span>              :   final List&lt;String&gt; _listTabTitle = &lt;String&gt;[</span>
<span id="L68"><span class="lineNum">      68</span>              :     EvoStrings.bottomBarRewardLabel,</span>
<span id="L69"><span class="lineNum">      69</span>              :     EvoStrings.transactionTitle</span>
<span id="L70"><span class="lineNum">      70</span>              :   ];</span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span>              :   final UnreadAnnouncementChecker _unreadAnnouncementChecker =</span>
<span id="L73"><span class="lineNum">      73</span>              :       getIt.get&lt;UnreadAnnouncementChecker&gt;();</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span>              :   final double _paddingTopPercentage = 0.08;</span>
<span id="L76"><span class="lineNum">      76</span>              : </span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L78"><span class="lineNum">      78</span>              :   void initState() {</span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaGNC">           1 :     super.initState();</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaGNC">           4 :     _tabController = TabController(length: _listTabTitle.length, vsync: this);</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaGNC">           3 :     _tabController.addListener(_handleChangeTab);</span></span>
<span id="L82"><span class="lineNum">      82</span>              :   }</span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L85"><span class="lineNum">      85</span>              :   void dispose() {</span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaGNC">           3 :     _tabController.removeListener(_handleChangeTab);</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           1 :     super.dispose();</span></span>
<span id="L88"><span class="lineNum">      88</span>              :   }</span>
<span id="L89"><span class="lineNum">      89</span>              : </span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L91"><span class="lineNum">      91</span>              :   Widget getContentWidget(BuildContext context) {</span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaGNC">           2 :     return _appState.isUserLogIn</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaGNC">           2 :         ? _featureToggle.enableTransactionNotificationFeature</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           1 :             ? _buildTabBar()</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaGNC">           1 :             : _buildRewardAnnouncementScreen()</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaGNC">           1 :         : _itemNonUser();</span></span>
<span id="L97"><span class="lineNum">      97</span>              :   }</span>
<span id="L98"><span class="lineNum">      98</span>              : </span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaGNC">           1 :   Widget _buildRewardAnnouncementScreen() {</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaGNC">           1 :     return Scaffold(</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaGNC">           2 :       backgroundColor: evoColors.background,</span></span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaGNC">           1 :       appBar: EvoAppBar(title: EvoStrings.announcementListTitle),</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaGNC">           1 :       body: RewardAnnouncementScreen(</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaGNC">           2 :         touchedAnnouncementId: widget.id,</span></span>
<span id="L105"><span class="lineNum">     105</span>              :       ),</span>
<span id="L106"><span class="lineNum">     106</span>              :     );</span>
<span id="L107"><span class="lineNum">     107</span>              :   }</span>
<span id="L108"><span class="lineNum">     108</span>              : </span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaGNC">           1 :   Widget _buildTabBar() {</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaGNC">           1 :     return DefaultTabController(</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaGNC">           2 :       length: _listTabTitle.length,</span></span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaGNC">           1 :       child: Scaffold(</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaGNC">           2 :         backgroundColor: evoColors.background,</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaGNC">           2 :         appBar: EvoAppBar(title: EvoStrings.announcementListTitle, bottom: _itemTabBar()),</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaGNC">           1 :         body: Column(</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaGNC">           1 :           children: &lt;Widget&gt;[</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaGNC">           1 :             Container(</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaGNC">           4 :                 height: 1, width: context.screenWidth, color: evoColors.disableTextFieldBorder),</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaGNC">           2 :             Expanded(child: _itemBody())</span></span>
<span id="L120"><span class="lineNum">     120</span>              :           ],</span>
<span id="L121"><span class="lineNum">     121</span>              :         ),</span>
<span id="L122"><span class="lineNum">     122</span>              :       ),</span>
<span id="L123"><span class="lineNum">     123</span>              :     );</span>
<span id="L124"><span class="lineNum">     124</span>              :   }</span>
<span id="L125"><span class="lineNum">     125</span>              : </span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaGNC">           2 :   Widget _itemNonUser() =&gt; Scaffold(</span></span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaGNC">           2 :       backgroundColor: evoColors.background,</span></span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaGNC">           1 :       appBar: EvoAppBar(title: EvoStrings.announcementListTitle),</span></span>
<span id="L129"><span class="lineNum">     129</span>              :       body: const SafeArea(child: NoAnnouncementWidget()));</span>
<span id="L130"><span class="lineNum">     130</span>              : </span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaGNC">           2 :   PreferredSizeWidget _itemTabBar() =&gt; PreferredSize(</span></span>
<span id="L132"><span class="lineNum">     132</span>              :         preferredSize: const Size.fromHeight(kToolbarHeight),</span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaGNC">           1 :         child: Align(</span></span>
<span id="L134"><span class="lineNum">     134</span>              :             alignment: Alignment.centerLeft,</span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaGNC">           1 :             child: TabBar(</span></span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaGNC">           1 :                 controller: _tabController,</span></span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaGNC">           2 :                 onTap: (_) =&gt; _handleChangeTab(),</span></span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaGNC">           2 :                 labelStyle: evoTextStyles.h300(),</span></span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaGNC">           2 :                 labelColor: evoColors.textActive,</span></span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaGNC">           2 :                 indicatorColor: evoColors.primary,</span></span>
<span id="L141"><span class="lineNum">     141</span>              :                 indicatorSize: TabBarIndicatorSize.tab,</span>
<span id="L142"><span class="lineNum">     142</span>              :                 indicatorPadding: const EdgeInsets.symmetric(horizontal: 20),</span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaGNC">           2 :                 unselectedLabelColor: evoColors.textPassive,</span></span>
<span id="L144"><span class="lineNum">     144</span>              :                 labelPadding: const EdgeInsets.all(16),</span>
<span id="L145"><span class="lineNum">     145</span> <span class="tlaGNC">           4 :                 unselectedLabelStyle: evoTextStyles.bodyLarge(evoColors.textPassive),</span></span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaGNC">           1 :                 tabs: _listTab())),</span></span>
<span id="L147"><span class="lineNum">     147</span>              :       );</span>
<span id="L148"><span class="lineNum">     148</span>              : </span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaGNC">           2 :   List&lt;Widget&gt; _listTab() =&gt; _listTabTitle</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaGNC">           3 :       .map((String title) =&gt; Text(</span></span>
<span id="L151"><span class="lineNum">     151</span>              :             title,</span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaGNC">           1 :             key: Key(title),</span></span>
<span id="L153"><span class="lineNum">     153</span>              :           ))</span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaGNC">           1 :       .toList();</span></span>
<span id="L155"><span class="lineNum">     155</span>              : </span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaGNC">           4 :   Widget _itemBody() =&gt; TabBarView(controller: _tabController, children: &lt;Widget&gt;[</span></span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaGNC">           1 :         RewardAnnouncementScreen(</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaGNC">           1 :           controller: _rewardController,</span></span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaGNC">           2 :           touchedAnnouncementId: widget.id,</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaGNC">           1 :           paddingTop: _paddingTop,</span></span>
<span id="L161"><span class="lineNum">     161</span>              :         ),</span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaGNC">           1 :         TransactionAnnouncementScreen(</span></span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaGNC">           1 :           controller: _transactionController,</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaGNC">           1 :           paddingTop: _paddingTop,</span></span>
<span id="L165"><span class="lineNum">     165</span>              :         )</span>
<span id="L166"><span class="lineNum">     166</span>              :       ]);</span>
<span id="L167"><span class="lineNum">     167</span>              : </span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaGNC">           1 :   void _handleChangeTab() {</span></span>
<span id="L169"><span class="lineNum">     169</span>              :     /// True: while we're animating from [previousIndex] to [index]</span>
<span id="L170"><span class="lineNum">     170</span>              :     /// False: when animation completed</span>
<span id="L171"><span class="lineNum">     171</span> <span class="tlaGNC">           2 :     if (!_tabController.indexIsChanging) {</span></span>
<span id="L172"><span class="lineNum">     172</span> <span class="tlaGNC">           4 :       if (_tabController.index == AnnouncementTabType.reward.value) {</span></span>
<span id="L173"><span class="lineNum">     173</span> <span class="tlaGNC">           3 :         _rewardController.reloadData?.call();</span></span>
<span id="L174"><span class="lineNum">     174</span>              :       } else {</span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaGNC">           3 :         _transactionController.reloadData?.call();</span></span>
<span id="L176"><span class="lineNum">     176</span>              :       }</span>
<span id="L177"><span class="lineNum">     177</span>              :     }</span>
<span id="L178"><span class="lineNum">     178</span>              :   }</span>
<span id="L179"><span class="lineNum">     179</span>              : </span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L181"><span class="lineNum">     181</span>              :   void didPop() {</span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaGNC">           1 :     super.didPop();</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaGNC">           2 :     _unreadAnnouncementChecker.checkUnreadAnnouncement();</span></span>
<span id="L184"><span class="lineNum">     184</span>              :   }</span>
<span id="L185"><span class="lineNum">     185</span>              : </span>
<span id="L186"><span class="lineNum">     186</span> <span class="tlaGNC">           3 :   double get _paddingTop =&gt; EvoUiUtils().calculateVerticalSpace(</span></span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaGNC">           1 :         context: context,</span></span>
<span id="L188"><span class="lineNum">     188</span> <span class="tlaGNC">           1 :         heightPercentage: _paddingTopPercentage,</span></span>
<span id="L189"><span class="lineNum">     189</span>              :       );</span>
<span id="L190"><span class="lineNum">     190</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
