<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/announcement/reward/reward_announcement_cubit.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/announcement/reward">lib/feature/announcement/reward</a> - reward_announcement_cubit.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">47</td>
            <td class="headerCovTableEntry">47</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/base/common_cubit.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/data/http_client/mock_config.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../../data/repository/announcement_repo.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../../data/request/reward_request.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../data/response/announcement_entity.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../data/response/announcement_list_entity.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../model/type_load_list.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../resources/global.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../utils/mock_file/mock_announcement_file_name.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import 'reward_announcement_state.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span>              : class RewardCubit extends CommonCubit&lt;RewardState&gt; {</span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaGNC">           9 :   RewardCubit(this._announcementRepo) : super(RewardState());</span></span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              :   final AnnouncementRepo _announcementRepo;</span>
<span id="L20"><span class="lineNum">      20</span>              :   List&lt;AnnouncementEntity&gt; _announcements = &lt;AnnouncementEntity&gt;[];</span>
<span id="L21"><span class="lineNum">      21</span>              :   String _nextCursor = '';</span>
<span id="L22"><span class="lineNum">      22</span>              : </span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           3 :   @visibleForTesting</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">           3 :   String get nextCursor =&gt; _nextCursor;</span></span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           1 :   set nextCursor(String value) =&gt; _nextCursor = value;</span></span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaGNC">           1 :   List&lt;AnnouncementEntity&gt; get announcements =&gt; _announcements;</span></span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaGNC">           1 :   set announcements(List&lt;AnnouncementEntity&gt; value) =&gt; _announcements = value;</span></span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           3 :   Future&lt;void&gt; loadAnnouncements(</span></span>
<span id="L36"><span class="lineNum">      36</span>              :       {required LoadListType type, bool isShowLoading = false, int? touchedAnnouncementId}) async {</span>
<span id="L37"><span class="lineNum">      37</span>              :     if (isShowLoading) {</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           6 :       emit(RewardLoadingState());</span></span>
<span id="L39"><span class="lineNum">      39</span>              :     }</span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           3 :     clearDataAnnouncementAndNextCursor(type: type);</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           6 :     final AnnouncementListEntity entity = await _announcementRepo.getAnnouncements(</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           6 :         AnnouncementRequest(nextCursor: _nextCursor, limit: defaultNumberItemPerPage, status: null),</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           9 :         mockConfig: MockConfig(enable: false, fileName: getAnnouncementsMockFileName(_nextCursor)));</span></span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           6 :     if (entity.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           3 :       _handleAnnouncementResp(entity);</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           3 :       _updateTouchedAnnouncementItemStatus(touchedAnnouncementId);</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           6 :       emit(RewardLoadedState(</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           6 :           announcements: _announcements.toList(),</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           3 :           allowLoadMore: allowLoadMore(),</span></span>
<span id="L52"><span class="lineNum">      52</span>              :           loadListType: type));</span>
<span id="L53"><span class="lineNum">      53</span>              :     } else {</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           2 :       emit(</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaGNC">           2 :         RewardErrorState(</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           2 :           ErrorUIModel.fromEntity(entity),</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           2 :           type == LoadListType.refresh,</span></span>
<span id="L58"><span class="lineNum">      58</span>              :         ),</span>
<span id="L59"><span class="lineNum">      59</span>              :       );</span>
<span id="L60"><span class="lineNum">      60</span>              :     }</span>
<span id="L61"><span class="lineNum">      61</span>              :   }</span>
<span id="L62"><span class="lineNum">      62</span>              : </span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaGNC">           2 :   void updateReadStatus(AnnouncementEntity announcement) {</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaGNC">           6 :     _announcementRepo.updateAnnouncementStatus(announcement.id, AnnouncementEntity.statusRead,</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaGNC">           2 :         mockConfig: MockConfig(</span></span>
<span id="L66"><span class="lineNum">      66</span>              :           enable: false,</span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           2 :           fileName: announcementUpdateReadStatusMockFileName(),</span></span>
<span id="L68"><span class="lineNum">      68</span>              :         ));</span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">           4 :     _updateTouchedAnnouncementItemStatus(announcement.id);</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaGNC">           8 :     emit(RewardLoadedState(announcements: _announcements, allowLoadMore: allowLoadMore()));</span></span>
<span id="L71"><span class="lineNum">      71</span>              :   }</span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaGNC">           3 :   void _handleAnnouncementResp(AnnouncementListEntity announcementListEntity) {</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaGNC">           3 :     final List&lt;AnnouncementEntity&gt;? announcements = announcementListEntity.announcements;</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaGNC">           6 :     _nextCursor = announcementListEntity.nextCursor ?? '';</span></span>
<span id="L76"><span class="lineNum">      76</span>              : </span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaGNC">           3 :     if (announcements == null || announcements.isEmpty) {</span></span>
<span id="L78"><span class="lineNum">      78</span>              :       return;</span>
<span id="L79"><span class="lineNum">      79</span>              :     }</span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaGNC">           6 :     _announcements.addAll(announcements);</span></span>
<span id="L81"><span class="lineNum">      81</span>              :   }</span>
<span id="L82"><span class="lineNum">      82</span>              : </span>
<span id="L83"><span class="lineNum">      83</span>              :   /// If nextCursor is empty, it means that there is no more page</span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">           3 :   @visibleForTesting</span></span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaGNC">           9 :   bool allowLoadMore() =&gt; nextCursor.isNotEmpty == true;</span></span>
<span id="L86"><span class="lineNum">      86</span>              : </span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           3 :   void _updateTouchedAnnouncementItemStatus(int? id) {</span></span>
<span id="L88"><span class="lineNum">      88</span>              :     if (id != null) {</span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">          10 :       final int index = _announcements.indexWhere((AnnouncementEntity e) =&gt; e.id == id);</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           4 :       if (index != -1) {</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaGNC">           4 :         _announcements[index] =</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaGNC">           6 :             _announcements[index].copyWith(status: AnnouncementEntity.statusRead);</span></span>
<span id="L93"><span class="lineNum">      93</span>              :       }</span>
<span id="L94"><span class="lineNum">      94</span>              :     }</span>
<span id="L95"><span class="lineNum">      95</span>              :   }</span>
<span id="L96"><span class="lineNum">      96</span>              : </span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaGNC">           3 :   void clearDataAnnouncementAndNextCursor({required LoadListType type}) {</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaGNC">           3 :     if (type == LoadListType.refresh) {</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaGNC">           3 :       _nextCursor = '';</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaGNC">           6 :       _announcements.clear();</span></span>
<span id="L101"><span class="lineNum">     101</span>              :     }</span>
<span id="L102"><span class="lineNum">     102</span>              :   }</span>
<span id="L103"><span class="lineNum">     103</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
