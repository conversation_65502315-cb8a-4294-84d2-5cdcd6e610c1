<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/referral_program/referral_sharing/page/get_referral_link_success_page.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/referral_program/referral_sharing/page">lib/feature/referral_program/referral_sharing/page</a> - get_referral_link_success_page.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">57</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:barcode_widget/barcode_widget.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter/material.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/global_key_provider.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/resources/resources.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/util/extension.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/widget/widgets.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../../../resources/resources.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../../util/ui_utils/evo_ui_utils.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../../widget/evo_appbar.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span>              : class GetReferralLinkSuccessPage extends StatelessWidget {</span>
<span id="L13"><span class="lineNum">      13</span>              :   final String referralLink;</span>
<span id="L14"><span class="lineNum">      14</span>              :   final String message;</span>
<span id="L15"><span class="lineNum">      15</span>              :   final void Function(String)? onClickCopy;</span>
<span id="L16"><span class="lineNum">      16</span>              :   final void Function(String)? onClickShare;</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaUNC">           0 :   const GetReferralLinkSuccessPage({</span></span>
<span id="L19"><span class="lineNum">      19</span>              :     required this.referralLink,</span>
<span id="L20"><span class="lineNum">      20</span>              :     required this.message,</span>
<span id="L21"><span class="lineNum">      21</span>              :     super.key,</span>
<span id="L22"><span class="lineNum">      22</span>              :     this.onClickCopy,</span>
<span id="L23"><span class="lineNum">      23</span>              :     this.onClickShare,</span>
<span id="L24"><span class="lineNum">      24</span>              :   });</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L27"><span class="lineNum">      27</span>              :   Widget build(BuildContext context) {</span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :     return Stack(</span></span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaUNC">           0 :       children: &lt;Widget&gt;[</span></span>
<span id="L30"><span class="lineNum">      30</span>              :         // Background image</span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :         SizedBox(</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :           height: context.screenHeight,</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :           child: evoImageProvider.asset(</span></span>
<span id="L34"><span class="lineNum">      34</span>              :             EvoImages.bgReferralQRCode,</span>
<span id="L35"><span class="lineNum">      35</span>              :             fit: BoxFit.cover,</span>
<span id="L36"><span class="lineNum">      36</span>              :           ),</span>
<span id="L37"><span class="lineNum">      37</span>              :         ),</span>
<span id="L38"><span class="lineNum">      38</span>              :         // Your screen content</span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :         Scaffold(</span></span>
<span id="L40"><span class="lineNum">      40</span>              :           backgroundColor: Colors.transparent,</span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :           appBar: EvoAppBar(</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :             leading: CloseButton(</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :               color: evoColors.icon,</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :               onPressed: () {</span></span>
<span id="L45"><span class="lineNum">      45</span>              :                 /// back to previous screen</span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :                 navigatorContext?.pop();</span></span>
<span id="L47"><span class="lineNum">      47</span>              :               },</span>
<span id="L48"><span class="lineNum">      48</span>              :             ),</span>
<span id="L49"><span class="lineNum">      49</span>              :           ),</span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :           body: SafeArea(</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :             child: Padding(</span></span>
<span id="L52"><span class="lineNum">      52</span>              :               padding: const EdgeInsets.symmetric(horizontal: 20),</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :               child: Column(</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :                 children: &lt;Widget&gt;[</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :                   Text(</span></span>
<span id="L56"><span class="lineNum">      56</span>              :                     EvoStrings.referralQrCodeTitle,</span>
<span id="L57"><span class="lineNum">      57</span>              :                     textAlign: TextAlign.center,</span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :                     style: evoTextStyles.h500(),</span></span>
<span id="L59"><span class="lineNum">      59</span>              :                   ),</span>
<span id="L60"><span class="lineNum">      60</span>              :                   const SizedBox(height: 8),</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :                   Text(</span></span>
<span id="L62"><span class="lineNum">      62</span>              :                     EvoStrings.referralQrCodeDescription,</span>
<span id="L63"><span class="lineNum">      63</span>              :                     textAlign: TextAlign.center,</span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :                     style: evoTextStyles.bodyLarge(evoColors.textActive).copyWith(height: 1.5),</span></span>
<span id="L65"><span class="lineNum">      65</span>              :                   ),</span>
<span id="L66"><span class="lineNum">      66</span>              :                   const SizedBox(height: 20),</span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :                   _buildQrCodeFrame(context),</span></span>
<span id="L68"><span class="lineNum">      68</span>              :                   const SizedBox(height: 20),</span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :                   CommonButton(</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :                     onPressed: () {</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :                       onClickShare?.call(message);</span></span>
<span id="L72"><span class="lineNum">      72</span>              :                     },</span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :                     style: evoButtonStyles.primary(ButtonSize.large),</span></span>
<span id="L74"><span class="lineNum">      74</span>              :                     child: const Text(EvoStrings.sharingText),</span>
<span id="L75"><span class="lineNum">      75</span>              :                   ),</span>
<span id="L76"><span class="lineNum">      76</span>              :                 ],</span>
<span id="L77"><span class="lineNum">      77</span>              :               ),</span>
<span id="L78"><span class="lineNum">      78</span>              :             ),</span>
<span id="L79"><span class="lineNum">      79</span>              :           ),</span>
<span id="L80"><span class="lineNum">      80</span>              :         ),</span>
<span id="L81"><span class="lineNum">      81</span>              :       ],</span>
<span id="L82"><span class="lineNum">      82</span>              :     );</span>
<span id="L83"><span class="lineNum">      83</span>              :   }</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :   Widget _buildQrCodeFrame(BuildContext context) {</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :     final double responsiveQRCodeFrameHeight = EvoUiUtils().calculateVerticalSpace(</span></span>
<span id="L87"><span class="lineNum">      87</span>              :       heightPercentage: 0.41,</span>
<span id="L88"><span class="lineNum">      88</span>              :       context: context,</span>
<span id="L89"><span class="lineNum">      89</span>              :     );</span>
<span id="L90"><span class="lineNum">      90</span>              : </span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :     return Container(</span></span>
<span id="L92"><span class="lineNum">      92</span>              :       clipBehavior: Clip.hardEdge,</span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :       decoration: ShapeDecoration(</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :         color: evoColors.referralQrCodeBackground,</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :         shape: RoundedRectangleBorder(</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :           borderRadius: BorderRadius.circular(16),</span></span>
<span id="L97"><span class="lineNum">      97</span>              :         ),</span>
<span id="L98"><span class="lineNum">      98</span>              :       ),</span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :       child: Column(</span></span>
<span id="L100"><span class="lineNum">     100</span>              :         mainAxisAlignment: MainAxisAlignment.center,</span>
<span id="L101"><span class="lineNum">     101</span>              :         crossAxisAlignment: CrossAxisAlignment.stretch,</span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :         children: &lt;Widget&gt;[</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :           Container(</span></span>
<span id="L104"><span class="lineNum">     104</span>              :             height: responsiveQRCodeFrameHeight,</span>
<span id="L105"><span class="lineNum">     105</span>              :             padding: const EdgeInsets.all(55),</span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :             decoration: ShapeDecoration(</span></span>
<span id="L107"><span class="lineNum">     107</span>              :               color: Colors.white,</span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :               shape: RoundedRectangleBorder(</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :                 side: BorderSide(color: evoColors.primary),</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :                 borderRadius: BorderRadius.circular(16),</span></span>
<span id="L111"><span class="lineNum">     111</span>              :               ),</span>
<span id="L112"><span class="lineNum">     112</span>              :             ),</span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :             child: _qrCodeWidget(),</span></span>
<span id="L114"><span class="lineNum">     114</span>              :           ),</span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :           Padding(</span></span>
<span id="L116"><span class="lineNum">     116</span>              :             padding: const EdgeInsets.all(8),</span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :             child: Row(</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :               children: &lt;Widget&gt;[</span></span>
<span id="L119"><span class="lineNum">     119</span>              :                 const SizedBox(width: 12),</span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :                 Expanded(</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :                   child: Text(</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :                     referralLink,</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :                     style: evoTextStyles.bodyMedium(evoColors.referralQrCodeWhiteText),</span></span>
<span id="L124"><span class="lineNum">     124</span>              :                     overflow: TextOverflow.ellipsis,</span>
<span id="L125"><span class="lineNum">     125</span>              :                     maxLines: 1,</span>
<span id="L126"><span class="lineNum">     126</span>              :                   ),</span>
<span id="L127"><span class="lineNum">     127</span>              :                 ),</span>
<span id="L128"><span class="lineNum">     128</span>              :                 const SizedBox(width: 8),</span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :                 InkWell(</span></span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :                   onTap: () {</span></span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :                     onClickCopy?.call(referralLink);</span></span>
<span id="L132"><span class="lineNum">     132</span>              :                   },</span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :                   child: Padding(</span></span>
<span id="L134"><span class="lineNum">     134</span>              :                     padding: const EdgeInsets.all(12.0),</span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :                     child: evoImageProvider.asset(</span></span>
<span id="L136"><span class="lineNum">     136</span>              :                       EvoImages.icCopy,</span>
<span id="L137"><span class="lineNum">     137</span>              :                       width: 20,</span>
<span id="L138"><span class="lineNum">     138</span>              :                       fit: BoxFit.fitWidth,</span>
<span id="L139"><span class="lineNum">     139</span>              :                     ),</span>
<span id="L140"><span class="lineNum">     140</span>              :                   ),</span>
<span id="L141"><span class="lineNum">     141</span>              :                 ),</span>
<span id="L142"><span class="lineNum">     142</span>              :               ],</span>
<span id="L143"><span class="lineNum">     143</span>              :             ),</span>
<span id="L144"><span class="lineNum">     144</span>              :           ),</span>
<span id="L145"><span class="lineNum">     145</span>              :         ],</span>
<span id="L146"><span class="lineNum">     146</span>              :       ),</span>
<span id="L147"><span class="lineNum">     147</span>              :     );</span>
<span id="L148"><span class="lineNum">     148</span>              :   }</span>
<span id="L149"><span class="lineNum">     149</span>              : </span>
<span id="L150"><span class="lineNum">     150</span>              :   /// what is errorCorrectLevel Error correction in QR codes is designed to ensure</span>
<span id="L151"><span class="lineNum">     151</span>              :   /// that the information stored in the code can still be read even if the code is partially damaged, dirty, or distorted</span>
<span id="L152"><span class="lineNum">     152</span>              :   /// Example:</span>
<span id="L153"><span class="lineNum">     153</span>              :   /// for Level L(low): This level provides 7% error correction capacity,</span>
<span id="L154"><span class="lineNum">     154</span>              :   /// which means that up to 7% of the QR code can be damaged or unreadable, and the QR code reader can still recover the original information</span>
<span id="L155"><span class="lineNum">     155</span>              :   /// Level M (medium - 15% error correction capacity) -&gt; may be selected for product packaging or marketing materials.</span>
<span id="L156"><span class="lineNum">     156</span>              :   /// Level Q (quartile - 25%) or H (high - 30%) may be selected for factory environment where QR Code get dirty,</span>
<span id="L157"><span class="lineNum">     157</span>              :   /// Level L (low - 7%) may be selected for clean environment with the large amount of data.</span>
<span id="L158"><span class="lineNum">     158</span>              :   /// referral https://www.qrcode.com/en/about/error_correction.html</span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :   Widget _qrCodeWidget() {</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :     return BarcodeWidget(</span></span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaUNC">           0 :       barcode: Barcode.qrCode(</span></span>
<span id="L162"><span class="lineNum">     162</span>              :         errorCorrectLevel: BarcodeQRCorrectionLevel.medium,</span>
<span id="L163"><span class="lineNum">     163</span>              :       ),</span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :       data: referralLink,</span></span>
<span id="L165"><span class="lineNum">     165</span>              :     );</span>
<span id="L166"><span class="lineNum">     166</span>              :   }</span>
<span id="L167"><span class="lineNum">     167</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
