<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/util/evo_action_handler.dart</title>
  <link rel="stylesheet" type="text/css" href="../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/util">lib/util</a> - evo_action_handler.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">97.9&nbsp;%</td>
            <td class="headerCovTableEntry">146</td>
            <td class="headerCovTableEntry">143</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import '../feature/main_screen/main_screen_initial_action/show_cashback_list_bottom_sheet_action.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter/foundation.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/base/page_base.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/ui_model/action_model.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/util/functions.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : import '../feature/activated_pos_limit/activate_card_introduction/activate_card_introduction_screen.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../feature/activated_pos_limit/setup_pos_limit_introduction/setup_pos_limit_introduction_screen.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../feature/announcement/announcement_screen.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../feature/deep_link/deep_link_handler.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../feature/deep_link/deep_link_utils.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../feature/deep_link/model/deep_link_model.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../feature/dop_native/features/introduction/dop_native_introduction_screen.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../feature/dop_native/features/qrcode_scanner/non_login_qr_code_scanner_screen.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../feature/emi_management/detail_screen/emi_management_detail_screen.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../feature/emi_management/management_screen/emi_management_screen.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../feature/login/new_device/input_phone_number/input_phone_number_page.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../feature/login/old_device/login_on_old_device_screen.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../feature/main_screen/main_screen.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../feature/main_screen/main_screen_controller.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../feature/payment/qrcode_scanner/qrcode_scanner_screen.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import '../feature/profile/profile_settings/profile_settings_page.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import '../feature/promotion_list/promotion_list_page.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import '../feature/referral_program/referral_sharing/referral_sharing_screen.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import '../feature/transaction_detail/transaction_detail_screen.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : import '../feature/tutorial/tutorial_screen.dart';</span>
<span id="L27"><span class="lineNum">      27</span>              : import '../feature/webview/utils/webview_util.dart';</span>
<span id="L28"><span class="lineNum">      28</span>              : import '../model/evo_action_model.dart';</span>
<span id="L29"><span class="lineNum">      29</span>              : import '../prepare_for_app_initiation.dart';</span>
<span id="L30"><span class="lineNum">      30</span>              : import '../resources/resources.dart';</span>
<span id="L31"><span class="lineNum">      31</span>              : import 'evo_authentication_helper.dart';</span>
<span id="L32"><span class="lineNum">      32</span>              : import 'evo_snackbar.dart';</span>
<span id="L33"><span class="lineNum">      33</span>              : import 'secure_storage_helper/secure_storage_helper.dart';</span>
<span id="L34"><span class="lineNum">      34</span>              : import 'token_utils/jwt_helper.dart';</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span>              : class EvoActionHandler {</span>
<span id="L37"><span class="lineNum">      37</span>              :   static EvoActionHandler? _instance;</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">         234 :   static final EvoActionHandler _originalInstance = EvoActionHandler._internal();</span></span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">          17 :   factory EvoActionHandler() {</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           1 :     return _instance ??= _originalInstance;</span></span>
<span id="L43"><span class="lineNum">      43</span>              :   }</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">          78 :   EvoActionHandler._internal();</span></span>
<span id="L46"><span class="lineNum">      46</span>              : </span>
<span id="L47"><span class="lineNum">      47</span>              :   // Method to replace the singleton instance (for testing only)</span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">          86 :   @visibleForTesting</span></span>
<span id="L49"><span class="lineNum">      49</span>              :   static void setInstanceForTesting(EvoActionHandler instance) {</span>
<span id="L50"><span class="lineNum">      50</span>              :     _instance = instance;</span>
<span id="L51"><span class="lineNum">      51</span>              :   }</span>
<span id="L52"><span class="lineNum">      52</span>              : </span>
<span id="L53"><span class="lineNum">      53</span>              :   // Method to reset the singleton instance (for testing only)</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">          77 :   @visibleForTesting</span></span>
<span id="L55"><span class="lineNum">      55</span>              :   static void resetToOriginalInstance() {</span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">          77 :     _instance = _originalInstance;</span></span>
<span id="L57"><span class="lineNum">      57</span>              :   }</span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; handle(EvoActionModel action, {PageBaseArg? arg, int? idAnnouncement}) async {</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaGNC">           1 :     final PageBaseArg? newArg = await _processPageBaseArg(action, arg);</span></span>
<span id="L61"><span class="lineNum">      61</span>              :     if (newArg == null) {</span>
<span id="L62"><span class="lineNum">      62</span>              :       return false;</span>
<span id="L63"><span class="lineNum">      63</span>              :     }</span>
<span id="L64"><span class="lineNum">      64</span>              : </span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaGNC">           1 :     if (await _handleCommonAction(action, newArg)) {</span></span>
<span id="L66"><span class="lineNum">      66</span>              :       return true;</span>
<span id="L67"><span class="lineNum">      67</span>              :     }</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">           1 :     return _handleSpecificAction(action, idAnnouncement);</span></span>
<span id="L70"><span class="lineNum">      70</span>              :   }</span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaGNC">           1 :   Future&lt;PageBaseArg?&gt; _processPageBaseArg(EvoActionModel action, PageBaseArg? arg) async {</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaGNC">           1 :     final WebViewUtil webViewUtil = WebViewUtil();</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaGNC">           1 :     return await webViewUtil.processPageBaseArg(action: action, arg: arg);</span></span>
<span id="L75"><span class="lineNum">      75</span>              :   }</span>
<span id="L76"><span class="lineNum">      76</span>              : </span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; _handleCommonAction(EvoActionModel action, PageBaseArg? newArg) async {</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaGNC">           2 :     return await commonUtilFunction.handleAction(action, arg: newArg);</span></span>
<span id="L79"><span class="lineNum">      79</span>              :   }</span>
<span id="L80"><span class="lineNum">      80</span>              : </span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; _handleSpecificAction(EvoActionModel action, int? idAnnouncement) async {</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaGNC">           3 :     final bool isLoggedIn = getIt.get&lt;AppState&gt;().isUserLogIn;</span></span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">           1 :     switch (action.type) {</span></span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaGNC">           1 :       case EvoActionModel.openAppScreen:</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaGNC">           1 :         return await _handleOpenAppScreen(action, isLoggedIn, idAnnouncement);</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           1 :       case EvoActionModel.promotionCopyAndMarkUsed:</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           1 :         return await _handlePromotionCopyAndMarkUsed(action);</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">           1 :       case EvoActionModel.promotionScanToPay:</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           1 :         QrCodeScannerScreen.openSingleInstance();</span></span>
<span id="L91"><span class="lineNum">      91</span>              :         return true;</span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaGNC">           1 :       case EvoActionModel.openDeepLink:</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaGNC">           3 :         return await openDeepLink(action.args?.link);</span></span>
<span id="L94"><span class="lineNum">      94</span>              :       default:</span>
<span id="L95"><span class="lineNum">      95</span>              :         return false;</span>
<span id="L96"><span class="lineNum">      96</span>              :     }</span>
<span id="L97"><span class="lineNum">      97</span>              :   }</span>
<span id="L98"><span class="lineNum">      98</span>              : </span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; _handleOpenAppScreen(</span></span>
<span id="L100"><span class="lineNum">     100</span>              :       EvoActionModel action, bool isLoggedIn, int? idAnnouncement) async {</span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaGNC">           2 :     final String? screenName = action.args?.screenName;</span></span>
<span id="L102"><span class="lineNum">     102</span>              :     if (screenName == null) {</span>
<span id="L103"><span class="lineNum">     103</span>              :       return false;</span>
<span id="L104"><span class="lineNum">     104</span>              :     }</span>
<span id="L105"><span class="lineNum">     105</span>              : </span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaGNC">           1 :     final Screen screen = Screen.byValue(screenName);</span></span>
<span id="L107"><span class="lineNum">     107</span>              :     switch (screen) {</span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaGNC">           1 :       case Screen.loginScreen:</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaGNC">           1 :         await openAuthenticationScreen();</span></span>
<span id="L110"><span class="lineNum">     110</span>              :         return true;</span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaGNC">           1 :       case Screen.homeScreen:</span></span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaGNC">           1 :         MainScreen.pushReplacementNamed(isLoggedIn: isLoggedIn, initialPage: MainScreenChild.home);</span></span>
<span id="L113"><span class="lineNum">     113</span>              :         return true;</span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaGNC">           1 :       case Screen.allPromotionListScreen:</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaGNC">           1 :         return await _navigateToScreenWithLoginCheck(</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaGNC">           2 :           () =&gt; MainScreen.pushReplacementNamed(</span></span>
<span id="L117"><span class="lineNum">     117</span>              :               isLoggedIn: true, initialPage: MainScreenChild.promotion),</span>
<span id="L118"><span class="lineNum">     118</span>              :         );</span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaGNC">           1 :       case Screen.campaignListScreen:</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaGNC">           1 :         MainScreen.pushReplacementNamed(</span></span>
<span id="L121"><span class="lineNum">     121</span>              :             isLoggedIn: isLoggedIn, initialPage: MainScreenChild.promotion);</span>
<span id="L122"><span class="lineNum">     122</span>              :         return true;</span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaGNC">           1 :       case Screen.myPromotionListScreen:</span></span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaGNC">           1 :         return await _navigateToScreenWithLoginCheck(</span></span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaGNC">           2 :           () =&gt; MainScreen.pushReplacementNamed(</span></span>
<span id="L126"><span class="lineNum">     126</span>              :             isLoggedIn: true,</span>
<span id="L127"><span class="lineNum">     127</span>              :             initialPage: MainScreenChild.promotion,</span>
<span id="L128"><span class="lineNum">     128</span>              :             initialPromotionTab: PromotionTabType.myVoucher,</span>
<span id="L129"><span class="lineNum">     129</span>              :           ),</span>
<span id="L130"><span class="lineNum">     130</span>              :         );</span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaGNC">           1 :       case Screen.transactionHistoryListScreen:</span></span>
<span id="L132"><span class="lineNum">     132</span>              :         final bool shouldAutoOpenCashbackSheet =</span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaGNC">           3 :             action.args?.parameters?.shouldOpenCashbackSheet == true;</span></span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaGNC">           3 :         return await _navigateToScreenWithLoginCheck(() =&gt; MainScreen.pushReplacementNamed(</span></span>
<span id="L135"><span class="lineNum">     135</span>              :               isLoggedIn: true,</span>
<span id="L136"><span class="lineNum">     136</span>              :               initialPage: MainScreenChild.history,</span>
<span id="L137"><span class="lineNum">     137</span>              :               initialAction:</span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :                   shouldAutoOpenCashbackSheet ? ShowCashbackListBottomSheetAction() : null,</span></span>
<span id="L139"><span class="lineNum">     139</span>              :             ));</span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaGNC">           1 :       case Screen.transactionHistoryDetailScreen:</span></span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaGNC">           1 :         return await _handleTransactionHistoryDetailScreen(action);</span></span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaGNC">           1 :       case Screen.profileScreen:</span></span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaGNC">           1 :         return await _navigateToScreenWithLoginCheck(</span></span>
<span id="L144"><span class="lineNum">     144</span> <span class="tlaGNC">           2 :           () =&gt; MainScreen.pushReplacementNamed(</span></span>
<span id="L145"><span class="lineNum">     145</span>              :               isLoggedIn: true, initialPage: MainScreenChild.account),</span>
<span id="L146"><span class="lineNum">     146</span>              :         );</span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaGNC">           1 :       case Screen.qrCodeScannerScreen:</span></span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaGNC">           1 :         return await _navigateToScreenWithLoginCheck(QrCodeScannerScreen.openSingleInstance);</span></span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaGNC">           1 :       case Screen.announcementListScreen:</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaGNC">           1 :         AnnouncementScreen.pushNamed(id: idAnnouncement);</span></span>
<span id="L151"><span class="lineNum">     151</span>              :         return true;</span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaGNC">           1 :       case Screen.tutorialScreen:</span></span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaGNC">           1 :         TutorialScreen.goNamed();</span></span>
<span id="L154"><span class="lineNum">     154</span>              :         return true;</span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaGNC">           1 :       case Screen.profileSettingScreen:</span></span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaGNC">           1 :         return await _navigateToScreenWithLoginCheck(ProfileSettingPage.pushNamed);</span></span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaGNC">           1 :       case Screen.referralSharingScreen:</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaGNC">           1 :         return await _handleReferralSharingScreen(action);</span></span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaGNC">           1 :       case Screen.emiManagementListScreen:</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaGNC">           1 :         return await _navigateToScreenWithLoginCheck(EmiManagementScreen.pushNamed);</span></span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaGNC">           1 :       case Screen.emiManagementDetailScreen:</span></span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaGNC">           1 :         return await _handleEmiManagementDetailScreen(action);</span></span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaGNC">           1 :       case Screen.dopNativeIntroductionScreen:</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaGNC">           1 :         return _openDopNativeIntroductionScreen();</span></span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaGNC">           1 :       case Screen.nonLoginQrCodeScannerScreen:</span></span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaUNC">           0 :         NonLoginQrCodeScannerScreen.openSingleInstance();</span></span>
<span id="L167"><span class="lineNum">     167</span>              :         return true;</span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaGNC">           1 :       case Screen.activateCardScreen:</span></span>
<span id="L169"><span class="lineNum">     169</span> <span class="tlaGNC">           1 :         return await _handleActivateCardIntroductionScreen(action);</span></span>
<span id="L170"><span class="lineNum">     170</span> <span class="tlaGNC">           1 :       case Screen.setupPosLimitScreen:</span></span>
<span id="L171"><span class="lineNum">     171</span> <span class="tlaGNC">           1 :         return await _handleSetupPosLimitIntroductionScreen(action);</span></span>
<span id="L172"><span class="lineNum">     172</span>              :       default:</span>
<span id="L173"><span class="lineNum">     173</span>              :         return false;</span>
<span id="L174"><span class="lineNum">     174</span>              :     }</span>
<span id="L175"><span class="lineNum">     175</span>              :   }</span>
<span id="L176"><span class="lineNum">     176</span>              : </span>
<span id="L177"><span class="lineNum">     177</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; _navigateToScreenWithLoginCheck(void Function() actionToBeHandledAfterLogin) async {</span></span>
<span id="L178"><span class="lineNum">     178</span> <span class="tlaGNC">           1 :     await handleOpenAuthenticationScreenIfUserNotLoggedIn(</span></span>
<span id="L179"><span class="lineNum">     179</span>              :         actionToBeHandledAfterLoggedIn: actionToBeHandledAfterLogin);</span>
<span id="L180"><span class="lineNum">     180</span>              :     return true;</span>
<span id="L181"><span class="lineNum">     181</span>              :   }</span>
<span id="L182"><span class="lineNum">     182</span>              : </span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; _handleActivateCardIntroductionScreen(EvoActionModel action) async {</span></span>
<span id="L184"><span class="lineNum">     184</span> <span class="tlaGNC">           2 :     final EvoParameters? parameters = action.args?.parameters;</span></span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaGNC">           1 :     final int? posLimitAllow = parameters?.posLimitAllow;</span></span>
<span id="L186"><span class="lineNum">     186</span> <span class="tlaGNC">           1 :     final int? creditLimit = parameters?.creditLimit;</span></span>
<span id="L187"><span class="lineNum">     187</span>              : </span>
<span id="L188"><span class="lineNum">     188</span>              :     if (posLimitAllow == null || creditLimit == null) {</span>
<span id="L189"><span class="lineNum">     189</span>              :       return false;</span>
<span id="L190"><span class="lineNum">     190</span>              :     }</span>
<span id="L191"><span class="lineNum">     191</span>              : </span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaGNC">           2 :     return await _navigateToScreenWithLoginCheck(() async {</span></span>
<span id="L193"><span class="lineNum">     193</span> <span class="tlaGNC">           1 :       return ActivateCardIntroductionScreen.pushNamed(</span></span>
<span id="L194"><span class="lineNum">     194</span>              :         creditLimit: creditLimit,</span>
<span id="L195"><span class="lineNum">     195</span>              :         posLimit: posLimitAllow,</span>
<span id="L196"><span class="lineNum">     196</span>              :       );</span>
<span id="L197"><span class="lineNum">     197</span>              :     });</span>
<span id="L198"><span class="lineNum">     198</span>              :   }</span>
<span id="L199"><span class="lineNum">     199</span>              : </span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; _handleSetupPosLimitIntroductionScreen(EvoActionModel action) async {</span></span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaGNC">           2 :     final EvoParameters? parameters = action.args?.parameters;</span></span>
<span id="L202"><span class="lineNum">     202</span> <span class="tlaGNC">           1 :     final int? posLimitAllow = parameters?.posLimitAllow;</span></span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaGNC">           1 :     final int? creditLimit = parameters?.creditLimit;</span></span>
<span id="L204"><span class="lineNum">     204</span>              : </span>
<span id="L205"><span class="lineNum">     205</span>              :     if (posLimitAllow == null || creditLimit == null) {</span>
<span id="L206"><span class="lineNum">     206</span>              :       return false;</span>
<span id="L207"><span class="lineNum">     207</span>              :     }</span>
<span id="L208"><span class="lineNum">     208</span>              : </span>
<span id="L209"><span class="lineNum">     209</span> <span class="tlaGNC">           2 :     return await _navigateToScreenWithLoginCheck(() async {</span></span>
<span id="L210"><span class="lineNum">     210</span> <span class="tlaGNC">           1 :       return SetupPosLimitIntroductionScreen.pushNamed(</span></span>
<span id="L211"><span class="lineNum">     211</span>              :         creditLimit: creditLimit,</span>
<span id="L212"><span class="lineNum">     212</span>              :         posLimit: posLimitAllow,</span>
<span id="L213"><span class="lineNum">     213</span>              :       );</span>
<span id="L214"><span class="lineNum">     214</span>              :     });</span>
<span id="L215"><span class="lineNum">     215</span>              :   }</span>
<span id="L216"><span class="lineNum">     216</span>              : </span>
<span id="L217"><span class="lineNum">     217</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; _handleTransactionHistoryDetailScreen(EvoActionModel action) async {</span></span>
<span id="L218"><span class="lineNum">     218</span> <span class="tlaGNC">           2 :     final Parameters? parameters = action.args?.parameters;</span></span>
<span id="L219"><span class="lineNum">     219</span> <span class="tlaGNC">           1 :     final String? transactionId = parameters?.id;</span></span>
<span id="L220"><span class="lineNum">     220</span>              :     if (parameters == null || transactionId == null) {</span>
<span id="L221"><span class="lineNum">     221</span>              :       return false;</span>
<span id="L222"><span class="lineNum">     222</span>              :     }</span>
<span id="L223"><span class="lineNum">     223</span>              : </span>
<span id="L224"><span class="lineNum">     224</span> <span class="tlaGNC">           2 :     return await _navigateToScreenWithLoginCheck(() async {</span></span>
<span id="L225"><span class="lineNum">     225</span> <span class="tlaGNC">           1 :       return TransactionDetailScreen.pushNamed(transactionId: transactionId);</span></span>
<span id="L226"><span class="lineNum">     226</span>              :     });</span>
<span id="L227"><span class="lineNum">     227</span>              :   }</span>
<span id="L228"><span class="lineNum">     228</span>              : </span>
<span id="L229"><span class="lineNum">     229</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; _handleReferralSharingScreen(EvoActionModel action) async {</span></span>
<span id="L230"><span class="lineNum">     230</span> <span class="tlaGNC">           2 :     final Parameters? parameters = action.args?.parameters;</span></span>
<span id="L231"><span class="lineNum">     231</span> <span class="tlaGNC">           1 :     final String? campaignId = parameters?.id;</span></span>
<span id="L232"><span class="lineNum">     232</span>              :     if (parameters == null || campaignId == null) {</span>
<span id="L233"><span class="lineNum">     233</span>              :       return false;</span>
<span id="L234"><span class="lineNum">     234</span>              :     }</span>
<span id="L235"><span class="lineNum">     235</span>              : </span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaGNC">           2 :     return await _navigateToScreenWithLoginCheck(() {</span></span>
<span id="L237"><span class="lineNum">     237</span> <span class="tlaGNC">           1 :       ReferralSharingScreen.pushNamed(campaignId: campaignId);</span></span>
<span id="L238"><span class="lineNum">     238</span>              :     });</span>
<span id="L239"><span class="lineNum">     239</span>              :   }</span>
<span id="L240"><span class="lineNum">     240</span>              : </span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; _handleEmiManagementDetailScreen(EvoActionModel action) async {</span></span>
<span id="L242"><span class="lineNum">     242</span> <span class="tlaGNC">           2 :     final Parameters? parameters = action.args?.parameters;</span></span>
<span id="L243"><span class="lineNum">     243</span> <span class="tlaGNC">           1 :     final String? emiManagementDetailId = parameters?.id;</span></span>
<span id="L244"><span class="lineNum">     244</span>              :     if (parameters == null || emiManagementDetailId == null) {</span>
<span id="L245"><span class="lineNum">     245</span>              :       return false;</span>
<span id="L246"><span class="lineNum">     246</span>              :     }</span>
<span id="L247"><span class="lineNum">     247</span>              : </span>
<span id="L248"><span class="lineNum">     248</span> <span class="tlaGNC">           2 :     return await _navigateToScreenWithLoginCheck(() {</span></span>
<span id="L249"><span class="lineNum">     249</span> <span class="tlaGNC">           1 :       EmiManagementDetailScreen.pushNamed(id: emiManagementDetailId);</span></span>
<span id="L250"><span class="lineNum">     250</span>              :     });</span>
<span id="L251"><span class="lineNum">     251</span>              :   }</span>
<span id="L252"><span class="lineNum">     252</span>              : </span>
<span id="L253"><span class="lineNum">     253</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; _handlePromotionCopyAndMarkUsed(EvoActionModel action) async {</span></span>
<span id="L254"><span class="lineNum">     254</span> <span class="tlaGNC">           3 :     final String? voucherCode = action.args?.parameters?.code;</span></span>
<span id="L255"><span class="lineNum">     255</span>              :     if (voucherCode == null) {</span>
<span id="L256"><span class="lineNum">     256</span>              :       return false;</span>
<span id="L257"><span class="lineNum">     257</span>              :     }</span>
<span id="L258"><span class="lineNum">     258</span>              : </span>
<span id="L259"><span class="lineNum">     259</span> <span class="tlaGNC">           1 :     await handleCopyPromotionCode(voucherCode);</span></span>
<span id="L260"><span class="lineNum">     260</span>              :     return true;</span>
<span id="L261"><span class="lineNum">     261</span>              :   }</span>
<span id="L262"><span class="lineNum">     262</span>              : </span>
<span id="L263"><span class="lineNum">     263</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L264"><span class="lineNum">     264</span>              :   Future&lt;void&gt; handleCopyPromotionCode(String code) async {</span>
<span id="L265"><span class="lineNum">     265</span> <span class="tlaGNC">           2 :     commonUtilFunction.copyToClipboard(code);</span></span>
<span id="L266"><span class="lineNum">     266</span> <span class="tlaGNC">           3 :     getIt.get&lt;EvoSnackBar&gt;().show(</span></span>
<span id="L267"><span class="lineNum">     267</span>              :           EvoStrings.copyPromotionCodeSuccess,</span>
<span id="L268"><span class="lineNum">     268</span> <span class="tlaGNC">           1 :           durationInSec: SnackBarDuration.short.value,</span></span>
<span id="L269"><span class="lineNum">     269</span>              :         );</span>
<span id="L270"><span class="lineNum">     270</span>              :   }</span>
<span id="L271"><span class="lineNum">     271</span>              : </span>
<span id="L272"><span class="lineNum">     272</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; openAuthenticationScreen(</span></span>
<span id="L273"><span class="lineNum">     273</span>              :       {bool isClearNavigationStack = false, bool isAutoLoginWithBiometric = true}) async {</span>
<span id="L274"><span class="lineNum">     274</span> <span class="tlaGNC">           2 :     if (await EvoAuthenticationHelper().isCanLogInOnOldDevice()) {</span></span>
<span id="L275"><span class="lineNum">     275</span> <span class="tlaGNC">           1 :       return _openLoginOnOldDeviceScreen(</span></span>
<span id="L276"><span class="lineNum">     276</span>              :         isClearNavigationStack: isClearNavigationStack,</span>
<span id="L277"><span class="lineNum">     277</span>              :         isAutoLoginWithBiometric: isAutoLoginWithBiometric,</span>
<span id="L278"><span class="lineNum">     278</span>              :       );</span>
<span id="L279"><span class="lineNum">     279</span>              :     } else {</span>
<span id="L280"><span class="lineNum">     280</span>              :       final String? lastPhoneNumberLogged =</span>
<span id="L281"><span class="lineNum">     281</span> <span class="tlaGNC">           3 :           await getIt.get&lt;EvoLocalStorageHelper&gt;().getUserPhoneNumber();</span></span>
<span id="L282"><span class="lineNum">     282</span> <span class="tlaGNC">           3 :       await getIt.get&lt;EvoLocalStorageHelper&gt;().clearAllUserData();</span></span>
<span id="L283"><span class="lineNum">     283</span> <span class="tlaGNC">           1 :       return _openInputPhoneNumberScreen(</span></span>
<span id="L284"><span class="lineNum">     284</span>              :         isClearNavigationStack: isClearNavigationStack,</span>
<span id="L285"><span class="lineNum">     285</span>              :         lastPhoneNumberLogged: lastPhoneNumberLogged,</span>
<span id="L286"><span class="lineNum">     286</span>              :       );</span>
<span id="L287"><span class="lineNum">     287</span>              :     }</span>
<span id="L288"><span class="lineNum">     288</span>              :   }</span>
<span id="L289"><span class="lineNum">     289</span>              : </span>
<span id="L290"><span class="lineNum">     290</span> <span class="tlaGNC">           1 :   void _openLoginOnOldDeviceScreen(</span></span>
<span id="L291"><span class="lineNum">     291</span>              :       {bool isClearNavigationStack = false, bool isAutoLoginWithBiometric = true}) {</span>
<span id="L292"><span class="lineNum">     292</span>              :     if (isClearNavigationStack) {</span>
<span id="L293"><span class="lineNum">     293</span> <span class="tlaGNC">           1 :       LoginOnOldDeviceScreen.goNamed(</span></span>
<span id="L294"><span class="lineNum">     294</span>              :         isAutoLoginWithBiometric: isAutoLoginWithBiometric,</span>
<span id="L295"><span class="lineNum">     295</span>              :       );</span>
<span id="L296"><span class="lineNum">     296</span>              :     } else {</span>
<span id="L297"><span class="lineNum">     297</span> <span class="tlaGNC">           1 :       LoginOnOldDeviceScreen.pushNamed(</span></span>
<span id="L298"><span class="lineNum">     298</span>              :         isAutoLoginWithBiometric: isAutoLoginWithBiometric,</span>
<span id="L299"><span class="lineNum">     299</span>              :       );</span>
<span id="L300"><span class="lineNum">     300</span>              :     }</span>
<span id="L301"><span class="lineNum">     301</span>              :   }</span>
<span id="L302"><span class="lineNum">     302</span>              : </span>
<span id="L303"><span class="lineNum">     303</span> <span class="tlaGNC">           1 :   void _openInputPhoneNumberScreen(</span></span>
<span id="L304"><span class="lineNum">     304</span>              :       {String? lastPhoneNumberLogged, bool isClearNavigationStack = false}) {</span>
<span id="L305"><span class="lineNum">     305</span>              :     if (isClearNavigationStack) {</span>
<span id="L306"><span class="lineNum">     306</span> <span class="tlaUNC">           0 :       InputPhoneNumberPage.goNamed(lastPhoneNumberLogged: lastPhoneNumberLogged);</span></span>
<span id="L307"><span class="lineNum">     307</span>              :     } else {</span>
<span id="L308"><span class="lineNum">     308</span> <span class="tlaGNC">           1 :       InputPhoneNumberPage.pushNamed(lastPhoneNumberLogged: lastPhoneNumberLogged);</span></span>
<span id="L309"><span class="lineNum">     309</span>              :     }</span>
<span id="L310"><span class="lineNum">     310</span>              :   }</span>
<span id="L311"><span class="lineNum">     311</span>              : </span>
<span id="L312"><span class="lineNum">     312</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; handleOpenAuthenticationScreenIfUserNotLoggedIn(</span></span>
<span id="L313"><span class="lineNum">     313</span>              :       {required void Function() actionToBeHandledAfterLoggedIn}) async {</span>
<span id="L314"><span class="lineNum">     314</span> <span class="tlaGNC">           2 :     final AppState appState = getIt.get&lt;AppState&gt;();</span></span>
<span id="L315"><span class="lineNum">     315</span> <span class="tlaGNC">           1 :     final bool isLogged = appState.isUserLogIn;</span></span>
<span id="L316"><span class="lineNum">     316</span> <span class="tlaGNC">           1 :     final bool isRefreshTokenExpired = await refreshTokenExpired();</span></span>
<span id="L317"><span class="lineNum">     317</span>              :     if (isLogged &amp;&amp; !isRefreshTokenExpired) {</span>
<span id="L318"><span class="lineNum">     318</span> <span class="tlaGNC">           1 :       actionToBeHandledAfterLoggedIn.call();</span></span>
<span id="L319"><span class="lineNum">     319</span>              :       return;</span>
<span id="L320"><span class="lineNum">     320</span>              :     }</span>
<span id="L321"><span class="lineNum">     321</span>              : </span>
<span id="L322"><span class="lineNum">     322</span> <span class="tlaGNC">           2 :     await EvoAuthenticationHelper().clearDataOnTokenInvalid();</span></span>
<span id="L323"><span class="lineNum">     323</span> <span class="tlaGNC">           1 :     appState.actionAfterLogin = actionToBeHandledAfterLoggedIn;</span></span>
<span id="L324"><span class="lineNum">     324</span> <span class="tlaGNC">           1 :     await openAuthenticationScreen(isClearNavigationStack: true);</span></span>
<span id="L325"><span class="lineNum">     325</span>              :   }</span>
<span id="L326"><span class="lineNum">     326</span>              : </span>
<span id="L327"><span class="lineNum">     327</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L328"><span class="lineNum">     328</span>              :   Future&lt;bool&gt; refreshTokenExpired() async {</span>
<span id="L329"><span class="lineNum">     329</span> <span class="tlaGNC">           2 :     final JwtHelper jwtHelper = getIt.get&lt;JwtHelper&gt;();</span></span>
<span id="L330"><span class="lineNum">     330</span> <span class="tlaGNC">           2 :     final EvoLocalStorageHelper storageHelper = getIt.get&lt;EvoLocalStorageHelper&gt;();</span></span>
<span id="L331"><span class="lineNum">     331</span> <span class="tlaGNC">           1 :     final String? refreshToken = await storageHelper.getRefreshToken();</span></span>
<span id="L332"><span class="lineNum">     332</span>              : </span>
<span id="L333"><span class="lineNum">     333</span> <span class="tlaGNC">           1 :     if (!jwtHelper.isCanUse(refreshToken)) {</span></span>
<span id="L334"><span class="lineNum">     334</span>              :       return true;</span>
<span id="L335"><span class="lineNum">     335</span>              :     }</span>
<span id="L336"><span class="lineNum">     336</span>              : </span>
<span id="L337"><span class="lineNum">     337</span>              :     return false;</span>
<span id="L338"><span class="lineNum">     338</span>              :   }</span>
<span id="L339"><span class="lineNum">     339</span>              : </span>
<span id="L340"><span class="lineNum">     340</span> <span class="tlaGNC">           1 :   bool _openDopNativeIntroductionScreen() {</span></span>
<span id="L341"><span class="lineNum">     341</span> <span class="tlaGNC">           2 :     final AppState appState = getIt.get&lt;AppState&gt;();</span></span>
<span id="L342"><span class="lineNum">     342</span> <span class="tlaGNC">           2 :     final String? uniqueToken = appState.deepLinkSharedData.dopUniqueToken;</span></span>
<span id="L343"><span class="lineNum">     343</span> <span class="tlaGNC">           2 :     final String? phoneNumber = appState.deepLinkSharedData.phoneNumber;</span></span>
<span id="L344"><span class="lineNum">     344</span> <span class="tlaGNC">           2 :     final bool? autoRequestOTP = appState.deepLinkSharedData.autoRequestOTP;</span></span>
<span id="L345"><span class="lineNum">     345</span>              : </span>
<span id="L346"><span class="lineNum">     346</span> <span class="tlaGNC">           1 :     DOPNativeIntroductionScreen.pushNamed(</span></span>
<span id="L347"><span class="lineNum">     347</span> <span class="tlaGNC">           1 :       arg: DOPNativeIntroductionScreenArg(</span></span>
<span id="L348"><span class="lineNum">     348</span>              :         uniqueToken: uniqueToken,</span>
<span id="L349"><span class="lineNum">     349</span>              :         phoneNumber: phoneNumber,</span>
<span id="L350"><span class="lineNum">     350</span>              :         autoRequestOTP: autoRequestOTP,</span>
<span id="L351"><span class="lineNum">     351</span>              :       ),</span>
<span id="L352"><span class="lineNum">     352</span>              :     );</span>
<span id="L353"><span class="lineNum">     353</span>              :     return true;</span>
<span id="L354"><span class="lineNum">     354</span>              :   }</span>
<span id="L355"><span class="lineNum">     355</span>              : </span>
<span id="L356"><span class="lineNum">     356</span> <span class="tlaGNC">           1 :   Future&lt;bool&gt; openDeepLink(String? link) async {</span></span>
<span id="L357"><span class="lineNum">     357</span> <span class="tlaGNC">           2 :     if (link == null || !deepLinkUtils.isEvoAppDeepLink(link)) {</span></span>
<span id="L358"><span class="lineNum">     358</span>              :       return false;</span>
<span id="L359"><span class="lineNum">     359</span>              :     }</span>
<span id="L360"><span class="lineNum">     360</span> <span class="tlaGNC">           2 :     final DeepLinkModel model = deepLinkUtils.generateDeepLinkModel(deepLinkValue: link);</span></span>
<span id="L361"><span class="lineNum">     361</span> <span class="tlaGNC">           3 :     final bool isLoggedIn = getIt.get&lt;AppState&gt;().isUserLogIn;</span></span>
<span id="L362"><span class="lineNum">     362</span> <span class="tlaGNC">           3 :     return await getIt&lt;DeepLinkHandler&gt;().executeDeepLink(deepLink: model, isLoggedIn: isLoggedIn);</span></span>
<span id="L363"><span class="lineNum">     363</span>              :   }</span>
<span id="L364"><span class="lineNum">     364</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
