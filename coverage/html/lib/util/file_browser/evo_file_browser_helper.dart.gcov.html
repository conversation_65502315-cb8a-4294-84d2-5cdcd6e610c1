<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/util/file_browser/evo_file_browser_helper.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/util/file_browser">lib/util/file_browser</a> - evo_file_browser_helper.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">93.5&nbsp;%</td>
            <td class="headerCovTableEntry">31</td>
            <td class="headerCovTableEntry">29</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:io';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter/foundation.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/common_package/path.dart' as path_package;</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/util/device_platform.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : import '../extension.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import 'file_browser_helper.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : class EvoFileBrowserHelperImpl extends FileBrowserHelper {</span>
<span id="L12"><span class="lineNum">      12</span>              :   final DevicePlatform devicePlatform;</span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaGNC">           1 :   EvoFileBrowserHelperImpl(this.devicePlatform);</span></span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L17"><span class="lineNum">      17</span>              :   Future&lt;List&lt;FileSystemEntity&gt;&gt; getAllFilesInDir(</span>
<span id="L18"><span class="lineNum">      18</span>              :     Directory directory, {</span>
<span id="L19"><span class="lineNum">      19</span>              :     bool showHidden = false,</span>
<span id="L20"><span class="lineNum">      20</span>              :     List&lt;String&gt;? extensions,</span>
<span id="L21"><span class="lineNum">      21</span>              :   }) async {</span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           1 :     final List&lt;FileSystemEntity&gt; files = &lt;FileSystemEntity&gt;[];</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           1 :     final List&lt;FileSystemEntity&gt; fileSystems = directory.listSync();</span></span>
<span id="L24"><span class="lineNum">      24</span>              : </span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           2 :     for (final FileSystemEntity file in fileSystems) {</span></span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC">           1 :       if (file is File) {</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           1 :         if (shouldIncludeFile(file, extensions, showHidden)) {</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           1 :           files.add(file);</span></span>
<span id="L29"><span class="lineNum">      29</span>              :         }</span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaGNC">           1 :       } else if (file is Directory) {</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaGNC">           1 :         if (shouldIncludeDirectory(file, showHidden)) {</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           2 :           files.addAll(await getAllFilesInDir(file, showHidden: showHidden));</span></span>
<span id="L33"><span class="lineNum">      33</span>              :         }</span>
<span id="L34"><span class="lineNum">      34</span>              :       }</span>
<span id="L35"><span class="lineNum">      35</span>              :     }</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span>              :     return files;</span>
<span id="L38"><span class="lineNum">      38</span>              :   }</span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L41"><span class="lineNum">      41</span>              :   Future&lt;Directory&gt; getEndUserInteractableDir() async {</span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           1 :     commonLog('getDirByPlatform');</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           2 :     if (devicePlatform.isAndroid()) {</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           1 :       return getDirForAndroid();</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           2 :     } else if (devicePlatform.isIOS()) {</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           1 :       return getDirForiOS();</span></span>
<span id="L47"><span class="lineNum">      47</span>              :     }</span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           1 :     throw Exception('This function support only Android and iOS');</span></span>
<span id="L49"><span class="lineNum">      49</span>              :   }</span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L52"><span class="lineNum">      52</span>              :   Future&lt;String?&gt; readContentFile(String path) async {</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :     return await File(path).readAsString();</span></span>
<span id="L54"><span class="lineNum">      54</span>              :   }</span>
<span id="L55"><span class="lineNum">      55</span>              : </span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L57"><span class="lineNum">      57</span>              :   Future&lt;Directory&gt; getDirForAndroid() async {</span>
<span id="L58"><span class="lineNum">      58</span>              :     // /data/data/&lt;package-name&gt;/app_flutter</span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           1 :     final Directory dir = await path_package.getApplicationDocumentsDirectory();</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaGNC">           3 :     commonLog('getDirForAndroid: ${dir.path}');</span></span>
<span id="L61"><span class="lineNum">      61</span>              :     return dir;</span>
<span id="L62"><span class="lineNum">      62</span>              :   }</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L65"><span class="lineNum">      65</span>              :   Future&lt;Directory&gt; getDirForiOS() async {</span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaGNC">           1 :     final Directory dir = await path_package.getApplicationDocumentsDirectory();</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaGNC">           3 :     commonLog('getDirForiOS: ${dir.path}');</span></span>
<span id="L68"><span class="lineNum">      68</span>              :     return dir;</span>
<span id="L69"><span class="lineNum">      69</span>              :   }</span>
<span id="L70"><span class="lineNum">      70</span>              : </span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L72"><span class="lineNum">      72</span>              :   bool shouldIncludeFile(</span>
<span id="L73"><span class="lineNum">      73</span>              :     FileSystemEntity file,</span>
<span id="L74"><span class="lineNum">      74</span>              :     List&lt;String&gt;? extensions,</span>
<span id="L75"><span class="lineNum">      75</span>              :     bool showHidden,</span>
<span id="L76"><span class="lineNum">      76</span>              :   ) {</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaGNC">           3 :     if (extensions != null &amp;&amp; !extensions.contains(path_package.extension(file.path))) {</span></span>
<span id="L78"><span class="lineNum">      78</span>              :       return false;</span>
<span id="L79"><span class="lineNum">      79</span>              :     }</span>
<span id="L80"><span class="lineNum">      80</span>              : </span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaGNC">           1 :     if (!showHidden &amp;&amp; file.isHidden) {</span></span>
<span id="L82"><span class="lineNum">      82</span>              :       return false;</span>
<span id="L83"><span class="lineNum">      83</span>              :     }</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span>              :     return true;</span>
<span id="L86"><span class="lineNum">      86</span>              :   }</span>
<span id="L87"><span class="lineNum">      87</span>              : </span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           1 :   @visibleForTesting</span></span>
<span id="L89"><span class="lineNum">      89</span>              :   bool shouldIncludeDirectory(FileSystemEntity directory, bool showHidden) {</span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           1 :     if (!showHidden &amp;&amp; directory.isHidden) {</span></span>
<span id="L91"><span class="lineNum">      91</span>              :       return false;</span>
<span id="L92"><span class="lineNum">      92</span>              :     }</span>
<span id="L93"><span class="lineNum">      93</span>              :     return true;</span>
<span id="L94"><span class="lineNum">      94</span>              :   }</span>
<span id="L95"><span class="lineNum">      95</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
