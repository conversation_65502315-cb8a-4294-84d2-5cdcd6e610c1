<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/util/mock_file_name_utils/mock_user_file_name.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/util/mock_file_name_utils">lib/util/mock_file_name_utils</a> - mock_user_file_name.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">32</td>
            <td class="headerCovTableEntry">32</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import '../../data/response/linked_card_status_checking_entity.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import '../../feature/manual_link_card/model/manual_link_card_result_model.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : enum MockDeactivateAccountStatus {</span>
<span id="L5"><span class="lineNum">       5</span>              :   success,</span>
<span id="L6"><span class="lineNum">       6</span>              :   fail,</span>
<span id="L7"><span class="lineNum">       7</span>              : }</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span> <span class="tlaGNC">           3 : String getUserInfoMockFileName() {</span></span>
<span id="L10"><span class="lineNum">      10</span>              :   return 'user_information.json';</span>
<span id="L11"><span class="lineNum">      11</span>              : }</span>
<span id="L12"><span class="lineNum">      12</span>              : </span>
<span id="L13"><span class="lineNum">      13</span> <span class="tlaGNC">           2 : String createPinMockFileName() {</span></span>
<span id="L14"><span class="lineNum">      14</span>              :   return 'pin_code_verify.json';</span>
<span id="L15"><span class="lineNum">      15</span>              : }</span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaGNC">           3 : String getBiometricTokenByPinMockFileName({String? pin, int numberOfVerifiedFailed = 0}) {</span></span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaGNC">           3 :   if (pin == '123456') {</span></span>
<span id="L19"><span class="lineNum">      19</span>              :     return 'get_biometric_token_success.json';</span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaGNC">           3 :   } else if (pin == '111111') {</span></span>
<span id="L21"><span class="lineNum">      21</span>              :     return 'get_biometric_token_success_with_challenge_type.json';</span>
<span id="L22"><span class="lineNum">      22</span>              :   } else {</span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           3 :     if (numberOfVerifiedFailed &lt;= 3) {</span></span>
<span id="L24"><span class="lineNum">      24</span>              :       return 'get_biometric_with_pin_verify_error.json';</span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           2 :     } else if (numberOfVerifiedFailed == 4) {</span></span>
<span id="L26"><span class="lineNum">      26</span>              :       return 'get_biometric_with_pin_warning_verify_error.json';</span>
<span id="L27"><span class="lineNum">      27</span>              :     } else {</span>
<span id="L28"><span class="lineNum">      28</span>              :       return 'get_biometric_with_pin_block_due_to_verify_error.json';</span>
<span id="L29"><span class="lineNum">      29</span>              :     }</span>
<span id="L30"><span class="lineNum">      30</span>              :   }</span>
<span id="L31"><span class="lineNum">      31</span>              : }</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaGNC">           2 : String getDeactivateAccountMockFileName({MockDeactivateAccountStatus? mockStatus}) {</span></span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           2 :   if (mockStatus == MockDeactivateAccountStatus.fail) {</span></span>
<span id="L35"><span class="lineNum">      35</span>              :     return 'deactivate_user_fail.json';</span>
<span id="L36"><span class="lineNum">      36</span>              :   }</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span>              :   return 'deactivate_user_success.json';</span>
<span id="L39"><span class="lineNum">      39</span>              : }</span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           2 : String getLinkedCardsMockFileName() {</span></span>
<span id="L42"><span class="lineNum">      42</span>              :   return 'get_linked_card_list_success.json';</span>
<span id="L43"><span class="lineNum">      43</span>              : }</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaGNC">           4 : String getPaymentMethodsMockFileName() {</span></span>
<span id="L46"><span class="lineNum">      46</span>              :   return 'payment_method_list_empty.json';</span>
<span id="L47"><span class="lineNum">      47</span>              : }</span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           3 : String checkLinkedCardsStatusMockFileName({String? mockVerdict}) {</span></span>
<span id="L50"><span class="lineNum">      50</span>              :   switch (mockVerdict) {</span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictUnfulfilledCard:</span></span>
<span id="L52"><span class="lineNum">      52</span>              :       return 'check_linked_cards_unfulfilled.json';</span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictWaitingForCardIssuing:</span></span>
<span id="L55"><span class="lineNum">      55</span>              :       return 'check_linked_cards_waiting_for_issuing.json';</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictUnqualifiedCard:</span></span>
<span id="L58"><span class="lineNum">      58</span>              :       return 'check_linked_cards_unqualified.json';</span>
<span id="L59"><span class="lineNum">      59</span>              : </span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictSuccess:</span></span>
<span id="L61"><span class="lineNum">      61</span>              :       return 'check_linked_cards_success.json';</span>
<span id="L62"><span class="lineNum">      62</span>              : </span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictDuplicatedLinkRequest:</span></span>
<span id="L64"><span class="lineNum">      64</span>              :       return 'check_linked_cards_duplicate_request.json';</span>
<span id="L65"><span class="lineNum">      65</span>              : </span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictUnqualifiedUserInformation:</span></span>
<span id="L67"><span class="lineNum">      67</span>              :       return 'check_linked_cards_unqualified_user_information.json';</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span>              :     // PREPARE LINK</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictLinkCardInvalidPhoneNumber:</span></span>
<span id="L71"><span class="lineNum">      71</span>              :       return 'check_linked_cards_invalid_phone_number.json';</span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictLinkCardAlreadyLinked:</span></span>
<span id="L74"><span class="lineNum">      74</span>              :       return 'check_linked_cards_already_linked.json';</span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictLinkCardInvalidParameters:</span></span>
<span id="L77"><span class="lineNum">      77</span>              :       return 'check_linked_card_invalid_parameters.json';</span>
<span id="L78"><span class="lineNum">      78</span>              : </span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictLinkCardLinkRequestIsProcessing:</span></span>
<span id="L80"><span class="lineNum">      80</span>              :       return 'check_linked_card_link_request_is_processing.json';</span>
<span id="L81"><span class="lineNum">      81</span>              : </span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictLinkCardInvalidBankCode:</span></span>
<span id="L83"><span class="lineNum">      83</span>              :       return 'check_linked_card_link_card_invalid_bank_code.json';</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictLinkCardNotFoundLinkInfo:</span></span>
<span id="L86"><span class="lineNum">      86</span>              :       return 'check_linked_card_link_card_not_found_link_info.json';</span>
<span id="L87"><span class="lineNum">      87</span>              : </span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictLinkCardBankProductNotSupported:</span></span>
<span id="L89"><span class="lineNum">      89</span>              :       return 'check_linked_card_link_card_bank_product_not_supported.json';</span>
<span id="L90"><span class="lineNum">      90</span>              : </span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictLinkCardFailure:</span></span>
<span id="L92"><span class="lineNum">      92</span>              :       return 'check_linked_card_link_card_failure.json';</span>
<span id="L93"><span class="lineNum">      93</span>              : </span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           3 :     case LinkedCardStatusCheckingEntity.verdictFailureAll:</span></span>
<span id="L95"><span class="lineNum">      95</span>              :       return 'check_linked_card_failure.json';</span>
<span id="L96"><span class="lineNum">      96</span>              : </span>
<span id="L97"><span class="lineNum">      97</span>              :     default:</span>
<span id="L98"><span class="lineNum">      98</span>              :       return 'check_linked_cards_unknown.json';</span>
<span id="L99"><span class="lineNum">      99</span>              :   }</span>
<span id="L100"><span class="lineNum">     100</span>              : }</span>
<span id="L101"><span class="lineNum">     101</span>              : </span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaGNC">           2 : String submitLinkCardMockFileName({String? mockFileName}) {</span></span>
<span id="L103"><span class="lineNum">     103</span>              :   return mockFileName ?? 'submit_link_card_open_three_d_secure.json';</span>
<span id="L104"><span class="lineNum">     104</span>              : }</span>
<span id="L105"><span class="lineNum">     105</span>              : </span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaGNC">           2 : String checkLinkCardSubmissionStatusMockFileName({ManualLinkCardResultType? mockTypeVerdict}) {</span></span>
<span id="L107"><span class="lineNum">     107</span>              :   switch (mockTypeVerdict) {</span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaGNC">           2 :     case ManualLinkCardResultType.succeeded:</span></span>
<span id="L109"><span class="lineNum">     109</span>              :       return 'get_link_card_submission_status_case_succeed.json';</span>
<span id="L110"><span class="lineNum">     110</span>              : </span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaGNC">           2 :     case ManualLinkCardResultType.processing:</span></span>
<span id="L112"><span class="lineNum">     112</span>              :       return 'get_link_card_submission_status_case_processing.json';</span>
<span id="L113"><span class="lineNum">     113</span>              : </span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaGNC">           2 :     case ManualLinkCardResultType.failed:</span></span>
<span id="L115"><span class="lineNum">     115</span>              :       return 'get_link_card_submission_status_case_failed.json';</span>
<span id="L116"><span class="lineNum">     116</span>              : </span>
<span id="L117"><span class="lineNum">     117</span>              :     default:</span>
<span id="L118"><span class="lineNum">     118</span>              :       return 'get_link_card_submission_status_case_unknown.json';</span>
<span id="L119"><span class="lineNum">     119</span>              :   }</span>
<span id="L120"><span class="lineNum">     120</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
