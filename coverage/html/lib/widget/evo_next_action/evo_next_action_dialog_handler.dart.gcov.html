<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/widget/evo_next_action/evo_next_action_dialog_handler.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/widget/evo_next_action">lib/widget/evo_next_action</a> - evo_next_action_dialog_handler.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">96.6&nbsp;%</td>
            <td class="headerCovTableEntry">29</td>
            <td class="headerCovTableEntry">28</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/global_key_provider.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/resources/resources.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/util/extension.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/common_dialog_bottom_sheet.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../data/response/action_entity.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../data/response/voucher_earning_entity.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../feature/main_screen/main_screen.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../feature/payment/utils/voucher_detail_action_helper.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../model/evo_action_model.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../model/evo_dialog_id.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../resources/resources.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../util/evo_action_handler.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../util/mapper.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../util/ui_utils/evo_dialog_helper.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              : class EvoNextActionDialogHandler {</span>
<span id="L20"><span class="lineNum">      20</span>              :   /// BE will return the user_message and user_message_title in the SUCCESS response.</span>
<span id="L21"><span class="lineNum">      21</span>              :   /// Defined response here: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3442606082/Mobile+API+contract</span>
<span id="L22"><span class="lineNum">      22</span>              :   /// Defined the mapping of user_message and user_message_title here: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3455452330/Verdict+message+mapping</span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; showVoucherEarningSuccessDialog(VoucherEarningEntity voucherEarningResult) {</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">           1 :     return _showBottomSheet(</span></span>
<span id="L25"><span class="lineNum">      25</span>              :       image: EvoImages.imgVoucherEarningSuccess,</span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC">           1 :       title: voucherEarningResult.userMessageTitle,</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           1 :       description: voucherEarningResult.userMessage,</span></span>
<span id="L28"><span class="lineNum">      28</span>              :       ctaText: EvoStrings.voucherEarningSuccessCtaText,</span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaGNC">           2 :       ctaCallback: () =&gt; _handleVoucherEarningSuccessCta(voucherEarningResult),</span></span>
<span id="L30"><span class="lineNum">      30</span>              :       dialogId: EvoDialogId.voucherEarningSuccessBottomSheet,</span>
<span id="L31"><span class="lineNum">      31</span>              :     );</span>
<span id="L32"><span class="lineNum">      32</span>              :   }</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; showVoucherEarningErrorDialog() {</span></span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           1 :     return _showBottomSheet(</span></span>
<span id="L36"><span class="lineNum">      36</span>              :       image: EvoImages.imgReSelectPromotion,</span>
<span id="L37"><span class="lineNum">      37</span>              :       title: EvoStrings.voucherEarningErrorTitle,</span>
<span id="L38"><span class="lineNum">      38</span>              :       ctaText: EvoStrings.moveToHome,</span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           2 :       ctaCallback: () =&gt; _gotoMainScreen(),</span></span>
<span id="L40"><span class="lineNum">      40</span>              :       dialogId: EvoDialogId.voucherEarningErrorBottomSheet,</span>
<span id="L41"><span class="lineNum">      41</span>              :     );</span>
<span id="L42"><span class="lineNum">      42</span>              :   }</span>
<span id="L43"><span class="lineNum">      43</span>              : </span>
<span id="L44"><span class="lineNum">      44</span>              :   /// BE will return the user_message and user_message_title in the FAILED response.</span>
<span id="L45"><span class="lineNum">      45</span>              :   /// Defined response here: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3442606082/Mobile+API+contract</span>
<span id="L46"><span class="lineNum">      46</span>              :   /// Defined the mapping of user_message and user_message_title here: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3455452330/Verdict+message+mapping</span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; showVoucherEarningFailedDialog(ErrorUIModel errorUIModel) {</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           1 :     return _showBottomSheet(</span></span>
<span id="L49"><span class="lineNum">      49</span>              :       image: EvoImages.imgEkycError,</span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           1 :       title: errorUIModel.userMessageTitle,</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           1 :       description: errorUIModel.userMessage,</span></span>
<span id="L52"><span class="lineNum">      52</span>              :       ctaText: EvoStrings.moveToHome,</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           2 :       ctaCallback: () =&gt; _gotoMainScreen(),</span></span>
<span id="L54"><span class="lineNum">      54</span>              :       dialogId: EvoDialogId.voucherEarningFailedBottomSheet,</span>
<span id="L55"><span class="lineNum">      55</span>              :     );</span>
<span id="L56"><span class="lineNum">      56</span>              :   }</span>
<span id="L57"><span class="lineNum">      57</span>              : </span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaGNC">           1 :   Future&lt;void&gt; _showBottomSheet({</span></span>
<span id="L59"><span class="lineNum">      59</span>              :     required String image,</span>
<span id="L60"><span class="lineNum">      60</span>              :     required String ctaText,</span>
<span id="L61"><span class="lineNum">      61</span>              :     required VoidCallback ctaCallback,</span>
<span id="L62"><span class="lineNum">      62</span>              :     required EvoDialogId dialogId,</span>
<span id="L63"><span class="lineNum">      63</span>              :     String? title,</span>
<span id="L64"><span class="lineNum">      64</span>              :     String? description,</span>
<span id="L65"><span class="lineNum">      65</span>              :   }) {</span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaGNC">           2 :     return EvoDialogHelper().showDialogBottomSheet(</span></span>
<span id="L67"><span class="lineNum">      67</span>              :       title: title,</span>
<span id="L68"><span class="lineNum">      68</span>              :       content: description,</span>
<span id="L69"><span class="lineNum">      69</span>              :       dialogId: dialogId,</span>
<span id="L70"><span class="lineNum">      70</span>              :       isShowButtonClose: true,</span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">           2 :       header: evoImageProvider.asset(</span></span>
<span id="L72"><span class="lineNum">      72</span>              :         image,</span>
<span id="L73"><span class="lineNum">      73</span>              :         width: double.infinity,</span>
<span id="L74"><span class="lineNum">      74</span>              :         fit: BoxFit.fitWidth,</span>
<span id="L75"><span class="lineNum">      75</span>              :       ),</span>
<span id="L76"><span class="lineNum">      76</span>              :       buttonListOrientation: ButtonListOrientation.verticalDown,</span>
<span id="L77"><span class="lineNum">      77</span>              : </span>
<span id="L78"><span class="lineNum">      78</span>              :       /// Positive button</span>
<span id="L79"><span class="lineNum">      79</span>              :       textPositive: ctaText,</span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaGNC">           2 :       positiveButtonStyle: evoButtonStyles.primary(ButtonSize.xLarge),</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaGNC">           1 :       onClickPositive: () {</span></span>
<span id="L82"><span class="lineNum">      82</span>              :         /// Close the dialog</span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaGNC">           2 :         navigatorContext?.maybePop();</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">           1 :         ctaCallback.call();</span></span>
<span id="L85"><span class="lineNum">      85</span>              :       },</span>
<span id="L86"><span class="lineNum">      86</span>              :     );</span>
<span id="L87"><span class="lineNum">      87</span>              :   }</span>
<span id="L88"><span class="lineNum">      88</span>              : </span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">           1 :   void _gotoMainScreen() {</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           1 :     MainScreen.removeUntilAndPushReplacementNamed(isLoggedIn: true);</span></span>
<span id="L91"><span class="lineNum">      91</span>              :   }</span>
<span id="L92"><span class="lineNum">      92</span>              : </span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaGNC">           1 :   void _handleVoucherEarningSuccessCta(VoucherEarningEntity voucherEarningResult) {</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           1 :     final ActionEntity? action = voucherEarningResult.action;</span></span>
<span id="L95"><span class="lineNum">      95</span>              :     if (action == null) {</span>
<span id="L96"><span class="lineNum">      96</span>              :       return;</span>
<span id="L97"><span class="lineNum">      97</span>              :     }</span>
<span id="L98"><span class="lineNum">      98</span>              : </span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaGNC">           1 :     final String? type = action.type;</span></span>
<span id="L100"><span class="lineNum">     100</span>              : </span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaGNC">           1 :     if (type == EvoActionModel.openAppScreen) {</span></span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :       EvoActionHandler().handle(action.toEvoActionModel());</span></span>
<span id="L103"><span class="lineNum">     103</span>              :     } else {</span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaGNC">           2 :       voucherDetailActionHelper.handleGotoVoucherDetailScreen(</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaGNC">           1 :         voucherEarningResult.toVoucherEntity(),</span></span>
<span id="L106"><span class="lineNum">     106</span>              :       );</span>
<span id="L107"><span class="lineNum">     107</span>              :     }</span>
<span id="L108"><span class="lineNum">     108</span>              :   }</span>
<span id="L109"><span class="lineNum">     109</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
