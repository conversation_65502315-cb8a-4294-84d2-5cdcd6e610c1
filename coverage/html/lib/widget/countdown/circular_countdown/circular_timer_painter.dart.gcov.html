<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/widget/countdown/circular_countdown/circular_timer_painter.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/widget/countdown/circular_countdown">lib/widget/countdown/circular_countdown</a> - circular_timer_painter.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryMed">89.2&nbsp;%</td>
            <td class="headerCovTableEntry">74</td>
            <td class="headerCovTableEntry">66</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-05-13 17:17:29</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:math' as math;</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'dart:ui' as ui;</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter/rendering.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : class CircularTimerPainter extends CustomPainter {</span>
<span id="L7"><span class="lineNum">       7</span> <span class="tlaGNC">           3 :   const CircularTimerPainter({</span></span>
<span id="L8"><span class="lineNum">       8</span>              :     required this.countdownTotal,</span>
<span id="L9"><span class="lineNum">       9</span>              :     required this.countdownRemaining,</span>
<span id="L10"><span class="lineNum">      10</span>              :     required this.countdownTotalColor,</span>
<span id="L11"><span class="lineNum">      11</span>              :     required this.countdownRemainingColor,</span>
<span id="L12"><span class="lineNum">      12</span>              :     required this.strokeWidth,</span>
<span id="L13"><span class="lineNum">      13</span>              :     required this.gapFactor,</span>
<span id="L14"><span class="lineNum">      14</span>              :     required this.isClockwise,</span>
<span id="L15"><span class="lineNum">      15</span>              :     this.countdownCurrentColor,</span>
<span id="L16"><span class="lineNum">      16</span>              :     this.textStyle,</span>
<span id="L17"><span class="lineNum">      17</span>              :   });</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              :   final int countdownTotal;</span>
<span id="L20"><span class="lineNum">      20</span>              :   final int countdownRemaining;</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span>              :   /// The color to use when painting passed units.</span>
<span id="L23"><span class="lineNum">      23</span>              :   final Color countdownTotalColor;</span>
<span id="L24"><span class="lineNum">      24</span>              : </span>
<span id="L25"><span class="lineNum">      25</span>              :   /// The color to use when painting remaining units.</span>
<span id="L26"><span class="lineNum">      26</span>              :   final Color countdownRemainingColor;</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span>              :   /// The color to use when painting the current unit.</span>
<span id="L29"><span class="lineNum">      29</span>              :   final Color? countdownCurrentColor;</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span>              :   /// The part of the circle that will be gap. (`1/gapFactor`)</span>
<span id="L32"><span class="lineNum">      32</span>              :   ///</span>
<span id="L33"><span class="lineNum">      33</span>              :   /// Example : `gapFactor: 2` means that 50% of the circle will be gaps.</span>
<span id="L34"><span class="lineNum">      34</span>              :   final double gapFactor;</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span>              :   /// The thickness of the circle in logical pixels.</span>
<span id="L37"><span class="lineNum">      37</span>              :   final double strokeWidth;</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span>              :   /// Whether the countdown is drawn clockwise or not.</span>
<span id="L40"><span class="lineNum">      40</span>              :   final bool isClockwise;</span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span>              :   /// The `TextStyle` to use to display the `countdownRemaining` value</span>
<span id="L43"><span class="lineNum">      43</span>              :   /// in the center of the widget.</span>
<span id="L44"><span class="lineNum">      44</span>              :   ///</span>
<span id="L45"><span class="lineNum">      45</span>              :   /// Warning : It will not displays if the `TextStyle.fontSize` is too large.</span>
<span id="L46"><span class="lineNum">      46</span>              :   final TextStyle? textStyle;</span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           4 :   Paint get _totalPaint =&gt; Paint()</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           2 :     ..style = PaintingStyle.stroke</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           4 :     ..strokeWidth = strokeWidth</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           4 :     ..color = countdownTotalColor;</span></span>
<span id="L52"><span class="lineNum">      52</span>              : </span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           4 :   Paint get _remainingPaint =&gt; Paint()</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           2 :     ..style = PaintingStyle.stroke</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaGNC">           4 :     ..strokeWidth = strokeWidth</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaGNC">           4 :     ..color = countdownRemainingColor;</span></span>
<span id="L57"><span class="lineNum">      57</span>              : </span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaGNC">           2 :   Paint? get _currentPaint {</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           2 :     if (countdownCurrentColor != null) {</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaGNC">           1 :       return Paint()</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaGNC">           1 :         ..style = PaintingStyle.stroke</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaGNC">           2 :         ..strokeWidth = strokeWidth</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaGNC">           2 :         ..color = countdownCurrentColor!;</span></span>
<span id="L64"><span class="lineNum">      64</span>              :     } else {</span>
<span id="L65"><span class="lineNum">      65</span>              :       return null;</span>
<span id="L66"><span class="lineNum">      66</span>              :     }</span>
<span id="L67"><span class="lineNum">      67</span>              :   }</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">          12 :   double get _emptyArcSize =&gt; 2 * math.pi / (gapFactor * countdownTotal);</span></span>
<span id="L70"><span class="lineNum">      70</span>              : </span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaGNC">          12 :   double get _fullArcSize =&gt; 2 * math.pi / countdownTotal - _emptyArcSize;</span></span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaGNC">           2 :   double _startAngle(int unit) =&gt;</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaGNC">          20 :       -math.pi / 2 + unit * (_emptyArcSize + _fullArcSize) + _emptyArcSize / 2;</span></span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaGNC">           5 :   double _getInnerDiameter(double width) =&gt; math.max(0, width - 2 * strokeWidth);</span></span>
<span id="L77"><span class="lineNum">      77</span>              : </span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaGNC">          12 :   double _getRadius(double width) =&gt; math.max(0, width / 2 - strokeWidth / 2);</span></span>
<span id="L79"><span class="lineNum">      79</span>              : </span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L81"><span class="lineNum">      81</span>              :   void paint(Canvas canvas, Size size) {</span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaGNC">          10 :     final ui.Offset offset = Offset(size.width / 2, size.height / 2);</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaGNC">           4 :     final double radius = _getRadius(size.width);</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">           2 :     final double arcSize = _fullArcSize;</span></span>
<span id="L85"><span class="lineNum">      85</span>              : </span>
<span id="L86"><span class="lineNum">      86</span>              :     ui.Paint? paint;</span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaGNC">           6 :     for (int unit = 0; unit &lt; countdownTotal; unit++) {</span></span>
<span id="L88"><span class="lineNum">      88</span>              :       // Set painter.</span>
<span id="L89"><span class="lineNum">      89</span>              : </span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           2 :       if (isClockwise) {</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaGNC">           1 :         if (_currentPaint != null) {</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaGNC">           4 :           if (countdownTotal - unit &lt; countdownRemaining) {</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaGNC">           1 :             paint = _remainingPaint;</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           4 :           } else if (countdownTotal - unit == countdownRemaining) {</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaGNC">           1 :             paint = _currentPaint;</span></span>
<span id="L96"><span class="lineNum">      96</span>              :           } else {</span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaGNC">           1 :             paint = _totalPaint;</span></span>
<span id="L98"><span class="lineNum">      98</span>              :           }</span>
<span id="L99"><span class="lineNum">      99</span>              :         } else {</span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :           if (countdownTotal - unit &lt;= countdownRemaining) {</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :             paint = _remainingPaint;</span></span>
<span id="L102"><span class="lineNum">     102</span>              :           } else {</span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :             paint = _totalPaint;</span></span>
<span id="L104"><span class="lineNum">     104</span>              :           }</span>
<span id="L105"><span class="lineNum">     105</span>              :         }</span>
<span id="L106"><span class="lineNum">     106</span>              :       } else {</span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaGNC">           2 :         if (_currentPaint != null) {</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :           if (unit + 1 &lt; countdownRemaining) {</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :             paint = _remainingPaint;</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :           } else if (unit + 1 == countdownRemaining) {</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :             paint = _currentPaint;</span></span>
<span id="L112"><span class="lineNum">     112</span>              :           } else {</span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :             paint = _totalPaint;</span></span>
<span id="L114"><span class="lineNum">     114</span>              :           }</span>
<span id="L115"><span class="lineNum">     115</span>              :         } else {</span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaGNC">           4 :           if (unit &lt; countdownRemaining) {</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaGNC">           2 :             paint = _remainingPaint;</span></span>
<span id="L118"><span class="lineNum">     118</span>              :           } else {</span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaGNC">           1 :             paint = _totalPaint;</span></span>
<span id="L120"><span class="lineNum">     120</span>              :           }</span>
<span id="L121"><span class="lineNum">     121</span>              :         }</span>
<span id="L122"><span class="lineNum">     122</span>              :       }</span>
<span id="L123"><span class="lineNum">     123</span>              : </span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaGNC">           2 :       canvas.drawArc(</span></span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaGNC">           2 :         Rect.fromCircle(</span></span>
<span id="L126"><span class="lineNum">     126</span>              :           center: offset,</span>
<span id="L127"><span class="lineNum">     127</span>              :           radius: radius,</span>
<span id="L128"><span class="lineNum">     128</span>              :         ),</span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaGNC">           2 :         _startAngle(unit),</span></span>
<span id="L130"><span class="lineNum">     130</span>              :         arcSize,</span>
<span id="L131"><span class="lineNum">     131</span>              :         false,</span>
<span id="L132"><span class="lineNum">     132</span>              :         paint!,</span>
<span id="L133"><span class="lineNum">     133</span>              :       );</span>
<span id="L134"><span class="lineNum">     134</span>              :     }</span>
<span id="L135"><span class="lineNum">     135</span>              : </span>
<span id="L136"><span class="lineNum">     136</span>              :     // Draws the current value in the middle of the widget</span>
<span id="L137"><span class="lineNum">     137</span>              :     // if there is enough space.</span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaGNC">           2 :     if (textStyle != null) {</span></span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaGNC">           2 :       final double innerDiameter = _getInnerDiameter(size.width);</span></span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaGNC">           1 :       final TextSpan text = TextSpan(</span></span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaGNC">           2 :         text: countdownRemaining.toString(),</span></span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaGNC">           1 :         style: textStyle,</span></span>
<span id="L143"><span class="lineNum">     143</span>              :       );</span>
<span id="L144"><span class="lineNum">     144</span> <span class="tlaGNC">           1 :       final TextPainter textPainter = TextPainter(</span></span>
<span id="L145"><span class="lineNum">     145</span>              :         text: text,</span>
<span id="L146"><span class="lineNum">     146</span>              :         textDirection: TextDirection.ltr,</span>
<span id="L147"><span class="lineNum">     147</span>              :         maxLines: 1,</span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaGNC">           1 :       )..layout(</span></span>
<span id="L149"><span class="lineNum">     149</span>              :           minWidth: innerDiameter,</span>
<span id="L150"><span class="lineNum">     150</span>              :           maxWidth: innerDiameter,</span>
<span id="L151"><span class="lineNum">     151</span>              :         );</span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaGNC">           1 :       final BoxConstraints constraints = BoxConstraints(</span></span>
<span id="L153"><span class="lineNum">     153</span>              :         maxWidth: innerDiameter,</span>
<span id="L154"><span class="lineNum">     154</span>              :         maxHeight: innerDiameter,</span>
<span id="L155"><span class="lineNum">     155</span>              :       );</span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaGNC">           1 :       final RenderParagraph renderParagraph = RenderParagraph(</span></span>
<span id="L157"><span class="lineNum">     157</span>              :         text,</span>
<span id="L158"><span class="lineNum">     158</span>              :         textDirection: TextDirection.ltr,</span>
<span id="L159"><span class="lineNum">     159</span>              :         overflow: TextOverflow.ellipsis,</span>
<span id="L160"><span class="lineNum">     160</span>              :         maxLines: 1,</span>
<span id="L161"><span class="lineNum">     161</span>              :         textAlign: TextAlign.center,</span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaGNC">           1 :       )..layout(constraints);</span></span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaGNC">           4 :       final double textWidth = renderParagraph.getMinIntrinsicWidth(size.width / 2).ceilToDouble();</span></span>
<span id="L164"><span class="lineNum">     164</span>              :       final double textHeight =</span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaGNC">           4 :           renderParagraph.getMinIntrinsicHeight(size.width / 2).ceilToDouble();</span></span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaGNC">           1 :       if (textWidth &lt;= innerDiameter) {</span></span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaGNC">           1 :         final Offset offset = Offset(</span></span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaGNC">           4 :           size.width / 2 - textWidth / 2,</span></span>
<span id="L169"><span class="lineNum">     169</span> <span class="tlaGNC">           4 :           size.height / 2 - textHeight / 2,</span></span>
<span id="L170"><span class="lineNum">     170</span>              :         );</span>
<span id="L171"><span class="lineNum">     171</span> <span class="tlaGNC">           1 :         textPainter.paint(canvas, offset);</span></span>
<span id="L172"><span class="lineNum">     172</span>              :       }</span>
<span id="L173"><span class="lineNum">     173</span>              :     }</span>
<span id="L174"><span class="lineNum">     174</span>              :   }</span>
<span id="L175"><span class="lineNum">     175</span>              : </span>
<span id="L176"><span class="lineNum">     176</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L177"><span class="lineNum">     177</span>              :   bool shouldRepaint(CircularTimerPainter oldDelegate) {</span>
<span id="L178"><span class="lineNum">     178</span> <span class="tlaGNC">           6 :     return countdownTotal != oldDelegate.countdownTotal ||</span></span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaGNC">           6 :         countdownRemaining != oldDelegate.countdownRemaining ||</span></span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaGNC">           6 :         countdownTotalColor != oldDelegate.countdownTotalColor ||</span></span>
<span id="L181"><span class="lineNum">     181</span> <span class="tlaGNC">           6 :         countdownRemainingColor != oldDelegate.countdownRemainingColor ||</span></span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaGNC">           6 :         countdownCurrentColor != oldDelegate.countdownCurrentColor ||</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaGNC">           6 :         gapFactor != oldDelegate.gapFactor ||</span></span>
<span id="L184"><span class="lineNum">     184</span> <span class="tlaGNC">           6 :         strokeWidth != oldDelegate.strokeWidth ||</span></span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaGNC">           6 :         textStyle != oldDelegate.textStyle;</span></span>
<span id="L186"><span class="lineNum">     186</span>              :   }</span>
<span id="L187"><span class="lineNum">     187</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.2-1</a></td></tr>
          </table>
          <br>

</body>
</html>
