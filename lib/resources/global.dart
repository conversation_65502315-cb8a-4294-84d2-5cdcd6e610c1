import 'package:flutter/foundation.dart';

import '../model/evo_action_model.dart';

/// number of items in each page
const int defaultNumberItemPerPage = 10;
const int defaultFirstPage = 1;

//currency code
const String vietNamCurrencySymbol = 'đ';

///User phone number format, just show last 3 char
const int phoneNumberFormatNumOfLastShow = 3;
const int maxLengthPhoneNumber = 12;
const String defaultTextPhoneNumber = '+';
const String defaultPrefixNationalPhone = '84';
const String defaultPrefixLocalPhone = '0';

///User identity card number format, just show last 3 char
const int identityCardNumberFormatNumOfLastShow = 3;

///private policy
///PRD: https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/3317760337/EVO+app+Get+user+s+consent+-+Ngh+nh+13?focusedCommentId=3318350001
const int delayTimeEnableCTAPrivatePolicy = 3;

///reset PIN
const int minLengthNationalId = 9;
const int maxLengthNationalId = 12;

class WebsiteUrl {
  /// Terms & conditions website
  static const String evoTermsAndConditionsUrl =
      'https://www.goevo.vn/dieu-khoan-va-dieu-kien-evo-app';
  static const String evoFaqUrl = 'https://www.goevo.vn/cau-hoi-thuong-gap-evo-app';
  static const String cardUsageGuideUrl = 'https://www.goevo.vn/tutorial';
  static const String evoAboutUsUrl = 'https://www.goevo.vn/ve-chung-toi';

  /// The URL define here :
  /// https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/3335356468/Card+Status+Tracking+-+View+card+activation+status
  static const String evoCardActivationTutorialUrl =
      'https://www.goevo.vn/app/huong-dan-kich-hoat-the';

  /// Refer to PRD of https://trustingsocial1.atlassian.net/browse/EMA-1777
  static const String evoEnablePOSTutorialUrl = 'https://www.goevo.vn/app/kich-hoat-pos';
}

class LocalHtmlFile {
  static const String rootFolder = 'assets/html';
}

class ContactInfo {
  static const String contactSupportEmail = '<EMAIL>';
  static const String dopSupportPhone = '1900585885';
  static const String tpbHotline = '19006036';
}

//use to measure app start time, logged to Firebase Performance
enum AppStartPhase {
  prepareForAppInitiation,
  splashScreenInit;

  String toFPMetricName() {
    switch (this) {
      case AppStartPhase.prepareForAppInitiation:
        return 'prepare_for_app_initiation';
      case AppStartPhase.splashScreenInit:
        return 'splash_screen_init';
    }
  }
}

enum Screen {
  splashScreen(splashScreenName),
  tutorialScreen(tutorialScreenName),
  mainScreen(mainScreenName),
  announcementListScreen(announcementListScreenName),
  inputPhoneNumberScreen(inputPhoneNumberScreenName),
  verifyOtpScreen(verifyOtpScreenName),
  homeScreen(homeScreenName),
  createEvoCardScreen(createEvoCardScreenName),
  feedbackScreen(feedbackScreenName),
  announcementListRewardScreen(announcementListRewardTabName),
  announcementListTransactionScreen(announcementListTransactionTabName),
  profileScreen(profileScreenName),
  errorScreen(errorScreenName),
  createPinScreen(createPinScreenName),
  profileDetailScreen(profileDetailScreenName),

  /// this is not a real screen, just a alias to handle the redirect action to
  /// either [inputPhoneNumberScreen] or [loginOnOldDeviceScreenName]
  ///
  /// Action type [EvoActionModel.open_app_screen] with [EvoActionModel.Agrs.screenName] equals to [loginScreen.name]
  /// will redirect the user to:
  /// 1. [inputPhoneNumberScreen] if the user has never logged in since installing the app.
  /// 2. [loginOnOldDeviceScreen] if the user has already logged in on the device.
  loginScreen(loginScreenName),
  loginOnOldDeviceScreen(loginOnOldDeviceScreenName),
  activeBiometric(activeBiometricScreenName),
  profileSettingScreen(profileSettingScreenName),
  profileWebViewScreen(profileWebViewScreenName),
  inputPinScreen(inputPinScreenName),
  campaignListScreen(campaignListScreenName),
  allPromotionListScreen(allPromotionListScreenName),
  myPromotionListScreen(myPromotionListScreenName),
  qrCodeScannerScreen(qrCodeScannerScreenName),
  paymentInputAmount(paymentInputAmountScreenName),
  confirmPaymentFail(confirmPaymentFailScreenName),
  threeDPolling(threeDPollingPaymentScreenName),
  threeDSecurePayment(threeDSecurePaymentScreenName),
  transactionHistoryListForAuthorizedUserWidget(transactionHistoryListForAuthorizedUserWidgetName),
  transactionHistoryListScreen(transactionHistoryListScreenName),
  transactionHistoryDetailScreen(transactionHistoryDetailScreenName),
  paymentResultScreen(paymentResultScreenName),
  paymentPromotionListScreen(paymentPromotionListScreenName),
  linkedCardDetailScreen(linkedCardDetailScreenName),
  dopCardStatusScreen(dopCardStatusScreenName),
  manualLinkCardResultScreen(manualLinkCardResultScreenName),
  cloneConfirmPayment(cloneConfirmPaymentScreenName),
  linkCardThreeDPolling(linkCardThreeDPollingScreenName),
  linkCardThreeDSecureScreen(linkCardThreeDSecureScreenName),
  linkCardSubmissionStatusPollingScreen(linkCardSubmissionStatusPollingScreenName),
  updateConfirmPaymentScreen(updateConfirmPaymentScreenName),

  // old face otp
  faceOTPInstructionScreen(faceOTPInstructionScreenName),
  preFaceOtpManualLinkCardScreen(preFaceOtpManualLinkCardScreenName),
  faceOTPStarterScreen(faceOtpStarterScreenName),
  faceOTPMatchingErrorScreen(faceOtpMatchingErrorScreenName),
  faceOTPMatchingProcessScreen(faceOtpMatchingProcessScreenName),
  ekycErrorScreen(ekycErrorScreenName),

  // new face auth
  faceAuthInstructionScreen(faceAuthInstructionScreenName),
  preFaceAuthManualLinkCardScreen(preFaceAuthManualLinkCardScreenName),
  faceAuthStarterScreen(faceAuthStarterScreenName),
  faceAuthMatchingErrorScreen(faceAuthMatchingErrorScreenName),
  faceAuthMatchingProcessScreen(faceAuthMatchingProcessScreenName),
  ekycV2ErrorScreen(ekycV2ErrorScreenName),
  privatePolicyScreen(privatePolicyScreenName),
  attentionNotesScreen(attentionNotesScreenName),
  deleteAccountSuccessScreen(deleteAccountSuccessScreenName),
  deleteAccountVerifyPinScreen(deleteAccountVerifyPinScreenName),
  deleteAccountSurveyScreen(deleteAccountSurveyScreenName),
  referralSharingScreen(referralSharingScreenName),
  emiOptionScreen(emiOptionScreenName),
  emiNotSupportScreen(emiNotSupportScreenName),
  nonUserHomeScreen(nonUserHomeScreenName),
  userMainScreen(userMainScreenName),
  storyWebViewScreen(storyWebViewScreenName),
  emiManagementListScreen(emiManagementListScreenName),
  emiManagementDetailScreen(emiManagementDetailScreenName),
  activateCardScreen(activateCardScreenName),
  setupPosLimitScreen(setupPosLimitScreenName),
  setupPosLimitGuidanceScreen(setupPosLimitGuidanceScreenName),
  activateCardGuidanceScreen(activateCardGuidanceScreenName),
  activatePosLimitScreen(activatePosLimitScreenName),
  activatePosLimitThreeDSecureScreen(activatePosLimitThreeDSecureScreenName),
  maintenanceScreen(maintenanceScreenName),

  /// DOP Native
  dopNativeStatusScreen(dopNativeStatusScreenName),
  dopNativeVerifyOtpScreen(dopNativeVerifyOtpScreenName),
  dopNativeWelcomeBackScreen(dopNativeWelcomeBackScreenName),
  dopNativeIntroductionScreen(dopNativeIntroductionScreenName),
  dopNativeSubIntroductionScreen(dopNativeSubIntroductionScreenName),
  dopNativeIdCardCaptureIntroductionScreen(dopNativeIdCardCaptureIntroductionScreenName),
  dopNativeIdCardFrontSideVerificationScreen(dopNativeIdCardFrontSideVerificationScreenName),
  dopNativeIdCardQRCodeVerificationScreen(dopNativeIdCardQRCodeVerificationScreenName),
  dopNativeIdCardBackSideVerificationScreen(dopNativeIdCardBackSideVerificationScreenName),
  dopNativeIdCardSuccessScreen(dopNativeIdCardSuccessScreenName),
  dopNativeNFCReaderIntroductionScreen(dopNativeNFCReaderIntroductionScreenName),
  dopNativeFailureScreen(dopNativeFailureScreenName),
  dopNativeEKYCConfirmScreen(dopNativeEKYCConfirmScreenName),
  dopNativeSingleEKYCConfirmScreen(dopNativeSingleEKYCConfirmScreenName),
  dopNativeThreeStepAppraisingVerificationScreen(
      dopNativeThreeStepAppraisingVerificationScreenName),
  dopNativeAppraisingVerificationScreen(dopNativeAppraisingVerificationScreenName),
  dopNativeSelfieFlashIntroductionScreen(dopNativeSelfieFlashIntroductionScreenName),
  dopNativeSelfieActiveIntroductionScreen(dopNativeSelfieActiveIntroductionScreenName),
  dopNativeSelfieVerificationScreen(dopNativeSelfieVerificationScreenName),
  dopNativeSelfieVerificationSuccessScreen(dopNativeSelfieVerificationSuccessScreenName),
  dopNativeInformSuccessSemiScreen(dopNativeInformSuccessSemiScreenName),
  dopNativeInformSuccessAutoPCBScreen(dopNativeInformSuccessAutoPCBScreenName),
  dopNativeInformSuccessAutoCICScreen(dopNativeInformSuccessAutoCICScreenName),
  dopNativeInformSuccessSophiaScreen(dopNativeInformSuccessSophiaScreenName),
  dopNativeAppFormAdditionalInfoScreen(dopNativeAppFormAdditionalInfoScreenName),
  dopNativeESignIntroScreen(dopNativeESignIntroScreenName),
  dopNativeESignIntroMWGScreen(dopNativeESignIntroMWGScreenName),
  dopNativeESignReviewScreen(dopNativeESignReviewScreenName),
  dopNativeESignOTPScreen(dopNativeESignOTPScreenName),
  dopNativeAppFormCardDesignSemiScreen(dopNativeAppFormCardDesignSemiScreenName),
  dopNativeAppFormCardDesignAutoPCBScreen(dopNativeAppFormCardDesignAutoPCBScreenName),
  dopNativeAppFormCardDesignAutoCICScreen(dopNativeAppFormCardDesignAutoCICScreenName),
  dopNativeAppFormStatementDateSemiScreen(dopNativeAppFormStatementDateSemiScreenName),
  dopNativeAppFormStatementDateAutoPCBScreen(dopNativeAppFormStatementDateAutoPCBScreenName),
  dopNativeAppFormStatementDateAutoCICScreen(dopNativeAppFormStatementDateAutoCICScreenName),
  dopNativeCifConfirmScreen(dopNativeCifConfirmScreenName),
  dopNativeCifNoBranchScreen(dopNativeCifNoBranchScreenName),
  dopNativeEkycConfirmAdditionalInfoScreen(dopNativeEkycConfirmAdditionalInfoScreenName),
  dopNativePDFViewScreen(dopNativePDFViewScreenName),
  dopNativeWebViewScreen(dopNativeWebViewScreenName),
  dopNativeEKYCLimitExceedScreen(dopNativeEKYCLimitExceedScreenName),
  dopNativeESuccessScreen(dopNativeESuccessScreenName),
  dopNativeESuccessCICHoldingScreen(dopNativeESuccessCICHoldingScreenName),
  dopNativeUnderwritingInProgressScreen(dopNativeUnderwritingInProgressScreenName),
  dopNativeUnderwritingCardIssuedScreen(dopNativeUnderwritingCardIssuedScreenName),
  dopNativeCardStatusCICBlockScreen(dopNativeCardStatusCICBlockScreenName),
  dopNativeCardActivatedScreen(dopNativeCardActivatedScreenName),
  dopNativeCardActivatedRetryPosLimitScreen(dopNativeCardActivatedRetryPosLimitScreenName),
  dopNativeCardActivatedPosFailedScreen(dopNativeCardActivatedPosFailedScreenName),
  dopNativeCardActiveRetryScreen(dopNativeCardActiveRetryScreenName),
  dopNativeCardActiveFailScreen(dopNativeCardActiveFailScreenName),
  dopNativeEContractDownloadScreen(dopNativeEContractDownloadScreenName),
  dopNativeAmericaCitizenScreen(dopNativeAmericaCitizenScreenName),
  dopNativeESuccessSemiScreen(dopNativeESuccessSemiScreenName),
  dopNativeCardStatusInformationScreen(dopNativeCardStatusInformationScreenName),
  dopNativeThreeDSecureScreen(dopNativeThreeDSecureScreenName),
  dopNativeUnderwritingCardStatusScreen(dopNativeUnderwritingCardStatusScreenName),
  nonLoginQrCodeScannerScreen(nonLoginQrCodeScannerScreenName),
  dopNativeSalesmanScreen(dopNativeSalesmanScreenName),
  dopNativeSalesmanConfirmScreen(dopNativeSalesmanConfirmScreenName),
  dopNativeAcquisitionRewardScreen(dopNativeAcquisitionRewardScreenName),
  dopNativeFaceOtpIntroductionScreen(dopNativeFaceOtpIntroductionScreenName),
  dopNativeFaceOtpSuccessScreen(dopNativeFaceOtpSuccessScreenName),
  dopNativeFaceOtpRetryScreen(dopNativeFaceOtpRetryScreenName),
  dopNativeNFCDeviceUnsupportedScreen(dopNativeNFCDeviceUnsupportedScreenName),
  dopNativeCollectLocationScreen(dopNativeCollectLocationScreenName),
  dopNativeFourthAppraisingScreen(dopNativeFourthAppraisingScreenName),
  ;

  /// DO NOT change the value of these fields without confirm with backend
  /// Screens CAN be redirected.
  @visibleForTesting
  static const String tutorialScreenName = 'tutorial_screen';

  @visibleForTesting
  static const String homeScreenName = 'home_screen';

  @visibleForTesting
  static const String announcementListScreenName = 'announcement_list_screen';

  @visibleForTesting
  static const String qrCodeScannerScreenName = 'qr_code_scanner_screen';

  @visibleForTesting
  static const String transactionHistoryListScreenName = 'transaction_history_list_screen';

  @visibleForTesting
  static const String profileScreenName = 'profile_screen';

  @visibleForTesting
  static const String campaignListScreenName = 'campaign_list_screen';

  @visibleForTesting
  static const String allPromotionListScreenName = 'all_promotion_list_screen';

  @visibleForTesting
  static const String myPromotionListScreenName = 'my_promotion_list_screen';

  @visibleForTesting
  static const String loginScreenName = 'login_screen';

  @visibleForTesting
  static const String profileSettingScreenName = 'profile_setting_screen';

  @visibleForTesting
  static const String transactionHistoryDetailScreenName = 'transaction_history_detail_screen';

  /// Screens CANNOT be redirected.
  @visibleForTesting
  static const String mainScreenName = 'main_screen';

  @visibleForTesting
  static const String splashScreenName = 'splash_screen';

  @visibleForTesting
  static const String inputPhoneNumberScreenName = 'input_phone_number_screen';

  @visibleForTesting
  static const String loginOnOldDeviceScreenName = 'login_on_old_device_screen';

  @visibleForTesting
  static const String createEvoCardScreenName = 'create_evo_card_screen';

  @visibleForTesting
  static const String verifyOtpScreenName = 'verify_otp_screen';

  @visibleForTesting
  static const String errorScreenName = 'error_screen';

  @visibleForTesting
  static const String createPinScreenName = 'create_pin_screen';

  @visibleForTesting
  static const String activeBiometricScreenName = 'active_biometric_screen';

  @visibleForTesting
  static const String profileWebViewScreenName = 'profile_webView_screen';

  @visibleForTesting
  static const String inputPinScreenName = 'input_pin_screen';

  @visibleForTesting
  static const String paymentInputAmountScreenName = 'payment_input_amount_screen';

  @visibleForTesting
  static const String confirmPaymentFailScreenName = 'confirm_payment_fail_screen';

  @visibleForTesting
  static const String threeDPollingPaymentScreenName = 'polling_payment_screen';

  @visibleForTesting
  static const String threeDSecurePaymentScreenName = 'three_d_secure_payment_screen';

  @visibleForTesting
  static const String paymentResultScreenName = 'payment_result_screen';

  @visibleForTesting
  static const String paymentPromotionListScreenName = 'payment_promotion_list_screen';

  @visibleForTesting
  static const String linkedCardDetailScreenName = 'linked_card_detail_screen';

  @visibleForTesting
  static const String profileDetailScreenName = 'profile_detail_screen';

  @visibleForTesting
  static const String feedbackScreenName = 'feedback_screen';

  @visibleForTesting
  static const String cloneConfirmPaymentScreenName = 'clone_confirm_payment_screen';

  @visibleForTesting
  static const String linkCardThreeDPollingScreenName = 'link_card_three_d_polling_screen';

  @visibleForTesting
  static const String linkCardThreeDSecureScreenName = 'link_card_three_d_secure_screen';

  @visibleForTesting
  static const String linkCardSubmissionStatusPollingScreenName =
      'link_card_submission_status_polling_screen';

  @visibleForTesting
  static const String dopCardStatusScreenName = 'dop_card_status_screen';

  @visibleForTesting
  static const String manualLinkCardResultScreenName = 'manual_link_card_result_screen';

  @visibleForTesting
  static const String updateConfirmPaymentScreenName = 'update_confirm_payment_screen';

  @visibleForTesting
  static const String preFaceOtpManualLinkCardScreenName = 'pre_face_otp_manual_link_card_screen';

  @visibleForTesting
  static const String faceOTPInstructionScreenName = 'face_otp_instruction_screen';

  @visibleForTesting
  static const String faceOtpStarterScreenName = 'face_otp_starter_screen';

  @visibleForTesting
  static const String faceOtpMatchingErrorScreenName = 'face_otp_matching_error_screen';

  @visibleForTesting
  static const String faceOtpMatchingProcessScreenName = 'face_otp_matching_process_screen';

  @visibleForTesting
  static const String ekycErrorScreenName = 'ekyc_error_screen';

  @visibleForTesting
  static const String preFaceAuthManualLinkCardScreenName = 'pre_face_auth_manual_link_card_screen';

  @visibleForTesting
  static const String faceAuthInstructionScreenName = 'face_auth_instruction_screen';

  @visibleForTesting
  static const String faceAuthStarterScreenName = 'face_auth_starter_screen';

  @visibleForTesting
  static const String faceAuthMatchingErrorScreenName = 'face_auth_matching_error_screen';

  @visibleForTesting
  static const String faceAuthMatchingProcessScreenName = 'face_auth_matching_process_screen';

  @visibleForTesting
  static const String ekycV2ErrorScreenName = 'ekyc_v2_error_screen';

  @visibleForTesting
  static const String privatePolicyScreenName = 'private_policy_screen';

  @visibleForTesting
  static const String attentionNotesScreenName = 'attention_notes_screen';

  @visibleForTesting
  static const String deleteAccountSuccessScreenName = 'delete_account_success_screen';

  @visibleForTesting
  static const String deleteAccountVerifyPinScreenName = 'delete_account_verify_pin_screen';

  @visibleForTesting
  static const String deleteAccountSurveyScreenName = 'delete_account_survey_screen';

  @visibleForTesting
  static const String referralSharingScreenName = 'referral_sharing_screen';

  @visibleForTesting
  static const String emiOptionScreenName = 'emi_option_screen';

  @visibleForTesting
  static const String emiManagementListScreenName = 'emi_management_list_screen';

  @visibleForTesting
  static const String emiManagementDetailScreenName = 'emi_management_detail_screen';

  @visibleForTesting
  static const String activateCardScreenName = 'activate_card_screen';

  @visibleForTesting
  static const String setupPosLimitScreenName = 'set_pos_limit_screen';

  @visibleForTesting
  static const String setupPosLimitGuidanceScreenName = 'set_pos_limit_guidance_screen';

  @visibleForTesting
  static const String activateCardGuidanceScreenName = 'activate_card_guidance_screen';

  @visibleForTesting
  static const String activatePosLimitThreeDSecureScreenName =
      'activate_pos_limit_three_d_secure_screen';

  @visibleForTesting
  static const String normalConfirmPaymentScreenName = 'normal_confirm_payment_screen';

  @visibleForTesting
  static const String emiNotSupportScreenName = 'emi_not_support_screen';

  @visibleForTesting
  static const String nonUserHomeScreenName = 'non_user_home_screen';

  @visibleForTesting
  static const String userMainScreenName = 'user_main_screen';

  @visibleForTesting
  static const String activatePosLimitScreenName = 'activate_pos_limit_screen';

  @visibleForTesting
  static const String maintenanceScreenName = 'maintenance_screen';

  /// DOP native screen
  @visibleForTesting
  static const String dopNativeStatusScreenName = 'dop_native_status_screen';

  @visibleForTesting
  static const String dopNativeVerifyOtpScreenName = 'dop_native_verify_otp_screen';

  @visibleForTesting
  static const String dopNativeIntroductionScreenName = 'dop_native_introduction_screen';

  @visibleForTesting
  static const String dopNativeWelcomeBackScreenName = 'dop_native_welcome_back_screen';

  @visibleForTesting
  static const String dopNativeSubIntroductionScreenName = 'dop_native_sub_introduction_screen';

  @visibleForTesting
  static const String dopNativeIdCardCaptureIntroductionScreenName =
      'dop_native_id_card_capture_introduction_screen';

  @visibleForTesting
  static const String dopNativeIdCardFrontSideVerificationScreenName =
      'dop_native_id_card_front_side_verification_screen';

  @visibleForTesting
  static const String dopNativeIdCardQRCodeVerificationScreenName =
      'dop_native_id_card_qr_code_verification_screen';

  @visibleForTesting
  static const String dopNativeIdCardBackSideVerificationScreenName =
      'dop_native_id_card_back_side_verification_screen';

  @visibleForTesting
  static const String dopNativeIdCardSuccessScreenName = 'dop_native_id_card_success_screen';

  @visibleForTesting
  static const String dopNativeNFCReaderIntroductionScreenName =
      'dop_native_nfc_reader_introduction_screen';

  @visibleForTesting
  static const String dopNativeFailureScreenName = 'dop_native_failure_screen';
  @visibleForTesting
  static const String dopNativeEKYCConfirmScreenName = 'dop_native_ekyc_confirm_screen';

  @visibleForTesting
  static const String dopNativeSingleEKYCConfirmScreenName =
      'dop_native_single_ekyc_confirm_screen';

  @visibleForTesting
  static const String dopNativeThreeStepAppraisingVerificationScreenName =
      'dop_native_three_step_appraising_verification_screen';
  @visibleForTesting
  static const String dopNativeAppraisingVerificationScreenName =
      'dop_native_appraising_verification_screen';
  @visibleForTesting
  static const String dopNativeInformSuccessSemiScreenName =
      'dop_native_inform_success_semi_screen';
  @visibleForTesting
  static const String dopNativeInformSuccessSophiaScreenName =
      'dop_native_inform_success_sophia_screen';
  @visibleForTesting
  static const String dopNativeInformSuccessAutoPCBScreenName =
      'dop_native_inform_success_auto_pcb_screen';
  @visibleForTesting
  static const String dopNativeInformSuccessAutoCICScreenName =
      'dop_native_inform_success_auto_cic_screen';
  @visibleForTesting
  static const String dopNativeAppFormAdditionalInfoScreenName =
      'dop_native_app_form_additional_info_screen';

  @visibleForTesting
  static const String dopNativeESignIntroScreenName = 'dop_native_esign_intro_screen';

  @visibleForTesting
  static const String dopNativeESignIntroMWGScreenName = 'dop_native_esign_intro_mwg_screen';

  @visibleForTesting
  static const String dopNativeESignReviewScreenName = 'dop_native_esign_review_screen';

  @visibleForTesting
  static const String dopNativeESignOTPScreenName = 'dop_native_esign_otp_screen';

  @visibleForTesting
  static const String dopNativeAppFormCardDesignSemiScreenName =
      'dop_native_app_form_card_design_semi_screen';
  @visibleForTesting
  static const String dopNativeAppFormCardDesignAutoPCBScreenName =
      'dop_native_app_form_card_design_auto_pcb_screen';
  @visibleForTesting
  static const String dopNativeAppFormCardDesignAutoCICScreenName =
      'dop_native_app_form_card_design_auto_cic_screen';
  @visibleForTesting
  static const String dopNativeAppFormStatementDateSemiScreenName =
      'dop_native_app_form_statement_date_semi_screen';
  @visibleForTesting
  static const String dopNativeAppFormStatementDateAutoPCBScreenName =
      'dop_native_app_form_statement_date_auto_pcb_screen';
  @visibleForTesting
  static const String dopNativeAppFormStatementDateAutoCICScreenName =
      'dop_native_app_form_statement_date_auto_cic_screen';
  @visibleForTesting
  static const String dopNativeCifConfirmScreenName = 'dop_native_cif_confirm_screen';

  @visibleForTesting
  static const String dopNativeCifNoBranchScreenName = 'dop_native_cif_no_branch_screen';

  static const String dopNativeSelfieFlashIntroductionScreenName =
      'dop_native_selfie_flash_introduction_screen';

  static const String dopNativeSelfieActiveIntroductionScreenName =
      'dop_native_selfie_active_introduction_screen';

  static const String dopNativeSelfieVerificationScreenName =
      'dop_native_selfie_verification_screen';

  @visibleForTesting
  static const String dopNativeEkycConfirmAdditionalInfoScreenName =
      'dop_native_ekyc_confirm_additional_info_screen';

  @visibleForTesting
  static const String dopNativeSelfieVerificationSuccessScreenName =
      'dop_native_selfie_verify_success_screen';

  @visibleForTesting
  static const String dopNativePDFViewScreenName = 'dop_native_pdf_view_screen';

  @visibleForTesting
  static const String dopNativeWebViewScreenName = 'dop_native_web_view_screen';

  @visibleForTesting
  static const String dopNativeEKYCLimitExceedScreenName = 'dop_native_ekyc_limit_exceed_screen';

  @visibleForTesting
  static const String dopNativeESuccessScreenName = 'dop_native_e_success_screen';

  @visibleForTesting
  static const String dopNativeESuccessCICHoldingScreenName =
      'dop_native_e_success_cic_holding_screen';

  @visibleForTesting
  static const String dopNativeUnderwritingInProgressScreenName =
      'dop_native_underwriting_in_progress_screen';

  @visibleForTesting
  static const String dopNativeUnderwritingCardIssuedScreenName =
      'dop_native_underwriting_card_issued_screen';

  @visibleForTesting
  static const String dopNativeUnderwritingCardStatusScreenName =
      'dop_native_underwriting_card_status_screen';

  @visibleForTesting
  static const String dopNativeCardStatusCICBlockScreenName =
      'dop_native_card_status_cic_block_screen';

  @visibleForTesting
  static const String dopNativeCardActivatedScreenName = 'dop_native_card_activated_screen';

  @visibleForTesting
  static const String dopNativeCardActivatedRetryPosLimitScreenName =
      'dop_native_card_activated_retry_pos_limit_screen';

  @visibleForTesting
  static const String dopNativeCardActivatedPosFailedScreenName =
      'dop_native_card_activated_pos_failed_screen';

  @visibleForTesting
  static const String dopNativeCardActiveRetryScreenName = 'dop_native_card_active_retry_screen';

  @visibleForTesting
  static const String dopNativeCardActiveFailScreenName = 'dop_native_card_active_fail_screen';

  @visibleForTesting
  static const String dopNativeEContractDownloadScreenName =
      'dop_native_e_contract_download_screen';

  @visibleForTesting
  static const String dopNativeAmericaCitizenScreenName = 'dop_native_america_citizen_screen';

  @visibleForTesting
  static const String dopNativeESuccessSemiScreenName = 'dop_native_e_success_semi_screen';

  @visibleForTesting
  static const String dopNativeCardStatusInformationScreenName =
      'dop_native_card_status_information_screen';

  @visibleForTesting
  static const String dopNativeThreeDSecureScreenName = 'dop_native_three_d_secure_screen';

  @visibleForTesting
  static const String nonLoginQrCodeScannerScreenName = 'non_login_qr_code_scanner_screen';

  @visibleForTesting
  static const String dopNativeSalesmanScreenName = 'dop_native_sale_man_screen';

  @visibleForTesting
  static const String dopNativeSalesmanConfirmScreenName = 'dop_native_sale_man_confirm_screen';

  @visibleForTesting
  static const String dopNativeAcquisitionRewardScreenName = 'dop_native_acquisition_reward_screen';

  @visibleForTesting
  static const String dopNativeFaceOtpIntroductionScreenName =
      'dop_native_face_otp_introduction_screen';

  @visibleForTesting
  static const String dopNativeFaceOtpSuccessScreenName = 'dop_native_face_otp_success_screen';

  @visibleForTesting
  static const String dopNativeFaceOtpRetryScreenName = 'dop_native_face_otp_retry_screen';

  @visibleForTesting
  static const String dopNativeNFCDeviceUnsupportedScreenName =
      'dop_native_nfc_device_unsupported_screen';

  @visibleForTesting
  static const String dopNativeCollectLocationScreenName = 'dop_native_collect_location_screen';

  @visibleForTesting
  static const String dopNativeFourthAppraisingScreenName = 'dop_native_fourth_appraising_screen';

  /// Widgets NOT a screen
  @visibleForTesting
  static const String announcementListRewardTabName = 'announcement_list_reward_tab';

  @visibleForTesting
  static const String announcementListTransactionTabName = 'announcement_list_transaction_tab';

  @visibleForTesting
  static const String transactionHistoryListForAuthorizedUserWidgetName =
      'transaction_history_list_for_authorized_user_widget';

  @visibleForTesting
  static const String storyWebViewScreenName = 'story_web_view';

  static Screen byValue(String value) {
    switch (value) {
      case tutorialScreenName:
        return tutorialScreen;
      case splashScreenName:
        return splashScreen;
      case mainScreenName:
        return mainScreen;
      case createPinScreenName:
        return createPinScreen;
      case homeScreenName:
        return homeScreen;
      case announcementListScreenName:
        return announcementListScreen;
      case inputPhoneNumberScreenName:
        return inputPhoneNumberScreen;
      case createEvoCardScreenName:
        return createEvoCardScreen;
      case verifyOtpScreenName:
        return verifyOtpScreen;
      case profileScreenName:
        return profileScreen;
      case feedbackScreenName:
        return feedbackScreen;
      case errorScreenName:
        return errorScreen;
      case profileDetailScreenName:
        return profileDetailScreen;
      case loginScreenName:
        return loginScreen;
      case loginOnOldDeviceScreenName:
        return loginOnOldDeviceScreen;
      case activeBiometricScreenName:
        return activeBiometric;
      case profileSettingScreenName:
        return profileSettingScreen;
      case profileWebViewScreenName:
        return profileWebViewScreen;
      case inputPinScreenName:
        return inputPinScreen;
      case campaignListScreenName:
        return campaignListScreen;
      case announcementListTransactionTabName:
        return announcementListTransactionScreen;
      case announcementListRewardTabName:
        return announcementListRewardScreen;
      case qrCodeScannerScreenName:
        return qrCodeScannerScreen;
      case allPromotionListScreenName:
        return allPromotionListScreen;
      case paymentInputAmountScreenName:
        return paymentInputAmount;
      case confirmPaymentFailScreenName:
        return confirmPaymentFail;
      case threeDPollingPaymentScreenName:
        return threeDPolling;
      case threeDSecurePaymentScreenName:
        return threeDSecurePayment;
      case transactionHistoryListForAuthorizedUserWidgetName:
        return transactionHistoryListForAuthorizedUserWidget;
      case transactionHistoryListScreenName:
        return transactionHistoryListScreen;
      case transactionHistoryDetailScreenName:
        return transactionHistoryDetailScreen;
      case paymentResultScreenName:
        return paymentResultScreen;
      case paymentPromotionListScreenName:
        return paymentPromotionListScreen;
      case linkedCardDetailScreenName:
        return linkedCardDetailScreen;
      case myPromotionListScreenName:
        return myPromotionListScreen;
      case dopCardStatusScreenName:
        return dopCardStatusScreen;
      case cloneConfirmPaymentScreenName:
        return cloneConfirmPayment;
      case linkCardThreeDPollingScreenName:
        return linkCardThreeDPolling;
      case linkCardThreeDSecureScreenName:
        return linkCardThreeDSecureScreen;
      case manualLinkCardResultScreenName:
        return manualLinkCardResultScreen;
      case linkCardSubmissionStatusPollingScreenName:
        return linkCardSubmissionStatusPollingScreen;
      case updateConfirmPaymentScreenName:
        return updateConfirmPaymentScreen;
      case faceOTPInstructionScreenName:
        return faceOTPInstructionScreen;
      case preFaceOtpManualLinkCardScreenName:
        return preFaceOtpManualLinkCardScreen;
      case faceOtpStarterScreenName:
        return faceOTPStarterScreen;
      case faceOtpMatchingErrorScreenName:
        return faceOTPMatchingErrorScreen;
      case faceOtpMatchingProcessScreenName:
        return faceOTPMatchingProcessScreen;
      case ekycErrorScreenName:
        return ekycErrorScreen;

      case faceAuthInstructionScreenName:
        return faceAuthInstructionScreen;
      case preFaceAuthManualLinkCardScreenName:
        return preFaceAuthManualLinkCardScreen;
      case faceAuthStarterScreenName:
        return faceAuthStarterScreen;
      case faceAuthMatchingErrorScreenName:
        return faceAuthMatchingErrorScreen;
      case faceAuthMatchingProcessScreenName:
        return faceAuthMatchingProcessScreen;
      case ekycV2ErrorScreenName:
        return ekycV2ErrorScreen;

      case privatePolicyScreenName:
        return privatePolicyScreen;
      case attentionNotesScreenName:
        return attentionNotesScreen;
      case deleteAccountSuccessScreenName:
        return deleteAccountSuccessScreen;
      case deleteAccountVerifyPinScreenName:
        return deleteAccountVerifyPinScreen;
      case deleteAccountSurveyScreenName:
        return deleteAccountSurveyScreen;
      case referralSharingScreenName:
        return referralSharingScreen;
      case emiOptionScreenName:
        return emiOptionScreen;
      case emiNotSupportScreenName:
        return emiNotSupportScreen;
      case emiManagementListScreenName:
        return emiManagementListScreen;
      case emiManagementDetailScreenName:
        return emiManagementDetailScreen;
      case activateCardScreenName:
        return activateCardScreen;
      case setupPosLimitScreenName:
        return setupPosLimitScreen;
      case setupPosLimitGuidanceScreenName:
        return setupPosLimitGuidanceScreen;
      case activateCardGuidanceScreenName:
        return activateCardGuidanceScreen;
      case activatePosLimitScreenName:
        return activatePosLimitScreen;
      case activatePosLimitThreeDSecureScreenName:
        return activatePosLimitThreeDSecureScreen;
      case nonUserHomeScreenName:
        return nonUserHomeScreen;
      case storyWebViewScreenName:
        return storyWebViewScreen;
      case maintenanceScreenName:
        return maintenanceScreen;
      case dopNativeStatusScreenName:
        return dopNativeStatusScreen;
      case dopNativeVerifyOtpScreenName:
        return dopNativeVerifyOtpScreen;
      case dopNativeWelcomeBackScreenName:
        return dopNativeWelcomeBackScreen;
      case dopNativeIntroductionScreenName:
        return dopNativeIntroductionScreen;
      case dopNativeSubIntroductionScreenName:
        return dopNativeSubIntroductionScreen;
      case dopNativeIdCardCaptureIntroductionScreenName:
        return dopNativeIdCardCaptureIntroductionScreen;
      case dopNativeIdCardFrontSideVerificationScreenName:
        return dopNativeIdCardFrontSideVerificationScreen;
      case dopNativeIdCardQRCodeVerificationScreenName:
        return dopNativeIdCardQRCodeVerificationScreen;
      case dopNativeIdCardBackSideVerificationScreenName:
        return dopNativeIdCardBackSideVerificationScreen;
      case dopNativeIdCardSuccessScreenName:
        return dopNativeIdCardSuccessScreen;
      case dopNativeFailureScreenName:
        return dopNativeFailureScreen;
      case dopNativeEKYCConfirmScreenName:
        return dopNativeEKYCConfirmScreen;
      case dopNativeSingleEKYCConfirmScreenName:
        return dopNativeSingleEKYCConfirmScreen;
      case dopNativeAppraisingVerificationScreenName:
        return dopNativeAppraisingVerificationScreen;
      case dopNativeThreeStepAppraisingVerificationScreenName:
        return dopNativeThreeStepAppraisingVerificationScreen;
      case dopNativeInformSuccessSophiaScreenName:
        return dopNativeInformSuccessSophiaScreen;
      case dopNativeInformSuccessSemiScreenName:
        return dopNativeInformSuccessSemiScreen;
      case dopNativeInformSuccessAutoPCBScreenName:
        return dopNativeInformSuccessAutoPCBScreen;
      case dopNativeInformSuccessAutoCICScreenName:
        return dopNativeInformSuccessAutoCICScreen;
      case dopNativeAppFormAdditionalInfoScreenName:
        return dopNativeAppFormAdditionalInfoScreen;
      case dopNativeESignOTPScreenName:
        return dopNativeESignOTPScreen;
      case dopNativeESignIntroScreenName:
        return dopNativeESignIntroScreen;
      case dopNativeESignIntroMWGScreenName:
        return dopNativeESignIntroMWGScreen;
      case dopNativeAppFormCardDesignSemiScreenName:
        return dopNativeAppFormCardDesignSemiScreen;
      case dopNativeAppFormCardDesignAutoPCBScreenName:
        return dopNativeAppFormCardDesignAutoPCBScreen;
      case dopNativeAppFormCardDesignAutoCICScreenName:
        return dopNativeAppFormCardDesignAutoCICScreen;
      case dopNativeAppFormStatementDateSemiScreenName:
        return dopNativeAppFormStatementDateSemiScreen;
      case dopNativeAppFormStatementDateAutoPCBScreenName:
        return dopNativeAppFormStatementDateAutoPCBScreen;
      case dopNativeAppFormStatementDateAutoCICScreenName:
        return dopNativeAppFormStatementDateAutoCICScreen;
      case dopNativeCifConfirmScreenName:
        return dopNativeCifConfirmScreen;
      case dopNativeCifNoBranchScreenName:
        return dopNativeCifNoBranchScreen;
      case dopNativeSelfieFlashIntroductionScreenName:
        return dopNativeSelfieFlashIntroductionScreen;
      case dopNativeSelfieActiveIntroductionScreenName:
        return dopNativeSelfieActiveIntroductionScreen;
      case dopNativeSelfieVerificationScreenName:
        return dopNativeSelfieVerificationScreen;
      case dopNativeEkycConfirmAdditionalInfoScreenName:
        return dopNativeEkycConfirmAdditionalInfoScreen;
      case dopNativeSelfieVerificationSuccessScreenName:
        return dopNativeSelfieVerificationSuccessScreen;
      case dopNativePDFViewScreenName:
        return dopNativePDFViewScreen;
      case dopNativeWebViewScreenName:
        return dopNativeWebViewScreen;
      case dopNativeEKYCLimitExceedScreenName:
        return dopNativeEKYCLimitExceedScreen;
      case dopNativeESuccessScreenName:
        return dopNativeESuccessScreen;
      case dopNativeESuccessCICHoldingScreenName:
        return Screen.dopNativeESuccessCICHoldingScreen;
      case dopNativeUnderwritingInProgressScreenName:
        return dopNativeUnderwritingInProgressScreen;
      case dopNativeUnderwritingCardIssuedScreenName:
        return dopNativeUnderwritingCardIssuedScreen;
      case dopNativeCardStatusCICBlockScreenName:
        return dopNativeCardStatusCICBlockScreen;
      case dopNativeUnderwritingCardStatusScreenName:
        return dopNativeUnderwritingCardStatusScreen;
      case dopNativeCardActivatedScreenName:
        return dopNativeCardActivatedScreen;
      case dopNativeCardActivatedRetryPosLimitScreenName:
        return dopNativeCardActivatedRetryPosLimitScreen;
      case dopNativeCardActivatedPosFailedScreenName:
        return dopNativeCardActivatedPosFailedScreen;
      case dopNativeCardActiveRetryScreenName:
        return dopNativeCardActiveRetryScreen;
      case dopNativeCardActiveFailScreenName:
        return dopNativeCardActiveFailScreen;
      case dopNativeESignReviewScreenName:
        return dopNativeESignReviewScreen;
      case dopNativeEContractDownloadScreenName:
        return dopNativeEContractDownloadScreen;
      case dopNativeAmericaCitizenScreenName:
        return dopNativeAmericaCitizenScreen;
      case dopNativeESuccessSemiScreenName:
        return dopNativeESuccessSemiScreen;
      case dopNativeCardStatusInformationScreenName:
        return dopNativeCardStatusInformationScreen;
      case dopNativeThreeDSecureScreenName:
        return dopNativeThreeDSecureScreen;
      case nonLoginQrCodeScannerScreenName:
        return nonLoginQrCodeScannerScreen;
      case dopNativeNFCReaderIntroductionScreenName:
        return dopNativeNFCReaderIntroductionScreen;
      case dopNativeSalesmanScreenName:
        return dopNativeSalesmanScreen;
      case dopNativeSalesmanConfirmScreenName:
        return dopNativeSalesmanConfirmScreen;
      case dopNativeAcquisitionRewardScreenName:
        return dopNativeAcquisitionRewardScreen;
      case dopNativeFaceOtpIntroductionScreenName:
        return dopNativeFaceOtpIntroductionScreen;
      case dopNativeFaceOtpSuccessScreenName:
        return dopNativeFaceOtpSuccessScreen;
      case dopNativeFaceOtpRetryScreenName:
        return dopNativeFaceOtpRetryScreen;
      case dopNativeNFCDeviceUnsupportedScreenName:
        return dopNativeNFCDeviceUnsupportedScreen;
      case dopNativeCollectLocationScreenName:
        return dopNativeCollectLocationScreen;
      case dopNativeFourthAppraisingScreenName:
        return dopNativeFourthAppraisingScreen;
      default:
        return mainScreen;
    }
  }

  static Screen byRouteName(String routeName) {
    return Screen.byValue(routeName.replaceAll('/', ''));
  }

  const Screen(this.name);

  final String name;

  String get routeName => '/$name';

  String routeNameWithPathParams(List<String> params) {
    String route = '/$name';
    for (final String param in params) {
      route += '/:$param';
    }
    return route;
  }
}
