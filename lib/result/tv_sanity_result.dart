class Detail {
  String? name;
  double? score;
  String? verdict;

  Detail(this.name, this.score, this.verdict);

  Detail.fromMap(Map<Object?, dynamic> map) {
    name = map['name'];
    String? strScore = map['score']?.toString();
    score = strScore != null ? double.tryParse(strScore) : null;
    verdict = map['verdict'];
  }
}

class TVSanityResult {
  double? score;
  bool? isGood;
  String? error;
  List<Detail?>? details;
  String? requestId;
  String? timestamp;

  TVSanityResult({required this.score, required this.isGood, this.error, this.details, this.requestId, this.timestamp});

  TVSanityResult.fromMap(Map<Object?, dynamic> map) {
    String? strScore = map['score']?.toString();
    score = strScore != null ? double.tryParse(strScore) : null;
    isGood = (map['isGood'] as bool?) != null ? map['isGood'] as bool : null;
    error = map['error'];
    details = (map['details'] as List?)?.map((dynamic e) => Detail.fromMap(e as Map<Object?, dynamic>)).toList();
    requestId = map['requestId'];
    timestamp = map['timestamp'];
  }
}
