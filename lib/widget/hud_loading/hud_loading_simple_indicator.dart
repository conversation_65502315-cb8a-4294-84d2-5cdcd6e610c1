import 'package:flutter/material.dart';

import '../../resources/resources.dart';

class HubLoadingSimpleIndicator extends StatelessWidget {
  final String? loadingText;
  final Widget animation;

  const HubLoadingSimpleIndicator({
    required this.animation,
    super.key,
    this.loadingText,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 44, vertical: 30),
      decoration: BoxDecoration(
        color: evoColors.textActive.withOpacity(0.75),
        borderRadius: const BorderRadius.all(Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: _getContent(),
      ),
    );
  }

  List<Widget> _getContent() {
    final List<Widget> contents = <Widget>[animation];

    if (loadingText?.isNotEmpty == true) {
      contents.addAll(<Widget>[
        const SizedBox(height: 18),
        Text(
          loadingText ?? '',
          style: evoTextStyles.h300(color: Colors.white),
        )
      ]);
    }
    return contents;
  }
}
