import 'package:flutter/material.dart';

import '../../evo_overlay/evo_overlay.dart';

class HudLoading extends EvoOverlay {
  static HudLoading? _instance;

  static final HudLoading _originalInstance = HudLoading._internal();

  factory HudLoading() {
    return _instance ??= _originalInstance;
  }

  HudLoading._internal();

  // Method to replace the singleton instance (for testing only)
  @visibleForTesting
  static void setInstanceForTesting(HudLoading instance) {
    _instance = instance;
  }

  // Method to reset the singleton instance (for testing only)
  @visibleForTesting
  static void resetToOriginalInstance() {
    _instance = _originalInstance;
  }
}
