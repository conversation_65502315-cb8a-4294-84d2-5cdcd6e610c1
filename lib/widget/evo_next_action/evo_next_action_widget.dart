import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../data/repository/campaign_repo.dart';
import '../../feature/webview/models/evo_webview_arg.dart';
import '../../model/evo_action_model.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/evo_action_handler.dart';
import 'earning_voucher_module.dart';
import 'evo_next_action_button.dart';
import 'evo_next_action_cubit.dart';
import 'evo_next_action_dialog_handler.dart';

class EvoNextActionWidget extends StatefulWidget {
  final EvoActionModel? nextAction;
  final bool? isUsed;
  final VoidCallback? preActionBeforeNextAction;

  /// [preActionBeforeNextAction] is an action that you want to perform before handling [nextAction].
  /// It is currently used to save the selected voucher into AppState for the Pre-apply Voucher Feature.
  /// Refer to https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/**********/Pre-select+Vouchers+at+Checkout#i.-Opening-promotion-list.
  const EvoNextActionWidget({
    super.key,
    this.nextAction,
    this.isUsed = false,
    this.preActionBeforeNextAction,
  });

  @override
  State<EvoNextActionWidget> createState() => _EvoNextActionWidgetState();
}

class _EvoNextActionWidgetState extends State<EvoNextActionWidget> {
  final EvoNextActionCubit _cubit = EvoNextActionCubit(campaignRepo: getIt.get<CampaignRepo>());

  /// [_preActionHandledTypes] defines a list of types for which you want to perform some actions before handling [widget.nextAction].
  /// It currently includes the type `promotion_scan_to_pay` to handle the Pre-apply Voucher Feature.
  /// Refer to https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/**********/Pre-select+Vouchers+at+Checkout#i.-Opening-promotion-list.
  final List<String> _preActionHandledTypes = <String>[
    EvoActionModel.promotionScanToPay,
  ];

  @override
  void initState() {
    _cubit.updateUsedVoucher(widget.isUsed);
    super.initState();
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: widget.nextAction != null,
      child: BlocProvider<EvoNextActionCubit>(
        create: (_) => _cubit,
        child: BlocBuilder<EvoNextActionCubit, EvoNextActionState>(
          buildWhen: (EvoNextActionState previous, EvoNextActionState current) {
            return current is EvoNextActionMarkUsed;
          },
          builder: (_, EvoNextActionState state) {
            bool isUsed = false;

            if (state is EvoNextActionMarkUsed) {
              isUsed = state.isUsed ?? false;
            }

            return EvoNextActionButton(
              title: _getLabelNextAction(
                  actionLabel: widget.nextAction?.args?.actionLabel, isUsed: isUsed),
              onPress: isUsed ? null : () => _handlePromotionAction(context, widget.nextAction),
            );
          },
        ),
      ),
    );
  }

  void _handlePromotionAction(BuildContext context, EvoActionModel? nextAction) {
    nextAction?.let(
      (EvoActionModel it) async {
        if (_preActionHandledTypes.contains(it.type)) {
          widget.preActionBeforeNextAction?.call();
        }

        switch (it.type) {
          case EvoActionModel.earnVoucherFromCampaign:
            _earnVoucherFromCampaign(campaignId: it.args?.parameters?.id);
            break;
          default:
            final EvoWebViewArg arg =
                EvoWebViewArg(title: EvoStrings.promotionDetailTitle, url: it.args?.link);

            EvoActionHandler().handle(it, arg: arg);
            break;
        }
      },
    );
  }

  String _getLabelNextAction({String? actionLabel, bool isUsed = false}) {
    if (isUsed == true) {
      return EvoStrings.promotionIsUsed;
    }

    return actionLabel ?? EvoStrings.campaignDefaultAction;
  }

  /// Handle the action to earn a voucher from a campaign.
  /// In case the user is not logged in, it will open the authentication screen. After the user logs in
  /// it will process the action to earn a voucher from the campaign.
  void _earnVoucherFromCampaign({String? campaignId}) {
    actionToBeHandledAfterLogin() async {
      final EarningVoucherModule earningVoucherModule = EarningVoucherModule(
        campaignRepo: getIt.get<CampaignRepo>(),
        dialogHandler: EvoNextActionDialogHandler(),
      );
      await earningVoucherModule.earningVoucher(campaignId);
    }

    EvoActionHandler().handleOpenAuthenticationScreenIfUserNotLoggedIn(
        actionToBeHandledAfterLoggedIn: actionToBeHandledAfterLogin);
  }
}
