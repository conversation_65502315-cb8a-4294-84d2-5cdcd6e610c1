import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../resources/resources.dart';

class EvoNextActionButton extends StatelessWidget {
  final String? title;
  final VoidCallback? onPress;

  const EvoNextActionButton({required this.title, super.key, this.onPress});

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
        color: Colors.white,
        child: CommonButton(
            isWrapContent: false,
            onPressed: onPress,
            style: evoButtonStyles.primary(ButtonSize.xLarge),
            child: Text(title ?? '')));
  }
}
