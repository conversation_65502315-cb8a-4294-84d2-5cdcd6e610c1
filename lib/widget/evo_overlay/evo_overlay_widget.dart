import 'package:flutter/material.dart';

import 'widgets/evo_overlay_entry.dart';

class EvoOverlayWidget extends StatefulWidget {
  final Widget? overlayChild;
  final void Function(EvoOverlayEntry) overlayEntryCallback;
  final WidgetBuilder builder;

  const EvoOverlayWidget({
    required this.overlayChild,
    required this.overlayEntryCallback,
    required this.builder,
    super.key,
  }) : assert(overlayChild != null);

  @override
  State<EvoOverlayWidget> createState() => _EvoOverlayWidgetState();
}

class _EvoOverlayWidgetState extends State<EvoOverlayWidget> {
  late EvoOverlayEntry _overlayEntry;

  @override
  void initState() {
    super.initState();
    _overlayEntry = EvoOverlayEntry(
      builder: widget.builder,
    );
    widget.overlayEntryCallback.call(_overlayEntry);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Overlay(
        initialEntries: <OverlayEntry>[
          EvoOverlayEntry(
            builder: (BuildContext context) {
              if (widget.overlayChild != null) {
                return widget.overlayChild!;
              } else {
                return const SizedBox.shrink();
              }
            },
          ),
          _overlayEntry,
        ],
      ),
    );
  }
}
