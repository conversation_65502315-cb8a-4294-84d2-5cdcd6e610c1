import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../resources/resources.dart';

enum TransactionStatusModel {
  processing('processing'),
  reachMaxRetries('reach_max_retries'),
  success('success'),
  failure('failure'),
  pending('pending');

  final String value;

  const TransactionStatusModel(this.value);

  static TransactionStatusModel? formatStatusString(String? value) {
    switch (value) {
      case 'processing':
        return TransactionStatusModel.processing;
      case 'reach_max_retries':
        return TransactionStatusModel.reachMaxRetries;
      case 'success':
        return TransactionStatusModel.success;
      case 'failure':
        return TransactionStatusModel.failure;
      case 'pending':
        return TransactionStatusModel.pending;
      default:
        return null;
    }
  }
}

extension TransactionStatusModelExt on TransactionStatusModel {
  Color? getTransactionStatusColor() {
    switch (this) {
      case TransactionStatusModel.processing:
      case TransactionStatusModel.reachMaxRetries:
      case TransactionStatusModel.pending:
        return evoColors.transactionHistoryProcessing;

      case TransactionStatusModel.success:
        return evoColors.transactionHistorySuccess;

      case TransactionStatusModel.failure:
        return evoColors.transactionHistoryFailure;

      default:
        return null;
    }
  }

  Color? getTransactionStatusBgColor() {
    switch (this) {
      case TransactionStatusModel.processing:
      case TransactionStatusModel.reachMaxRetries:
      case TransactionStatusModel.pending:
        return evoColors.transactionHistoryBgProcessing;

      case TransactionStatusModel.success:
        return evoColors.transactionHistoryBgSuccess;

      case TransactionStatusModel.failure:
        return evoColors.transactionHistoryBgFailure;

      default:
        return null;
    }
  }

  String getTransactionStatusShortName() {
    switch (this) {
      case TransactionStatusModel.processing:
      case TransactionStatusModel.reachMaxRetries:
      case TransactionStatusModel.pending:
        return EvoStrings.transactionHistoryProcessing;

      case TransactionStatusModel.success:
        return EvoStrings.transactionHistorySuccess;

      case TransactionStatusModel.failure:
        return EvoStrings.transactionHistoryFailure;

      default:
        return '';
    }
  }

  String? getTransactionStatusNameFull() {
    switch (this) {
      case TransactionStatusModel.processing:
      case TransactionStatusModel.reachMaxRetries:
      case TransactionStatusModel.pending:
        return EvoStrings.paymentResultPendingFullName;

      case TransactionStatusModel.success:
        return EvoStrings.paymentResultSuccessFullName;

      case TransactionStatusModel.failure:
        return EvoStrings.paymentResultFailFullName;

      default:
        return null;
    }
  }

  String? getTransactionStatusListIcon() {
    switch (this) {
      case TransactionStatusModel.processing:
      case TransactionStatusModel.reachMaxRetries:
      case TransactionStatusModel.pending:
        return EvoImages.icTransactionHistoryProcessing;

      case TransactionStatusModel.success:
        return EvoImages.icTransactionHistorySuccess;

      case TransactionStatusModel.failure:
        return EvoImages.icTransactionHistoryFailure;

      default:
        return null;
    }
  }

  String getPaymentResultStatusTitle(String? merchantName, {bool isEmiTransaction = false}) {
    String? title;
    switch (this) {
      case TransactionStatusModel.processing:
      case TransactionStatusModel.reachMaxRetries:
      case TransactionStatusModel.pending:
        title = EvoStrings.paymentResultEmiTitleProcess;
        break;
      case TransactionStatusModel.success:
        title = isEmiTransaction
            ? EvoStrings.paymentResultEmiTitleSuccess
            : EvoStrings.paymentResultOutrightOPurchaseTitleSuccess;
        break;
      case TransactionStatusModel.failure:
        title = EvoStrings.paymentResultEmiTitleError;
        break;
      default:
        title = '';
    }
    return title.replaceVariableByValue(<String>[merchantName ?? '']);
  }

  String getTransactionDetailStatusTitle(String? merchantName, {bool isEmiTransaction = false}) {
    String? title;
    switch (this) {
      case TransactionStatusModel.processing:
      case TransactionStatusModel.reachMaxRetries:
      case TransactionStatusModel.pending:
        title = EvoStrings.transactionDetailTitleProcessing;
        break;
      case TransactionStatusModel.success:
        title = isEmiTransaction
            ? EvoStrings.transactionDetailEmiTitleSuccess
            : EvoStrings.transactionDetailOutrightPurchaseTitleSuccess;
        break;
      case TransactionStatusModel.failure:
        title = EvoStrings.transactionDetailTitleErrorPreFix;
        break;
      default:
        title = '';
    }
    return title.replaceVariableByValue(<String>[merchantName ?? '']);
  }

  String getTransactionStatusIconPath() {
    String iconPath;
    switch (this) {
      case TransactionStatusModel.processing:
      case TransactionStatusModel.reachMaxRetries:
      case TransactionStatusModel.pending:
        iconPath = EvoImages.icProcessingEmi;
        break;
      case TransactionStatusModel.success:
        iconPath = EvoImages.icSuccessEmi;
        break;
      case TransactionStatusModel.failure:
        iconPath = EvoImages.icErrorEmi;
        break;
    }
    return iconPath;
  }
}
