
// Enum strings
enum Orientation {
  landscape,
  portrait
}

enum QRType {
  qrCode,
  barCode
}

enum ActionMode {
  FACE_MATCHING,
  FULL,
  LIVENESS,
  READ_CARD_INFO
}
//  NONE, PASSIVE, ACTIVE, FLASH, FLASH_EDGE, FLASH_ADVANCED, FLASH_8, FLASH_16, FLASH_32;
enum LivenessMode {
  active,
  passive,
  none,
  flash,
  flash_edge,
  flash_advanced,
  flash_8,
  flash_16,
  flash_32
}

enum SelfieCameraMode {
  front,
  back,
  both
}

enum CompareImageResult {
  matched,
  unmatched,
  unsure
}

enum CardSide {
  front,
  back
}

enum TVErrorCode {
  authentication_missing_error,
  internal_error,
  sdk_canceled,
  access_denied_exception,
  qr_skip,
  qr_timeout
}


