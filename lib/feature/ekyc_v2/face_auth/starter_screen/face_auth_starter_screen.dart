import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../widget/evo_appbar.dart';
import '../../../../widget/evo_appbar_leading_button.dart';
import '../../ekyc_v2_flow_callback.dart';
import '../../ekyc_v2_flow_mixin.dart';
import '../../ekyc_v2_flow_type.dart';
import '../../error_screen/ekyc_v2_error_screen.dart';
import '../../widgets/ekyc_v2_block_widget.dart';
import '../../widgets/ekyc_v2_loading_widget.dart';
import '../instruction/face_auth_instruction_screen.dart';
import 'face_auth_starter_cubit.dart';

class FaceAuthStarterScreenArg extends PageBaseArg {
  final EkycV2FlowType flowType;
  final EkycV2FlowCallback callback;
  final String? sessionToken;

  FaceAuthStarterScreenArg({
    required this.flowType,
    required this.callback,
    this.sessionToken,
  });
}

class FaceAuthStarterScreen extends PageBase {
  static void pushReplacementNamed({
    required EkycV2FlowType flowType,
    required EkycV2FlowCallback callback,
    required String? sessionToken,
  }) {
    return navigatorContext?.pushReplacementNamed(
      Screen.faceAuthStarterScreen.name,
      extra: FaceAuthStarterScreenArg(
        flowType: flowType,
        callback: callback,
        sessionToken: sessionToken,
      ),
    );
  }

  static void pushNamed({
    required EkycV2FlowType flowType,
    required EkycV2FlowCallback callback,
    required String? sessionToken,
  }) {
    return navigatorContext?.pushNamed(
      Screen.faceAuthStarterScreen.name,
      extra: FaceAuthStarterScreenArg(
        flowType: flowType,
        callback: callback,
        sessionToken: sessionToken,
      ),
    );
  }

  final EkycV2FlowType flowType;
  final EkycV2FlowCallback callback;
  final String? sessionToken;

  const FaceAuthStarterScreen({
    required this.flowType,
    required this.callback,
    this.sessionToken,
    super.key,
  });

  @override
  State<FaceAuthStarterScreen> createState() => _FaceAuthStarterState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.faceAuthStarterScreen.routeName);
}

class _FaceAuthStarterState extends EvoPageStateBase<FaceAuthStarterScreen> with EkycV2FlowMixin {
  late final FaceAuthStarterCubit _cubit = FaceAuthStarterCubit(
    appState: getIt.get<AppState>(),
    flowType: widget.flowType,
  );

  @override
  void initState() {
    super.initState();
    _cubit.initialize(
      sessionToken: widget.sessionToken,
      callback: widget.callback,
    );
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<FaceAuthStarterCubit>(
      create: (_) => _cubit,
      child: BlocConsumer<FaceAuthStarterCubit, FaceAuthStarterState>(
        listener: (BuildContext context, FaceAuthStarterState state) {
          _onStateChangeListener(state);
        },
        builder: (BuildContext context, FaceAuthStarterState state) {
          return _buildContent(state);
        },
      ),
    );
  }

  Widget _buildContent(FaceAuthStarterState state) {
    if (state is FaceAuthStarterLoadingState) {
      return _buildLoadingContent();
    } else if (state is FaceAuthStarterInitializeExceedLimitationState) {
      return _exceedLimitationWidget();
    } else if (state is FaceAuthStarterInitializeSuccessState) {
      return const SizedBox.shrink();
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget _buildLoadingContent() {
    return PopScope(
      canPop: false,
      child: SafeArea(
        child: Scaffold(
          appBar: EvoAppBar(
            leading: CloseButton(
              color: evoColors.icon,
              onPressed: () {
                _handleNormalBackButton();
              },
            ),
          ),
          body: const EkycV2LoadingWidget(
            title: EvoStrings.eKYCLoadingTitle,
            content: EvoStrings.eKYCLoadingContent,
          ),
        ),
      ),
    );
  }

  Widget _exceedLimitationWidget() {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: EvoAppBar(
          leading: EvoAppBarLeadingButton(
            onPressed: () {
              _handleExceedLimitBackButton();
            },
          ),
        ),
        backgroundColor: evoColors.background,
        body: SafeArea(
          child: EkycV2BlockWidget(
            description: EvoStrings.faceOtpExceedLimitationDescription,
            imageUrl: EvoImages.imgLimitationAccess,
            title: EvoStrings.faceOtpExceedLimitErrorTitle,
            buttonTitle: EvoStrings.faceOtpBackButtonTitle,
            onButtonTap: () {
              _handleExceedLimitBackButton();
            },
          ),
        ),
      ),
    );
  }

  void _onStateChangeListener(FaceAuthStarterState state) {
    if (state is FaceAuthStarterInitializeSuccessState) {
      FaceAuthInstructionScreen.pushReplacementNamed(flowType: widget.flowType);
      return;
    }

    if (state is FaceAuthStarterInitializeEkycBridgeErrorState) {
      EkycV2ErrorScreen.pushReplacementNamed(
        isAbleToRetry: checkIsAbleToRetryFacialVerificationIfModuleHasError(
          bridgeErrorReason: state.errorReason,
        ),
        onTapCloseButton: () {
          handleCloseFacialVerificationIfModuleHasError(
            bridgeErrorReason: state.errorReason,
          );
        },
        onTapRetryButton: () {
          FaceAuthStarterScreen.pushReplacementNamed(
            flowType: widget.flowType,
            callback: widget.callback,
            sessionToken: widget.sessionToken,
          );
        },
      );
      return;
    }

    if (state is FaceAuthStarterInitializeApiErrorState) {
      handleEvoApiError(state.errorUIModel);
      if (state.backToEntryPoint) {
        onEkycError(errorReason: EkycV2FlowErrorReason.initializeError);
      }
      return;
    }
  }

  void _handleNormalBackButton() {
    onEkycError(errorReason: EkycV2FlowErrorReason.userCancelled);
  }

  void _handleExceedLimitBackButton() {
    onEkycError(errorReason: EkycV2FlowErrorReason.exceedLimit);
  }
}
