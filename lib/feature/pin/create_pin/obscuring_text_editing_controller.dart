import 'package:flutter/material.dart';

import '../../../util/functions.dart';

class ObscuringTextEditingController extends TextEditingController {
  final String obscureText;

  ObscuringTextEditingController({this.obscureText = '•'});

  bool _showObscureText = true;

  set updateShowObscureText(bool value) {
    _showObscureText = value;
  }

  @override
  TextSpan buildTextSpan(
      {required BuildContext context, required bool withComposing, TextStyle? style}) {
    final String displayValue = _showObscureText
        ? evoUtilFunction.convertStringToObscureText(value.text, obscureText)
        : value.text;
    if (!value.composing.isValid || !withComposing) {
      return TextSpan(style: style, text: displayValue);
    }
    final TextStyle? composingStyle = style?.merge(
      const TextStyle(decoration: TextDecoration.underline),
    );
    return TextSpan(
      style: style,
      children: <TextSpan>[
        TextSpan(text: value.composing.textBefore(displayValue)),
        TextSpan(
          style: composingStyle,
          text: value.composing.textInside(displayValue),
        ),
        TextSpan(text: value.composing.textAfter(displayValue)),
      ],
    );
  }
}
