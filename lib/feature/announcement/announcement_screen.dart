import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../base/evo_page_state_base.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/ui_utils/evo_ui_utils.dart';
import '../../widget/evo_appbar.dart';
import '../feature_toggle.dart';
import 'reward/reward_announcement_screen.dart';
import 'transaction/transaction_announcement_screen.dart';
import 'utils/unread_announcement_checker.dart';
import 'widget/no_announcement_widget.dart';

enum AnnouncementTabType {
  reward(0),
  transaction(1);

  final int value;

  const AnnouncementTabType(this.value);
}

class AnnouncementReloadController {
  void Function()? reloadData;
}

class AnnouncementArg extends PageBaseArg {
  final int? id;

  AnnouncementArg(this.id);
}

class AnnouncementScreen extends PageBase {
  static void pushNamed({int? id}) {
    return navigatorContext?.pushNamed(Screen.announcementListScreen.name,
        extra: AnnouncementArg(id));
  }

  final int? id;

  const AnnouncementScreen({super.key, this.id});

  @override
  EvoPageStateBase<AnnouncementScreen> createState() => AnnouncementScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.announcementListScreen.routeName);
}

@visibleForTesting
class AnnouncementScreenState extends EvoPageStateBase<AnnouncementScreen>
    with SingleTickerProviderStateMixin {
  final AnnouncementReloadController _rewardController = AnnouncementReloadController();
  final AnnouncementReloadController _transactionController = AnnouncementReloadController();
  late TabController _tabController;

  final FeatureToggle _featureToggle = getIt.get<FeatureToggle>();
  final AppState _appState = getIt.get<AppState>();

  final List<String> _listTabTitle = <String>[
    EvoStrings.bottomBarRewardLabel,
    EvoStrings.transactionTitle
  ];

  final UnreadAnnouncementChecker _unreadAnnouncementChecker =
      getIt.get<UnreadAnnouncementChecker>();

  final double _paddingTopPercentage = 0.08;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _listTabTitle.length, vsync: this);
    _tabController.addListener(_handleChangeTab);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleChangeTab);
    super.dispose();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return _appState.isUserLogIn
        ? _featureToggle.enableTransactionNotificationFeature
            ? _buildTabBar()
            : _buildRewardAnnouncementScreen()
        : _itemNonUser();
  }

  Widget _buildRewardAnnouncementScreen() {
    return Scaffold(
      backgroundColor: evoColors.background,
      appBar: EvoAppBar(title: EvoStrings.announcementListTitle),
      body: RewardAnnouncementScreen(
        touchedAnnouncementId: widget.id,
      ),
    );
  }

  Widget _buildTabBar() {
    return DefaultTabController(
      length: _listTabTitle.length,
      child: Scaffold(
        backgroundColor: evoColors.background,
        appBar: EvoAppBar(title: EvoStrings.announcementListTitle, bottom: _itemTabBar()),
        body: Column(
          children: <Widget>[
            Container(
                height: 1, width: context.screenWidth, color: evoColors.disableTextFieldBorder),
            Expanded(child: _itemBody())
          ],
        ),
      ),
    );
  }

  Widget _itemNonUser() => Scaffold(
      backgroundColor: evoColors.background,
      appBar: EvoAppBar(title: EvoStrings.announcementListTitle),
      body: const SafeArea(child: NoAnnouncementWidget()));

  PreferredSizeWidget _itemTabBar() => PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: Align(
            alignment: Alignment.centerLeft,
            child: TabBar(
                controller: _tabController,
                onTap: (_) => _handleChangeTab(),
                labelStyle: evoTextStyles.h300(),
                labelColor: evoColors.textActive,
                indicatorColor: evoColors.primary,
                indicatorSize: TabBarIndicatorSize.tab,
                indicatorPadding: const EdgeInsets.symmetric(horizontal: 20),
                unselectedLabelColor: evoColors.textPassive,
                labelPadding: const EdgeInsets.all(16),
                unselectedLabelStyle: evoTextStyles.bodyLarge(evoColors.textPassive),
                tabs: _listTab())),
      );

  List<Widget> _listTab() => _listTabTitle
      .map((String title) => Text(
            title,
            key: Key(title),
          ))
      .toList();

  Widget _itemBody() => TabBarView(controller: _tabController, children: <Widget>[
        RewardAnnouncementScreen(
          controller: _rewardController,
          touchedAnnouncementId: widget.id,
          paddingTop: _paddingTop,
        ),
        TransactionAnnouncementScreen(
          controller: _transactionController,
          paddingTop: _paddingTop,
        )
      ]);

  void _handleChangeTab() {
    /// True: while we're animating from [previousIndex] to [index]
    /// False: when animation completed
    if (!_tabController.indexIsChanging) {
      if (_tabController.index == AnnouncementTabType.reward.value) {
        _rewardController.reloadData?.call();
      } else {
        _transactionController.reloadData?.call();
      }
    }
  }

  @override
  void didPop() {
    super.didPop();
    _unreadAnnouncementChecker.checkUnreadAnnouncement();
  }

  double get _paddingTop => EvoUiUtils().calculateVerticalSpace(
        context: context,
        heightPercentage: _paddingTopPercentage,
      );
}
