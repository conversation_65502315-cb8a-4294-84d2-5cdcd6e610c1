import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/announcement_repo.dart';
import '../../../data/request/transaction_request.dart';
import '../../../data/response/transaction_entity.dart';
import '../../../data/response/transaction_list_entity.dart';
import '../../../model/type_load_list.dart';
import '../../../resources/global.dart';
import '../utils/mock_file/mock_announcement_file_name.dart';
import 'transaction_announcement_state.dart';

class TransactionCubit extends CommonCubit<TransactionState> {
  TransactionCubit(this._announcementRepo) : super(TransactionState());

  final AnnouncementRepo _announcementRepo;
  final List<TransactionEntity> _transactions = <TransactionEntity>[];
  int _lastNotificationId = 0;
  bool _isLoadMore = true;

  Future<void> loadTransactions(
      {LoadListType type = LoadListType.loadMore, bool isShowLoading = false}) async {
    if (isShowLoading) {
      emit(TransactionLoadingState());
    }
    if (type == LoadListType.refresh) {
      _lastNotificationId = 0;
      _transactions.clear();
    }
    final TransactionListEntity entity = await _announcementRepo.getTransactions(
        TransactionRequest(
            lastNotificationId: _lastNotificationId, limit: defaultNumberItemPerPage, status: null),
        mockConfig: MockConfig(enable: false, fileName: getAnnouncementTransactionsMockFileName()));
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      _handleAnnouncementResp(entity.transactions);
      emit(TransactionLoadedState(transactions: _transactions.toList(), isLoadMore: _isLoadMore));
    } else {
      emit(TransactionErrorState(ErrorUIModel.fromEntity(entity)));
    }
  }

  void _handleAnnouncementResp(List<TransactionEntity>? announcements) {
    if (announcements == null || announcements.isEmpty) {
      _isLoadMore = false;
      return;
    }
    _transactions.addAll(announcements);
    _lastNotificationId = announcements.last.id;
    _isLoadMore = announcements.length == defaultNumberItemPerPage;
  }
}
