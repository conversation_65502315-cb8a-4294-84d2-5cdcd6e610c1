import '../../deep_link/deep_link_constants.dart';

class OneLinkModel {
  /// The URI scheme fallback value to launch the app
  final String afDP;

  /// Campaign
  final String c;

  /// Must use af_xp to pass user-specific data, campaign identifiers,
  /// or other metadata that your app can read and use when the user opens the app through the deep link.
  final String afXp;

  /// Uniquely identifies an AppsFlyer integrated partner.
  final String pid;

  /// Force deep linking into the activity specified in af_dp value
  final bool afForceDeeplink;

  /// Deeplink to open app, handle action,
  /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3433234592/EVO+App+Deep+Links
  final String deepLinkValue;

  /// af_xp, af_dp and af_force_deeplink have fixed value and the same in 3 environments
  /// other value will be change depend one environments, campaign, feature,....
  ///
  /// View defined here: https://trustingsocial1.atlassian.net/browse/EMA-3010
  ///
  const OneLinkModel({
    required this.c,
    required this.pid,
    required this.deepLinkValue,
    this.afXp = 'custom',
    this.afDP = DeepLinkConstants.evoAppDeepLinkPrefix,
    this.afForceDeeplink = true,
  });
}
