part of 'my_voucher_view_cubit.dart';

abstract class MyVoucherViewState extends BlocState {}

class MyVoucherViewLoading extends MyVoucherViewState {}

class MyVoucherViewInfoSuccess extends MyVoucherViewState {
  final List<VoucherEntity>? vouchers;
  final bool canLoadMore;

  MyVoucherViewInfoSuccess({this.vouchers, this.canLoadMore = false});
}

class MyVoucherViewFail extends MyVoucherViewState {
  final ErrorUIModel? error;
  final bool isRefresh;

  MyVoucherViewFail({this.error, this.isRefresh = false});
}
