import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/shimmer/shimmer_style.dart';

class SkeletonVoucherItemWidget extends StatelessWidget {
  final double sizeImage;
  final double cornerRadiusImage;

  const SkeletonVoucherItemWidget(
      {required this.sizeImage, required this.cornerRadiusImage, super.key});

  @override
  Widget build(BuildContext context) {
    return Row(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
      Container(
          width: sizeImage,
          height: sizeImage,
          decoration:
              _shimmerType.copyWith(borderRadius: BorderRadius.circular(cornerRadiusImage))),
      const SizedBox(width: 16),
      Expanded(
          child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
        Container(height: 48, decoration: _shimmerType),
        const SizedBox(height: 4),
        Container(height: 16, decoration: _shimmerType),
        const SizedBox(height: 6),
        Container(height: 32, width: 72, decoration: _shimmerType)
      ]))
    ]);
  }

  BoxDecoration get _shimmerType => ShimmerStyle.shimmerContainerDecoration;
}
