import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/shimmer/shimmer.dart';

import '../../base/evo_page_state_base.dart';
import '../../resources/resources.dart';
import '../../util/ui_utils/evo_ui_utils.dart';
import '../../widget/evo_appbar.dart';
import '../campaign_list/campaign_view/campaign_view.dart';
import '../logging/evo_event_tracking_screen_id.dart';
import 'my_voucher_view/my_voucher_view.dart';

enum PromotionTabType {
  campaign(0),
  myVoucher(1);

  final int value;

  const PromotionTabType(this.value);
}

class PromotionListPage extends PageBase {
  final TabController controller;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.allPromotionListScreen.routeName);

  const PromotionListPage({required this.controller, super.key});

  @override
  EventTrackingScreenId get eventTrackingScreenId => EvoEventTrackingScreenId.offerListingScreen;

  @override
  State<PromotionListPage> createState() => PromotionListPageState();
}

class PromotionListPageState extends EvoPageStateBase<PromotionListPage> {
  final double paddingTopPercentage = 0.086;
  late int numberOfPromotionTab;

  @override
  void initState() {
    numberOfPromotionTab = widget.controller.length;
    super.initState();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return DefaultTabController(
        length: numberOfPromotionTab,
        child: Scaffold(
            backgroundColor: evoColors.background,
            appBar: _appBar(),
            body: SafeArea(
                child: Column(children: <Widget>[
              Container(
                  height: 1, width: context.screenWidth, color: evoColors.disableTextFieldBorder),
              Expanded(child: ShimmerAnimation(child: _contentPromotionList()))
            ]))));
  }

  PreferredSizeWidget _appBar() => EvoAppBar(
      title: EvoStrings.promotionListPageTitle,
      styleTitle: evoTextStyles.h500(),
      centerTitle: false,
      leadingWidth: 0,
      leading: const SizedBox.shrink(),
      bottom: _loggedInTabBar());

  PreferredSizeWidget _loggedInTabBar() => PreferredSize(
      preferredSize: const Size.fromHeight(64),
      child: Align(
          alignment: Alignment.centerLeft,
          child: TabBar(
              controller: widget.controller,
              labelStyle: evoTextStyles.h300(),
              labelColor: evoColors.textActive,
              indicatorColor: evoColors.primary,
              indicatorSize: TabBarIndicatorSize.tab,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              labelPadding: const EdgeInsets.only(top: 12),
              unselectedLabelColor: evoColors.textPassive,
              unselectedLabelStyle: evoTextStyles.bodyLarge(evoColors.textPassive),
              tabs: const <Widget>[
                Tab(text: EvoStrings.allCampaigns),
                Tab(text: EvoStrings.myVoucher)
              ])));

  Widget _contentPromotionList() {
    return TabBarView(controller: widget.controller, children: <Widget>[
      CampaignView(
        onErrorUIModel: (ErrorUIModel? error) {
          handleEvoApiError(error);
        },
        paddingTopOfEmptyView: paddingTopOfEmptyView,
      ),
      MyVoucherView(
        onErrorUIModel: (ErrorUIModel? error) {
          handleEvoApiError(error);
        },
        paddingTopOfEmptyView: paddingTopOfEmptyView,
      )
    ]);
  }

  double get paddingTopOfEmptyView => EvoUiUtils().calculateVerticalSpace(
        context: context,
        heightPercentage: paddingTopPercentage,
      );
}
