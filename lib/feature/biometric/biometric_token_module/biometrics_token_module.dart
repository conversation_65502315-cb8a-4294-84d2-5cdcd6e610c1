// ignore_for_file: avoid_catches_without_on_clauses

import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:local_auth/local_auth.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

import '../../../data/repository/user_repo.dart';
import '../../../data/response/biometric_token_entity.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/functions.dart';
import '../../../util/mock_file_name_utils/mock_user_file_name.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/secure_storage_helper/secure_storage_helper_impl.dart';
import '../../../util/token_utils/jwt_helper.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../logging/evo_logging_event.dart';
import '../activate_biometric/enter_pin_popup/enter_pin_popup.dart';
import '../base/ext_biometric_token_entity.dart';
import '../model/biometric_status_change_notifier.dart';
import '../utils/bio_auth_result.dart';
import '../utils/biometrics_authenticate.dart';
import 'biometric_token_action.dart';
import 'extra_biometric_challenge.dart';

abstract class BiometricTokenModuleCallback {
  void onSuccess();

  void onError(
      {required BiometricTokenModuleErrorType type,
      String? userMessage,
      ErrorUIModel? error,
      BioAuthError? bioError});
}

enum BiometricTokenModuleErrorType {
  noSupportExtraChallenge,
  biometrics,
  ignoreExtraChallenge,
  apiError,
  unknown;
}

class BiometricsTokenModule with LogErrorMixin implements BiometricChallengeCallback {
  final BiometricsAuthenticate biometricsAuthenticate;
  final UserRepo userRepo;
  final EvoLocalStorageHelper secureStorageHelper;
  late BiometricTokenModuleCallback? callback;
  final TsBioDetectChanged bioDetectChanged;
  final JwtHelper jwtHelper;

  bool isProcessing = false;

  BiometricsTokenModule({
    required this.biometricsAuthenticate,
    required this.userRepo,
    required this.secureStorageHelper,
    required this.bioDetectChanged,
    required this.jwtHelper,
  });

  /// When [ignoreChallenge] is true, we will ignore handle [challenge_type] from GetBiometricToken API
  /// default, we **always** check [challenge_type] value and do corresponding action
  /// using LoadingOverlay widget to avoid user perform tap action while [_getBiometricToken] is processing
  /// example:
  ///   GoRoute(
  ///       name: Screen.profileSettingScreen.name,
  ///       path: Screen.profileSettingScreen.routeName,
  ///       builder: (_, GoRouterState state) {
  ///         return const LoadingOverlay(child: ProfileSettingPage());
  ///       },
  ///     ),
  Future<void> enable(
      {BiometricTokenModuleCallback? callback, bool ignoreChallenge = false}) async {
    if (isProcessing) {
      return;
    }
    this.callback = callback;
    isProcessing = true;

    final BioAuthResult result = await biometricsAuthenticate.authenticate(
        localizedReason: EvoStrings.localizedReasonForUsingBiometrics);
    if (result.isAuthSuccess) {
      final BiometricTokenAction action =
          await _getBiometricToken(ignoreChallenge: ignoreChallenge);
      _handleBiometricsToken(action);
    } else {
      _handleBiometricsToken(BiometricTokenAction.biometricError(bioError: result.error));
    }
  }

  Future<void> enableBiometricAuthenticatorFeature(String? biometricToken) async {
    if (biometricToken != null && biometricToken.isNotEmpty) {
      await secureStorageHelper.setBiometricToken(biometricToken);
      await secureStorageHelper.setBiometricAuthenticator(true);
      await bioDetectChanged.initialize();

      getIt.get<AppState>().biometricStatusChangeNotifier.update(BiometricStatus.usable);
    }
  }

  Future<void> disableBiometricAuthenticatorFeature() async {
    await secureStorageHelper.setBiometricAuthenticator(false);
    await secureStorageHelper.delete(key: EvoSecureStorageHelperImpl.biometricTokenKey);
    await secureStorageHelper.saveTimeShowBiometric(evoUtilFunction.getCurrentTimeString());
  }

  Future<bool> isBiometricTokenUsable() async {
    final String? biometricToken = await secureStorageHelper.getBiometricToken();
    return jwtHelper.isCanUse(biometricToken);
  }

  @override
  void onBioChallengeError(ErrorUIModel error) {
    _handleBiometricsToken(BiometricTokenAction.apiError(error: error));
  }

  @override
  void onBioChallengeSuccess(String? biometricToken) {
    _handleBiometricsToken(BiometricTokenAction.success(biometricToken));
  }

  @override
  void onBioChallengeCancel() {
    isProcessing = false;
  }

  Future<BiometricTokenAction> _getBiometricToken({bool ignoreChallenge = false}) async {
    EvoUiUtils().showHudLoading();

    final BiometricTokenEntity entity = await userRepo.getBiometricTokenByPin(
        mockConfig: MockConfig(enable: false, fileName: getBiometricTokenByPinMockFileName()));

    EvoUiUtils().hideHudLoading();

    if (entity.statusCode != CommonHttpClient.SUCCESS) {
      final ErrorUIModel errorModel = ErrorUIModel.fromEntity(entity);
      return BiometricTokenAction.apiError(error: errorModel);
    }

    if (entity.isNeedChallenge() && !ignoreChallenge) {
      final ChallengeType type = entity.getChallengeType();
      final BiometricChallengeWidget? widget = extraChallengeBuilder(type);
      showChallenge(type, widget);
      return BiometricTokenAction.challenge();
    } else {
      return BiometricTokenAction.success(entity.biometricToken);
    }
  }

  @visibleForTesting
  void showChallenge(ChallengeType type, BiometricChallengeWidget? widget) {
    if (widget == null) {
      _handleBiometricsToken(BiometricTokenAction.error(
          err: BiometricTokenModuleErrorType.noSupportExtraChallenge, errMessage: ''));
      return;
    }

    /// adding delay to make transition closing bottom sheet
    /// and opening Biometrics popup smoothly
    Future<void>.delayed(const Duration(milliseconds: 300), () {
      switch (type) {
        case ChallengeType.pin:
          _showBottomSheet(widget);
          break;
        default:
          break;
      }
    });
  }


  @visibleForTesting
  BiometricChallengeWidget? extraChallengeBuilder(ChallengeType type) {
    switch (type) {
      case ChallengeType.pin:
        return EnterPinPopup(callback: this);
      default:
        return null;
    }
  }

  void _showBottomSheet(BiometricChallengeWidget widget) {
    final BuildContext? navigatorCtx = navigatorContext;
    if (navigatorCtx == null) {
      return;
    }
    showModalBottomSheet<void>(
        context: navigatorCtx,
        enableDrag: false,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (_) {
          return widget;
        });
  }

  Future<void> _handleBiometricsToken(BiometricTokenAction action) async {
    if (action.type != ActionType.challenge) {
      isProcessing = false;
    }

    switch (action.type) {
      case ActionType.success:
        await enableBiometricAuthenticatorFeature(action.biometricToken);
        callback?.onSuccess();
        break;
      case ActionType.error:
        callback?.onError(
            type: action.errorType ?? BiometricTokenModuleErrorType.unknown,
            userMessage: action.errorMessage,
            error: action.error,
            bioError: action.bioError);
        break;
      case ActionType.challenge:

        /// do nothing
        break;
      default:
        break;
    }
  }

  /// Pls note that, when [getBiometricChanged] is returns true,
  /// consider to call [disableBiometricAuthenticatorFeature]
  /// to clear all biometric feature
  Future<bool> getBiometricChanged() async {
    bool isBiometricsChanged;
    // Platform messages may fail, so we use a try/catch PlatformException.
    // We also handle the message potentially returning null.
    try {
      isBiometricsChanged = await bioDetectChanged.isBiometricChanged() ?? false;
    } catch (e) {
      logException(
        eventType: EvoEventType.biometrics,
        methodName: 'getBiometricChanged',
        exception: e,
      );
      isBiometricsChanged = false;
    }

    return isBiometricsChanged;
  }

  Future<bool> hasEnrolledBiometrics() async {
    final List<BiometricType> enrolledBiometrics =
        await biometricsAuthenticate.getAvailableBiometricType();
    return enrolledBiometrics.isNotEmpty;
  }

  Future<bool> isEnableBiometricAuthenticator() {
    return secureStorageHelper.isEnableBiometricAuthenticator();
  }
}
