import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../base/evo_page_state_base.dart';
import '../../data/repository/decree_consent_repo.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import '../feature_toggle.dart';
import '../home_screen/non_user/v2/non_user_home_v2_page.dart';
import '../promotion_list/promotion_list_page.dart';
import 'bloc/main_cubit.dart';
import 'main_screen_controller.dart';
import 'main_screen_dialog_handler/main_screen_dialog_handler.dart';
import 'main_screen_initial_action/main_screen_initial_action.dart';
import 'user/user_main_screen.dart';

class MainScreenArg extends PageBaseArg {
  bool isLoggedIn;
  MainScreenChild? initialPage;
  MainScreenInitialAction? initialAction;
  PromotionTabType? initialPromotionTab;
  ActivateBiometricUseCase? activateBiometricUseCase;
  Key? key;

  MainScreenArg({
    required this.isLoggedIn,
    this.initialPage,
    this.initialAction,
    this.initialPromotionTab,
    this.activateBiometricUseCase,
    this.key,
  });
}

class MainScreen extends PageBase {
  /// Make sure you call [EvoPageStateBase.updateUserLoginStatus] function if need
  /// NOTE: be-careful when using this func, if there already this screen in the navigation stack -> initState will not be called
  /// refer bug: https://trustingsocial1.atlassian.net/browse/EMA-6141
  static void goNamed({
    required bool isLoggedIn,
    MainScreenChild? initialPage,
    MainScreenInitialAction? initialAction,
    PromotionTabType? initialPromotionTab,
    ActivateBiometricUseCase? activateBiometricUseCase,
  }) {
    return navigatorContext?.goNamed(
      Screen.mainScreen.name,
      extra: MainScreenArg(
        isLoggedIn: isLoggedIn,
        initialPage: initialPage,
        initialAction: initialAction,
        initialPromotionTab: initialPromotionTab,
        activateBiometricUseCase: activateBiometricUseCase,
      ),
    );
  }

  /// Make sure you call [EvoPageStateBase.updateUserLoginStatus] function if need
  static void pushReplacementNamed({
    required bool isLoggedIn,
    MainScreenChild? initialPage,
    MainScreenInitialAction? initialAction,
    PromotionTabType? initialPromotionTab,
    ActivateBiometricUseCase? activateBiometricUseCase,
  }) {
    return navigatorContext?.pushReplacementNamed(
      Screen.mainScreen.name,
      extra: MainScreenArg(
        isLoggedIn: isLoggedIn,
        initialPage: initialPage,
        initialAction: initialAction,
        initialPromotionTab: initialPromotionTab,
        activateBiometricUseCase: activateBiometricUseCase,

        /// Add unique key to rebuild main screen when pushReplacementNamed
        /// Rebuild is expensive, so if you don't need to rebuild,
        /// please do not pass key
        key: UniqueKey(),
      ),
    );
  }

  static void removeUntilAndPushReplacementNamed({
    required bool isLoggedIn,
    MainScreenChild? initialPage,
    MainScreenInitialAction? initialAction,
    PromotionTabType? initialPromotionTab,
    ActivateBiometricUseCase? activateBiometricUseCase,
  }) {
    return navigatorContext?.removeUntilAndPushReplacementNamed(
      Screen.mainScreen.name,
      (Route<dynamic> route) => route.isFirst,
      extra: MainScreenArg(
        isLoggedIn: isLoggedIn,
        initialPage: initialPage,
        initialAction: initialAction,
        initialPromotionTab: initialPromotionTab,
        activateBiometricUseCase: activateBiometricUseCase,

        /// Add unique key to rebuild main screen when pushReplacementNamed
        /// Rebuild is expensive, so if you don't need to rebuild,
        /// please do not pass key
        key: UniqueKey(),
      ),
    );
  }

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.mainScreen.routeName);

  final bool isLoggedIn;
  final MainScreenInitialAction? initialAction;

  // params of logged-in user screen
  final MainScreenChild? initialPage;
  final PromotionTabType? initialPromotionTab;
  final ActivateBiometricUseCase? activateBiometricUseCase;

  final MainCubit? mainCubit;

  const MainScreen({
    required this.isLoggedIn,
    super.key,
    this.initialPage = MainScreenChild.home,
    this.initialAction,
    this.initialPromotionTab,
    this.activateBiometricUseCase,
    this.mainCubit,
  });

  @override
  State<MainScreen> createState() => MainScreenState();
}

@visibleForTesting
class MainScreenState extends EvoPageStateBase<MainScreen> {
  @visibleForTesting
  late final MainCubit cubit = widget.mainCubit ??
      MainCubit(
        decreeConsentRepo: getIt.get<DecreeConsentRepo>(),
        featureToggle: getIt.get<FeatureToggle>(),
        appState: getIt.get<AppState>(),
        isLoggedIn: widget.isLoggedIn,
        initialAction: widget.initialAction,
        mainScreenDialogHandler: MainScreenDialogHandler(),
      );

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      cubit.initialize();
    });
  }

  // need to ignore because this is an container screen
  // enable will lead to some duplicate event (e.g: handleBiometricChangedIfNeed)
  @override
  bool hasRouteObserver() => false;

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<MainCubit>(
      create: (_) => cubit,
      child: widget.isLoggedIn ? buildUserScreen() : buildNonUserScreen(),
    );
  }

  Widget buildUserScreen() => UserMainScreen(
        activateBiometricUseCase: widget.activateBiometricUseCase,
        initialPage: widget.initialPage,
        initialPromotionTab: widget.initialPromotionTab,
      );

  Widget buildNonUserScreen() => const NonUserHomeV2Page();
}
