import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../../../../../resources/resources.dart';
import '../../../../../base/evo_page_state_base.dart';
import '../../../data/repository/decree_consent_repo.dart';
import '../../../data/response/private_policy_entity.dart';
import '../../../model/evo_dialog_id.dart';
import '../../../model/user_status.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../util/ui_utils/evo_dialog_helper.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import '../../feature_toggle.dart';
import '../../home_screen/user/home_user_page.dart';
import '../../logging/evo_event_tracking_screen_id.dart';
import '../../payment/qrcode_scanner/qrcode_scanner_screen.dart';
import '../../privacy_policy/privacy_policy_screen.dart';
import '../../profile/profile_screen/profile_page.dart';
import '../../promotion_list/promotion_list_page.dart';
import '../../transaction_history_screen/transaction_history_page.dart';
import '../bloc/main_cubit.dart';
import '../bottom_bar_item_model.dart';
import '../bottom_bar_item_widget.dart';
import '../bottom_bar_scanner_button_widget.dart';
import '../main_screen_controller.dart';
import '../navigation_tab_history.dart';
import '../main_screen_dialog_handler/main_screen_dialog_handler.dart';
import 'cubit/user_main_cubit.dart';
import 'cubit/user_main_state.dart';

class UserMainScreen extends PageBase {
  final MainScreenChild? initialPage;
  final PromotionTabType? initialPromotionTab;
  final ActivateBiometricUseCase? activateBiometricUseCase;

  final UserMainCubit? cubit;

  const UserMainScreen({
    this.initialPage,
    this.initialPromotionTab,
    this.activateBiometricUseCase,
    this.cubit,
    super.key,
  });

  @override
  State<UserMainScreen> createState() => UserMainScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EvoEventTrackingScreenId.userMainScreen;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.userMainScreen.routeName);
}

@visibleForTesting
class UserMainScreenState extends EvoPageStateBase<UserMainScreen>
    with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin
    implements MainScreenController {
  static const int numberOfItemNavigationTabHistory = 2;

  @visibleForTesting
  late final MainCubit mainCubit = context.read<MainCubit>();

  @visibleForTesting
  late final UserMainCubit cubit = widget.cubit ??
      UserMainCubit(
        appState: getIt.get<AppState>(),
        decreeConsentRepo: getIt.get<DecreeConsentRepo>(),
        featureToggle: getIt.get<FeatureToggle>(),
      );

  @visibleForTesting
  DateTime? currentBackPressTime;

  @visibleForTesting
  final double bottomTabBarHeight = 60;

  @visibleForTesting
  final List<BottomBarItemModel> bottomBarData = <BottomBarItemModel>[];

  @visibleForTesting
  late PageController pageController;

  @visibleForTesting
  late TabController promotionTabController;

  @visibleForTesting
  final NavigationHistoryStack navigationHistoryTab = NavigationHistoryStack(
    defaultPage: MainScreenChild.home,
    size: numberOfItemNavigationTabHistory,
  );

  @visibleForTesting
  final double welcomeDialogImageHeightPercentage = 0.23;

  @override
  void initState() {
    super.initState();

    promotionTabController = TabController(
      initialIndex: widget.initialPromotionTab?.index ?? 0,
      length: PromotionTabType.values.length,
      vsync: this,
    );
    initBottomBarModel();

    navigationHistoryTab.push(widget.initialPage ?? MainScreenChild.home);

    pageController = PageController(initialPage: navigationHistoryTab.top.pageIndex);
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    promotionTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildVisibilityDetectorPage(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    // Note: Fix for show SnackBar on FAB and TabBar
    // Link: https://stackoverflow.com/a/58834439/10262450
    return BlocProvider<UserMainCubit>(
      create: (_) => cubit,
      child: MultiBlocListener(
        listeners: <BlocListener<dynamic, dynamic>>[
          BlocListener<MainCubit, MainState>(
            listener: (_, MainState state) {
              listenMainStateChange(state);
            },
          ),
          BlocListener<UserMainCubit, UserMainState>(
            listener: (_, UserMainState state) {
              listenUserMainStateChange(state);
            },
          ),
        ],
        child: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return PopScope(
                canPop: false,
                onPopInvokedWithResult: (bool didPop, _) async {
                  if (didPop) {
                    return;
                  }
                  handleOnWillPop();
                },
                child: Scaffold(
                  body: PageView(
                    controller: pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: bottomBarData.map((BottomBarItemModel e) => e.page).toList(),
                  ),
                  bottomNavigationBar: BottomAppBar(
                    color: Colors.white,
                    child: SizedBox(child: buildBottomBarWidget()),
                  ),
                  floatingActionButton: BottomBarScannerButtonWidget(
                    onTap: () {
                      startScanQRPayment();
                    },
                  ),
                  floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
                  resizeToAvoidBottomInset: false,
                ),
              );
            },
          ),
          resizeToAvoidBottomInset: false,
        ),
      ),
    );
  }

  @visibleForTesting
  Widget buildBottomBarWidget() {
    final List<Widget> bottomBarItemWidgets = <Widget>[];
    for (int index = 0; index < bottomBarData.length; index++) {
      final BottomBarItemModel element = bottomBarData[index];
      bottomBarItemWidgets.add(
        Flexible(
          fit: FlexFit.tight,
          child: SizedBox(
            height: bottomTabBarHeight,
            child: BottomBarItemWidget(
              label: element.label,
              icon: element.icon,
              isSelected: index == navigationHistoryTab.top.pageIndex,
              onTap: () {
                changeTabIfNeed(index);
              },
            ),
          ),
        ),
      );
    }

    /// Insert hidden item for scanner button
    bottomBarItemWidgets.insert(
      bottomBarItemWidgets.length ~/ 2,
      Flexible(
        fit: FlexFit.tight,
        child: SizedBox(
          height: bottomTabBarHeight,
          child: BottomBarItemWidget(
            label: EvoStrings.bottomBarScanQRLabel,
            icon: null,
            isSelected: false,
            onTap: () {
              startScanQRPayment();
            },
          ),
        ),
      ),
    );

    return Wrap(children: <Widget>[
      Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: bottomBarItemWidgets,
      )
    ]);
  }

  @visibleForTesting
  void initBottomBarModel() {
    bottomBarData.clear();
    bottomBarData.addAll(
      <BottomBarItemModel>[
        BottomBarItemModel(
          label: EvoStrings.bottomBarHomeLabel,
          icon: EvoImages.icBottomBarHome,
          page: HomeUserPage(mainScreenController: this),
        ),
        BottomBarItemModel(
          label: EvoStrings.bottomBarHistoryLabel,
          icon: EvoImages.icBottomBarHistory,
          page: TransactionHistoryPage(
            isLoggedIn: true,
          ),
        ),
        BottomBarItemModel(
          label: EvoStrings.bottomBarRewardLabel,
          icon: EvoImages.icBottomBarReward,
          page: PromotionListPage(controller: promotionTabController),
        ),
        BottomBarItemModel(
          label: EvoStrings.bottomBarAccountLabel,
          icon: EvoImages.icBottomProfile,
          page: ProfileScreen(mainScreenController: this),
        )
      ],
    );
  }

  @visibleForTesting
  void changeTabIfNeed(int index) {
    if (navigationHistoryTab.top.pageIndex != index) {
      showPageAtIndex(index);
    }
  }

  @visibleForTesting
  void showPageAtIndex(int index) {
    navigationHistoryTab.push(MainScreenChild.getByIndex(index));
    pageController.jumpToPage(index);
    setState(() {});
  }

  @override
  void jumpToPage(MainScreenChild screenChild) {
    showPageAtIndex(screenChild.pageIndex);
  }

  @override
  void jumpToPromotionPage({PromotionTabType type = PromotionTabType.campaign}) {
    promotionTabController.index = type.value;
    showPageAtIndex(MainScreenChild.promotion.pageIndex);
  }

  @override
  MainScreenChild getCurrentPage() {
    return navigationHistoryTab.top;
  }

  /// ** Edge case ** :
  /// Because UserMainScreen contains subPage HomePage, HistoryPage, PromotionPage, ProfilePage (Bottom Navigation Menu)
  /// so when navigate to UserMainScreen => UserMainScreen & 1 of 4 above pages return isTopVisible = true
  /// => [handleBiometricChangedIfNeed] is called 2 times when app resumed. to ensure [handleBiometricChangedIfNeed]
  /// is called 1 time, set [hasRouteObserver()] = false
  @override
  bool hasRouteObserver() => false;

  /// Exist app if the current page is [HomePage] and the user clicked Back button 2 times within 2 seconds.
  /// Otherwise show toast to guide them
  @visibleForTesting
  void handleOnWillPop() {
    // If the current tab is not HomePage, pop to previous history tab
    if (navigationHistoryTab.top != MainScreenChild.home || navigationHistoryTab.length != 1) {
      final MainScreenChild tab = navigationHistoryTab.pop();
      jumpToPage(tab);
      return;
    }

    // If the current page is HomePage, check if the user clicked Back button 2 times within 2 seconds
    // If not, show a toast to guide them
    final DateTime now = DateTime.now();
    if (currentBackPressTime == null ||
        commonUtilFunction.isOverDuration(
          timeToCheck: currentBackPressTime!,
          durationInMilliseconds: 2000,
        )) {
      currentBackPressTime = now;
      showSnackBarNeutral(EvoStrings.existWarning);
      return;
    }
    // If the user clicked Back button 2 times within 2 seconds, exit the app
    SystemNavigator.pop();
  }

  @visibleForTesting
  Future<void> listenMainStateChange(MainState state) async {
    if (state is MainInitializedState && state.isDeepLinkProceed == false) {
      cubit.checkDecreeConsent();
      return;
    }

    if (state is AllFeaturesWithDialogProcessed) {
      cubit.clearUserStatus();
      handleUserStatus(state.userStatus);
      return;
    }
  }

  @visibleForTesting
  Future<void> listenUserMainStateChange(UserMainState state) async {
    if (state is BiometricChangedState) {
      await showBiometricChangedPopupAndUpdateStatus();
      mainCubit.handleFeatureProcessed(FeaturesWithDialogDisplay.biometricChanged);
      return;
    }

    if (state is BiometricUnusableState || state is BiometricValidState) {
      mainCubit.handleFeatureProcessed(FeaturesWithDialogDisplay.biometricChanged);
    }

    if (state is BiometricUnusableState) {
      await showBiometricTokenUnUsableToastAndUpdateStatus();
      return;
    }

    if (state is DecreeConsentedState) {
      handleDecreeConsented();
      return;
    }

    if (state is DecreeNotYetConsentState) {
      handleDecreeNotYetConsent(entity: state.entity);
      return;
    }

    if (state is DecreeConsentErrorState) {
      // only check for server error 500 - refer: https://trustingsocial1.atlassian.net/browse/EMA-1096
      if (state.errorUIModel.statusCode == CommonHttpClient.INTERNAL_SERVER_ERROR) {
        handleDecreeNotYetConsent(errorCode: state.errorUIModel.statusCode);
      }
      handleEvoApiError(state.errorUIModel);
      return;
    }

    if (state is RequestUserActivateBiometricHandledState) {
      mainCubit.handleFeatureProcessed(FeaturesWithDialogDisplay.activeBiometric);
      return;
    }
  }

  @visibleForTesting
  void handleDecreeConsented() {
    mainCubit.handleFeatureProcessed(FeaturesWithDialogDisplay.decreeConsent);
    handleAfterLoggedIn();
  }

  @visibleForTesting
  void handleDecreeNotYetConsent({PrivacyPolicyEntity? entity, int? errorCode}) {
    PrivacyPolicyScreen.pushReplacementNamed(
      privatePolicyEntity: entity,
      errorCode: errorCode,
    );
  }

  @visibleForTesting
  Future<void> handleAfterLoggedIn() async {
    final AppState appState = getIt.get<AppState>();
    if (appState.actionAfterLogin != null) {
      appState.actionAfterLogin?.call();

      // reset callback after called
      appState.actionAfterLogin = null;
      return;
    }

    cubit.handleBiometricFeature(activateBiometricUseCase: widget.activateBiometricUseCase);
    return;
  }

  /// Show WelcomeBackDialog if a user has logged in within 30 days
  /// after their account was successfully deleted.
  @visibleForTesting
  void handleUserStatus(UserStatus? userStatus) {
    if (userStatus != UserStatus.revokeDeletion) {
      return;
    }

    EvoDialogHelper().showDialogConfirm(
      imageHeader: evoImageProvider.asset(
        EvoImages.imgWelcomeBack,
        height: EvoUiUtils().calculateVerticalSpace(
          context: context,
          heightPercentage: welcomeDialogImageHeightPercentage,
        ),
      ),
      isShowButtonClose: true,
      title: EvoStrings.welcomeBackDialogTitle,
      titleTextStyle: evoTextStyles.h500(color: evoColors.textActive),
      content: EvoStrings.welcomeBackDialogContent,
      contentTextStyle: evoTextStyles.bodyMedium(evoColors.textPassive),
      textPositive: EvoStrings.welcomeBackDialogBtn,
      dialogId: EvoDialogId.welcomeBackDialog,
      isDismissible: false,
      positiveButtonStyle: evoButtonStyles.primary(ButtonSize.xLarge),
      onClickPositive: () {
        /// dismiss popup
        navigatorContext?.pop();
      },
      titleTextAlign: TextAlign.center,
      contentTextAlign: TextAlign.center,
    );
  }

  @visibleForTesting
  void startScanQRPayment() {
    cubit.preparePaymentDataIfNeed();
    QrCodeScannerScreen.openSingleInstance();
  }
}
