import 'package:flutter/material.dart';

import '../../../../data/response/payment_result_transaction_entity.dart';
import '../../../../model/transaction_status_model.dart';
import '../../../../resources/resources.dart';
import '../../../../util/extension.dart';

class TransactionHistoryInfoItem extends StatelessWidget {
  final PaymentResultTransactionEntity? transaction;

  const TransactionHistoryInfoItem({super.key, this.transaction});

  @override
  Widget build(BuildContext context) {
    final TransactionStatusModel? status =
        TransactionStatusModel.formatStatusString(transaction?.status);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        _buildTitleWidget(status),
        const SizedBox(height: 4),
        Text(
          transaction?.storeInfo?.address ?? '',
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          style: evoTextStyles.bodySmall().copyWith(height: 1.3),
        ),
        const SizedBox(height: 2),
        Text(
          transaction?.createdAtDateTime?.toNotificationDateTimeFormat(dateFormat: 'dd/MM/yyyy') ??
              '',
          style: evoTextStyles.bodyXSmall(color: evoColors.textPassive2),
        ),
      ],
    );
  }

  Widget _buildTitleWidget(TransactionStatusModel? status) {
    return status != null
        ? Row(
            children: <Widget>[
              evoImageProvider.asset(
                status.getTransactionStatusListIcon() ?? '',
                height: 10,
                width: 10,
                fit: BoxFit.fill,
              ),
              const SizedBox(width: 6),
              Text(
                status.getTransactionStatusShortName(),
                style: evoTextStyles.h100(
                  color: status.getTransactionStatusColor(),
                ),
              ),
            ],
          )
        : const SizedBox.shrink();
  }
}
