import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../data/repository/checkout_repo.dart';
import '../../../data/request/transaction_history_request.dart';
import '../../../data/request/transaction_history_request_v2.dart';
import '../../../data/response/payment_result_transaction_entity.dart';
import '../../../data/response/transaction_history_entity.dart';
import '../../../model/type_load_list.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/global.dart';
import '../../feature_toggle.dart';
import '../../payment/mock_file/mock_checkout_file_name.dart';
import 'transaction_history_state.dart';

class TransactionHistoryCubit extends CommonCubit<TransactionHistoryState> {
  TransactionHistoryCubit(this._checkOutRepo) : super(TransactionHistoryInitialState());

  final CheckOutRepo _checkOutRepo;

  List<PaymentResultTransactionEntity> _transactions = <PaymentResultTransactionEntity>[];
  int _currentPage = defaultFirstPage;
  bool _isLoadMore = true;

  @visibleForTesting
  int get currentPage => _currentPage;

  @visibleForTesting
  bool get isLoadMore => _isLoadMore;

  @visibleForTesting
  List<PaymentResultTransactionEntity> get transactions => _transactions;

  @visibleForTesting
  set isLoadMore(bool value) {
    _isLoadMore = value;
  }

  @visibleForTesting
  set currentPage(int value) {
    _currentPage = value;
  }

  @visibleForTesting
  set transactions(List<PaymentResultTransactionEntity> value) {
    _transactions = value;
  }

  Future<void> loadTransactions({
    LoadListType type = LoadListType.loadMore,
    bool isShowLoading = false,
  }) async {
    if (isShowLoading) {
      emit(TransactionHistoryLoadingState());
    }

    updatePagingRequestData(type);

    await _loadTransactions(type);
  }

  Future<void> _loadTransactions(LoadListType type) async {
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();

    final TransactionHistoryEntity entity =
        featureToggle.transactionHistoryVersion == TransactionHistoryVersion.version_1
            ? await _checkOutRepo.getTransactionsHistory(
                request: TransactionHistoryRequest(
                  pageId: _currentPage,
                  perPage: defaultNumberItemPerPage,
                ),
                mockConfig: MockConfig(
                  enable: false,
                  fileName: getTransactionsHistoryMockFileName(),
                ),
              )
            : await _checkOutRepo.getTransactionsHistoryV2(
                request: TransactionHistoryRequestV2(
                  timeTo: getTimeTo(_transactions),
                  perPage: defaultNumberItemPerPage,
                ),
                mockConfig: MockConfig(
                  enable: false,
                  fileName: getTransactionsHistoryV2MockFileName(),
                ),
              );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      handleTransactionsResponse(entity.transactions);
      emit(TransactionHistoryLoadedState(
        cashback: entity.cashback,
        transactions: _transactions.toList(),
        isLoadMore: _isLoadMore,
      ));
    } else {
      emit(TransactionHistoryErrorState(
        ErrorUIModel.fromEntity(entity),
        type == LoadListType.refresh,
      ));
    }
  }

  /// [timeNow] is created for testing
  @visibleForTesting
  String? getTimeTo(List<PaymentResultTransactionEntity> transactions, {String? timeNow}) {
    if (transactions.isEmpty) {
      return timeNow ?? DateTime.now().toServerFormatInMs();
    }

    final PaymentResultTransactionEntity lastTransaction = transactions.last;
    if (lastTransaction.type == TransactionType.cashback) {
      return lastTransaction.updatedAt;
    }

    return lastTransaction.createdAt;
  }

  @visibleForTesting
  void updatePagingRequestData(LoadListType type) {
    if (type == LoadListType.refresh) {
      _currentPage = defaultFirstPage;
      _transactions.clear();
    }
  }

  @visibleForTesting
  void handleTransactionsResponse(List<PaymentResultTransactionEntity>? transactions) {
    if (transactions == null || transactions.isEmpty) {
      _isLoadMore = false;
      return;
    }
    _transactions.addAll(transactions);

    _isLoadMore = transactions.length == defaultNumberItemPerPage;
    if (_isLoadMore) {
      _currentPage++;
    }
  }
}
