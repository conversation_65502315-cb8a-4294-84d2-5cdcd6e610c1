import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../data/response/current_cashback_info_entity.dart';
import '../../../../resources/resources.dart';
import '../../../../util/functions.dart';
import '../../../../widget/evo_divider_widget.dart';
import '../../../emi_management/widgets/emi_custom_progressbar_widget.dart';

class CashbackContentDialogWidget extends StatefulWidget {
  final CurrentCashbackInfoEntity? currentCashback;
  final List<CurrentCashbackInfoEntity>? previousCashbackRecords;

  const CashbackContentDialogWidget({
    required this.currentCashback,
    required this.previousCashbackRecords,
    super.key,
  });

  @override
  State<CashbackContentDialogWidget> createState() => _CashbackContentDialogWidgetState();
}

class _CashbackContentDialogWidgetState extends State<CashbackContentDialogWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        _progressCallbackItem(context),
        const SizedBox(height: 16),
        _callbackList(widget.previousCashbackRecords),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _progressCallbackItem(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          _buildTitleMonth(
            month: widget.currentCashback?.month,
            year: widget.currentCashback?.year,
          ),
          style: evoTextStyles.bodyLarge(evoColors.textPassive),
        ),
        const SizedBox(height: 8),
        Text(
          _formatCashbackCurrency(
            amount: widget.currentCashback?.amount,
            limit: widget.currentCashback?.limit,
          ),
          style: evoTextStyles.h400(),
        ),
        const SizedBox(height: 16),
        _buildCashbackProcess(
          context: context,
          amount: widget.currentCashback?.amount,
          limit: widget.currentCashback?.limit,
        ),
        const SizedBox(height: 16),
        Text(
          EvoStrings.transactionHistoryCashbackBottomSheetDesc,
          style: evoTextStyles.bodyMedium(evoColors.textPassive),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildCashbackProcess({
    required BuildContext context,
    required int? amount,
    required int? limit,
  }) {
    if (amount == null || limit == null) {
      return const SizedBox.shrink();
    }

    final double progressPaidAmount = amount / limit;
    return EmiCustomProgressbarWidget(
      width: context.screenWidth,
      height: 6,
      background: evoColors.transactionHistoryTotalAmountProgress,
      progressBarColor: evoColors.transactionHistoryPaidAmount,
      progress: progressPaidAmount,
    );
  }

  Widget _callbackList(List<CurrentCashbackInfoEntity>? cashbackTransactions) {
    if (cashbackTransactions == null || cashbackTransactions.isEmpty) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      height: 339,
      child: ListView.separated(
        separatorBuilder: (BuildContext context, int index) => EvoDividerWidget(
          height: 1,
          color: evoColors.divider,
        ),
        itemBuilder: (BuildContext context, int index) {
          return _callbackItem(cashbackTransactions.elementAt(index));
        },
        itemCount: cashbackTransactions.length,
      ),
    );
  }

  Widget _callbackItem(CurrentCashbackInfoEntity? entity) {
    return Column(
      children: <Widget>[
        Divider(
          height: 1,
          color: evoColors.divider,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(EvoStrings.transactionHistoryCashbackNameBottomSheetTitle,
                        style: evoTextStyles.h200()),
                    const SizedBox(height: 4),
                    Text(
                      _buildTitleMonth(
                        month: entity?.month,
                        year: entity?.year,
                      ),
                      style: evoTextStyles.bodySmall(color: evoColors.textPassive),
                    ),
                  ],
                ),
              ),
              Text(
                '+${evoUtilFunction.evoFormatCurrency(
                  entity?.amount,
                  currencySymbol: vietNamCurrencySymbol,
                )}',
                style: evoTextStyles.h300(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _buildTitleMonth({
    int? month,
    int? year,
  }) {
    if (month == null) {
      return '';
    }

    final int yearNow = DateTime.now().year;
    final int monthNow = DateTime.now().month;
    if (year != null && yearNow == year && monthNow == month) {
      return EvoStrings.transactionHistoryCashbackPeriodTitle
          .replaceVariableByValue(<String>[month.toString()]);
    }

    return EvoStrings.transactionHistoryCashbackPeriodTitle
        .replaceVariableByValue(<String>['${month.toString()}/${year.toString()}']);
  }

  String _formatCashbackCurrency({
    int? amount,
    int? limit,
  }) {
    return '${_handleFormatCurrency(amount)}/${_handleFormatCurrency(limit)}';
  }

  String _handleFormatCurrency(num? value) {
    return evoUtilFunction.evoFormatCurrency(
      value?.toInt(),
      currencySymbol: vietNamCurrencySymbol,
    );
  }
}
