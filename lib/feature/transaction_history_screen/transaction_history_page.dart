import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';

import '../../base/evo_page_state_base.dart';
import '../../resources/resources.dart';
import '../../widget/evo_appbar.dart';
import 'transaction_history_no_login_widget.dart';
import 'transaction_list/transaction_history_list_widget.dart';

class TransactionHistoryPage extends PageBase {
  final bool isLoggedIn;

  const TransactionHistoryPage({
    required this.isLoggedIn,
    super.key,
  });

  @override
  State<TransactionHistoryPage> createState() => _HistoryPageState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.transactionHistoryListScreen.routeName);
}

class _HistoryPageState extends EvoPageStateBase<TransactionHistoryPage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildVisibilityDetectorPage(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      appBar: EvoAppBar(
          backgroundColor: Colors.white,
          title: EvoStrings.transactionHistory,
          styleTitle: evoTextStyles.h500(),
          centerTitle: false,
          leadingWidth: 0,
          leading: const SizedBox.shrink()),
      body: widget.isLoggedIn
          ? TransactionHistoryListWidget()
          : const TransactionHistoryNoLoginWidget(),
    );
  }
}
