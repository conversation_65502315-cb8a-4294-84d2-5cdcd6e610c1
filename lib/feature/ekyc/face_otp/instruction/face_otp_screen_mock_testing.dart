import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/util/permission/device_permission.dart';
import 'package:flutter_common_package/util/permission/permission_handler_mixin.dart';

import '../../../../prepare_for_app_initiation.dart';
import '../../../mock_test/mock_test_feature_type.dart';
import '../../../mock_test/mock_test_helper.dart';

mixin FaceOtpInstructionScreenMockTesting implements PermissionHandlerCallback {
  final MockTestHelper _mockTestHelper = getIt.get<MockTestHelper>();

  MockTestFeatureType get mockTestFeatureType;

  @visibleForTesting
  bool isIgnoredMockTest = false;

  String? mockFaceOTPImageId;

  bool get isMockDataAvailable =>
      isEnableMockFaceOtpTestFlow && (mockFaceOTPImageId?.isNotEmpty == true);

  bool get isEnableMockFaceOtpTestFlow =>
      !isIgnoredMockTest && _mockTestHelper.canExecutedMockTest(mockTestFeatureType);

  void requestStoragePermissionForMockTest() {
    if (isEnableMockFaceOtpTestFlow) {
      _mockTestHelper.requestStoragePermission(this);
    }
  }

  @override
  void onDeniedPermission(TsDevicePermission permission) {
    if (permission == MockTestHelper.requiredPermission) {
      isIgnoredMockTest = true;
      onIgnoreMockTest();
    }
  }

  @override
  void onGrantedPermission(TsDevicePermission permission) {
    if (permission == MockTestHelper.requiredPermission) {
      startMockTestFaceOtpIfCan();
    }
  }

  @visibleForTesting
  Future<void> startMockTestFaceOtpIfCan() async {
    if (!isEnableMockFaceOtpTestFlow) {
      return;
    }

    final String? imageId = await _mockTestHelper.initMockTestFaceOtpFlow(mockTestFeatureType);

    if (imageId?.isNotEmpty == true) {
      mockFaceOTPImageId = imageId;

      onMockDataIsReady();
    } else {
      isIgnoredMockTest = true;
      onIgnoreMockTest();
    }
  }

  void onMockDataIsReady();

  void onIgnoreMockTest();
}
