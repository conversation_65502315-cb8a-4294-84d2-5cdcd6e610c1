import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/repository/ekyc_repo.dart';
import '../../../../data/response/ekyc_face_matching_result_entity.dart';
import '../../../../data/response/reset_pin_entity.dart';
import '../../../../data/response/sign_in_otp_entity.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../ekyc_bridge/ekyc_bridge.dart';
import '../../ekyc_flow_mixin.dart';
import '../../model/ekyc_for_flow_type.dart';
import '../../model/face_otp_payload.dart';
import '../../widgets/ekyc_matching_process_widget.dart';
import '../error_screen/face_otp_matching_error_screen.dart';
import 'face_otp_matching_process_cubit.dart';

class FaceOtpMatchingProcessScreenArg extends PageBaseArg {
  final String selfieImageId;
  final EkycFlowType flowType;

  FaceOtpMatchingProcessScreenArg({
    required this.flowType,
    required this.selfieImageId,
  });
}

class FaceOtpMatchingProcessScreen extends PageBase {
  static void pushReplacementNamed({
    required EkycFlowType flowType,
    String? selfieImageId,
  }) {
    return navigatorContext?.pushReplacementNamed(
      Screen.faceOTPMatchingProcessScreen.name,
      extra: FaceOtpMatchingProcessScreenArg(
        flowType: flowType,
        selfieImageId: selfieImageId!,
      ),
    );
  }

  final String selfieImageId;
  final EkycFlowType flowType;

  const FaceOtpMatchingProcessScreen({
    required this.selfieImageId,
    required this.flowType,
    super.key,
  });

  @override
  State<FaceOtpMatchingProcessScreen> createState() => _FaceOtpMatchingProcessState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.faceOTPMatchingProcessScreen.routeName);
}

class _FaceOtpMatchingProcessState extends EvoPageStateBase<FaceOtpMatchingProcessScreen>
    with EkycFlowMixin {
  final FaceOtpMatchingProcessCubit _cubit = FaceOtpMatchingProcessCubit(
    ekycRepo: getIt.get<EKYCRepo>(),
    ekycBridge: getIt.get<EkycBridge>(),
    authenticationRepo: getIt.get<AuthenticationRepo>(),
  );

  @override
  void initState() {
    super.initState();
    if (widget.flowType == EkycFlowType.linkCard) {
      _cubit.faceMatchingForLinkCard(selfieImageId: widget.selfieImageId);
    } else if (widget.flowType == EkycFlowType.faceOtpSignIn) {
      _cubit.faceMatchingForSignIn(selfieImageId: widget.selfieImageId);
    }
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<FaceOtpMatchingProcessCubit>(
      create: (_) => _cubit,
      child: PopScope(
        canPop: false,
        child: Scaffold(
          backgroundColor: evoColors.background,
          body: BlocConsumer<FaceOtpMatchingProcessCubit, FaceOtpMatchingProcessState>(
            listener: (BuildContext context, FaceOtpMatchingProcessState state) {
              _handleFaceMatchingState(state);
            },
            builder: (BuildContext context, FaceOtpMatchingProcessState state) {
              return const EKYCMatchingProcessWidget();
            },
          ),
        ),
      ),
    );
  }

  void _handleFaceMatchingState(FaceOtpMatchingProcessState state) {
    if (state is FaceOtpMatchingSuccess) {
      _handleFaceOTPMatchingSuccess(state.result);
      return;
    }

    if (state is FaceOtpMatchingLimitExceed) {
      _handleFaceMatchingLimitExceed(state);
      return;
    }

    if (state is FaceOtpMatchingError) {
      _handleFaceMatchError(state);
      return;
    }
  }

  void _handleFaceMatchingLimitExceed(FaceOtpMatchingLimitExceed state) {
    _goToFaceOTPMatchingErrorScreen(
      errorUIModel: state.error,
      errorType: FaceOtpMatchingErrorType.limitExceed,
    );
  }

  void _handleFaceMatchError(FaceOtpMatchingError state) {
    final ErrorUIModel errorUIModel = state.error;
    if (errorUIModel.statusCode == CommonHttpClient.INVALID_TOKEN) {
      switch (widget.flowType) {
        case EkycFlowType.linkCard:
          // do nothing, due to app is unauthorized -> app automatically show bottom up to force user login again
          return;
        case EkycFlowType.faceOtpSignIn:
          // handle face auth sign in error
          _goToFaceOTPMatchingErrorScreen(
            errorUIModel: state.error,
            errorType: FaceOtpMatchingErrorType.sessionExpired,
          );
          return;
      }
    }

    _goToFaceOTPMatchingErrorScreen(errorUIModel: state.error);
  }

  void _handleFaceOTPMatchingSuccess(Object entity) {
    showSnackBar(EvoStrings.faceOtpSuccess);

    FaceOtpPayload payload = FaceOtpPayload(sessionToken: null, entity: null);
    if (entity is EkycFaceMatchingResultEntity) {
      payload = FaceOtpPayload(sessionToken: entity.sessionToken, entity: null);
    } else if (entity is SignInOtpEntity) {
      payload = FaceOtpPayload(
        sessionToken: entity.sessionToken,
        entity: entity,
      );
    } else if (entity is ResetPinEntity) {
      payload = FaceOtpPayload(
        sessionToken: entity.sessionToken,
        entity: entity,
      );
    }

    onEkycSuccess(context, payload);
  }

  void _goToFaceOTPMatchingErrorScreen({
    required ErrorUIModel errorUIModel,
    FaceOtpMatchingErrorType errorType = FaceOtpMatchingErrorType.other,
  }) {
    FaceOtpMatchingErrorScreen.pushReplacementNamed(
      flowType: widget.flowType,
      errorUIModel: errorUIModel,
      errorType: errorType,
    );
  }
}
