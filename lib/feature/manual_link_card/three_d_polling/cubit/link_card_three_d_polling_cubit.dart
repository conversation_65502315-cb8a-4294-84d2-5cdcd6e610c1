import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/user_repo.dart';
import '../../../../data/response/submit_link_card_entity.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../util/mock_file_name_utils/mock_user_file_name.dart';
import '../../../payment/payment_shared_data_cubit.dart';

part 'link_card_three_d_polling_state.dart';

class LinkCardThreeDPollingCubit extends PaymentSharedDataCubit<LinkCardThreeDPollingState> {
  final UserRepo _userRepo;

  late String? _linkCardSession;
  late String? _linkCardRequestId;

  LinkCardThreeDPollingCubit(
    this._userRepo,
    AppState appState,
  ) : super(LinkCardThreeDPollingInitial(), appState);

  void processLinkCardRequestData() {
    /// Clear link card request id
    _linkCardRequestId = linkCardRequestId;
    _linkCardSession = linkCardSession;
    clearLinkCardRequestData();
  }

  Future<void> submitLinkCard() async {
    emit(LinkCardThreeDPollingLoading());

    final SubmitLinkCardEntity submitLinkCardEntity = await _userRepo.submitLinkCard(
      linkCardSession: _linkCardSession,
      linkCardRequestId: _linkCardRequestId,
      mockConfig: MockConfig(
        enable: false,
        fileName: submitLinkCardMockFileName(),
      ),
    );

    if (submitLinkCardEntity.statusCode == CommonHttpClient.SUCCESS) {
      emit(LinkCardThreeDPollingSuccess(data: submitLinkCardEntity));
    } else {
      emit(LinkCardThreeDPollingError(error: ErrorUIModel.fromEntity(submitLinkCardEntity)));
    }
  }
}
