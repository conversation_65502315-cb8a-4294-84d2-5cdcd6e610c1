import 'package:flutter/cupertino.dart';

enum PollingEvent {
  done,
  reset,
  cancel,
}

abstract class PollingHandler {
  /// Setup Init polling and timeout
  /// [limitPollingTimeInMs] is limit time for polling => Timeout
  /// [intervalPollingTimeInMs] is interval time for polling
  /// [listenPollingTimeOut] is callback when polling timeout
  void startPolling({
    required int limitPollingTimeInMs,
    required int intervalPollingTimeInMs,
    required VoidCallback listenPollingTimeOut,
  });

  /// Do action when polling
  /// [onDoPolling] is callback when polling
  Future<void> doPolling({required VoidCallback onDoPolling});

  /// Change state for polling with [event]
  void executePollingEvent({required PollingEvent event});

  /// Update Interval Polling Time
  /// [intervalPollingTimeInMs] is interval time for polling
  void updateIntervalPollingTime({required int? intervalPollingTimeInMs});

  /// Check can polling
  bool canPolling();

  /// Check polling is done
  bool isPollingDone();

  /// Check polling is stopped
  bool isPollingStopped();

  /// Dispose Polling
  /// Cancel all timer
  void disposePolling();
}
