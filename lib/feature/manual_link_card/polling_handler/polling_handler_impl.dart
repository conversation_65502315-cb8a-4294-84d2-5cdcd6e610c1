import 'dart:async';

import 'package:flutter/material.dart';

import 'polling_controller.dart';
import 'polling_handler.dart';

class PollingHandlerImpl implements PollingHandler {
  @visibleForTesting
  PollingController pollingController = PollingController();

  @visibleForTesting
  late int intervalPollingTimeInMilliseconds;
  @visibleForTesting
  Timer? timerTimeout;
  @visibleForTesting
  Timer? timerIntervalPolling;

  @override
  void startPolling({
    required int limitPollingTimeInMs,
    required int intervalPollingTimeInMs,
    required VoidCallback listenPollingTimeOut,
  }) {
    updateIntervalPollingTime(intervalPollingTimeInMs: intervalPollingTimeInMs);
    pollingController.start();
    _startTimeoutCountdown(
      limitPollingTimeInMs: limitPollingTimeInMs,
      listenPollingTimeOut: listenPollingTimeOut,
    );
  }

  void _startTimeoutCountdown({
    required int limitPollingTimeInMs,
    required VoidCallback listenPollingTimeOut,
  }) {
    timerTimeout = Timer(Duration(milliseconds: limitPollingTimeInMs), () {
      pollingController.timeout();
      listenPollingTimeOut.call();
    });
  }

  @override
  bool canPolling() => pollingController.canPolling;

  @override
  void executePollingEvent({required PollingEvent event}) {
    switch (event) {
      case PollingEvent.done:
        pollingController.done();
        break;
      case PollingEvent.cancel:
        pollingController.cancel();
        break;
      case PollingEvent.reset:
        pollingController.reset();
        break;
    }
  }

  @override
  Future<void> doPolling({required VoidCallback onDoPolling}) async {
    timerIntervalPolling = Timer(Duration(milliseconds: intervalPollingTimeInMilliseconds), () {
      // Restart polling controller
      pollingController.start();
      onDoPolling.call();
    });
  }

  @override
  void updateIntervalPollingTime({required int? intervalPollingTimeInMs}) {
    if (intervalPollingTimeInMs == null || intervalPollingTimeInMs <= 0) {
      return;
    }

    intervalPollingTimeInMilliseconds = intervalPollingTimeInMs;
  }

  @override
  bool isPollingStopped() => pollingController.isStopPolling;

  @override
  bool isPollingDone() => pollingController.isDone;

  @override
  void disposePolling() {
    timerIntervalPolling?.cancel();
    timerTimeout?.cancel();
  }
}
