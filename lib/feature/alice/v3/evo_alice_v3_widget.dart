import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../resources/resources.dart';
import '../../webview/models/evo_webview_arg.dart';
import '../alice_utils.dart';
import 'evo_alice_v3_config_values.dart';

class EvoAliceV3 extends StatelessWidget {
  const EvoAliceV3({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        child: Stack(
          children: <Widget>[
            CommonWebView(
              arg: EvoWebViewArg(
                title: null,
                formatAdditionalSpecialLink: AliceUtils().getSpecialLinkRegExp(),
                additionalSpecialLinkCallback: (String? url) {
                  CommonWebView.pushNamed(arg: EvoWebViewArg(title: null, url: url));
                },
                url: EvoAliceV3ConfigValues.getAliceV3BaseUrlBasedOnEnvironment(),
                appBar: const PreferredSize(
                  preferredSize: Size.zero,
                  child: SizedBox.shrink(),
                ),
                //resizeToAvoidBottomInset true to make sure the input widget is push up when keyboard show
                resizeToAvoidBottomInset: true,
              ),
            ),
            Positioned(
              right: 8,
              top: 8,
              child: CloseButton(
                color: evoColors.icon,
                onPressed: () => navigatorContext?.pop(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
