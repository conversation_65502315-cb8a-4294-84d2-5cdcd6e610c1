import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/in_app_review/in_app_review_wrapper.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/ui_strings.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../model/evo_dialog_id.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/evo_snackbar.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../util/ui_utils/evo_dialog_helper.dart';
import '../feature_toggle.dart';

enum ReviewStatus {
  error,
  available,
  lostConnection,
}

mixin InAppReviewHandlerMixin {
  final EvoLocalStorageHelper _localStorageHelper = getIt.get<EvoLocalStorageHelper>();
  final InAppReviewWrapper _inAppReviewWrapper = getIt.get<InAppReviewWrapper>();
  final FeatureToggle _featureToggle = getIt.get<FeatureToggle>();

  @visibleForTesting
  final String evoAppAppleId = '**********';
  @visibleForTesting
  static const int delayRequestRatingPopupInMs = 2000;

  /// Flag used to prevent Review/rating popup from being displayed
  /// if the user leaves the screen during [delayRequestRatingPopupInMs] delay
  @visibleForTesting
  bool isRequestingShowPopup = false;

  Future<void> showRequestRatingDialogIfNeeded({
    required String title,
    required String content,
  }) async {
    if (!_featureToggle.enableRequestReviewRatingFeature) {
      return;
    }

    final bool? hasShownPopup = await _localStorageHelper.getValueToCheckReviewPopupShown();
    if (hasShownPopup == true) {
      commonLog('Review/rating is not shown, because popup was already shown');
      return;
    }

    commonLog('Delay $delayRequestRatingPopupInMs to show Review/rating popup');
    await Future<void>.delayed(const Duration(milliseconds: delayRequestRatingPopupInMs));
    if (isRequestingShowPopup) {
      commonLog('Dismiss Review/rating popup');
      isRequestingShowPopup = false;
      return;
    }

    commonLog('Show Review/rating popup');
    return EvoDialogHelper().showDialogConfirm(
      isDismissible: false,
      dialogId: EvoDialogId.requestRatingAndReviewDialog,
      title: title,
      content: content,
      textNegative: EvoStrings.ignore,
      textPositive: EvoStrings.review,
      onClickNegative: () async {
        /// dismiss popup
        navigatorContext?.pop();
      },
      onClickPositive: () async {
        await requestRatingReview();
      },
    );
  }

  @visibleForTesting
  Future<void> onClose() async {
    /// dismiss popup
    navigatorContext?.pop();

    await _localStorageHelper.setValueToCheckReviewPopupShown(true);
  }

  @visibleForTesting
  Future<ReviewStatus> requestRatingReview() async {
    if (!(await checkInternetConnection())) {
      return ReviewStatus.lostConnection;
    }

    onClose();

    try {
      if (await _inAppReviewWrapper.isAvailable()) {
        await _inAppReviewWrapper.requestReview();
        return ReviewStatus.available;
      } else {
        /// If the in-app review is not available,
        /// open the store listing to the user reviews
        await openStoreListing();
        return ReviewStatus.available;
      }
    } on Exception catch (e) {
      commonLog('Error requesting review: $e');
      return ReviewStatus.error;
    }
  }

  @visibleForTesting
  Future<void> openStoreListing() async {
    await _inAppReviewWrapper.openStoreListing(
      appStoreId: evoAppAppleId,
    );
  }

  @visibleForTesting
  Future<bool> checkInternetConnection() async {
    final NetworkManager networkManager = getIt.get<NetworkManager>();
    if (networkManager.hasInternet) {
      return true;
    }

    final EvoSnackBar snackBar = getIt.get<EvoSnackBar>();
    snackBar.show(
      CommonStrings.genericNoInternetErrorMessage,
      typeSnackBar: SnackBarType.error,
      durationInSec: SnackBarDuration.short.value,
    );
    return false;
  }

  /// prevent Review/rating popup from being displayed
  /// if the user leaves the screen or another screen is pushed on the screen
  /// during [delayRequestRatingPopupInMs] delay
  void disposeShowRatingPopup() {
    isRequestingShowPopup = true;
  }
}
