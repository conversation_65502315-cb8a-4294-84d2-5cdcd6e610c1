import 'package:flutter/foundation.dart';

import '../../widget/evo_overlay/evo_overlay.dart';

/// NOTE: init TermAndConditionOverlay in main.dart
class TermAndConditionOverlay extends EvoOverlay {
  static TermAndConditionOverlay? _instance;

  static final TermAndConditionOverlay _originalInstance = TermAndConditionOverlay._internal();

  factory TermAndConditionOverlay() {
    return _instance ??= _originalInstance;
  }

  TermAndConditionOverlay._internal();

  // Method to replace the singleton instance (for testing only)
  @visibleForTesting
  static void setInstanceForTesting(TermAndConditionOverlay instance) {
    _instance = instance;
  }

  // Method to reset the singleton instance (for testing only)
  @visibleForTesting
  static void resetToOriginalInstance() {
    _instance = _originalInstance;
  }
}
