import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/token_utils/jwt_helper.dart';
import '../../biometric/biometric_token_module/biometric_token_usability_mixin.dart';
import '../../biometric/biometric_token_module/biometrics_token_module.dart';
import '../../biometric/utils/biometrics_authenticate.dart';

abstract class LoginOnOldDeviceBaseCubit<State> extends CommonCubit<State>
    with BiometricTokenUsabilityMixin {
  EvoLocalStorageHelper? localStorageHelper;
  final JwtHelper? jwtHelper;
  final BiometricsAuthenticate? biometricsAuthenticate;
  final BiometricsTokenModule? biometricsTokenModule;

  LoginOnOldDeviceBaseCubit(
    super.initialState, {
    this.localStorageHelper,
    this.jwtHelper,
    this.biometricsAuthenticate,
    this.biometricsTokenModule,
  });

  Future<String?> getUserPhoneNumberFromLocal() async {
    return await localStorageHelper?.getUserPhoneNumber();
  }

  Future<bool> checkCanLoginByBiometric() async {
    ///Check biometric is enabled
    final bool? isEnableAuthByBiometrics =
        await biometricsTokenModule?.isEnableBiometricAuthenticator();
    if (isEnableAuthByBiometrics != true) {
      return false;
    }

    /// Check biometricToken is can use or not
    if (await checkAndHandleBiometricTokenUnUsable() == true) {
      return false;
    }

    return true;
  }
}
