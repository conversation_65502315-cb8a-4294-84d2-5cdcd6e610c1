import 'package:flutter/material.dart';

import '../../../../../../resources/resources.dart';
import '../story_config.dart';

class StoryProgressBarWidget extends StatelessWidget {
  final double value;
  final double progressBarHeight;
  final Color? backgroundColor;
  final Color? activeColor;

  const StoryProgressBarWidget({
    required this.value,
    this.backgroundColor,
    this.activeColor,
    this.progressBarHeight = StoryConfig.progressBarHeight,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: progressBarHeight,
      child: LinearProgressIndicator(
        value: value,
        backgroundColor: backgroundColor ?? evoColors.storyProgressBarBackground,
        color: activeColor ?? evoColors.background,
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }
}
