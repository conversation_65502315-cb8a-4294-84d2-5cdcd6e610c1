import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_component/ui_component_widget.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:sliver_app_bar_builder/sliver_app_bar_builder.dart';

import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/evo_action_handler.dart';
import '../../../../util/evo_flutter_wrapper.dart';
import '../../../alice/alice_utils.dart';
import '../../../announcement/model/announcement_info.dart';
import '../../../announcement/utils/unread_announcement_checker.dart';
import '../../../feature_toggle.dart';
import '../color_tween_widget.dart';
import '../hero_banner/hero_banner_widget.dart';
import '../icon_with_red_dot_widget.dart';
import 'home_app_bar_configs.dart';
import 'home_app_bar_cubit.dart';
import 'home_app_bar_state.dart';

class HomeAppBarController {
  void Function()? reloadAppBar;
}

class HomeAppBar extends StatefulWidget {
  final double heightAppbar;
  final bool isLoggedIn;
  final VoidCallback? onTapNotification;
  final HomeAppBarController? homeAppBarController;
  final VoidCallback? onRefresh;

  const HomeAppBar({
    super.key,
    this.heightAppbar = kToolbarHeight,
    this.homeAppBarController,
    this.onTapNotification,
    this.onRefresh,
    this.isLoggedIn = false,
  });

  @override
  State<HomeAppBar> createState() => _HomeAppBarState();
}

class _HomeAppBarState extends State<HomeAppBar> with SingleTickerProviderStateMixin {
  double currentRatio = 0;
  bool hasHeroBanner = true;

  ColorTweenController? appbarColorTweenController = ColorTweenController();
  ColorTweenController? logoColorTweenController = ColorTweenController();
  ColorTweenController? iconNotifyColorTweenController = ColorTweenController();
  final UiComponentController _heroBannerController = UiComponentController();
  final HomeAppBarCubit _cubit = HomeAppBarCubit();
  final UnreadAnnouncementChecker _unreadAnnouncementChecker =
      getIt.get<UnreadAnnouncementChecker>();
  final AppState _appState = getIt.get<AppState>();

  bool allowPullToRefresh = false;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    widget.homeAppBarController?.reloadAppBar = _reloadAppbar;

    _unreadAnnouncementChecker.checkUnreadAnnouncement();
  }

  @override
  void dispose() {
    appbarColorTweenController?.dispose?.call();
    logoColorTweenController?.dispose?.call();
    iconNotifyColorTweenController?.dispose?.call();
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<HomeAppBarCubit>(
      create: (_) => _cubit,
      child: BlocConsumer<HomeAppBarCubit, HomeAppBarState>(
          listener: (BuildContext context, HomeAppBarState state) {
        if (state is HomeAppBarLoaded) {
          _handleShowHeroBanner(state.hasHeroBanner);
        }
      }, builder: (BuildContext context, HomeAppBarState state) {
        isLoading = state is HomeAppBarLoading && state.isLoading;
        return _sliverAppbarWidget(isShowLoading: isLoading);
      }),
    );
  }

  Widget _sliverAppbarWidget({bool isShowLoading = false}) {
    return SliverAppBarBuilder(
        initialBarHeight: HomeAppBarConfigs.toolbarHeight,
        barHeight: HomeAppBarConfigs.toolbarHeight,
        initialContentHeight: _getInitialContentHeightSliverAppBar(hasHeroBanner),
        pinned: true,
        contentBelowBar: false,
        stretch: true,
        stretchConfiguration: OverScrollHeaderStretchConfiguration(
          onStretchTrigger: _onStretchCallback,
        ),
        backgroundColorAll: Colors.transparent,
        leadingActions: <ExpandRatioBuilderBarCallback>[
          (BuildContext context, double expandRatio, double barHeight, bool overlapsContent) {
            /// [expandRatio] of "leadingActions" = (1 - expandRatio of "contentBuilder")
            /// [expandRatio] = [0..1]
            return _pinAppBarWidget(expandRatio);
          }
        ],
        contentBuilder: (BuildContext context, double expandRatio, double contentHeight,
            EdgeInsets centerPadding, bool overlapsContent) {
          /// [overlapsContent] always = false when [floating] of "SliverAppBarBuilder" = false
          /// [expandRatio] of "contentBuilder" = (1 - expandRatio of "leadingActions")
          /// [expandRatio] = [0..1]
          /// init of [contentHeight] = [widget.heightAppbar]
          _handleScrollAppbar(1 - expandRatio);
          _handlePullToRefresh(
              expandRatio: expandRatio,
              contentHeight: contentHeight,
              overlapsContent: overlapsContent);

          return _contentSliverAppBarWidget(
              isShowLoading: isShowLoading,
              expandRatio: expandRatio,
              contentHeight: contentHeight,
              centerPadding: centerPadding,
              overlapsContent: overlapsContent);
        });
  }

  double _getInitialContentHeightSliverAppBar(bool hasHeroBanner) {
    /// [context.screenPadding.top] varies between Android and iOS.
    /// [widget.heightAppbar] = ratio of HeroBanner
    return hasHeroBanner
        ? widget.heightAppbar - context.screenPadding.top
        : HomeAppBarConfigs.toolbarHeight + HomeAppBarConfigs.heightBottomAppbar;
  }

  Widget _pinAppBarWidget(double expandRatioOfLeading) {
    return SizedBox(
      height: HomeAppBarConfigs.toolbarHeight,
      width: context.screenWidth,
      child: Stack(
        children: <Widget>[
          Row(children: <Widget>[
            _itemLeading(),
            const Spacer(),
            _buildEvoAliceChatButtonIfNeed(),
            if (!widget.isLoggedIn) _itemLoggedButton(),
            const SizedBox(width: 10),
            _itemNotificationButton(),
            const SizedBox(width: 20)
          ]),
          // Display "underLine" when the user scrolls up and "contentBuilder" reach to "leadingActions"'s offset
          _underLineBelowAppBar(expandRatioOfLeading),
        ],
      ),
    );
  }

  Widget _buildEvoAliceChatButtonIfNeed() {
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();
    if (featureToggle.enableEvoAliceChat == AliceVersion.none || widget.isLoggedIn) {
      return const SizedBox.shrink();
    }

    /// TODO <EMAIL> this is a temporary button for testing, it will be removed later
    return CommonButton(
      onPressed: () {
        AliceUtils().showAliceEvoChatBox();
      },
      style: evoButtonStyles.tertiary(ButtonSize.medium),
      child: const Text('Alice'),
    );
  }

  Widget _contentSliverAppBarWidget({
    required bool isShowLoading,
    required double expandRatio,
    required double contentHeight,
    required EdgeInsets centerPadding,
    required bool overlapsContent,
  }) {
    return SizedBox(
      height: contentHeight,
      width: double.infinity,
      child: Stack(
        children: <Widget>[
          HeroBannerWidget(
            heightBanner: contentHeight,
            updateStatusHeroBanner: (bool status) {
              _cubit.loaded(status);
            },
            controller: _heroBannerController,
          ),
          Align(
              alignment: Alignment.bottomCenter,
              child: _itemBottomAppBar(heightBottomAppbar: HomeAppBarConfigs.heightBottomAppbar)),

          /// Change color pin AppBar default
          /// When the user scrolls up "contentBuilder"
          EvoColorTweenWidget(
            endColor: evoColors.background,
            colorTweenController: appbarColorTweenController,
            onChangeColor: (Color? color) {
              return Container(
                  color: color,
                  height: HomeAppBarConfigs.toolbarHeight + context.screenPadding.top);
            },
          ),

          /// Show Loading on iOS
          evoFlutterWrapper.isAndroid()
              ? const SizedBox.shrink()
              : _cupertinoIndicatorLoading(
                  isShowLoading,
                  verticalPadding: HomeAppBarConfigs.toolbarHeight + context.screenPadding.top,
                  radiusIndicatorSize: HomeAppBarConfigs.radiusCupertinoIndicatorSize,
                ),
        ],
      ),
    );
  }

  ///https://trustingsocial1.atlassian.net/browse/EMA-265
  ///https://trustingsocial1.atlassian.net/browse/EMA-292
  Widget _itemLeading() {
    return EvoColorTweenWidget(
        beginColor: evoColors.background,
        endColor: evoColors.primary,
        colorTweenController: iconNotifyColorTweenController,
        onChangeColor: (Color? color) {
          return Container(
              margin: const EdgeInsets.only(left: 20),
              child: evoImageProvider.asset(EvoImages.icEvo, width: 68, color: color));
        });
  }

  ///https://trustingsocial1.atlassian.net/browse/EMA-276
  ///https://trustingsocial1.atlassian.net/browse/EMA-293
  Widget _itemNotificationButton() {
    return EvoColorTweenWidget(
      beginColor: evoColors.background,
      endColor: evoColors.textPassive,
      colorTweenController: logoColorTweenController,
      onChangeColor: (Color? color) {
        return Center(
          child: ValueListenableBuilder<AnnouncementStatus>(
            valueListenable: _appState.announcementInfo.statusNotifier,
            builder: (BuildContext context, AnnouncementStatus value, _) {
              final bool hasUnreadItems = value == AnnouncementStatus.hasUnreadItems;
              return IconWithDotRedWidget(EvoImages.icNotify,
                  hasDotRed: hasUnreadItems, onIconClick: widget.onTapNotification, color: color);
            },
          ),
        );
      },
    );
  }

  ///https://trustingsocial1.atlassian.net/browse/EMA-292
  Widget _itemLoggedButton() {
    return Center(
        child: CommonButton(
            onPressed: () {
              EvoActionHandler().openAuthenticationScreen();
            },
            style: evoButtonStyles.primary(ButtonSize.medium),
            child: Text(EvoStrings.authorizationSessionTimeoutConfirmLogin,
                style: evoTextStyles
                    .bodyMedium(evoColors.background)
                    .copyWith(fontWeight: FontWeight.w700))));
  }

  Widget _underLineBelowAppBar(double expandRatioOfLeadingActions) {
    return expandRatioOfLeadingActions == 1
        ? Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              height: 1,
              color: evoColors.disableTextFieldBorder,
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _cupertinoIndicatorLoading(
    bool isShowLoading, {
    required double verticalPadding,
    required double radiusIndicatorSize,
  }) {
    return isShowLoading
        ? Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: verticalPadding),
              child: CupertinoActivityIndicator(radius: radiusIndicatorSize),
            ),
          )
        : const SizedBox.shrink();
  }

  void _handleShowHeroBanner(bool status) {
    commonLog('updateHeroBanner : $status');
    hasHeroBanner = status;
    appbarColorTweenController?.updateColor
        ?.call(status ? Colors.transparent : evoColors.primary, null);
  }

  void _reloadAppbar() {
    // Loading AppBar for Pulling to Refresh on Android
    if (evoFlutterWrapper.isAndroid()) {
      _cubit.loading(true);
    }

    _unreadAnnouncementChecker.checkUnreadAnnouncement();

    ///reset background color appbar
    appbarColorTweenController?.updateColor?.call(null, null);
    _heroBannerController.loadData?.call();
  }

  /// Handle event when stretching SliverAppBar
  /// This method only works on iOS
  Future<void> _onStretchCallback() {
    _cubit.loading(true);

    // The user stretches HeroBanner and stops drag => Pull to Refresh
    allowPullToRefresh = true;

    return Future<void>.value();
  }

  void _handleScrollAppbar(double expandRatio) {
    if (currentRatio != expandRatio) {
      currentRatio = expandRatio;
      if (hasHeroBanner) {
        appbarColorTweenController?.onUpdateController?.call(expandRatio);
        logoColorTweenController?.onUpdateController?.call(expandRatio);
        iconNotifyColorTweenController?.onUpdateController?.call(expandRatio);
      } else {
        ///[expandRatio != 0] when scrolling
        appbarColorTweenController?.onChangeScroll?.call(expandRatio != 0);
        logoColorTweenController?.onChangeScroll?.call(expandRatio != 0);
        iconNotifyColorTweenController?.onChangeScroll?.call(expandRatio != 0);
      }
    }
  }

  /// This method only checks on iOS
  void _handlePullToRefresh({
    required double expandRatio,
    required double contentHeight,
    required bool overlapsContent,
  }) {
    if (evoFlutterWrapper.isAndroid()) {
      return;
    }

    // Hide loading when the user scrolls up while pull to refresh
    if (expandRatio < 1 && isLoading) {
      isLoading = false;
      allowPullToRefresh = false;
      _cubit.loading(isLoading);
    }

    final double contentHeightCanPullToRefresh = hasHeroBanner
        ? widget.heightAppbar
        : (HomeAppBarConfigs.toolbarHeight +
            HomeAppBarConfigs.heightBottomAppbar +
            context.screenPadding.top);

    /// Handle case: the user stops Stretch
    /// and Position of "contentBuilder" goes back [widget.heightAppbar]
    /// => Allow to Pull to Refresh
    final bool canPullToRefresh = allowPullToRefresh &&
        expandRatio == 1.0 &&
        !overlapsContent &&
        contentHeight == contentHeightCanPullToRefresh;

    if (!canPullToRefresh) {
      return;
    }

    _pullToRefresh();
  }

  void _pullToRefresh() {
    widget.onRefresh?.call();
    allowPullToRefresh = false;
  }

  /// Display corner layout below AppBar
  Widget _itemBottomAppBar({required double heightBottomAppbar}) => Container(
      height: heightBottomAppbar,
      width: context.screenWidth,
      decoration: BoxDecoration(
          color: evoColors.background,
          border: Border.all(color: evoColors.background, width: 0),
          borderRadius: const BorderRadius.only(
              topRight: Radius.circular(20), topLeft: Radius.circular(20))));
}
