import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/user_repo.dart';
import '../../../data/response/card_status_entity.dart';
import '../../../data/response/linked_card_list_entity.dart';
import '../../../util/mock_file_name_utils/mock_user_file_name.dart';

part 'profile_page_state.dart';

/// For get user's card
class ProfilePageCardCubit extends CommonCubit<ProfilePageCardState> {
  final UserRepo _userRepo;

  ProfilePageCardCubit(this._userRepo) : super(ProfilePageCardInfoLoading());

  Future<void> initData() async {
    _getCardInfo();
  }

  Future<void> _getCardInfo() async {
    emit(ProfilePageCardInfoLoading());

    final LinkedCardListEntity entity = await _userRepo.getLinkedCards(
      mockConfig: MockConfig(
        enable: false,
        fileName: getLinkedCardsMockFileName(),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(ProfilePageCardInfoSuccess(linkedCardListEntity: entity));
    } else {
      emit(ProfilePageCardInfoFail(error: ErrorUIModel.fromEntity(entity)));
    }
  }
}
