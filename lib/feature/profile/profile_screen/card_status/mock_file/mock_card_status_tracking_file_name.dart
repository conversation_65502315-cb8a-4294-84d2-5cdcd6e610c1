enum CardStatusTrackingMockCase {
  /// Display cases
  displayNull('display_null'),
  displayFalse('display_false'),
  ctaWidgetDisplayFalse('cta_widget_display_false'),

  /// Credit UI cases
  creditLimitWidgetWaitingForApproval('credit_limit_widget_waiting_for_approval'),
  creditLimitWidgetNotReadyForPayment('credit_limit_widget_not_ready_for_payment'),
  creditLimitWidgetReadyForPayment('credit_limit_widget_ready_for_payment'),
  creditLimitWidgetOutOfSync('credit_limit_widget_out_of_sync'),

  /// CTA UI cases
  ctaWidgetLinkedOnly('cta_widget_linked_only'),
  ctaWidgetActivatedOnly('cta_widget_activated_only'),
  ctaWidgetAllActionDone('cta_widget_all_action_done'),

  /// Getting error cases
  verdictRenovateStatusLimitExceeded('verdict_renovate_status_limit_exceeded'),
  verdictRenovateStatusFailed('verdict_renovate_status_failed');

  final String value;

  const CardStatusTrackingMockCase(this.value);
}

String getCardStatusTrackingMock(CardStatusTrackingMockCase mockCase) {
  return 'get_card_status_${mockCase.value}.json';
}
