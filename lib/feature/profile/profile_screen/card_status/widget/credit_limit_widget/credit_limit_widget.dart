import 'package:flutter/material.dart';

import '../../../../../../data/response/credit_limit_widget_config_entity.dart';
import '../../../../../../resources/dimensions.dart';
import 'credit_limit_amount/credit_limit_amount_widget.dart';
import 'credit_limit_title_widget.dart';

class CreditLimitWidget extends StatelessWidget {
  final CreditLimitWidgetConfigEntity? creditLimitWidgetConfig;

  const CreditLimitWidget({super.key, this.creditLimitWidgetConfig});

  @override
  Widget build(BuildContext context) {
    if (creditLimitWidgetConfig?.display != true) {
      return const SizedBox.shrink();
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        CreditLimitTitleWidget(creditLimitWidgetConfig: creditLimitWidgetConfig),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: EvoDimension.profilePageHorizontalMargin,
          ),
          child: CreditLimitAmountWidget(creditLimitWidgetConfig: creditLimitWidgetConfig),
        ),
      ],
    );
  }
}
