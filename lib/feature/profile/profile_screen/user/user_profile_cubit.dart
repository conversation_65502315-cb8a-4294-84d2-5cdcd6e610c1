import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/user_repo.dart';
import '../../../../data/response/user_entity.dart';
import '../../../../data/response/user_information_entity.dart';
import '../../../../util/mock_file_name_utils/mock_user_file_name.dart';

part 'user_profile_state.dart';

class UserProfileCubit extends CommonCubit<UserProfileState> {
  final UserRepo _userRepo;

  UserProfileCubit(this._userRepo) : super(UserProfileLoading());

  Future<void> getUserInfo() async {
    final UserEntity userInfo = await _userRepo.getUserInfo(
        mockConfig: MockConfig(enable: false, fileName: getUserInfoMockFileName()));

    if (userInfo.statusCode == CommonHttpClient.SUCCESS) {
      emit(UserProfileLoadedSuccess(user: userInfo.userInformation));
    } else {
      emit(UserProfileLoadedFail(error: ErrorUIModel.fromEntity(userInfo)));
    }
  }
}
