import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_textfield_widget.dart';

import '../../../../resources/resources.dart';

class ProfileDetailTextField extends StatefulWidget {
  final double? height;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final String? labelText;
  final TextStyle? labelStyle;
  final Color? background;
  final TextInputType? keyboardType;
  final Widget? suffixIcon;
  final bool readOnly;
  final bool? enableInteractiveSelection;

  const ProfileDetailTextField({
    super.key,
    this.height = 64,
    this.controller,
    this.focusNode,
    this.labelText,
    this.labelStyle,
    this.background,
    this.keyboardType,
    this.suffixIcon,
    this.readOnly = true,
    this.enableInteractiveSelection = false,
  });

  @override
  State<ProfileDetailTextField> createState() => ProfileDetailTextFieldState();
}

@visibleForTesting
class ProfileDetailTextFieldState extends State<ProfileDetailTextField> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      padding: const EdgeInsets.only(top: 16, bottom: 8),
      decoration: BoxDecoration(
          color: widget.readOnly ? evoColors.disableTextFieldBg : widget.background,
          border: Border.all(
            color: (widget.focusNode?.hasFocus == true)
                ? evoColors.focusedTextFieldBorder
                : evoColors.textFieldBorder,
          ),
          borderRadius: BorderRadius.circular(8)),
      child: CommonTextFieldWidget(
        focusNode: widget.focusNode,
        readOnly: widget.readOnly,
        enableInteractiveSelection: widget.enableInteractiveSelection,
        controller: widget.controller,
        keyboardType: widget.keyboardType,
        style: TextStyle(color: widget.readOnly ? evoColors.textPassive2 : null),
        decoration: InputDecoration(
          labelText: widget.labelText,
          labelStyle: widget.labelStyle ??
              evoTextStyles
                  .h300(color: evoColors.textPassive2)
                  .copyWith(fontWeight: FontWeight.w500),
          counterText: '',
          focusedBorder: const OutlineInputBorder(
            borderSide: BorderSide(
              width: 0,
              color: Colors.transparent,
            ),
          ),
          enabledBorder: const OutlineInputBorder(
            borderSide: BorderSide(
              width: 0,
              color: Colors.transparent,
            ),
          ),
          suffixIcon: widget.suffixIcon,
        ),
      ),
    );
  }

  // Reload UI for testing
  @visibleForTesting
  void onReloadUI() {
    setState(() {});
  }
}
