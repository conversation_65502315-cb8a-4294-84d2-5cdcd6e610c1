import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';
import '../../../../util/functions.dart';

class ActivatePosSuggestionListWidget extends StatelessWidget {
  final List<int> suggestions;
  final void Function(int suggestion) onSuggestionTap;

  const ActivatePosSuggestionListWidget({
    required this.suggestions,
    required this.onSuggestionTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 24,
      child: ListView.separated(
        shrinkWrap: true,
        itemBuilder: (_, int index) {
          return _buildItemSuggestion(suggestions[index]);
        },
        padding: EdgeInsets.symmetric(horizontal: 20),
        itemCount: suggestions.length,
        separatorBuilder: (_, int index) {
          return const SizedBox(width: 8);
        },
        scrollDirection: Axis.horizontal,
        physics: BouncingScrollPhysics(),
      ),
    );
  }

  Widget _buildItemSuggestion(int suggestion) {
    return InkWell(
      onTap: () {
        onSuggestionTap(suggestion);
      },
      borderRadius: BorderRadius.circular(32),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: evoColors.itemPOSLimitSuggestionBg,
          borderRadius: BorderRadius.circular(32),
        ),
        child: Text(
          evoUtilFunction.evoFormatCurrency(
            suggestion,
            currencySymbol: vietNamCurrencySymbol,
          ),
          style: evoTextStyles
              .bodyMedium(evoColors.textActive)
              .merge(TextStyle(fontWeight: FontWeight.w700)),
        ),
      ),
    );
  }
}
