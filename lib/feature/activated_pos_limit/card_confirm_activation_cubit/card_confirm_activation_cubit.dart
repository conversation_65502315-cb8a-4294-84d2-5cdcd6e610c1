import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/user_repo.dart';
import '../mock/mock_activate_card_case.dart';
import '../models/platform_active_card.dart';

part 'card_confirm_activation_state.dart';

class CardConfirmActivationCubit extends CommonCubit<CardConfirmActivationState> {
  final UserRepo userRepo;

  CardConfirmActivationCubit({required this.userRepo}) : super(CardConfirmActivationInitialState());

  Future<void> confirmActivationCard({PlatformActiveCard? platform}) async {
    emit(CardConfirmActivationLoadingState());

    final BaseEntity entity = await userRepo.cardConfirmActivation(
      platform: platform != null ? platform.value : PlatformActiveCard.tpBank.value,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockCardConfirmActivationCaseFileName(MockCardConfirmActivationCase.success),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(CardConfirmActivationSucceedState());
      return;
    }

    emit(CardConfirmActivationFailureState(
      errorUIModel: ErrorUIModel.fromEntity(entity),
    ));
  }
}
