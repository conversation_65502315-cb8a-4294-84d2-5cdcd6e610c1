enum UpdateOrderMockCase {
  success('success'),
  successWithInvalidVoucher('success_with_invalid_voucher'),
  successEmptyData('success_empty_data'),
  failRecordNotFound('fail_record_not_found'),
  failSessionNotOpened('fail_session_not_opened'),
  failPaymentInvalid('fail_payment_invalid'),
  failSessionExpired('fail_session_expired'),
  failEmiUnqualified('fail_emi_unqualified'),
  successEmiUpdateOrder('success_emi_with_packages');

  const UpdateOrderMockCase(this.value);

  final String value;
}

String getUpdateOrderMockFileName(UpdateOrderMockCase mockCase) {
  return 'update_order_${mockCase.value}.json';
}
