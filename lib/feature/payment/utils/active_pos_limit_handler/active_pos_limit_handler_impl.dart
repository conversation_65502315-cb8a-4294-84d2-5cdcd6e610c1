part of 'active_pos_limit_handler.dart';

class ActivePosLimitHandlerImpl implements ActivePosLimitHandler {
  final ActivatedPOSLimitFlow activatedPOSLimitFlow = ActivatedPOSLimitFlowImpl();

  @override
  Future<bool> handleActivateCardOrPosLimitIfNeed({
    required OrderActivatePosLimitEntity orderActivatePosLimitEntity,
    required Screen entryPoint,
    void Function(OrderActivatePosLimitEntity)? onActivatedPOSLimitFlowSucceed,
  }) async {
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();
    if (!featureToggle.enableActivatePOSLimitFeature) {
      return false;
    }

    final ActionEntity? action = orderActivatePosLimitEntity.prerequisitesAction;
    if (action == null) {
      return false;
    }

    final String? screenName = action.args?.screenName;
    if (!isActivatePosLimitScreen(screenName)) {
      return false;
    }

    createActivatePOSLimitFlow(
      orderActivatePosLimitEntity,
      entryPoint: entryPoint,
      orderAmount: orderActivatePosLimitEntity.orderSession?.orderAmount,
      onActivatedPOSLimitFlowSucceed: onActivatedPOSLimitFlowSucceed,
    );

    final EvoActionModel evoActionModel = action.toEvoActionModel();
    final EvoWebViewArg arg = EvoWebViewArg(
      url: evoActionModel.args?.link,
      title: null,
    );
    return await EvoActionHandler().handle(evoActionModel, arg: arg);
  }

  @override
  void handleActivatedPOSLimitErrorState({
    required OrderActivatePosLimitEntity orderActivatePosLimitEntity,
    required Screen entryPoint,
    CreateOrderEntity? entity,
    void Function(OrderActivatePosLimitEntity)? onActivatedPOSLimitFlowSucceed,
  }) {
    final String? verdict = entity?.verdict;

    switch (verdict) {
      case CreateOrderEntity.verdictUnqualifiedToActive:
        createActivatePOSLimitFlow(
          orderActivatePosLimitEntity,
          entryPoint: entryPoint,
          onActivatedPOSLimitFlowSucceed: onActivatedPOSLimitFlowSucceed,
        );
        ActivateCardGuidanceScreen.pushNamed();
        break;

      case CreateOrderEntity.verdictPosLimitInsufficient:
      case CreateOrderEntity.verdictCardUnqualifiedToSetPosLimit:
        createActivatePOSLimitFlow(
          orderActivatePosLimitEntity,
          entryPoint: entryPoint,
          onActivatedPOSLimitFlowSucceed: onActivatedPOSLimitFlowSucceed,
        );
        SetupPosLimitGuidanceScreen.pushNamed();
        break;

      default:
        throw Exception('Unknown verdict: $verdict');
    }
  }

  @visibleForTesting
  bool isActivatePosLimitScreen(String? screenName) {
    if (screenName == Screen.activateCardScreen.name ||
        screenName == Screen.setupPosLimitScreen.name) {
      return true;
    }

    return false;
  }

  @visibleForTesting
  void createActivatePOSLimitFlow(
    OrderActivatePosLimitEntity orderActivatePosLimitEntity, {
    required Screen entryPoint,
    int? orderAmount,
    void Function(OrderActivatePosLimitEntity)? onActivatedPOSLimitFlowSucceed,
  }) {
    activatedPOSLimitFlow.prepareActivatePOSLimitFlow(
      screenName: entryPoint,
      orderAmount: orderAmount,
      callback: ActivatedPOSLimitFlowCallback(
        onSuccess: (BuildContext context, ActivatedPOSLimitFlowPayload? payload) {
          handleActivatedPOSLimitFlowSucceed(
            orderActivatePosLimitEntity: orderActivatePosLimitEntity,
            payload: payload,
            onActivatedPOSLimitFlowSucceed: onActivatedPOSLimitFlowSucceed,
            entryPoint: entryPoint,
          );
        },
        onFailed: (
          BuildContext context,
          ActivatedPOSLimitFlowFailedReason reason,
          String? userMessage,
        ) {
          handleActivatedPOSLimitFlowFailed(
            reason: reason,
            userMessage: userMessage,
          );
        },
      ),
    );
  }

  @visibleForTesting
  void handleActivatedPOSLimitFlowSucceed({
    required OrderActivatePosLimitEntity orderActivatePosLimitEntity,
    required Screen entryPoint,
    ActivatedPOSLimitFlowPayload? payload,
    void Function(OrderActivatePosLimitEntity)? onActivatedPOSLimitFlowSucceed,
  }) {
    if (payload is CardConfirmActivationSucceedPayload) {
      /// Pop to Scan QR Code screen
      /// Because EVO order may very well be expired by the time they return to EVO App
      navigatorContext?.popUntilNamed(Screen.qrCodeScannerScreen.name);
      return;
    }

    if (payload is AcceptedActivationCardAndPOSLimitSucceedPayload) {
      /// Pop to entry point to navigate to next screen (E.g: CheckOutScreen)
      /// And do not need to "return"
      navigatorContext?.popUntilNamed(entryPoint.name);
    }

    if (orderActivatePosLimitEntity.orderSession == null) {
      // do nothing if orderSession is null
      return;
    }

    final OrderActivatePosLimitEntity newOrderActivatePosLimitEntity = OrderActivatePosLimitEntity(
      paymentService: orderActivatePosLimitEntity.paymentService,
      orderSession: orderActivatePosLimitEntity.orderSession,
      emiPackages: orderActivatePosLimitEntity.emiPackages,
    );

    onActivatedPOSLimitFlowSucceed?.call(newOrderActivatePosLimitEntity);
  }

  @visibleForTesting
  void handleActivatedPOSLimitFlowFailed({
    required ActivatedPOSLimitFlowFailedReason reason,
    String? userMessage,
  }) {
    if (reason == ActivatedPOSLimitFlowFailedReason.userCancelled ||
        reason == ActivatedPOSLimitFlowFailedReason.userPayAgain) {
      /// Pop to Scan QR Code screen
      /// Because EVO order may very well be expired by the time they return to EVO App
      navigatorContext?.popUntilNamed(Screen.qrCodeScannerScreen.name);
      return;
    }

    if (reason == ActivatedPOSLimitFlowFailedReason.userNotActivatedCard ||
        reason == ActivatedPOSLimitFlowFailedReason.userNotSetPosLimit) {
      MainScreen.removeUntilAndPushReplacementNamed(isLoggedIn: true);
      return;
    }

    /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-6145
    if (reason == ActivatedPOSLimitFlowFailedReason.redirectToTPBApp) {
      navigatorContext?.popUntilNamed(Screen.qrCodeScannerScreen.name);

      ActivateCardGuidanceScreen.pushNamed();
      return;
    }

    commonLog('Error: $userMessage');
  }
}
