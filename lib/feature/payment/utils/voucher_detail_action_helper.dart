import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../data/response/action_entity.dart';
import '../../../data/response/voucher_entity.dart';
import '../../../model/evo_action_model.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/evo_action_handler.dart';
import '../../../util/mapper.dart';
import '../../../widget/evo_next_action/evo_next_action_widget.dart';
import '../../feature_toggle.dart';
import '../../webview/models/evo_webview_arg.dart';
import '../models/payment_entry_point.dart';

VoucherDetailActionHelper get voucherDetailActionHelper => getIt.get<VoucherDetailActionHelper>();

abstract class VoucherDetailActionHelper {
  void handleGotoVoucherDetailScreen(VoucherEntity? voucher);
}

class VoucherDetailActionHelperImpl extends VoucherDetailActionHelper {
  @override
  void handleGotoVoucherDetailScreen(VoucherEntity? voucher) {
    if (voucher == null) {
      return;
    }

    final ActionEntity? action = voucher.action;
    final EvoActionModel? evoActionModel = action?.toEvoActionModel();
    evoActionModel?.let((EvoActionModel it) async {
      final EvoWebViewArg arg = EvoWebViewArg(
        title: EvoStrings.promotionDetailTitle,
        url: it.args?.link,
        nextActionWidget: buildNextActionButton(voucher),
      );

      EvoActionHandler().handle(evoActionModel, arg: arg);
    });
  }

  @visibleForTesting
  Widget buildNextActionButton(VoucherEntity voucher) {
    return EvoNextActionWidget(
      nextAction: voucher.action?.args?.nextAction?.toEvoActionModel(),
      isUsed: voucher.isUsed,
      preActionBeforeNextAction: () {
        applyVoucherIfEnable(voucher);
      },
    );
  }

  @visibleForTesting
  void applyVoucherIfEnable(VoucherEntity voucher) {
    final AppState appState = getIt.get<AppState>();
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();

    if (featureToggle.enablePreApplyVoucher) {
      appState.paymentSharedData.paymentEntryPoint = PaymentEntryPoint.voucherDetailScreen;
      appState.paymentSharedData.selectedVoucher = voucher;
    }
  }
}
