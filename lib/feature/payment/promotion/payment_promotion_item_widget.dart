import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../../model/promotion_status_ui_model.dart';
import '../../../resources/resources.dart';
import '../../../util/promotion/status_ui_data_creator_factory/promotion_source_data.dart';
import '../../../util/promotion/status_ui_data_creator_factory/promotion_status_ui_creator_factory.dart';
import 'model/payment_promotion_model.dart';

class PaymentPromotionItemWidget extends StatelessWidget {
  final PaymentPromotionModel? item;
  final VoidCallback? onSelected;
  final VoidCallback? onUnSelected;
  final VoidCallback? onViewDetail;
  @visibleForTesting
  final PromotionStatusUIModel? promotionStatusUIModel;

  const PaymentPromotionItemWidget({
    super.key,
    this.item,
    this.onSelected,
    this.onUnSelected,
    this.onViewDetail,
    this.promotionStatusUIModel,
  });

  @override
  Widget build(BuildContext context) {
    final BorderRadius borderRadius = BorderRadius.circular(16);
    const SizedBox spacing = SizedBox(height: 16);
    final bool? isSelected = item?.isSelected;

    return Card(
      shadowColor: evoColors.paymentPromotionCardItemShadow,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius,
        side: BorderSide(
          color: _getBorderColor(
            isSelected: isSelected,
            isVoucherQualified: isVoucherQualified,
          ),
        ),
      ),
      elevation: 5,
      child: InkWell(
        borderRadius: borderRadius,
        onTap: onViewDetail,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            spacing,
            if (!isVoucherQualified) ..._buildVoucherUnqualifiedTitle(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                item?.voucher?.title ?? '',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: evoTextStyles.h200(
                  color: evoColors.paymentPromotionTitle,
                ),
              ),
            ),
            spacing,
            DashSeparator(),
            spacing,
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ConstrainedBox(
                /// The minHeight is based on the Design and QC comments defined at
                /// https://trustingsocial1.atlassian.net/browse/EMA-1423?focusedCommentId=96942
                /// https://www.figma.com/file/bFht7WW9fsBDkPexbH5QSf/EVO-App-Hand-Off?node-id=27402%3A13025&mode=dev
                constraints: const BoxConstraints(minHeight: 32),
                child: Row(
                  children: <Widget>[
                    ..._buildExpiredTimeWidget(),
                    ..._getActionWidget(),
                  ],
                ),
              ),
            ),
            spacing,
          ],
        ),
      ),
    );
  }

  List<Widget> _buildVoucherUnqualifiedTitle() {
    return <Widget>[
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Text(
          EvoStrings.promotionItemUnqualified,
          style: evoTextStyles.bodySmall(
            color: evoColors.promotionItemUnqualifiedText,
          ),
        ),
      ),
      const SizedBox(height: 4),
    ];
  }

  List<Widget> _buildExpiredTimeWidget() {
    final PromotionStatusUIModel? voucherStatusUIModel = promotionStatusUIModel ??
        PromotionStatusUICreatorFactory()
            .create(VoucherSourceData(
              validToDate: item?.voucher?.validToDateTime,
            ))
            .createPromotionStatusUIData();

    return <Widget>[
      evoImageProvider.asset(EvoImages.icPaymentPromotionTime),
      const SizedBox(width: 8),
      Text(
        voucherStatusUIModel?.title ?? '',
        style: evoTextStyles.bodySmall(
          color: evoColors.textPassive2,
        ),
      ),
    ];
  }

  List<Widget> _getActionWidget() {
    /// If the user has selected a voucher, we always show the "Bỏ chọn" button.
    /// This allows the user to unselect the voucher if they want, or if there is an error in applying the voucher.
    if (item?.isSelected == true) {
      return _buildUnSelectButton();
    }

    /// If the voucher is not qualified, we always show the "Không đủ điều kiện" text.
    if (!isVoucherQualified) {
      return _buildNotQualifiedWidget();
    }

    if (item?.isCanSelected != true) {
      return _buildCanNotSelectWidget();
    }

    return _buildSelectButton();
  }

  List<Widget> _buildUnSelectButton() {
    return <Widget>[
      const Spacer(),
      CommonButton(
        onPressed: onUnSelected,

        /// If the tapTargetSize is larger than minimumSize, the button will include a transparent margin that responds to taps.
        /// The default tapTargetSize is [MaterialTapTargetSize.padded], which expands the minimum tap target size to 48px by 48px.
        /// Refer to detail of tapTargetSize: https://api.flutter.dev/flutter/material/ButtonStyle/tapTargetSize.html
        /// Refer to Widgets contain this property: https://api.flutter.dev/flutter/material/MaterialTapTargetSize.html
        style: evoButtonStyles.secondary(
          ButtonSize.small,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        child: const Text(EvoStrings.paymentUnSelectPromotionButton),
      )
    ];
  }

  List<Widget> _buildSelectButton() {
    return <Widget>[
      const Spacer(),
      CommonButton(
        onPressed: onSelected,

        /// If the tapTargetSize is larger than minimumSize, the button will include a transparent margin that responds to taps.
        /// The default tapTargetSize is [MaterialTapTargetSize.padded], which expands the minimum tap target size to 48px by 48px.
        /// Refer to detail of tapTargetSize: https://api.flutter.dev/flutter/material/ButtonStyle/tapTargetSize.html
        /// Refer to Widgets contain this property: https://api.flutter.dev/flutter/material/MaterialTapTargetSize.html
        style: evoButtonStyles.accent(
          ButtonSize.small,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        child: const Text(EvoStrings.paymentSelectPromotionButton),
      )
    ];
  }

  List<Widget> _buildNotQualifiedWidget() {
    return <Widget>[
      Expanded(
        child: Text(
          EvoStrings.paymentPromotionNotQualified,
          style: evoTextStyles.bodySmall(color: evoColors.error),
          textAlign: TextAlign.end,
        ),
      ),
      const SizedBox(width: 8),
      evoImageProvider.asset(EvoImages.icPaymentPromotionInvalid),
    ];
  }

  List<Widget> _buildCanNotSelectWidget() {
    return <Widget>[
      Expanded(
        child: Text(
          EvoStrings.paymentPromotionCanNotSelected,
          style: evoTextStyles.bodySmall(color: evoColors.error),
          textAlign: TextAlign.end,
        ),
      ),
      const SizedBox(width: 8),
      evoImageProvider.asset(EvoImages.icPaymentPromotionInvalid),
    ];
  }

  Color _getBorderColor({
    bool? isSelected,
    bool? isVoucherQualified,
  }) {
    if (isSelected == true) {
      if (isVoucherQualified == true) {
        return evoColors.primary;
      } else {
        return evoColors.promotionItemUnqualifiedBorder;
      }
    }

    return Colors.transparent;
  }

  bool get isVoucherQualified => item?.voucher?.isQualified ?? false;
}
