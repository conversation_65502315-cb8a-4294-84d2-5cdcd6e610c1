import '../../prepare_for_app_initiation.dart';
import '../../resources/global.dart';
import '../../resources/ui_strings.dart';
import '../../util/functions.dart';
import 'payment_shared_data.dart';
import 'qrcode_scanner/model/qr_code_type.dart';

class AmountValidator {
  final AppState appState;

  AmountValidator(this.appState);

  String? getAmountError({
    required String amount,
    required String? productCode,
    String currencySuffix = '',
  }) {
    /// VNPay QR, bypass check order amount
    /// refer to ticket: https://trustingsocial1.atlassian.net/browse/EMA-5642
    if (productCode == QrProductCode.vn01String) {
      return null;
    }

    final int minOrderAmount = appState.paymentSharedData.paymentConfig.minOrderAmount;
    final int maxOrderAmount = appState.paymentSharedData.paymentConfig.maxOrderAmount;

    final int? amountInNum = evoUtilFunction.getAmountFromStr(
      amount,
      currencySuffix: currencySuffix,
    );
    if (amountInNum == null) {
      return EvoStrings.paymentAmountInvalid;
    }

    if (amountInNum < minOrderAmount) {
      return '${EvoStrings.paymentAmountMinInvalid} ${evoUtilFunction.evoFormatCurrency(minOrderAmount)}$vietNamCurrencySymbol';
    }

    if (amountInNum > maxOrderAmount) {
      return '${EvoStrings.paymentAmountMaxInvalid} ${evoUtilFunction.evoFormatCurrency(maxOrderAmount)}$vietNamCurrencySymbol';
    }

    return null;
  }

  bool validateAmount(int amountInNum, String? productCode) {
    /// VNPay QR, bypass check order amount
    /// refer to ticket: https://trustingsocial1.atlassian.net/browse/EMA-5642
    if (productCode == QrProductCode.vn01String) {
      return true;
    }

    final PaymentConfigModel paymentConfig = appState.paymentSharedData.paymentConfig;

    return amountInNum >= paymentConfig.minOrderAmount &&
        amountInNum <= paymentConfig.maxOrderAmount;
  }
}
