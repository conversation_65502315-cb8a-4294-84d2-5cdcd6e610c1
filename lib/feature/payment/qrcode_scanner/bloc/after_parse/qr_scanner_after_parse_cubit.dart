import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/repository/checkout_repo.dart';
import '../../../../../data/repository/merchant_repo.dart';
import '../../../../../data/response/create_order_entity.dart';
import '../../../../../data/response/qr_code_parse_entity.dart';
import '../../../../../data/response/store_entity.dart';
import '../../../../../data/response/store_info_entity.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../mock_file/mock_checkout_file_name.dart';
import '../../../payment_config.dart';
import '../../../payment_shared_data.dart';
import '../../../utils/payment_with_emi_utils.dart';
import '../../model/qr_code_type.dart';
import '../../model/store_status.dart';
import '../../utils/mock_file/mock_merchant_use_case.dart';
import 'dynamic_qr_code_specialized_state.dart';
import 'qr_scanner_after_parse_state.dart';

class QrScannerAfterParseCubit extends CommonCubit<QrScannerAfterParseState> {
  @visibleForTesting
  QrCodeParseEntity? qrParseEntity;
  @visibleForTesting
  bool isQrCodeParseSuccess = false;
  @visibleForTesting
  bool isEmiRulesLoaded = false;

  final MerchantRepo merchantRepo;
  final CheckOutRepo checkOutRepo;
  final PaymentWithEMIUtils paymentWithEMIUtils;

  QrScannerAfterParseCubit({
    required this.checkOutRepo,
    required this.merchantRepo,
    required this.paymentWithEMIUtils,
  }) : super(QrScannerAfterParseInitialState());

  Future<void> onEmiRulesLoaded() async {
    isEmiRulesLoaded = true;
    await handleQrTypeIfReady();
  }

  Future<void> onParseQrCodeSuccess(QrCodeParseEntity entity) async {
    qrParseEntity = entity;
    isQrCodeParseSuccess = true;
    await handleQrTypeIfReady();
  }

  /// Handles the QR code type if the parsing is successful and, for EMI version 1,
  /// if the EMI data is completely loaded.
  @visibleForTesting
  Future<void> handleQrTypeIfReady() async {
    bool isReadyToHandleQrType = isQrCodeParseSuccess;

    if (paymentWithEMIUtils.isEmiFeatureEnabled()) {
      isReadyToHandleQrType &= isEmiRulesLoaded;
    }

    final QrCodeParseEntity? localQrEntity = qrParseEntity;
    if (isReadyToHandleQrType && localQrEntity != null) {
      await handleQrType(localQrEntity);
    }
  }

  @visibleForTesting
  Future<void> handleQrType(QrCodeParseEntity qrCodeParseEntity) async {
    switch (qrCodeParseEntity.productCode) {
      case QrProductCode.pa02String:
      case QrProductCode.vn01String:
        await getStoreInfo(qrCodeParseEntity);
        break;
      case QrProductCode.pa03String:
        await handleDynamicQrCode(qrCodeParseEntity);
        break;
    }

    resetForNewScanningQRCode();
  }

  /// Reset the fields in case the user scans a new QR code (e.g when the user go back from the next screen)
  @visibleForTesting
  void resetForNewScanningQRCode() {
    qrParseEntity = null;
    isQrCodeParseSuccess = false;
  }

  /// Get Store info by store id
  @visibleForTesting
  Future<void> getStoreInfo(QrCodeParseEntity qrCodeEntity) async {
    emit(QrScannerAfterParseApiLoading());

    final StoreEntity storeEntity = await merchantRepo.getStore(
      qrCodeEntity.storeId,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockMerchantUseCase(MockMerchantUseCase.storeInfoActivated),
      ),
    );

    final PaymentConfigEntity? payment = storeEntity.store?.payment;
    getIt<AppState>().paymentSharedData.paymentConfig = PaymentConfigModel(
      minOrderAmount: payment?.minOrderAmount ?? PaymentConfig.defaultMinPaymentAmount,
      maxOrderAmount: payment?.maxOrderAmount ?? PaymentConfig.defaultMaxPaymentAmount,
    );

    if (storeEntity.statusCode == CommonHttpClient.SUCCESS) {
      final bool isDeactivated = storeEntity.store?.status == StoreStatus.deactivated.value;
      if (isDeactivated) {
        emit(StoreNotFoundOrDeactivatedState());
        return;
      }

      emit(StoreInfoLoaded(
        store: storeEntity.store,
        productCode: qrCodeEntity.productCode,
        merchantId: qrCodeEntity.merchantId,
        vnPayQrInfo: qrCodeEntity.vnPayQrInfo,
        type: qrCodeEntity.type,
      ));
    } else if (storeEntity.verdict == StoreEntity.verdictRecordNotFound) {
      emit(StoreNotFoundOrDeactivatedState(errorUIModel: ErrorUIModel.fromEntity(storeEntity)));
    } else {
      emit(QrScannerAfterParseCommonError(errorUIModel: ErrorUIModel.fromEntity(storeEntity)));
    }
  }

  /// Dynamic order: Create order by id
  @visibleForTesting
  Future<void> handleDynamicQrCode(QrCodeParseEntity qrCodeEntity) async {
    emit(QrScannerAfterParseApiLoading());
    final CreateOrderEntity entity = await checkOutRepo.createOrderByOrderId(
      storeId: qrCodeEntity.storeId,
      merchantId: qrCodeEntity.merchantId,
      productCode: qrCodeEntity.productCode,
      orderId: qrCodeEntity.orderId,
      mockConfig: MockConfig(
        enable: false,
        fileName: createOrderMockFileName(verdict: CreateOrderMockVerdict.emiPackageForDynamicQR),
      ),
    );
    getIt<AppState>().paymentSharedData.orderExtraInfo = entity.extraInfo;
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(CreateDynamicOrderSuccess(entity));
      return;
    }

    if (entity.statusCode == CommonHttpClient.BAD_REQUEST &&
        (entity.verdict == CreateOrderEntity.verdictUnqualifiedToActive ||
            entity.verdict == CreateOrderEntity.verdictPosLimitInsufficient ||
            entity.verdict == CreateOrderEntity.verdictCardUnqualifiedToSetPosLimit)) {
      emit(CreateDynamicOrderWithActivatePosLimitErrorState(entity));
      return;
    }

    emit(QrScannerAfterParseCommonError(errorUIModel: ErrorUIModel.fromEntity(entity)));
  }
}
