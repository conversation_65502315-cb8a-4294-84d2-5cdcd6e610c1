import 'package:flutter_common_package/util/permission/device_permission.dart';
import 'package:flutter_common_package/util/permission/permission_handler_mixin.dart';

import '../../../prepare_for_app_initiation.dart';
import '../../mock_test/mock_test_feature_type.dart';
import '../../mock_test/mock_test_helper.dart';

mixin QrCodeScannerScreenMockTest implements PermissionHandlerCallback {
  final MockTestHelper _mockTestHelper = getIt.get<MockTestHelper>();

  bool _isIgnoredMockTest = false;

  String mockQRCodeData = '';

  bool get isMockDataAvailable => enableMockTestFlow() && mockQRCodeData.isNotEmpty;

  bool enableMockTestFlow() =>
      !_isIgnoredMockTest && _mockTestHelper.canExecutedMockTest(MockTestFeatureType.payment);

  void requestStoragePermissionForMockTest() {
    if (enableMockTestFlow()) {
      _mockTestHelper.requestStoragePermission(this);
    }
  }

  @override
  void onDeniedPermission(TsDevicePermission permission) {
    if (permission == MockTestHelper.requiredPermission) {
      _isIgnoredMockTest = true;
      onIgnoreMockTest();
    }
  }

  @override
  void onGrantedPermission(TsDevicePermission permission) {
    if (permission == MockTestHelper.requiredPermission) {
      _startMockTestScanQRFlowIfCan();
    }
  }

  Future<void> _startMockTestScanQRFlowIfCan() async {
    if (!enableMockTestFlow()) {
      return;
    }

    final String? data = await _mockTestHelper.initMockTestPaymentFlow();

    if (data != null && data.isNotEmpty) {
      mockQRCodeData = data;

      onMockDataIsReady();
    } else {
      _isIgnoredMockTest = true;
      onIgnoreMockTest();
    }
  }

  void onMockDataIsReady();

  void onIgnoreMockTest();
}
