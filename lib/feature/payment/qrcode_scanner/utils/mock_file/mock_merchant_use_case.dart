enum MockMerchantUseCase {
  storeInfoActivated('store_info_activated.json'),
  storeInfoDeactivated('store_info_deactivated.json'),
  storeInfoRecordNotFound('store_info_record_not_found.json'),
  storeInfoVerdictUnknown('store_info_verdict_unknown.json');

  final String value;

  const MockMerchantUseCase(this.value);
}

String getMockMerchantUseCase(MockMerchantUseCase mockCase) {
  return mockCase.value;
}
