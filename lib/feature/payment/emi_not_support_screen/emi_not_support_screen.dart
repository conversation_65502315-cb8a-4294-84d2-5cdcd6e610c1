import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/checkout_repo.dart';
import '../../../data/response/order_session_entity.dart';
import '../../../data/response/qr_code_parse_entity.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../../widget/evo_appbar.dart';
import '../confirm_payment/update_confirm_payment_screen/update_confirm_payment_screen.dart';
import '../switch_order_to_outright_purchase/switch_order_to_outright_purchase_cubit.dart';
import '../utils/active_pos_limit_handler/active_pos_limit_handler.dart';
import '../utils/payment_navigate_helper_mixin.dart';
import '../utils/payment_with_emi_utils.dart';
import 'emi_not_support_body_widget.dart';

class EmiNotSupportScreenArg extends PageBaseArg {
  final QrCodeParseEntity qrCodeParseEntity;

  EmiNotSupportScreenArg({required this.qrCodeParseEntity});
}

class EmiNotSupportScreen extends PageBase {
  static void pushNamed({required QrCodeParseEntity qrCodeParseEntity}) {
    return navigatorContext?.pushNamed(
      Screen.emiNotSupportScreen.name,
      extra: EmiNotSupportScreenArg(
        qrCodeParseEntity: qrCodeParseEntity,
      ),
    );
  }

  final EmiNotSupportScreenArg arg;

  const EmiNotSupportScreen({
    required this.arg,
    super.key,
  });

  @override
  State<EmiNotSupportScreen> createState() => _EmiNotSupportScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.emiNotSupportScreen.routeName);
}

class _EmiNotSupportScreenState extends EvoPageStateBase<EmiNotSupportScreen>
    with PaymentNavigationHelperMixin {
  final SwitchOrderToOutRightPurchaseCubit _switchToOutRightPaymentCubit =
      SwitchOrderToOutRightPurchaseCubit(
    checkOutRepo: getIt.get<CheckOutRepo>(),
    paymentForEmiUtils: PaymentWithEMIUtils(),
  );

  final ActivePosLimitHandler _activePosLimitHandler = ActivePosLimitHandlerImpl();

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<SwitchOrderToOutRightPurchaseCubit>(
      create: (_) => _switchToOutRightPaymentCubit,
      child: BlocListener<SwitchOrderToOutRightPurchaseCubit, SwitchToOutrightPaymentState>(
        listener: (BuildContext context, SwitchToOutrightPaymentState state) {
          _handleSwitchToOutPaymentState(state);
        },
        child: Scaffold(
          backgroundColor: evoColors.background,
          appBar: EvoAppBar(
            leading: CloseButton(
              color: evoColors.icon,
              onPressed: () {
                /// back to previous screen
                navigatorContext?.pop();
              },
            ),
          ),
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: EmiNotSupportBodyWidget(
                onClickPayInAll: _onClickPayInAll,
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onClickPayInAll() {
    _switchToOutRightPaymentCubit.convertOrderToOutRightPayment(widget.arg.qrCodeParseEntity);
  }

  void _handleSwitchToOutPaymentState(SwitchToOutrightPaymentState state) {
    if (state is SwitchToOutrightPaymentApiLoadingState) {
      EvoUiUtils().showHudLoading();
      return;
    }

    EvoUiUtils().hideHudLoading();
    if (state is CreateDynamicOrderSuccessState) {
      _handleCreatePaymentSuccessState(state);
      return;
    }

    if (state is CreateOrderActivatePosLimitErrorState) {
      _activePosLimitHandler.handleActivatedPOSLimitErrorState(
        orderActivatePosLimitEntity: OrderActivatePosLimitEntity(
          paymentService: PaymentService.outrightPurchase,
          orderSession: state.entity?.session,
          emiPackages: state.entity?.emiPackages,
          prerequisitesAction: state.entity?.prerequisitesAction,
        ),
        entity: state.entity,
        entryPoint: Screen.qrCodeScannerScreen,
        onActivatedPOSLimitFlowSucceed: (OrderActivatePosLimitEntity entity) {
          UpdateConfirmPaymentScreen.pushReplacementNamed(
            orderSession: entity.orderSession,
            selectedVoucher: appState.paymentSharedData.selectedVoucher,
          );
        },
      );
    }

    if (state is SwitchToOutrightPaymentCommonErrorState) {
      handleEvoApiError(state.errorUIModel);
      return;
    }

    if (state is SwitchToOutrightPaymentOrderExpiredState) {
      showExpiredOrderBottomSheet();
      return;
    }
  }

  Future<void> _handleCreatePaymentSuccessState(CreateDynamicOrderSuccessState state) async {
    final PaymentService paymentService = PaymentService.outrightPurchase;
    final OrderSessionEntity? orderSession = state.entity?.session;

    final bool isActivateCardOrPosLimit =
        await _activePosLimitHandler.handleActivateCardOrPosLimitIfNeed(
      orderActivatePosLimitEntity: OrderActivatePosLimitEntity(
        paymentService: paymentService,
        orderSession: orderSession,
        prerequisitesAction: state.entity?.prerequisitesAction,
      ),
      entryPoint: Screen.paymentInputAmount,
      onActivatedPOSLimitFlowSucceed: (OrderActivatePosLimitEntity entity) {
        final CreateDynamicOrderSuccessState newState = CreateDynamicOrderSuccessState(
          entity: state.entity,
        );
        _handleCreatePaymentSuccessState(newState);
      },
    );

    if (isActivateCardOrPosLimit) {
      return;
    }

    UpdateConfirmPaymentScreen.pushReplacementNamed(
      orderSession: orderSession,
      selectedVoucher: appState.paymentSharedData.selectedVoucher,
    );
  }
}
