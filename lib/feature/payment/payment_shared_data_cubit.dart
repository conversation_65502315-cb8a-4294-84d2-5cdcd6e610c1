import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../data/response/emi_package_entity.dart';
import '../../data/response/order_session_entity.dart';
import '../../data/response/voucher_entity.dart';
import '../../prepare_for_app_initiation.dart';
import '../manual_link_card/manual_link_card_config.dart';

abstract class PaymentSharedDataCubit<State> extends CommonCubit<State> {
  final AppState _appState;

  PaymentSharedDataCubit(super.initialState, this._appState);

  OrderSessionEntity? get orderSessionInAppState => _appState.paymentSharedData.orderSession;

  set orderSessionInAppState(OrderSessionEntity? orderSessionEntity) {
    _appState.paymentSharedData.orderSession = orderSessionEntity;
  }

  bool? get enableRatingPrompt => _appState.paymentSharedData.orderExtraInfo?.enableRatingPrompt;

  EmiPackageEntity? get emiPackageInAppState => _appState.paymentSharedData.selectedEmiPackage;

  set emiPackageInAppState(EmiPackageEntity? emiPackageEntity) {
    _appState.paymentSharedData.selectedEmiPackage = emiPackageEntity;
  }

  VoucherEntity? get selectedVoucherInAppState => _appState.paymentSharedData.selectedVoucher;

  set selectedVoucherInAppState(VoucherEntity? voucherEntity) {
    _appState.paymentSharedData.selectedVoucher = voucherEntity;
  }

  bool get hasSharedOrderSession => _appState.paymentSharedData.orderSession != null;

  set updateTimeToWaitingForNextLinkCard(int? timeToWaitingForNextLinkCardInMin) {
    if (timeToWaitingForNextLinkCardInMin != null) {
      _appState.manualLinkCardSharedData.timeToWaitingForNextLinkCardInMin =
          timeToWaitingForNextLinkCardInMin;
    }
  }

  String? get sharedOrderSessionId => _appState.paymentSharedData.orderSession?.id;

  int get timeToWaitingForNextLinkCardInMin =>
      _appState.manualLinkCardSharedData.timeToWaitingForNextLinkCardInMin ??
      ManualLinkCardConfig.defaultNextRetryIfExitDurationInMinute;

  set linkCardRequestId(String? linkCardRequestId) {
    _appState.manualLinkCardSharedData.linkCardRequestId = linkCardRequestId;
  }

  String? get linkCardRequestId => _appState.manualLinkCardSharedData.linkCardRequestId;

  String? get linkCardSession => _appState.manualLinkCardSharedData.linkCardSession;

  set linkCardSession(String? linkCardSession) {
    _appState.manualLinkCardSharedData.linkCardSession = linkCardSession;
  }

  @visibleForTesting
  void clearTimeToWaitingForNextLinkCard() {
    _appState.manualLinkCardSharedData.clearTimeToWaitingForNextLinkCard();
  }

  void clearPaymentSharedData() {
    _appState.paymentSharedData.clearAll();
  }

  void clearOrderInfo() {
    _appState.paymentSharedData.clearOrderInfo();
  }

  void clearLinkCardRequestData() {
    _appState.manualLinkCardSharedData.clearLinkCardRequestData();
  }

  void clearSelectedVoucher() {
    _appState.paymentSharedData.selectedVoucher = null;
  }
}
