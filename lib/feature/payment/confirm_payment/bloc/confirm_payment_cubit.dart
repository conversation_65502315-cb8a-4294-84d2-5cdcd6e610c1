import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/uuid/uuid_generator.dart';

import '../../../../data/repository/checkout_repo.dart';
import '../../../../data/response/confirm_and_pay_order_entity.dart';
import '../../../../data/response/emi_package_entity.dart';
import '../../../../data/response/order_session_entity.dart';
import '../../../../data/response/voucher_entity.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../util/functions.dart';
import '../../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../activated_pos_limit/utils/activate_pos_limit_utils.dart';
import '../../../biometric_pin_confirm/biometric_pin_data.dart';
import '../../../feature_toggle.dart';
import '../../mock_file/mock_checkout_file_name.dart';

part 'confirm_payment_state.dart';

class ConfirmPaymentCubit extends CommonCubit<ConfirmPaymentState> {
  final CheckOutRepo checkOutRepo;
  final UUIDGenerator uuidGenerator;
  final EvoLocalStorageHelper evoLocalStorageHelper;

  ConfirmPaymentCubit({
    required this.checkOutRepo,
    required this.uuidGenerator,
    required this.evoLocalStorageHelper,
  }) : super(ConfirmPaymentInitial());

  Future<void> confirmAndPay({
    required AuthenticateType authenticateType,
    String? biometricToken,
    String? pin,
    VoucherEntity? selectedVoucher,
    OrderSessionEntity? orderSession,
    EmiPackageEntity? emiPackage,
  }) async {
    emit(ConfirmPaymentLoading());

    final ConfirmAndPayOrderEntity entity = await checkOutRepo.confirmAndPay(
      sessionId: orderSession?.id,
      idempotencyKey: uuidGenerator.genV4(),
      authType: authenticateType,
      biometricToken: biometricToken,
      pin: pin,
      voucherIds: selectedVoucher?.id != null ? <int?>[selectedVoucher?.id] : <int?>[],
      paymentMethodId: evoUtilFunction.getDefaultPaymentMethod(orderSession?.paymentInfo)?.id,
      userChargeAmount: orderSession?.userChargeAmount,
      emiOfferId: emiPackage?.offer?.id,
      mockConfig: MockConfig(
        enable: false,
        fileName: getConfirmAndPayMockFileName(
            mockVerdict: ConfirmAndPayMockVerdict.creditLimitInsufficient),
      ),
    );

    switch (entity.statusCode) {
      case CommonHttpClient.SUCCESS:
        emit(ConfirmPaymentSuccess(orderSession: entity.session));
        break;

      case CommonHttpClient.BAD_REQUEST:
        handleStateIfBadRequest(entity);
        break;

      case CommonHttpClient.NO_INTERNET:
      case CommonHttpClient.SOCKET_ERRORS:
        emit(ConfirmPaymentNetworkError(ErrorUIModel.fromEntity(entity)));
        break;

      case CommonHttpClient.LIMIT_EXCEEDED:
        handleStateIfLimitExceeded(entity);
        break;

      default:
        emit(ConfirmAndPayCommonFailed(ErrorUIModel.fromEntity(entity)));
        break;
    }
  }

  @visibleForTesting
  void handleStateIfBadRequest(ConfirmAndPayOrderEntity entity) {
    switch (entity.verdict) {
      case ConfirmAndPayOrderEntity.verdictExpiredToken:
        emit(RequestPinPopup());
        break;

      case ConfirmAndPayOrderEntity.verdictSessionNotOpened:
      case ConfirmAndPayOrderEntity.verdictSessionExpired:
        emit(ConfirmPaymentFailedWhenOrderExpired(ErrorUIModel.fromEntity(entity)));
        break;

      case ConfirmAndPayOrderEntity.verdictOneLastTry:
      case ConfirmAndPayOrderEntity.verdictInvalidCredential:
        emit(InvalidCredentialPin(ErrorUIModel.fromEntity(entity)));
        break;

      case ConfirmAndPayOrderEntity.verdictPromotionInvalid:
      case ConfirmAndPayOrderEntity.verdictPromotionExpired:
      case ConfirmAndPayOrderEntity.verdictPromotionUnqualified:
      case ConfirmAndPayOrderEntity.verdictPromotionDuplicate:
      case ConfirmAndPayOrderEntity.verdictPromotionPermissionDenied:
        emit(ConfirmPaymentPromotionError(ErrorUIModel.fromEntity(entity)));
        break;

      case ConfirmAndPayOrderEntity.verdictMissingPaymentMethod:
        emit(ConfirmPaymentMissingPaymentMethod(ErrorUIModel.fromEntity(entity)));
        break;

      case ConfirmAndPayOrderEntity.verdictCreditLimitInsufficientLimit:
        emit(ConfirmAndPayInsufficientCreditLimit());
        break;

      default:
        emit(ConfirmAndPayCommonFailed(ErrorUIModel.fromEntity(entity)));
        break;
    }
  }

  @visibleForTesting
  void handleStateIfLimitExceeded(ConfirmAndPayOrderEntity entity) {
    switch (entity.verdict) {
      case ConfirmAndPayOrderEntity.verdictLimitExceed:
        emit(ConfirmAndPayCommonFailed(ErrorUIModel.fromEntity(entity)));
        break;

      case ConfirmAndPayOrderEntity.verdictTransactionTooSoon:
        emit(ConfirmAndPayTransactionTooSoon(ErrorUIModel.fromEntity(entity)));
        break;

      default:
        emit(ConfirmAndPayCommonFailed(ErrorUIModel.fromEntity(entity)));
        break;
    }
  }

  Future<void> checkShowWaitingPopUp({OrderSessionEntity? order}) async {
    if (getIt.get<FeatureToggle>().enableActivatePOSLimitFeature == false) {
      emit(NoNeedShowWaitingPopup(order: order));
      return;
    }

    final int? savedTime = await evoLocalStorageHelper.getLastTimeRequest3DSCardActivation();

    if (savedTime == null) {
      emit(NoNeedShowWaitingPopup(order: order));
      return;
    }

    final int remainingTime = ActivatePosLimitUtils().calculateRemainingCountdownTime(savedTime);

    if (remainingTime > 0) {
      emit(NeedShowWaitingPopup(remainingTime));
      return;
    }

    evoLocalStorageHelper.setLastTimeRequest3DSCardActivation(null);
    emit(NoNeedShowWaitingPopup(order: order));
  }
}
