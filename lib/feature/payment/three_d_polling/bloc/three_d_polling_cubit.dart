import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/cancelable_task_controller.dart';

import '../../../../data/repository/checkout_repo.dart';
import '../../../../data/response/create_order_entity.dart';
import '../../../../data/response/emi_package_entity.dart';
import '../../../../data/response/order_session_entity.dart';
import '../../../../data/response/voucher_entity.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../logging/evo_logging_event.dart';
import '../../mock_file/mock_checkout_file_name.dart';
import '../../payment_shared_data_cubit.dart';

part 'three_d_polling_state.dart';

class ThreeDPollingCubit extends PaymentSharedDataCubit<ThreeDPollingState> with LogErrorMixin {
  late final CheckOutRepo _checkOutRepo;
  late final CancelableTaskController _getCheckoutDetailTaskController;

  @visibleForTesting
  bool isAutoCancelOrder = false;

  @visibleForTesting
  bool isCancellingOrder = false;

  ThreeDPollingCubit({
    required CheckOutRepo checkOutRepo,
    required CancelableTaskController getCheckoutDetailTaskController,
    required AppState appState,
  }) : super(
          ThreeDPollingInitial(),
          appState,
        ) {
    _checkOutRepo = checkOutRepo;
    _getCheckoutDetailTaskController = getCheckoutDetailTaskController;
  }

  Future<void> handleGetCheckOutDetail(String? id) async {
    try {
      await getCheckOutDetail(id);
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      /// task is cancelable, so we need to catch the exception
      logException(
          eventType: EvoEventType.cancelableFutureFeature,
          methodName: 'handleGetCheckOutDetail',
          exception: e);
    }
  }

  Future<void> cancelTransaction(String? id, {bool isAutoCancel = false}) async {
    isAutoCancelOrder = isAutoCancel;

    if (isCancellingOrder) {
      return;
    }

    /// cancel the getCheckoutDetail API if it is running. to prevent the case that app still handling
    /// the response of getCheckoutDetail API while user cancel the order.
    /// Related ticket: https://trustingsocial1.atlassian.net/browse/EMA-1406
    _getCheckoutDetailTaskController.cancel();
    isCancellingOrder = true;

    emit(CancelOrderLoading(isAutoCancel: isAutoCancelOrder));

    await _checkOutRepo.cancelTransaction(id,
        mockConfig: MockConfig(enable: false, fileName: cancelTransactionMockFileName()));

    final String? orderSessionId = sharedOrderSessionId;
    final EmiPackageEntity? emiPackage = emiPackageInAppState;
    final VoucherEntity? selectedVoucher = selectedVoucherInAppState;

    clearOrderInfo();

    emit(CancelOrderCompleted(
      isAutoCancel: isAutoCancelOrder,
      emiPackage: emiPackage,
      orderSessionId: orderSessionId,
      selectedVoucher: selectedVoucher,
    ));
    isCancellingOrder = false;
  }

  @visibleForTesting
  Future<void> getCheckOutDetail(String? id) async {
    emit(ThreeDPollingLoading());

    final CreateOrderEntity? createOrderEntity =
        await _getCheckoutDetailTaskController.perform<CreateOrderEntity>(
      _checkOutRepo.getCheckOutDetail(id,
          mockConfig: MockConfig(
            enable: false,
            fileName: getCheckOutDetailMockFileName(),
          )),
    );

    if (createOrderEntity?.statusCode == CommonHttpClient.SUCCESS) {
      emit(ThreeDPollingSuccess(orderSession: createOrderEntity?.session));
    } else {
      emit(ThreeDPollingError(error: ErrorUIModel.fromEntity(createOrderEntity)));
    }
  }
}
