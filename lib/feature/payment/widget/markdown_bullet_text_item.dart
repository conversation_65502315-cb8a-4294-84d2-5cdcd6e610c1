import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

import '../../../resources/resources.dart';

class MarkDownBulletTextItem extends StatelessWidget {
  final String text;

  const MarkDownBulletTextItem({required this.text, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: ShapeDecoration(
        shape: RoundedRectangleBorder(
          side: BorderSide(color: evoColors.disableTextFieldBorder),
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      alignment: Alignment.center,
      padding: const EdgeInsets.all(12),
      child: Row(
        children: <Widget>[
          _buildDot(),
          const SizedBox(width: 8),
          Expanded(
            child: Container(
              alignment: Alignment.center,
              // height: heightOfTextBox,
              child: Markdown(
                shrinkWrap: true,
                styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
                  p: evoTextStyles.bodyMedium(evoColors.textPassive).copyWith(height: 1.42),
                  strong: evoTextStyles.h200(color: evoColors.textActive).copyWith(height: 1.42),
                ),
                physics: const NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                data: text,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDot() {
    return Padding(
      padding: const EdgeInsets.all(4),
      child: Container(
        width: 8,
        height: 8,
        decoration: ShapeDecoration(
          color: evoColors.emiTenorBackground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}
