import 'package:flutter/material.dart';

import '../../../resources/resources.dart';
import '../../payment/payment_config.dart';

class PaymentResultTitleWidget extends StatelessWidget {
  final String title;

  const PaymentResultTitleWidget({
    required this.title,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      maxLines: PaymentConfig.paymentResultEmiTitleMaxLines,
      overflow: TextOverflow.ellipsis,
      style: evoTextStyles.h600(evoColors.emiTenorBackground),
    );
  }
}
