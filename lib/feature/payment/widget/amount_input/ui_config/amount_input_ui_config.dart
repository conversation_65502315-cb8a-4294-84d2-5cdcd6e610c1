import 'package:flutter/material.dart';

import '../../../../../resources/resources.dart';
import 'amount_input_border.dart';
import 'currency_display_type.dart';

class AmountInputUIConfig {
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final TextStyle? errorStyle;
  final AmountInputBorder? inputBorder;
  final EdgeInsetsGeometry? contentPadding;
  final bool? isDense;
  final CurrencyDisplayType currencyDisplayType;

  AmountInputUIConfig._({
    this.textStyle,
    this.hintStyle,
    this.errorStyle,
    this.inputBorder,
    this.contentPadding,
    this.isDense,
    this.currencyDisplayType = CurrencyDisplayType.icon,
  });

  factory AmountInputUIConfig.defaultInputBorder() {
    return AmountInputUIConfig._(
      textStyle: evoTextStyles.h600(evoColors.textActive),
      hintStyle: evoTextStyles.h600(evoColors.textHint),
      errorStyle: evoTextStyles.bodySmall(color: evoColors.error),
      inputBorder: AmountDefaultInputBorder(),
    );
  }

  factory AmountInputUIConfig.nonInputBorder() {
    return AmountInputUIConfig._(
      currencyDisplayType: CurrencyDisplayType.text,
      textStyle: evoTextStyles.h600(evoColors.primary),
      hintStyle: evoTextStyles.h600(evoColors.paymentInputAmountV2TextHint),
      errorStyle: evoTextStyles.bodyMedium(evoColors.error),
      contentPadding: EdgeInsets.zero,
      isDense: true,
      inputBorder: AmountNonInputBorder(),
    );
  }
}
