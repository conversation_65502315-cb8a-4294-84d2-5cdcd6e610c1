import '../../../prepare_for_app_initiation.dart';
import '../emi_shared_data.dart';

mixin EmiConditionsUtilsMixin {
  EmiConditionInfo sharedEmiConditionInfo() {
    final AppState appState = getIt.get<AppState>();
    return appState.paymentSharedData.emiSharedData.emiConditionInfo;
  }

  bool checkIfProductCodeValid(String? productCode) {
    if (productCode == null) {
      return false;
    }
    final EmiConditionInfo emiConditionInfo = sharedEmiConditionInfo();
    final List<String> allowProductCodes = emiConditionInfo.allowProductCodes ?? <String>[];
    return allowProductCodes.contains(productCode);
  }

  bool checkIfMerchantIsValid(String? productCode, String? merchantId) {
    if (productCode == null || merchantId == null) {
      return false;
    }
    final EmiConditionInfo emiConditionInfo = sharedEmiConditionInfo();

    final List<String> listBlockedMerchants =
        emiConditionInfo.productCodeToBlockedMerchants?[productCode] ?? <String>[];
    return !listBlockedMerchants.contains(merchantId);
  }

  bool checkIfUserChargeAmountValid(int amount) {
    final EmiConditionInfo emiConditionInfo = sharedEmiConditionInfo();
    final int? minAmount = emiConditionInfo.minUserChargeAmount;
    if (minAmount == null) {
      return true;
    }
    return amount >= minAmount;
  }

  bool checkIfOrderValidForEMI({
    required String productCode,
    required int amount,
    required String merchantId,
  }) {
    return checkIfProductCodeValid(productCode) &&
        checkIfUserChargeAmountValid(amount) &&
        checkIfMerchantIsValid(productCode, merchantId);
  }
}
