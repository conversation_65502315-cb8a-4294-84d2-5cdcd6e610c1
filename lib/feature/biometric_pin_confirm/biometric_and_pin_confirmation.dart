import '../../resources/resources.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../util/token_utils/jwt_helper.dart';
import '../biometric/utils/bio_auth_result.dart';
import '../biometric/utils/biometrics_authenticate.dart';
import 'confirm_pin/confirm_pin_popup.dart';

abstract class ConfirmBiometricAndPinCallback {
  void onBiometricConfirm(String? biometricToken);

  void onPinInputConfirm(String? pin);

  void onConfirmPinPopupClosed();
}

class BiometricAndPinConfirmation implements ConfirmPinPopupCallback {
  final BiometricsAuthenticate biometricsAuthenticate;
  final EvoLocalStorageHelper secureStorageHelper;
  final JwtHelper jwtHelper;
  late ConfirmBiometricAndPinCallback? callback;

  BiometricAndPinConfirmation(
      {required this.biometricsAuthenticate,
      required this.secureStorageHelper,
      required this.jwtHelper});

  @override
  void onInputPin(String? pin) {
    callback?.onPinInputConfirm(pin);
  }

  Future<void> confirm({
    required ConfirmBiometricAndPinCallback callback,
    bool isForcePin = false,
  }) async {
    this.callback = callback;

    /// check biometric is enable or not
    final bool isEnableBioAuth = await _isEnableBiometricAuthenticator();
    final bool isTokenUsable = await _isBiometricTokenUsable();

    if (isForcePin || !isEnableBioAuth || !isTokenUsable) {
      await _showConfirmPinPopup(callback);
      return;
    }

    final String? biometricToken = await _getBiometricToken();
    final BioAuthResult result = await biometricsAuthenticate.authenticate(
        localizedReason: EvoStrings.localizedReasonForUsingBiometrics);
    if (result.isAuthSuccess && biometricToken != null) {
      this.callback?.onBiometricConfirm(biometricToken);
    } else {
      await _showConfirmPinPopup(callback);
      return;
    }
  }

  Future<bool> _isEnableBiometricAuthenticator() {
    return secureStorageHelper.isEnableBiometricAuthenticator();
  }

  Future<bool> _isBiometricTokenUsable() async {
    final String? biometricToken = await secureStorageHelper.getBiometricToken();
    return jwtHelper.isCanUse(biometricToken);
  }

  Future<String?> _getBiometricToken() {
    return secureStorageHelper.getBiometricToken();
  }

  Future<void> _showConfirmPinPopup(
    ConfirmBiometricAndPinCallback callback,
  ) async {
    await ConfirmPinPopup.show(callback: this);
    callback.onConfirmPinPopupClosed();
  }
}
