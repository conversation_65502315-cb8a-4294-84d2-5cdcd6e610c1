class DOPNativeConstants {
  static const int resendOTPDisplayDelayInSeconds = 15;

  /// this is percentage of icon height to screen height as figma design
  /// related screen:
  /// - [DOPNativeIdCardBackSideVerificationScreen]
  /// - [DOPNativeIdCardFrontSideVerificationScreen]
  /// - [DOPNativeIdCardSuccessScreen]
  /// - [DOPNativeSelfieVerificationScreen]
  /// - [DOPNativeSelfieVerifySuccessScreen]
  static const double statusIconHeightPercentage = 131 / figmaScreenHeight;

  static const double ekycInstructionIconHeightPercentage = 107.39 / figmaScreenHeight;

  static const double fptNfcIntroductionAnimationHeightPercentage = 108 / figmaScreenHeight;

  static const double fptNfcVideoIntroductionRatio = 16 / 9;

  static const double figmaScreenHeight = 812;

  static const double figmaScreenWidth = 375;

  static const double dopNativeAppBarHeight = 64;

  static const double dopNativeLoadingAnimationSize = 34;

  // Refer: https://docs.google.com/spreadsheets/d/*********************************/edit#gid=*********
  static const int dopNativeMaxAddressLength = 70;

  // Refer: https://docs.google.com/spreadsheets/d/*********************************/edit#gid=*********
  static const int dopNativeMaxCompanyNameLength = 200;

  // Refer: https://docs.google.com/spreadsheets/d/*********************************/edit#gid=*********&range=H12
  static const int incomeThreshold = 0;
  static const double maxIncomeInBillions = 999;

  static const String incomeDecimalSeparator = ',';

  // Refer to: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3503359294/S19.+Card+Activation#:~:text=At%20processing%20screen%20of%20Activation%20process%20%2D%20wait%20for%2015%20seconds%20for%20calling%20API%20result
  static const int delayCallGetCardStatusInSeconds = 15;

  static const int defaultPollingIntervalTimeInMs = 1000;

  static const int cardStatusCountdownAllProgressInSecs = 30;

  /// Refer to: https://trustingsocial1.atlassian.net/browse/EMA-5474
  static const int cardStatusCountdownToPoolingInSecs = 10;

  /// Refer to: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3503359294/S19.+Card+Activation
  static const int delayToGetAppStateCardActivatedInSecs = 3;

  static const String dopDateFormat = 'yyyy-MM-dd';
  static const String tvNfcSdkDateFormat = 'dd/MM/yyyy';
  static const String fptNfcSdkDateFormat = 'yyMMdd';

  /// API is timed out with default 10s send timeout.
  /// Only for DOP Native, we need to increase the timeout to 30s.
  /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-3180
  /// Refer: https://trustingsocial.slack.com/archives/C06BBSRR99P/p1714031607837079
  static const int receiveTimeoutInSecond = 30;
  static const int sendTimeoutInSecond = 30;

  static const int nfcErrorPopupPositiveButtonDelayInSeconds = 2;

  /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-5444
  static const int defaultMWGAutoPCBPollingInterval = 10;

  static const String appraisingPrefix = 'appraising';
}

class DopDevicePlatformConstant {
  static const String android = 'android';
  static const String ios = 'ios';
}
