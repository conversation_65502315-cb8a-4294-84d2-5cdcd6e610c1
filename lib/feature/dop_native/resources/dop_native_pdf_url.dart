import 'package:flutter_common_package/flavors/flavor_config.dart';

import '../../../flavors/flavors_type.dart';

class DOPNativePDFUrl {
  // Refer to: https://trustingsocial.slack.com/archives/C06BBSRR99P/p1717390955351339?thread_ts=1716098541.011819&cid=C06BBSRR99P
  static const String proURL = 'https://dop-vnetwork-cdn.goevo.vn/documents/';
  static const String stagUATURL = 'https://dop-release-vnetwork-cdn.goevo.vn/documents/';

  // Refer to: https://trustingsocial1.atlassian.net/wiki/x/QwLE0
  static const String termConditionUrl = 'T&C_EVO.pdf';
  static const String dataProtectionUrl = 'T&C_EVO_Protect_Data.pdf';

  // Refer to: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3504374392/S13+14.+e-sign+intro+e-sign+review+e-sign+OTP
  static const String fptCATermCondition = 'T&C_EVO_FPT_CA.pdf';
}

String getDOPNativePDFURLByFlavor(String url) {
  if (FlavorConfig.instance.flavor == FlavorType.prod.name) {
    return '${DOPNativePDFUrl.proURL}$url';
  }

  return '${DOPNativePDFUrl.stagUATURL}$url';
}
