class DOPNativeStrings {
  /// Common
  static const String dopNativeHelpChat = 'Trợ giúp';
  static const String dopNativeScreenTitle = 'Đăng ký mở thẻ tại cửa hàng';
  static const String dopOnWebTitle = 'Đăng ký mở thẻ';
  static const String dopNativeAnd = 'và';
  static const String dopNativeOr = 'hoặc';
  static const String dopNativeOur = 'về bảo vệ dữ liệu cá nhân của TPBank';
  static const String dopNativeNote = 'Lưu ý';
  static const String dopNativeOpenEVOCard = 'Mở thẻ ngay';
  static const String dopNativeRetry = 'Thử lại';
  static const String dopNativeSetup = 'Cài đặt';
  static const String dopNativeIgnore = 'Bỏ qua';
  static const String dopNativeDoNot = 'Không';
  static const String dopNativeNext = 'Tiếp tục';
  static const String dopNativeMinute = 'phút';
  static const String dopNativeHour = 'giờ';
  static const String dopNativeConfirm = 'Đồng ý';
  static const String dopNativeDone = 'Hoàn tất';
  static const String dopNativeActive = 'Kích hoạt';
  static const String dopNativeDownload = 'Tải ngay';
  static const String dopNativeDownloadTPBankMobile = 'Tải TPBank Mobile';
  static const String dopNativeReceiveVoucher = 'Nhận ưu đãi';
  static const String dopNativeAgree = 'Xác nhận';
  static const String dopNativeTPBankAppName = 'TPBank Mobile';
  static const String dopNativeClose = 'Đóng';
  static const String dopNativeGoToHome = 'Về trang chủ';
  static const String dopNativeAutoNavigateAfter = 'Tự động chuyển màn hình sau';

  /// OTP screen
  static const String dopNativeVerifyOtpTitle = 'Xác thực số điện thoại';
  static const String dopNativeVerifyOtpDescription =
      'Vui lòng nhập mã xác thực (OTP) đã được gửi đến số điện thoại của bạn.';
  static const String dopNativeNote1 = 'Bạn chỉ được nhập 1 mã OTP tối đa 3 lần.';
  static const String dopNativeNote2 =
      'Phiên làm việc sẽ tạm ngưng 30 phút nếu gửi lại OTP quá 4 lần.';
  static const String otpGeneralErrorMessage = 'Mã OTP không đúng, vui lòng nhập lại.';

  /// Error Screen
  static const String dopNativeCommonErrorTitle = 'Có lỗi xảy ra';
  static const String dopNativeCommonErrorDescription = 'Vui lòng thử lại sau ít phút';
  static const String dopNativeCommonErrorButtonTitle = 'Thử lại';
  static const String dopNativeCommonBackToHomeButtonTitle = 'Quay lại';
  static const String dopNativeOTPCodeErrorTitle = 'Lỗi mã xác thực';
  static const String dopNativeOTPCodeErrorDescription =
      'Mã xác thực lỗi hoặc đã hết hiệu lực, vui lòng nhấn vào nút bên dưới để nhận lại mã';
  static const String dopNativeOTPCodeErrorButtonTitle = 'Gửi lại mã xác thực';
  static const String dopNativeOTPRateLimitErrorTitle = 'Vượt quá số lần nhập\nmã xác thực';
  static const String dopNativeOTPRateLimitErrorDescription =
      'Bạn đã nhập mã xác thực quá số lần cho phép. Vui lòng quay lại sau 30 phút để tiếp tục';
  static const String dopNativeOTPTimeoutErrorTitle = 'Hết thời hạn đăng ký';
  static const String dopNativeOTPTimeoutErrorDescription =
      'Đã quá thời gian đăng ký sản phẩm ưu đãi dành cho quý khách. Vui lòng liên hệ 1900 58 58 85 để được hỗ trợ';
  static const String dopNativeOTPTimeoutErrorButtonTitle = 'Đăng ký lại';
  static const String otpDidNotReceive = 'Không nhận được mã OTP?';
  static const String otpResend = 'Gửi lại OTP';

  static const String dopNativeRegisterPTokenErrorTitle = 'Mã QR hết hiệu lực';
  static const String dopNativeRegisterPTokenErrorDescription =
      'Mã QR mở thẻ đã hết thời gian hiệu lực, bạn vui lòng liên hệ nhân viên bán hàng để quét lại';
  static const String dopNativeRegisterPTokenErrorCTAButton = 'Quét lại mã QR';

  /// Input phone number Dialog
  static const String dopNativeInputPhoneDialogTitle =
      'Vui lòng nhập số điện thoại để xác thực và đăng ký';
  static const String dopNativeInputPhoneHint = 'Số điện thoại của bạn';
  static const String dopNativeInputPhoneNumberDescription =
      'Tôi xác nhận đã đọc, hiểu rõ và đồng ý về';
  static const String dopNativeInputPhoneNumberTermConditionAppDescription =
      'Điều khoản sử dụng dịch vụ';
  static const String dopNativeInputPhoneNumberTermConditionPersonalDescription =
      'Điều kiện điều khoản';
  static const String dopNativeInputPhoneNumberFormatFailed =
      'SĐT sai định dạng, vui lòng nhập lại';
  static const String dopNativeInputPhoneNumberEmptyFailed = 'Vui lòng nhập số điện thoại';

  /// Rejection screen
  static const String rejectionContentTitle = 'Đăng ký chưa thành công';
  static const String rejectionContentDescription1 =
      'Hiện tại chúng tôi chưa tìm được sản phẩm thẻ phù hợp với bạn.\nBạn có thể liên hệ';
  static const String rejectionContentDescription2 =
      'để được tư vấn thêm hoặc tham khảo các sản phẩm thẻ tín dụng khác của TPBank.';
  static const String rejectionButtonTitle = 'Xem các sản phẩm khác';

  /// Sub Introduction Screen
  static const String dopNativeSubIntroductionFooter = 'Đăng ký 100% online,\nphê duyệt tức thì';

  /// FaceOTP
  static const String faceOtpIntroductionTitle = 'Chào mừng bạn đã trở lại';
  static const String faceOtpIntroductionDescription =
      'Để tiếp tục hành trình mở thẻ, xin vui lòng đăng nhập bằng cách chụp ảnh chân dung (selfie) \ncủa bạn.';
  static const String faceOtpIntroductionCTA = 'Mở camera';
  static const String faceOtpSuccessTitle = 'Xác thực khuôn mặt\nthành công!';
  static const String dopNativeFaceOTPInvalidTitle =
      'Rất tiếc, ảnh của bạn chưa hợp lệ. Bạn thực hiện lại nhé!';

  //Id verification Instruction Screen
  static const String dopNativeIdVerificationInstructionDescTitle = 'Xác thực CCCD\ngắn chip';
  static const String dopNativeIdVerificationInstructionDesc =
      'Thông tin từ CCCD gắn chip sẽ được hệ thống tự động đọc và điền giúp bạn. Vui lòng kiểm tra lại thông tin sau khi hoàn thành bước chụp.';
  static const String dopNativeStartCapture = 'Bắt đầu chụp';
  static const String dopNativeIdVerificationInstructionAware1Prefix = 'Chỉnh hướng chụp để';
  static const String dopNativeIdVerificationInstructionAware1Suffix = 'bị loá sáng';
  static const String dopNativeIdVerificationInstructionAware2Suffix =
      'dùng giấy tờ mất góc, bị nhòe, mờ';
  static const String dopNativeIdVerificationInstructionAware3Suffix =
      'dùng bản sao hoặc không chính chủ';
  static const String dopNativeIdVerificationInstructionGraphicCaption =
      'Chụp ảnh 2 mặt CCCD gắn chip';
  static const String dopNativeIdVerificationOpenSettingDialogTitle = 'Quyền truy cập Camera';
  static const String dopNativeIdVerificationOpenSettingDialogContent =
      'Kích hoạt Camera trong phần cài đặt để thực hiện tính năng xác thực CCCD gắn chip.';

  // eKYC Processing screen
  static const String dopNativeEkycProcessingTitle = 'Vui lòng không đóng ứng dụng';
  static const String dopNativeEkycProcessingDesc =
      'Hệ thống đang kiểm tra\nthông tin trên CMND/CCCD của bạn.';

  // eKYC Success screen
  static const String dopNativeIdCardVerificationSuccessTitle = 'Xác thực CCCD/CMND\nthành công';
  static const String dopNativeIdCardVerificationSuccessDesc =
      'Mời bạn tiếp tục xác thực CCCD gắn chip ở bước tiếp theo';

  // eKYC Error screen
  static const String dopNativeEkycInvalidImageCapturedTitle =
      'Rất tiếc, ảnh của bạn chưa\nhợp lệ. Bạn thực hiện lại nhé!';
  static const String dopNativeEkycExceedCapturingLimitTitle = 'Vượt quá số lần chụp';
  static const String dopNativeEkycInvalidIDVerificationTitle =
      'Giấy tờ không phải là CCCD gắn chip';
  static const String dopNativeEkycInvalidIDVerificationDesc =
      'Vui lòng sử dụng CCCD gắn chip để  \nđăng ký mở thẻ online';
  static const String dopNativeDefaultEkycErrorMessage =
      'Ảnh chụp không hợp lệ. Vui lòng đặt CMND/CCCD vào khung hình của máy ảnh, hình chụp đầy đủ, rõ ràng, không có vật che, không bị mờ/chói sáng.  \n**Lưu ý: Bạn chỉ có tối đa 5 lần chụp**';
  static const String dopNativeEkycExceedCapturingLimitDesc =
      'Bạn đã vượt quá số lần chụp hình, vui lòng thử lại sau 30 phút.';

  // selfie verification introduction screen
  static const String dopNativeSelfieCaptureIntroductionTitle = 'Xác thực\nkhuôn mặt';
  static const String dopNativeSelfieCaptureInstructionDesc =
      'Chụp ảnh chân dung (selfie) để xác thực hồ sơ theo quy định của Ngân hàng Nhà nước. Mọi thông tin của bạn được bảo mật an toàn.';
  static const String dopNativeSelfieCaptureAware1 = 'Luôn giữ đầu trong khung hình';
  static const String dopNativeSelfieCaptureAwareSuffix2 =
      'đeo kính râm, nón hoặc các phụ kiện che mặt';
  static const String dopNativeSelfieCaptureAwarePrefix3 = 'Môi trường chụp';
  static const String dopNativeSelfieCaptureAwareSuffix3 = 'quá tối hoặc chói sáng';
  static const String dopNativeSelfieVerificationTitle =
      'Hệ thống đang kiểm tra ảnh chân dung của bạn.';
  static const String dopNativeSelfieVerificationDescription = 'Vui lòng không đóng ứng dụng';
  static const String dopNativeSelfieVerificationSuccessTitle = 'Xác thực khuôn mặt\nthành công!';
  static const String dopNativeDefaultSelfieEkycErrorMessage =
      'Ảnh chụp không hợp lệ. Vui lòng chụp ảnh chân dung rõ nét, nhìn thẳng vào camera và khuôn mặt chiếm 70%-80% ảnh, không đội mũ/ đeo kính râm/ khẩu trang/ xoã tóc trước mặt, không bị chói sáng.  \n**Lưu ý: Bạn chỉ có tối đa 5 lần chụp**';
  static const String dopNativeSelfieVerificationOpenSettingDialogTitle = 'Quyền truy cập Camera';
  static const String dopNativeSelfieVerificationOpenSettingDialogContent =
      'Kích hoạt Camera trong phần cài đặt để thực hiện tính năng xác thực khuôn mặt.';

  // metadata popup
  static const String dopNativeSearchInputHint = 'Tìm Kiếm...';

  // Confirm close DOP Native flow dialog
  static const String confirmCloseDOPNativeFlowTitle =
      'Bạn có chắc muốn dừng hành trình mở thẻ không?';
  static const String confirmCloseDOPNativeFlowContent =
      '“Tiếp tục” hành trình để không bỏ lỡ các ưu đãi hấp dẫn từ thẻ TPBank EVO';

  // address dialog
  static const String dopNativeProvinceDialogTitle = 'Tỉnh/Thành phố';
  static const String dopNativeDistrictDialogTitle = 'Quận/Huyện';
  static const String dopNativeWardDialogTitle = 'Phường/Xã';

  // ID Verification Confirm screen
  static const String dopNativeIdVerificationName = 'Họ và tên';
  static const String dopNativeIdVerificationIdCard = 'Số CCCD';
  static const String dopNativeIdVerificationOldIdCard = 'Số CMND cũ';
  static const String dopNativeIdVerificationDOB = 'Ngày sinh';
  static const String dopNativeIdVerificationGender = 'Giới tính';
  static const String dopNativeIdVerificationPlaceOfIssue = 'Nơi cấp';
  static const String dopNativeIdVerificationPermanentResidence = 'Địa chỉ trên sổ\nhộ khẩu';
  static const String dopNativeIdVerificationOldIdCardInput = 'Vui lòng bổ sung nếu có';
  static const String dopNativeIdVerificationConfirmIndicatorTitle = 'Xác nhận thông tin';
  static const String dopNativeIdVerificationConfirmScreenTitle = 'Thông tin trên CCCD';
  static const String dopNativeIdVerificationConfirmScreenDesc =
      'Vui lòng kiểm tra thông tin từ CCCD và bổ sung một số thông tin cần thiết.';
  static const String dopNativeIdVerificationPermanentResidenceInputTextTitle =
      'Địa chỉ trên sổ hộ khẩu';
  static const String dopNativeIdVerificationPermanentResidenceInputTextTitleAddition = 'cụ thể';
  static const String dopNativeIdVerificationPermanentResidenceInputTextHint =
      'Chọn địa chỉ khu vực';
  static const String dopNativeIdVerificationStreetInputTextHint = 'Nhập số nhà, tên đường';
  static const String dopNativeInputPermanentAddressTooLongFailed = 'Không được dài hơn {0} kí tự';
  static const String dopNativeInputPermanentAddressSpecialCharacterFailed =
      'Không được chứa ký tự đặc biệt (VD: !@#\$%^&*())';
  static const String dopNativePermanentAddressEmptyFailed = 'Vui lòng nhập địa chỉ';
  static const String dopNativeResidenceAddressEmptyFailed = 'Vui lòng chọn địa chỉ khu vực';

  // employment dialog
  static const String dopNativeEmploymentStatusTitle = 'Tình trạng việc làm';
  static const String dopNativeEmploymentTitle = 'Nghề nghiệp';

  // Appraising Verification Screen
  static const String dopNativeAppraisingVerificationTitle = 'TPBank đang xử lý thông tin';
  static const String dopNativeAppraisingVerificationDesc = 'Vui lòng không đóng ứng dụng';

  // Internet error
  static const String dopNativeInternetErrorTitle = 'Lỗi kết nối';
  static const String dopNativeInternetErrorDesc =
      'Vui lòng kiểm tra kết nối mạng của bạn và nhấn nút bên dưới để tiếp tục';

  // Invalid token
  static const String dopNativeInvalidTokenTitle = 'Phiên làm việc hết hạn';
  static const String dopNativeInvalidTokenDesc =
      'Phiên làm việc của bạn đã hết hạn, vui lòng nhấn vào nút bên dưới để tiếp tục';
  static const String dopNativeInvalidTokenButtonTitle = 'Thử lại';

  //Inform success
  static const String dopNativeApproveSuccess = 'Phê duyệt thẻ thành công';
  static const String dopNativeRegisterSuccessAutoDesc1 =
      'Chúc mừng bạn đã được phê duyệt thẻ tín dụng TPBank EVO với hạn mức';
  static const String dopNativeRegisterSuccessAutoDesc2 =
      'Vui lòng bổ sung một số thông tin liên hệ để chúng tôi hoàn thiện hồ sơ và giao thẻ.';
  static const String dopNativeVietnameseCurrencySymbolFull = 'đồng';
  static const String dopNativeInformSuccessTitle = 'Bổ sung thông tin';
  static const String dopNativeInformSuccessDesc =
      'Vui lòng bổ sung thông tin để chúng tôi hoàn thiện hồ sơ';

  // ID Verification Confirm Additional Info screen
  static const String dopNativeAdditionalInfoScreenTitle = 'Thông tin bổ sung';
  static const String dopNativePaymentTypeTitle = 'Hình thức nhận lương';
  static const String dopNativeAddressInputHint = 'Chọn theo khu vực';
  static const String dopNativeAddressInputLabel = 'Địa chỉ nơi ở hiện tại';
  static const String dopNativeEmploymentInputHint = 'Chọn';
  static const String dopNativeEmploymentInputLabel = 'Tình trạng việc làm và Nghề nghiệp';
  static const String dopNativeEmailInputHint = 'Email';
  static const String dopNativeEmailInputLabel = 'Nhập email của bạn';
  static const String dopNativeIncomeInputHint = 'Nhập số tiền';
  static const String dopNativeIncomeInputLabel = 'Thu nhập hàng tháng (Đơn vị: Triệu đồng)';
  static const String dopNativePaymentTypeEmptyErrorMsg = 'Vui lòng chọn hình thức nhận lương';
  static const String dopNativeEmailEmptyErrorMsg = 'Vui lòng nhập email';
  static const String dopNativeEmailInvalidErrorMsg = 'Email không đúng định dạng';
  static const String dopNativeIncomeEmptyErrorMsg = 'Vui lòng nhập thu nhập hàng tháng';
  static const String dopNativeIncomeTooSmallErrorMsg = 'Thu nhập phải lớn hơn {0}';
  static const String dopNativeIncomeInvalidErrorMsg = 'Không đúng định dạng';
  static const String dopNativeIncomeExceedCapPrefix = 'Thu nhập kê khai không được vượt quá';
  static const String dopNativeIncomeExceedCapPostfix = 'triệu';
  static const String dopNativeEmploymentEmptyErrorMsg =
      'Vui lòng chọn Tình trạng việc làm và Nghề nghiệp';

  /// CIF Confirm screen
  static const String cifConfirmUpdateInfoAtCounterTitle = 'Cập nhật thông tin tại quầy';
  static const String cifConfirmNoBranchTitle = 'Đăng ký chưa thành công';
  static const String cifConfirmOpenCardInfoTitle = 'Xác nhận thông tin mở thẻ';

  // Subtitle
  static const String cifConfirmExistingCustomerSubtitle = 'Bạn là khách hàng hiện hữu của TPBank.';
  static const String cifConfirmNoBranchSubTitle =
      'Thông tin mở thẻ chưa khớp với thông tin định danh (CIF) đã đăng ký tại TPBank. Tạm thời chúng tôi chưa thể mở thẻ cho bạn';

  // Without CIF Info
  static const String cifConfirmPleaseOpenCardNote =
      'Vui lòng chọn "Mở thẻ" để xác nhận bạn đồng ý mở thẻ tín dụng TPBank EVO với thông tin số CMND/CCCD đã đăng ký dịch vụ tại TPBank.';
  static const String cifConfirmUpdateIDCardNote =
      'Nếu bạn muốn mở thẻ với số CMND/CCCD hiện tại, vui lòng đến Chi nhánh/ Phòng giao dịch gần nhất của TPBank trong vòng 7 ngày để cập nhật thông tin và quay lại EVO app, chọn "Tôi đã cập nhật thông tin" để tiếp tục mở thẻ.';

  static const String cifConfirmDifPhoneNote =
      'Số điện thoại bạn đăng ký mở thẻ tín dụng không khớp với số điện thoại bạn đã đăng ký dịch vụ trước đó tại TPBank';
  static const String cifConfirmDifCifNote =
      'Thông tin giấy tờ tùy thân đang sử dụng hiện chưa khớp đúng với thông tin mà bạn đã đăng ký với TPBank trước đó.';
  static const String cifConfirmDifInfoNote =
      'Họ và tên, ngày tháng năm sinh đang sử dụng hiện chưa khớp với thông tin mà bạn đã đăng ký với TPBank.';
  static const String cifConfirmCifReopenNote =
      'Thông tin giấy tờ tuỳ thân của bạn trùng với hồ sơ đã đóng tại TPBank.';

  static const String cifConfirmUpdateAtCounterNote =
      'Vui lòng đến Chi nhánh/ Phòng giao dịch gần nhất của TPBank trong vòng 7 ngày để cập nhật thông tin và quay lại EVO app, chọn "Tôi đã cập nhật thông tin" để tiếp tục mở thẻ.';

  // With CIF Info
  static const String cifConfirmDifNationIdNote =
      'Thông tin số CMND/CCCD đang đăng ký khác với thông tin bạn đã đăng ký dịch vụ tại TPBank.';
  static const String cifConfirmWait24hNote =
      'Nếu bạn đã thực hiện cập nhật thông tin tại Chi nhánh của TPBank, vui lòng đợi hệ thống đồng bộ thông tin trong vòng 24h.';
  static const String cifConfirmOpenWithRegisteredIdCardNote =
      'Nếu bạn muốn mở thẻ TPBank EVO với số CMND/CCCD đã đăng ký dịch vụ tại TPBank, vui lòng chọn "Mở thẻ".';
  static const String cifConfirmDifOtherInfoNote =
      'Thông tin bạn đang sử dụng hiện chưa khớp đúng với thông tin mà bạn đã đăng ký với TPBank trước đó.';
  static const String cifConfirmForSafetyNote =
      'Để bảo đảm an toàn cho giao dịch của bạn, vui lòng đến Chi nhánh/ Phòng giao dịch gần nhất của TPBank trong vòng 7 ngày để cập nhật thông tin và quay lại EVO app, chọn "Tôi đã cập nhật thông tin" để tiếp tục mở thẻ';

  // CTA
  static const String cifConfirmOpenCardCTA = 'Mở thẻ';
  static const String cifConfirmNoBranchCTA = 'Đã hiểu';
  static const String cifConfirmConfirmInfoUpdatedCTA = 'Tôi đã cập nhật thông tin';
  static const String cifConfirmViewNearestBranchesCTA = 'Xem chi nhánh gần bạn';

  /// Additional Form - Secret Question
  static const String dopNativeSecretQuestionTitle = 'Câu hỏi bảo mật';
  static const String dopNativeSecretQuestionDesc =
      'Bạn cần ghi nhớ câu trả lời cho câu hỏi này để dùng khi bị mất thẻ.';
  static const String dopNativeSecretQuestionLabel = 'Tên trường tiểu học của bạn';
  static const String dopNativeSecretQuestionHint = 'Nhập tên trường';
  static const String dopNativeSecretQuestionEmptyErrorMsg = 'Vui lòng nhập câu trả lời';
  static const String additionalFormTitle = 'Thông tin bổ sung';

  // Additional info - Subscribe channel
  static const String dopNativeInputZaloPhoneNumberFormatFailed =
      'Số điện thoại không đúng định dạng';
  static const String dopNativeInputZaloPhoneNumberEmptyFailed =
      'Vui lòng nhập số điện thoại tài khoản Zalo';
  static const String dopNativeRegisterInfo = 'Đăng ký nhận thông tin';
  static const String dopNativeFavoriteInfoChannel = 'Kênh nhận thông tin yêu thích';
  static const String dopNativeYourZaloAccount = 'Tài khoản Zalo của bạn:';
  static const String dopNativeCurrentPhone = 'Số điện thoại hiện tại';
  static const String dopNativeOtherPhone = 'Số điện thoại khác';
  static const String dopNativeZaloInfoReceiveDesc =
      'Thông tin về kết quả đăng ký và các chương trình khuyến mãi sẽ được gửi qua Zalo cho bạn.';

  // Additional info - Address additional info
  static const String dopNativeCurrentAddressTitle = 'Địa chỉ hiện tại';
  static const String dopNativeStreetOfCurrentAddressInputTextHint = 'Số nhà, tên đường';
  static const String dopNativeCompanyAddressTitle = 'Thông tin nơi làm việc';
  static const String dopNativeCompanyNameInputTextLabel = 'Tên công ty của bạn';
  static const String dopNativeCompanyNameInputTextHint = 'Nhập tên công ty';
  static const String dopNativeCompanyAddressInputTextLabel = 'Địa chỉ khu vực';
  static const String dopNativeCompanyAddressInputTextHint = 'Chọn theo khu vực';
  static const String dopNativeCompanyStreetOfAddressInputTextLabel = 'Địa chỉ cụ thể';
  static const String dopNativeCompanyStreetOfAddressInputTextHint = 'Nhập số nhà, tên đường';
  static const String dopNativeCardDeliveryTypeTitle = 'Địa chỉ nhận thẻ';
  static const String dopNativeCompanyNameEmptyFailed = 'Vui lòng nhập tên cơ quan';
  static const String dopNativeCompanyNameTooLongFailed = 'Tên cơ quan không được quá {0} ký tự';
  static const String dopNativeStreetOfCompanyInputTextHint = 'Nhập số nhà, tên đường';

  /// Additional Form - Emergency Contact
  static const String dopNativeEmergencyContactTitle = 'Thông tin liên hệ khẩn cấp';
  static const String dopNativeEmergencyContactDesc =
      'Cung cấp 2 số điện thoại khác nhau của người thân hoặc bạn bè.';
  static const String dopNativeEmergencyContactNotice = '* Không điền số điện thoại khác của bạn.';
  static const String dopNativeEmergencyContact1Label = 'Số điện thoại người thân 1';
  static const String dopNativeEmergencyContact2Label = 'Số điện thoại người thân 2';
  static const String dopNativeEmergencyContactInvalidErrorMsg =
      'Số điện thoại không đúng định dạng';
  static const String dopNativeEmergencyContactIsIdenticalErrorMsg =
      'Số liên lạc khẩn cấp phải khác nhau';
  static const String dopNativeEmergencyContact1EmptyErrorMsg =
      'Vui lòng nhập số điện thoại người thân 1';
  static const String dopNativeEmergencyContact2EmptyErrorMsg =
      'Vui lòng nhập số điện thoại người thân 2';
  static const String dopNativeEmergencyContactIsUserPhoneErrorMsg =
      'Vui lòng không nhập số điện thoại của chính bạn';

  // EContract, TPB contact & TPB warning widget
  static const String viewEContract = 'Xem hợp đồng điện tử';
  static const String tpbContact = 'Tổng đài TPBank';
  static const String dopNativeTPBWarning =
      'TPBank không liên hệ khách hàng để quảng cáo dịch vụ rút tiền mặt từ thẻ hay yêu cầu cung cấp các thông tin thẻ.';
  static const String dopNativeTPBWarningRejectMoneyWithdrawInvitation =
      'Hãy từ chối mọi lời mời rút tiền mặt từ thẻ tín dụng TPBank EVO.';
  static const String dopNativeTPBWarningProtectCVV =
      'Tuyệt đối không cung cấp thông tin thẻ, mã bảo mật (CVV) cho bất kì ai.';
  static const String dopNativeTPBWarningProtectCVVViewDetail = 'Xem chi tiết';
  static const String dopNativeTPBWarningProtectCVVViewDetailHere = 'Tại đây';

  // Card status
  static const String dopNativeCardActivatedSuccessTitle = 'Đã kích hoạt thẻ tín dụng thành công.';
  static const String dopNativeCardActivatedSuccessDesc =
      'Thanh toán ngay đơn hàng bằng thẻ TPBank EVO để hưởng trọn ưu đãi hấp dẫn dành riêng cho bạn';
  static const String dopNativeActiveCardRetryTitle = 'Kích hoạt thẻ\nkhông thành công';
  static const String dopNativeActiveCardRetryDesc =
      'Thẻ tín dụng TPBank EVO của bạn được kích hoạt không thành công. Vui lòng kích hoạt lại.';
  static const String dopNativeActiveEvoNow = 'Kích hoạt thẻ tín dụng\nTPBank EVO ngay';
  static const String dopNativeActiveCardFailTitle = 'Kích hoạt thẻ\nkhông thành công';
  static const String dopNativeActiveCardFailDesc =
      'Thẻ tín dụng TPBank EVO của bạn được kích hoạt không thành công, vui lòng tải app TPBank để thực hiện kích hoạt lại.';
  static const String dopNativeDownloadTPBankToActive =
      'Tải ứng dụng TPBank\nMobile để kích hoạt thẻ';
  static const String dopNativeCountDownBannerTitle = 'Sẵn sàng để\nkích hoạt thẻ sau:';
  static const String dopNativeCardActivatedRetryPosLimitBannerTitle =
      'Đang cài đặt\nhạn mức giao dịch';
  static const String dopNativeCardActivatedRetryPosLimitTitle = 'Vui lòng chờ để cài đặt\nhạn mức';
  static const String dopNativeCardActivatedRetryPosLimitDesc =
      'Thẻ TPBank EVO của bạn đã được kích hoạt thành công. Hệ thống đang thiết lập hạn mức giao dịch, vui lòng ở lại trang và chờ ít phút.';
  static const String dopNativeCardActivatedPosFailedBannerTitle = 'Cài đặt\nhạn mức giao dịch';
  static const String dopNativeCardActivatedPosFailedTitle = 'Thẻ chưa cài đặt hạn mức\ngiao dịch.';
  static const String dopNativeCardActivatedPosFailedDesc =
      'Thẻ tín dụng TPBank EVO của bạn đã được kích hoạt thành công. Vui lòng tải TPBank Mobile để cài đặt hạn mức giao dịch.';

  // ID Verification QR Code
  static const String dopNativeQRCodeTimeoutDialogTitle =
      'Quá thời gian quét. Vui lòng thực hiện lại';
  static const String dopNativeQRCodeTimeoutDialogContent =
      'Đưa camera vào vị trí mã QR trên nhé. Canh vừa vào 4 góc và thử dịch chuyển camera (xa-gần) để camera điện thoại lấy nét chuẩn hơn';
  static const String dopNativeQRCodeRetryCTA = 'Thực hiện lại';

  // E-Sign review
  static const String dopNativeSignEContract = 'Ký hợp đồng điện tử';
  static const String dopNativeESignUserProclamation =
      'Bằng cách nhấn vào nút Ký hợp đồng, tôi yêu cầu FPT-CA cung cấp dịch vụ chứng thư số và xác nhận:';
  static const String dopNativeESignHasReadFPTCA1 =
      'Đã đọc, hiểu rõ, đồng ý và cam kết tuân thủ các ';
  static const String dopNativeESignHasReadFPTCA2 = 'điều kiện và điều khoản';
  static const String dopNativeESignHasReadFPTCA3 = ' sử dụng dịch vụ Chứng thư số FPT.CA.';
  static const String dopNativeESignAgreeFPTCASign =
      'Đồng ý sử dụng chữ ký số FPT.CA để ký điện tử văn bản, hợp đồng điện tử với TPBank với nội dung như trên.';
  static const String dopNativeESignResponsibility =
      'Chịu trách nhiệm đối với các phát sinh liên quan đến việc sử dụng thẻ tín dụng được cấp theo  điều khoản và điều kiện sử dụng thẻ tín dụng TPBank.';
  static const String dopNativeUserDoNotHaveAmericaElement = 'Khách hàng không có yếu tố Hoa Kỳ';
  static const String dopNativeUserAgreeLinkCardToEVO = 'Tôi đồng ý liên kết thẻ vào EVO app';
  static const String dopNativeFATCATooltip =
      'Thông tin cho mục đích tuân thủ Đạo luật tuân thủ với tài khoản nước ngoài (FATCA)';
  static const String dopNativeLinkCardToolTip =
      'Liên kết thẻ tín dụng TPBank EVO vào EVO app để thanh toán dễ dàng với nhiều ưu đãi hấp dẫn.';
  static const String dopNativeSignESign = 'Ký hợp đồng';
  static const String dopNativeESignNotificationForSMSOTP =
      'Phương thức xác thực mặc định trên ứng dụng eBank của TPBank là SMS OTP. Phí dịch vụ được áp dụng theo biểu phí hiện hành của Ngân hàng. Để thay đổi phương thức xác thực, Quý khách vui lòng thực hiện trên ứng dụng TPBank eBank.';

  // Download eContract
  static const String dopNativeEContractDownloadTitle = 'Xem và tải hợp đồng điện tử';
  static const String dopNativeEContractDownloadCTA = 'Tải hợp đồng điện tử';
  static const String dopNativeEContractBackCTA = 'Quay lại';
  static const String dopNativeEContractDownloadZoomInGuide = 'Nhấn vào đây để xem chi tiết';
  static const String dopNativeEContractDownloadFileName = 'Hop dong mo the';
  static const String dopNativeEContractDownloading = 'Đang tải file, bạn đợi chút nhé';
  static const String dopNativeEContractDownloadFailed = 'Tải file không thành công';
  static const String dopNativeEContractDownloadSuccess = 'Tải file thành công';

  // E-sign - American citizen
  static const String dopNativeAmericanCitizenTitle =
      'Thông tin cho mục đích tuân thủ Đạo luật tuân thủ thuế với tài khoản nước ngoài (FATCA). Vui lòng chọn nội dung thích hợp bên dưới nếu bạn có yếu tố liên quan đến Hoa Kỳ.';
  static const String dopNativeYouAreAmericaCitizen =
      'Bạn là công dân Hoa Kỳ hoặc là thường trú nhân hợp pháp tại Hoa Kỳ (có thẻ xanh)';
  static const String dopNativeBornAmerica = 'Bạn sinh ra tại Hoa Kỳ (Nơi sinh tại Hoa Kỳ)';
  static const String dopNativeInstructDepositToOrWithdrawFromUSAddress =
      'Bạn có cung cấp hướng dẫn chuyển tiền thường xuyên nào tới một tài khoản được duy trì tại Hoa Kỳ hoặc hướng dẫn nhận tiền thường xuyên từ 1 địa chỉ tại Hoa Kỳ';
  static const String dopNativeHaveAmericaAddress =
      'Bạn có địa chỉ Hoa Kỳ (bao gồm cả địa chỉ hộp thư P.O.box) hay số điện thoại Hoa Kỳ';
  static const String dopNativeIsDelegateToUSAddress =
      'Bạn có ủy quyền cho người có địa chỉ tại Hoa Kỳ';
  static const String dopNativeHaveUniqueUSMailAddress =
      'Bạn có địa chỉ "gửi qua" hoặc "giữ thư" là địa chỉ duy nhất của bạn tại Hoa Kỳ';

  // E-Sign - link card popup
  static const String dopNativeLinkCardPopupTitle =
      'Liên kết thẻ tín dụng TPBank EVO vào EVO app để thanh toán dễ dàng với nhiều ưu đãi hấp dẫn.';
  static const String dopNativeLinkCardPopupDesc =
      'Việc chia sẻ hoàn toàn bảo mật, chỉ phục vụ mục đích thanh toán cho thẻ TPBank EVO';
  static const String dopNativeAgreeLinkCard = 'Đồng ý liên kết thẻ';
  static const String dopNativeNotAgreeLinkCard = 'Không đồng ý';

  // E-sign - otp
  static const String dopNativeConfirmSignContractTitle = 'Xác nhận ký hợp đồng điện tử';
  static const String dopNativeConfirmSignContractDesc =
      'Mã OTP đã được gửi đến số điện thoại của bạn để xác nhận đồng ý nội dung Đơn đề nghị phát hành thẻ tín dụng TPBank kiêm Hợp đồng mở thẻ tín dụng, Hợp đồng mở và sử dụng tài khoản thanh toán.';

  // E-success
  static const String dopNativeESuccessTitle = 'Đăng ký thẻ thành công';
  static const String dopNativePcbDescPrefix =
      'Chúc mừng bạn đã được phê duyệt thẻ với hạn mức ban đầu';
  static const String dopNativePcbDescSuffix = '\nHạn mức chính thức sẽ được thông báo qua email.';
  static const String dopNativeCicDescPrefix = 'Hạn mức tín dụng của bạn là';
  static const String dopNativeCicDescSuffix =
      'TPBank sẽ liên hệ để chuyển thẻ vật lý tới bạn trong thời gian sớm nhất.';
  static const String dopNativeESuccessSemiTitle = 'Hồ sơ đang được xử lý';
  static const String dopNativeESuccessSemiDesc =
      'Hồ sơ của bạn sẽ được xử lý ngay khi có kết quả tra cứu thông tin lịch sử tín dụng.\nBạn sẽ nhận được thông báo kết quả trong vòng 1 đến 5 ngày làm việc qua email đăng ký.';
  static const String dopNativeESuccessAutoPCBMWGTitle = 'Đang tra cứu thông tin\nlịch sử tín dụng';
  static const String dopNativeESuccessAutoPCBMWGPleaseWait = 'Bạn vui lòng chờ tối đa';
  static const String dopNativeESuccessAutoPCBMWGStatusAutoUpdate =
      'Trạng thái hồ sơ sẽ được tự động cập nhật sau mỗi';
  static const String dopNativeESuccessMWGAutoPCBWaitingDuration = '30 phút.';
  static const String dopNativeESuccessMWGAutoPCBPollingInterval = '10 giây.';
  static const String dopNativeESuccessAutoPCBMWGTemporarilyCredit = 'Hạn mức tạm tính của bạn là';

  // underwriting Sub flow
  static const String dopNativeCardCICBlockedTitle = 'Rất tiếc! Thẻ tín dụng bị khoá';
  static const String dopNativeUnderWritingInProgressTitle = 'Hồ sơ đang được xử lý';
  static const String dopNativeUnderWritingInProgressDesc =
      'Bạn sẽ nhận được cuộc gọi xác nhận thông tin hoặc TPBank sẽ thông báo kết quả xử lý hồ sơ qua email đã đăng ký trong vòng 24 giờ.';

  // underwriting card issued
  static const String dopNativeCardIssuedActivatedCardTitle =
      'Đã kích hoạt thẻ tín dụng\nthành công.';
  static const String dopNativeCardIssuedActivatedCardDescription =
      'Thanh toán ngay đơn hàng bằng thẻ TPBank EVO để hưởng trọn ưu đãi hấp dẫn dành riêng cho bạn';
  static const String dopNativeCardIssuedTitle = 'Thẻ tín dụng đã được cấp thành công.';
  static const String dopNativeCardIssuedDescription =
      'TPBank sẽ liên hệ để chuyển thẻ vật lý tới bạn trong vòng 5 ngày làm việc.';

  // Pos Limit Dialog
  static const String dopNativeSetupPosLimitTitle = 'Cài đặt hạn mức giao dịch';
  static const String dopNativeSetupPosLimitDescription = 'Hạn mức thanh toán POS (VNĐ/lần)';
  static const String dopNativeSetupPosLimitHintText = 'Hạn mức lần (nhập hạn mức giao dịch lần)';
  static const String dopNativeSetupPosLimitValidInfoInput =
      'Hạn mức giao dịch POS tối đa có thể thiết lập ban đầu là {0} VNĐ';
  static const String dopNativeSetupPosLimitInvalidInfoInput1 =
      'Hạn mức giao dịch được thiết lập không vượt quá {0} VNĐ. Để thiết lập hạn mức giao dịch cao hơn, vui lòng thực hiện trên ứng dụng ';
  static const String dopNativeSetupPosLimitNoteTitle = 'Lưu ý';
  static const String dopNativeSetupPosLimitNoteDescription =
      'Sau khi kích hoạt thẻ, Quý khách vui lòng giữ bảo mật thông tin thẻ để tránh phát sinh các rủi ro. Thẻ cứng sẽ được chuyển tới Quý khách theo đúng quy trình vận hành tại TPBank.';

  // ESign intro
  static const String dopNativeESignIntroTitle = 'Hồ sơ đã hoàn tất';
  static const String dopNativeESignIntroDescription = 'Mời bạn ký hợp đồng điện tử';
  static const String dopNativeESignIntroCreditLimit = 'Hạn mức tín dụng của bạn là';
  static const String dopNativeESignIntroGotoSignESign = 'Tiếp theo mời bạn ký hợp đồng điện tử';

  // pdf viewer
  static String dopNativePdfViewError = 'Vui lòng thử lại để xem\nhợp đồng điện tử';

  // underwriting card status
  static const String dopNativeUnderwritingCardStatusTitle = 'Thẻ tín dụng đã được cấp thành công.';
  static const String dopNativeUnderwritingCardStatusDesc =
      'TPBank sẽ liên hệ để chuyển thẻ vật lý tới bạn trong vòng 5 ngày làm việc.';

  // NFC reader introduction screen
  // retry NFC verification popup
  static const String retryNFCVerificationPopupTitle = 'Chưa thể tải thông tin';
  static const String retryNFCVerificationPopupContent =
      'Hiện tại chúng tôi chưa thể xử lý thông tin. Vui lòng thử lại nhé!';

  // NFC verification with invalid token popup
  static const String nfcVerificationInvalidTokenPopupContent =
      'Vui lòng nhấn vào nút bên dưới để xác thực lại và tiếp tục';
  static const String nfcInvalidTokenRetry = 'Xác thực lại';
  static const String nfcInternetErrorDesc =
      'Vui lòng kiểm tra kết nối mạng của bạn và nhấn nút bên dưới để thử lại';

  // TrustVision
  static const String dopNativeTvNFCInstructionTitle = 'Quét chip của\nCCCD';
  static const String dopNativeTvNFCInstructionDesc =
      'Hệ thống tự động đọc thông tin trong chip để đáp ứng quy định của Ngân hàng nhà nước';
  static const String dopNativeTvNFCInstructionCaption =
      'Áp chip của CCCD vào vị trí được hướng dẫn ở\nmặt sau điện thoại.';
  static const String dopNativeTvNFCStart = 'Bắt đầu xác thực';
  static const String dopNativeTvNFCAware1 = 'Áp chip\nCCCD vào\nđúng vị trí';
  static const String dopNativeTvNFCAware2 = 'Giữ cố định\ntối thiểu\n3 giây';
  static const String dopNativeTvNFCAware3 = 'Nhận thẻ\nTPBank EVO';

  // FPT
  static const String dopNativeFptNFCStart = 'Bắt đầu quét';
  static const String dopNativeFptNFCInstructionTitle = 'Quét chip của CCCD';
  static const String dopNativeFptNFCInstructionDesc =
      'Lật mặt sau của CCCD gắn chip và bắt đầu quét theo hướng dẫn';
  static const String dopNativeFptNFCInstructionGuide1 = 'Áp chip CCCD vào đúng vị trí';
  static const String dopNativeFptNFCInstructionGuide2 = 'Giữ cố định tối thiểu 3 giây';
  static const String dopNativeFptNFCInstructionGuideButtonText = 'Xem video hướng dẫn';

  static const String dopNativeFptNFCInstructionSecondIntroTitle =
      'Video hướng dẫn quét chip của CCCD';
  static const String dopNativeFptNFCInstructionSecondIntroDescTitle = 'Mẹo quét nhanh và dễ dàng';
  static const String dopNativeFptNFCInstructionSecondIntroDescAndroidContent1 =
      'Gỡ ốp lưng hoặc thẻ từ gắn trên điện thoại';
  static const String dopNativeFptNFCInstructionSecondIntroDescAndroidContent2 =
      'Giữ nguyên CCCD khi đang quét';
  static const String dopNativeFptNFCInstructionSecondIntroDescAndroidContent3 =
      'Sử dụng CCCD gắn chip chính chủ';
  static const String dopNativeFptNFCInstructionSecondIntroDescAndroidContent4 =
      'Vệ sinh và lau khô chip trên CCCD';
  static const String dopNativeFptNFCInstructionSecondIntroDescAndroidContent5 =
      'Thử tắt và bật lại NFC nếu chưa quét được';

  static const String dopNativeFptNFCInstructionSecondIntroDescIOSContent1 =
      'Gỡ ốp lưng hoặc thẻ từ gắn trên điện thoại';
  static const String dopNativeFptNFCInstructionSecondIntroDescIOSContent2 =
      'Giữ nguyên CCCD khi đang quét';
  static const String dopNativeFptNFCInstructionSecondIntroDescIOSContent3 =
      'Sử dụng CCCD gắn chip chính chủ';
  static const String dopNativeFptNFCInstructionSecondIntroDescIOSContent4 =
      'Vệ sinh và lau khô chip trên CCCD';

  static String dopNativeFptNFCInstructionSecondIntroRetryVideo = 'Có lỗi xảy ra, nhấn để tải lại';

  static const String dopNativeFptNfcEnableNfcTitle = 'Bật chức năng NFC';
  static const String dopNativeFptNfcEnableNfcContent =
      'Chức năng NFC của điện thoại được sử dụng để đọc Chip NFC của CCCD';
  static const String dopNativeFptNfcEnableNfcButton = 'Bật NFC';

  static const String dopNativeFptNfcErrorTimeoutTitle = 'Phiên làm việc hết hạn';
  static const String dopNativeFptNfcErrorTimeoutContent =
      'Vui lòng nhấn vào nút bên dưới để xác thực lại và tiếp tục';
  static const String dopNativeFptNfcErrorTimeoutButton = 'Xác thực lại';

  static const String dopNativeFptNfcErrorWrongInfoTitle = 'Thông tin CCCD không trùng khớp';
  static const String dopNativeFptNfcErrorWrongInfoContent = 'Vui lòng dùng CCCD chính chủ';
  static const String dopNativeFptNfcErrorWrongInfoButton = 'Xác thực lại';

  static const String dopNativeFptNfcErrorUnknownTitle = 'Có lỗi xảy ra';
  static const String dopNativeFptNfcErrorUnknownContent = 'Vui lòng áp sát CCCD và giữ cố định!';
  static const String dopNativeFptNfcErrorUnknownButton = 'Xác thực lại';

  /// Salesman & Rewards
  static const String storeInfo = 'Thông tin cửa hàng';
  static const String salesmanID = 'Mã nhân viên';
  static const String salesmanIDDesc =
      'Là thông tin của nhân viên bán hàng hỗ trợ bạn đăng ký thẻ tín dụng tại cửa hàng.';
  static const String pleaseEnterSalesmanID = 'Vui lòng nhập mã nhân viên';
  static const String confirmSalesmanID = 'Xác nhận mã nhân viên';
  static const String confirmSalesmanIDDesc =
      'Vui lòng xác nhận mã nhân viên đã hỗ trợ bạn hoàn thành hồ sơ nhé.';
  static const String viettelStoreSalesmanIDErrorMessage =
      'Mã nhân viên phải bắt đầu là VST, theo sau 6 chữ số';
  static const String mwgSalesmanIDErrorMessage = 'Mã nhân viên phải gồm 4 đến 6 chữ số';
  static const String frtSalesmanIDErrorMessage = 'Mã nhân viên phải gồm 1 đến 6 chữ số';

  // Acquisition Reward
  static const String acquisitionRewardTitle = 'Chọn ưu đãi mở thẻ';
  static const String acquisitionRewardDescription =
      'Ưu đãi đã sẵn sàng - Chờ bạn chi tiêu\nHãy chọn 1 trong các ưu đãi bên dưới nhé!';
  static const String acquisitionRewardViewDetailTitle = 'Chi tiết thể lệ chương trình';
  static const String acquisitionRewardViewDetailBottomSheetTitle = 'Thể lệ chương trình';

  // NFC device unsupported screen
  static const String nfcDeviceUnsupportedTitle = 'Thiết bị không hỗ trợ tính năng quét chip CCCD';
  static const String nfcDeviceUnsupportedDesc =
      'Vui lòng sử dụng thiết bị khác quét QR bên dưới để thử lại';
  static const String nfcDeviceUnsupportedRequireDevice = 'Yêu cầu về thiết bị';
  static const String nfcDeviceUnsupportedRequireIOS =
      'iOS: iPhone 8 trở lên và hệ điều hành từ iOS 13 trở lên';
  static const String nfcDeviceUnsupportedRequireAndroid =
      'Android: Điện thoại sản xuất từ năm 2012 trở đi và hệ điều hành từ Android 5.0 trở lên';
  static const String nfcDeviceUnsupportedShareToOtherDevice = 'Chia sẻ tới thiết bị khác';
  static const String nfcDeviceUnsupportedSharedMessage =
      'Tải app EVO theo đường link này {0} để tiếp tục quá trình Quét chip CCCD. Lưu ý hãy sử dụng một thiết bị có hỗ trợ NFC để tiếp tục quá trình.';

  //Switch flow
  static const String dopNativeExistingRecord = 'Bạn đang có 1 hồ sơ mở thẻ chưa hoàn thành tại';
  static const String dopNativeCreateNewRecord = 'Bạn có muốn tạo hồ sơ mới tại';
  static const String dopNativeCompleteOldRecord = 'Hoàn thành hồ sơ cũ';
  static const String dopNativeRegisterNew = 'Đăng ký mới';
  static const String dopNativeAlreadyHaveCard = 'Bạn đã có thẻ';
  static const String dopNativeAlreadyHaveCardDesc =
      'Bạn đã là khách hàng của TPBank EVO, tiếp tục sử dụng thẻ để nhận thêm ưu đãi';
  static const String dopNativeUnderstand = 'Đã hiểu';

  // Collect location
  static const String dopNativeCollectLocationTitle = 'Cho phép truy cập địa chỉ';
  static const String dopNativeCollectLocationDescription =
      'Chúng tôi cần quyền truy cập vào địa chỉ của bạn để cung cấp các dịch vụ phù hợp';
  static const String dopNativeCollectLocationAllowButton = 'Cho phép';
  static const String dopNativeCollectLocationDenyButton = 'Từ chối';

  // Fourth appraising
  static const String dopNativeFourthAppraisingTitle = 'Đang tra cứu thông tin\nlịch sử tín dụng';
  static const String dopNativeFourthAppraisingDesc = 'Bạn vui lòng chờ trong giây lát...';

  // Sophia call
  static const String dopNativeAdditionalInfoCall = 'Cuộc gọi bổ sung thông tin';
  static const String dopNativeWeWillCallYouAfterMinutes =
      'Chúng tôi có thể liên hệ bạn sau\\\nít phút nữa';
  static const String dopNativeAdditionalInfoCallContent =
      'Cuộc gọi diễn ra từ 3-5 phút\nVui lòng nghe máy để nhận kết quả mở thẻ nhanh chóng';
  static const String dopNativeAdditionalInfoCallButtonNote =
      'Trong lúc chờ cuộc gọi, bấm tiếp tục để hoàn thành các bước tiếp theo';
}
