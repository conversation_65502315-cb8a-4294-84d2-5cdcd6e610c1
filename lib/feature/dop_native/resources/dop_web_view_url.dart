import 'package:flutter_common_package/flavors/flavor_config.dart';

import '../../../flavors/flavors_type.dart';

class DOPWebViewUrl {
  /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/**********/CO-9705+TPBank+EVO+Web+DOE+Dive+In+Implement+the+solution+to+allow+user+to+switch+between+different+UI+flows#:~:text=Define%20DEEPLINK/%20WEBLINK%20template
  static const String dopWebViewStagUrl = 'https://staging-tpbank.tsengineering.io/';
  static const String dopWebViewUATUrl = 'https://release-tpbank.tsengineering.io/';
  static const String dopWebViewProdUrl = 'https://evocard.tpb.vn/';
}

String getDopWebViewUrlByFlavor(String? token) {
  if (FlavorConfig.instance.flavor == FlavorType.prod.name) {
    return '${DOPWebViewUrl.dopWebViewProdUrl}$token?enable_webview=true';
  }

  /// return UAT URL because EVO Staging environment point to DOP UAT environment
  /// Can be change on feature
  return '${DOPWebViewUrl.dopWebViewUATUrl}$token?enable_webview=true';
}
