import 'dart:async';

import 'dop_native_submit_status_polling.dart';

class DOPNativeSubmitStatusPollingImpl extends DOPNativeSubmitStatusPolling {
  final Duration intervalDuration;

  Timer? _timerIntervalPolling;

  DOPNativeSubmitStatusPollingImpl({
    required this.intervalDuration,
  });

  @override
  void delayToPolling({required void Function() onDoPolling}) {
    _timerIntervalPolling = Timer(
      intervalDuration,
      () {
        onDoPolling();
      },
    );
  }

  @override
  void cancel() {
    _timerIntervalPolling?.cancel();
  }
}
