import 'package:flutter_nfc_reader/flutter_nfc_reader.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/result/nfc_availability_type.dart';

NfcAvailabilityWrapper nfcAvailabilityWrapper = getIt.get<NfcAvailabilityWrapper>();

class NfcAvailabilityWrapper {
  Future<NfcAvailabilityType> checkNFCSupport() async {
    final NFCAvailability nfcCompatibility = await FlutterNfcReader.checkNFCAvailability();

    return switch (nfcCompatibility) {
      //hardware does not support NFC
      NFCAvailability.not_supported => NfcAvailabilityType.unsupported,
      //when NFC is disabled (Android only)
      NFCAvailability.disabled => NfcAvailabilityType.disabled,
      //supported and enabled,
      NFCAvailability.available => NfcAvailabilityType.available,
    };
  }
}
