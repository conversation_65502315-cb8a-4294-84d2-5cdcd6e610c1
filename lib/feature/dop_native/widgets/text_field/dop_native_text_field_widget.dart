import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../resources/resources.dart';
import '../../dialogs/metadata_popup/cubit/debouncer.dart';
import '../../resources/dop_native_images.dart';
import '../../resources/dop_native_resources.dart';

class DOPNativeTextField extends StatefulWidget {
  final void Function(String)? onSubmitted;
  final void Function(String)? onChanged;
  final String? errorText;

  final TextInputType keyboardType;
  final String hintText;
  final TextEditingController? controller;
  final Widget? suffixIcon;
  final bool? isEnabled;
  final Color? backgroundColor;
  final bool hasBorder;
  final FocusNode? focusNode;
  final Color? cursorColor;
  final TextStyle? textStyle;
  final List<TextInputFormatter>? inputFormatters;
  final String? label;
  final int? hintMaxLines;
  final Color? focusedBorderColor;
  final Widget? prefixWidget;

  const DOPNativeTextField({
    required this.hintText,
    this.onSubmitted,
    this.onChanged,
    this.errorText,
    this.suffixIcon,
    this.keyboardType = TextInputType.text,
    this.controller,
    this.isEnabled,
    this.backgroundColor,
    this.hasBorder = true,
    this.focusNode,
    this.cursorColor,
    this.textStyle,
    this.inputFormatters,
    this.label,
    this.hintMaxLines,
    this.focusedBorderColor,
    this.prefixWidget,
    super.key,
  });

  @override
  State<DOPNativeTextField> createState() => _DOPNativeTextFieldState();
}

class _DOPNativeTextFieldState extends State<DOPNativeTextField> {
  late FocusNode _focusNode;
  final BorderRadius _borderRadius = BorderRadius.circular(8);
  late final TextEditingController _textEditingController;
  final Debouncer _debouncer = Debouncer(duration: Duration.zero);

  bool _isFocused = false;
  String _errorMsg = '';

  bool get _hasError => _errorMsg.isNotEmpty == true;

  @override
  void initState() {
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_listenFocusTextField);
    _textEditingController = widget.controller ?? TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    _focusNode.removeListener(_listenFocusTextField);
    _focusNode.dispose();
    super.dispose();
  }

  void _listenFocusTextField() {
    setState(() {
      if (!mounted) {
        return;
      }

      _isFocused = _focusNode.hasFocus;
    });
  }

  Color _getBorderColor() {
    if (_hasError) {
      return dopNativeColors.error;
    }

    return _isFocused
        ? (widget.focusedBorderColor ?? dopNativeColors.focusedTextFieldBorder)
        : dopNativeColors.textFieldBorder;
  }

  @override
  Widget build(BuildContext context) {
    _errorMsg = widget.errorText ?? '';

    return Column(
      children: <Widget>[
        _buildLabelWidget(),
        widget.hasBorder
            ? Container(
                decoration: BoxDecoration(
                  color: widget.backgroundColor ?? dopNativeColors.background,
                  borderRadius: _borderRadius,
                  border: Border.all(
                    color: _getBorderColor(),
                  ),
                ),
                child: _contentTextFieldWidget(),
              )
            : _contentTextFieldWidget(),
        _buildErrorTextWidget(),
      ],
    );
  }

  Widget _suffixIconTextField() {
    final Widget? suffix = widget.suffixIcon;
    if (suffix == null) {
      return const SizedBox.shrink();
    }

    return suffix;
  }

  Widget _prefixTextField() {
    final Widget? prefix = widget.prefixWidget;
    if (prefix == null) {
      return const SizedBox.shrink();
    }

    return prefix;
  }

  double _leftPadding() => widget.prefixWidget == null ? 16 : 0;

  double _rightPadding() => widget.suffixIcon == null ? 16 : 0;

  Widget _contentTextFieldWidget() {
    return Row(
      children: <Widget>[
        _prefixTextField(),
        Expanded(
          child: Padding(
            padding: EdgeInsets.fromLTRB(_leftPadding(), 12, _rightPadding(), 12),
            child: TextField(
              cursorColor: widget.cursorColor,
              enabled: widget.isEnabled,
              focusNode: _focusNode,
              controller: _textEditingController,
              keyboardType: widget.keyboardType,
              inputFormatters: widget.inputFormatters,
              style: widget.textStyle ?? dopNativeTextStyles.bodyLarge(dopNativeColors.textActive),
              decoration: InputDecoration(
                hintMaxLines: widget.hintMaxLines,
                isDense: true,
                contentPadding: EdgeInsets.zero,
                hintText: widget.hintText,
                hintStyle: dopNativeTextStyles.bodyLarge(
                  dopNativeColors.textActive.withOpacity(0.2),
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
              ),
              onChanged: (String value) {
                /// If user enables auto correct on keyboard (iOS device),
                /// onChanged event will be called multiple times when user types in the text field.
                /// I tried to set autocorrect = false, but it doesn't work.
                /// Refer to issues:
                /// - https://github.com/flutter/flutter/issues/82887
                /// - https://github.com/flutter/flutter/issues/50163
                _debouncer.run(() {
                  widget.onChanged?.call(value);
                });
              },
              onSubmitted: widget.onSubmitted,
            ),
          ),
        ),
        _suffixIconTextField(),
      ],
    );
  }

  Widget _buildErrorTextWidget() {
    if (!_hasError) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          evoImageProvider.asset(
            DOPNativeImages.icErrorTextField,
            height: 16,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              _errorMsg,
              style: dopNativeTextStyles.bodyMedium(
                dopNativeColors.error,
              ),
              textAlign: TextAlign.start,
              overflow: TextOverflow.ellipsis,
              // Refer: https://trustingsocial1.atlassian.net/browse/EMA-2706
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLabelWidget() {
    return widget.label != null
        ? Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: const EdgeInsets.only(bottom: 4.0),
              child: Text(
                widget.label!,
                style: dopNativeTextStyles.bodySmall(
                  color: dopNativeColors.textPassive,
                ),
              ),
            ),
          )
        : const SizedBox.shrink();
  }
}
