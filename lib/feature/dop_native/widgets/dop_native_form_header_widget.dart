import 'package:flutter/material.dart';

import '../resources/dop_native_resources.dart';
import 'dop_native_title_widget.dart';
import 'dop_title_with_step_indicator_widget.dart';

class DOPNativeFormHeaderWidget extends StatelessWidget {
  const DOPNativeFormHeaderWidget({
    required this.currentStep,
    required this.titleStep,
    this.totalStep = 3,
    this.titleStyle,
    super.key,
  });

  final int currentStep;
  final String titleStep;
  final int totalStep;
  final TextStyle? titleStyle;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        const DOPNativeTitleWidget(),
        _buildTitleStepIndicator(),
      ],
    );
  }

  Widget _buildTitleStepIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: DOPTitleWithStepIndicator(
        progress: currentStep / totalStep,
        title: Text(
          titleStep,
          style: titleStyle ?? dopNativeTextStyles.h300(),
        ),
      ),
    );
  }
}
