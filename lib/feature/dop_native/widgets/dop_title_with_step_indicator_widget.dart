import 'package:flutter/material.dart';

import '../../../util/ui_utils/evo_ui_utils.dart';
import '../resources/dop_native_resources.dart';

class DOPTitleWithStepIndicator extends StatelessWidget {
  const DOPTitleWithStepIndicator({required this.title, this.progress = 0, super.key});

  final Widget title;
  final double progress;

  final double progressIndicatorHeight = 4.0;
  final double progressIndicatorWidthPercentage = 100 / 375;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        title,
        SizedBox.fromSize(
          size: Size(
              EvoUiUtils().calculateHorizontalSpace(
                  context: context, widthPercentage: progressIndicatorWidthPercentage),
              progressIndicatorHeight),
          child: LinearProgressIndicator(
            backgroundColor: dopNativeColors.dopNativeProgressIndicatorBg,
            color: dopNativeColors.textPrimary,
            value: progress,
            minHeight: progressIndicatorHeight,
            borderRadius: BorderRadius.circular(progressIndicatorHeight),
          ),
        ),
      ],
    );
  }
}
