part of 'dop_native_e_success_cubit.dart';

abstract class DOPNativeESuccessState implements BlocState {}

class DOPNativeESuccessInitial implements DOPNativeESuccessState {}

class GetScreenDataLoading implements DOPNativeESuccessState {}

class GetScreenDataSuccess implements DOPNativeESuccessState {
  final DOPNativeCardStatusEntity? cardStatus;
  final DOPNativeCreditAssignmentEntity? creditAssignment;

  GetScreenDataSuccess({
    required this.cardStatus,
    this.creditAssignment,
  });
}

class GetScreenDataFailure implements DOPNativeESuccessState {
  final ErrorUIModel error;

  GetScreenDataFailure(this.error);
}
