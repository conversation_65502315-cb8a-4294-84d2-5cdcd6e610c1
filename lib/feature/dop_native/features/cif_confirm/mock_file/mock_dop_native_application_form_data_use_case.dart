enum MockDOPNativeApplicationFormData {
  successWithUseNewCif('dop_native_application_form_data_with_use_new_cif.json'),
  successWithoutUseNewCif('dop_native_application_form_data_without_use_new_cif.json'),
  error('dop_native_application_form_data_error.json'),
  getPaymentTypeSuccess('dop_native_get_payment_type_success.json'),
  getApplicationFormDataSuccess('dop_native_get_application_form_data_success.json'),
  getApplicationFormDataNFCInvalidData(
      'dop_native_get_application_form_data_nfc_invalid_data.json');

  final String value;

  const MockDOPNativeApplicationFormData(this.value);
}

String getMockDOPNativeApplicationFormData(MockDOPNativeApplicationFormData mockCase) {
  return mockCase.value;
}
