import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';

import '../../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import '../../../../../../data/request/dop_native/dop_native_id_card_image_type.dart';
import '../../../../../../data/response/dop_native/dop_native_check_id_card_entity.dart';
import '../../../../../../data/response/dop_native/dop_native_ekyc_status_entity.dart';
import '../../../../../../data/response/dop_native/dop_native_upload_id_card_entity.dart';
import '../../../../util/dop_native_submit_status_polling/dop_native_submit_status_polling.dart';
import '../../mock/mock_dop_native_check_id_card.dart';
import '../../mock/mock_dop_native_ekyc_use_case.dart';
import '../../mock/mock_dop_native_get_submit_status_use_case.dart';
import '../../ui_model/ekyc_error_ui_model.dart';
import '../../utils/dop_native_ekyc_api_response_handler/dop_native_ekyc_api_response_handler.dart';

part 'dop_native_id_card_qr_code_verification_state.dart';

class DOPNativeIdCardQRCodeVerificationCubit
    extends CommonCubit<DOPNativeIdCardQRCodeVerificationState> {
  final DopNativeEkycUIOnlyRepo dopNativeEkycUiOnlyRepo;
  final DOPNativeSubmitStatusPolling dopNativeSubmitStatusPolling;
  final EkycCommonApiResponsesHandler commonApisResponseHandler;

  DOPNativeIdCardQRCodeVerificationCubit({
    required this.dopNativeEkycUiOnlyRepo,
    required this.dopNativeSubmitStatusPolling,
    required this.commonApisResponseHandler,
  }) : super(QRCodeVerificationInitial());

  Future<void> uploadQrCodeIdCard({
    required String cardQrCodeBase64Image,
  }) async {
    emit(QRCodeVerificationProcessing());

    final DOPNativeUploadIdCardEntity entity = await dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
      cardQrCodeBase64Image: cardQrCodeBase64Image,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeEkycFileNameByCase(
          MockTestDOPNativeEkycUseCase.uploadEkycIDCardImageFail,
        ),
      ),
    );

    handleAfterUploadImage(entity);
  }

  @visibleForTesting
  void handleAfterUploadImage(DOPNativeUploadIdCardEntity entity) {
    final EkycErrorUIModel? ekycErrorUIModel =
        commonApisResponseHandler.getUploadImageError(entity);

    if (ekycErrorUIModel == null) {
      pollingSubmitStatus(entity.jobId);
      return;
    }

    emit(QRCodeUploadFailed(error: ekycErrorUIModel));
  }

  /// Because uploading the QR code image is not required,
  /// Therefore when the API has an error, continue to allow the user to upload the back side of the ID-Card.
  @visibleForTesting
  Future<void> pollingSubmitStatus(String? jobId) async {
    final DOPNativeEkycStatusEntity entity = await dopNativeEkycUiOnlyRepo.getSubmitStatus(
      jobId: jobId,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeGetSubmitStatusFileNameByCase(
          MockDOPNativeGetSubmitStatusUseCase.success,
        ),
      ),
    );

    final EkycErrorUIModel? ekycErrorUIModel = commonApisResponseHandler.getStatusError(entity);

    if (ekycErrorUIModel == null) {
      handlePollingSubmitStatusSuccess(entity, jobId);
      return;
    }

    emit(QRCodeVerificationFailed(error: ekycErrorUIModel));
  }

  @visibleForTesting
  void handlePollingSubmitStatusSuccess(
    DOPNativeEkycStatusEntity entity,
    String? jobId,
  ) {
    final String? submitStatus = entity.status;
    switch (submitStatus) {
      case DOPNativeEkycStatusEntity.statusSuccess:
        commonApisResponseHandler.saveAccessTokenIfNeeded(entity);
        emit(QRCodeVerificationSucceed());
        break;
      case DOPNativeEkycStatusEntity.statusFailure:
        emit(QRCodeVerificationFailed(
          error: EkycErrorUIModel.fromHttpCode(
            statusCode: entity.statusCode,
            message: entity.userMessage,
          ),
        ));
        break;

      /// Continue polling
      case DOPNativeEkycStatusEntity.statusInProgress:
      case DOPNativeEkycStatusEntity.statusPending:
      default:
        dopNativeSubmitStatusPolling.delayToPolling(onDoPolling: () {
          pollingSubmitStatus(jobId);
        });
        break;
    }
  }

  void cancelPollingSubmitStatus() {
    dopNativeSubmitStatusPolling.cancel();
  }

  Future<void> checkFrontIdCard({
    String? cardType,
  }) async {
    emit(QRCodeVerificationProcessing());
    final DOPNativeCheckIDCardEntity entity = await dopNativeEkycUiOnlyRepo.checkIdCard(
      imageType: DOPNativeIDCardImageType.front,
      cardType: cardType,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeCheckIDCardFileName(
          MockDOPNativeCheckIDCardUseCase.success,
        ),
      ),
    );

    final EkycErrorUIModel? ekycErrorUIModel =
        commonApisResponseHandler.getCheckIdCardError(entity);

    if (ekycErrorUIModel == null) {
      pollingSubmitStatus(entity.jobId);
      return;
    }

    emit(QRCodeVerificationFailed(error: ekycErrorUIModel));
  }
}
