/// Refer to: https://docs.google.com/document/d/1kcxnBpIRBeUsdi4Tcl4j70bmMJZggW5G/edit
/// Device does not support NFC
const String deviceNotSupportNFCAndroid = 'ERROR_CODE_UN_SUPPORT_NFC';
const String deviceNotSupportNFCIos = 'NFCNotSupported';

/// Device supports NFC but cannot connect to the NFC feature
const String cannotOpenNFCAndroid = 'CANNOT_OPEN_DEVICE';

/// The OS is not supported
const String errorCodeUnSupportAPIVersionAndroid = 'ERROR_CODE_UN_SUPPORT_API_VERSION';

/// Connect to NFC timeout
const String timeoutAndroid = 'ERROR_CODE_TIME_OUT';
const String timeoutIos = 'Timeout';

/// Id card number/Date of birth/Date of expiry information does not match the input parameter
const String wrongInfoAndroid = 'WRONG_CITIZEN_ID_CARD';
const String wrongInfoIos = 'InvalidMRZKey';

/// The Id card is lost connection or disconnected (system error)
const String connectionErrorAndroid = 'CARD_LOST_CONNECTION';
const String connectionErrorIos = 'ConnectionError';

/// The user cancels the scan
const String userCanceledAndroid = 'ERROR_CODE_USER_CANCELED';
const String userCanceledIos = 'UserCanceled';

/// Unknown error
const String unknownErrorAndroid = 'UNKNOWN';
const String unknownErrorIos = 'ResponseError';

/// The error when the user does not interrupt device for 2s between 2 NFC reading sessions
const String notInterrupt2sIos = 'UnexpectedError';

enum NFCError {
  /// grouped error from SDK, check [fromValue] function
  deviceNotSupportNFC,
  cannotOpenNFC,
  osNotSupportNFC,
  timeout,
  wrongInfo,
  connectionError,
  userCanceled,
  unknownError,
  notInterrupt2s,

  /// customized error for EVO App to cover other exception cases
  nullResult, // SDK return null result
  sdkException; // catch exception when call SDK (PlatformException or other exception)

  static NFCError? fromValue(String? value) {
    if (value == null || value.isEmpty) {
      return null;
    }

    switch (value) {
      case deviceNotSupportNFCAndroid:
      case deviceNotSupportNFCIos:
        return NFCError.deviceNotSupportNFC;

      case cannotOpenNFCAndroid:
        return NFCError.cannotOpenNFC;

      case errorCodeUnSupportAPIVersionAndroid:
        return NFCError.osNotSupportNFC;

      case timeoutAndroid:
      case timeoutIos:
        return NFCError.timeout;

      case wrongInfoAndroid:
      case wrongInfoIos:
        return NFCError.wrongInfo;

      case connectionErrorAndroid:
      case connectionErrorIos:
        return NFCError.connectionError;

      case userCanceledAndroid:
      case userCanceledIos:
        return NFCError.userCanceled;

      case notInterrupt2sIos:
        return NFCError.notInterrupt2s;

      case unknownErrorAndroid:
      case unknownErrorIos:
        return NFCError.unknownError;
      default:
        return NFCError.unknownError;
    }
  }

  String get errorCode {
    return switch (this) {
      NFCError.deviceNotSupportNFC => 'device_not_support_nfc',
      NFCError.cannotOpenNFC => 'cannot_open_nfc',
      NFCError.osNotSupportNFC => 'os_not_support_nfc',
      NFCError.timeout => 'timeout',
      NFCError.wrongInfo => 'wrong_info',
      NFCError.connectionError => 'connection_error',
      NFCError.userCanceled => 'user_canceled',
      NFCError.unknownError => 'unknown_error',
      NFCError.notInterrupt2s => 'not_interrupt_2s',
      NFCError.nullResult => 'null_result',
      NFCError.sdkException => 'sdk_exception',
    };
  }
}
