import 'nfc_reader/result/nfc_availability_type.dart';
import 'nfc_reader/result/nfc_detection_result.dart';

abstract class FptSdkBridge {
  /// Refer: Document https://docs.google.com/document/d/1kcxnBpIRBeUsdi4Tcl4j70bmMJZggW5G/edit
  Future<NFCDetectionResult> readNfc({
    required String idCardNumber,
    String? dateOfBirth,
    String? dateOfExpiry,
  });

  // Refer: BUG https://trustingsocial1.atlassian.net/browse/EMA-4106
  // Ticket https://trustingsocial1.atlassian.net/browse/EMA-4176
  Future<NfcAvailabilityType> checkNfcSupport();
}
