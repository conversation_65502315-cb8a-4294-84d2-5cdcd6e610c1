// ignore_for_file: constant_identifier_names
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:trust_vision_plugin/enums.dart';

import '../../../../../../prepare_for_app_initiation.dart';

enum LivenessModeWrapper {
  //  NONE, PASSIVE, ACTIVE, FLASH, FLASH_EDGE, FLASH_ADVANCED, FLASH_8, FLASH_16, FLASH_32;
  active('active'),
  passive('passive'),
  none('none'),
  flash('flash'),
  flash_edge('flash_edge'),
  flash_advanced('flash_advanced'),
  flash_8('flash_8'),
  flash_16('flash_16'),
  flash_32('flash_32');

  final String value;

  const LivenessModeWrapper(this.value);

  /// Determined by Story 5: User captures selfie image
  /// https://docs.google.com/document/d/1VSiomTbrqcbgNzH-hxBrOQ6L-BYiK8V5t_gOMZFUKQ4/edit#heading=h.hfk447e6pixu
  static LivenessModeWrapper getFlashLivenessModeWrapperByPlatform() {
    final DevicePlatform devicePlatform = getIt.get<DevicePlatform>();

    if (devicePlatform.isAndroid()) {
      return LivenessModeWrapper.flash_32;
    } else if (devicePlatform.isIOS()) {
      return LivenessModeWrapper.flash_16;
    } else {
      return LivenessModeWrapper.flash_16;
    }
  }

  static LivenessModeWrapper getActiveLivenessModeWrapper() => LivenessModeWrapper.active;

  LivenessMode toTvSdkLivenessMode() {
    switch (this) {
      case LivenessModeWrapper.active:
        return LivenessMode.active;
      case LivenessModeWrapper.passive:
        return LivenessMode.passive;
      case LivenessModeWrapper.none:
        return LivenessMode.none;
      case LivenessModeWrapper.flash:
        return LivenessMode.flash;
      case LivenessModeWrapper.flash_edge:
        return LivenessMode.flash_edge;
      case LivenessModeWrapper.flash_advanced:
        return LivenessMode.flash_advanced;
      case LivenessModeWrapper.flash_8:
        return LivenessMode.flash_8;
      case LivenessModeWrapper.flash_16:
        return LivenessMode.flash_16;
      case LivenessModeWrapper.flash_32:
        return LivenessMode.flash_32;
    }
  }
}
