import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import '../../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../../prepare_for_app_initiation.dart';
import '../../../../../../resources/resources.dart';
import '../../../../../../util/app_setting_util.dart';
import '../../../../../../util/evo_flutter_wrapper.dart';
import '../../../../../../widget/evo_appbar_leading_button.dart';
import '../../../../../logging/evo_event_tracking_utils/evo_event_tracking_utils_impl.dart';
import '../../../../base/cubit/dop_native_application_state_cubit.dart';
import '../../../../base/dop_native_page_state_base.dart';
import '../../../../dop_native_constants.dart';
import '../../../../models/dop_native_dialog_id.dart';
import '../../../../resources/dop_native_resources.dart';
import '../../../../resources/dop_native_ui_strings.dart';
import '../../../../util/dop_functions.dart';
import '../../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../../widgets/dop_native_title_widget.dart';
import '../../../../widgets/video_player/dop_native_video_player_widget.dart';
import '../../../logging/dop_native_event_tracking_screen_id.dart';
import '../../../logging/metadata_define/dop_native_event_metadata.dart';
import '../../../logging/screen_action_define/dop_native_fpt_nfc_reader_introduction_screen_action_event.dart';
import '../../sdk_bridge/fpt/fpt_sdk_bridge.dart';
import '../../sdk_bridge/fpt/nfc_reader/result/nfc_error.dart';
import '../constants/dop_native_nfc_entry_point.dart';
import '../unsupported/dop_native_nfc_device_unsupported_screen.dart';
import 'cubit/dop_native_fpt_nfc_reader_cubit.dart';
import 'models/dop_native_fpt_nfc_reader_introduction_step.dart';
import 'widgets/dop_native_fpt_nfc_reader_animation_introduction_widget.dart';
import 'widgets/dop_native_fpt_nfc_reader_video_introduction_widget.dart';

class DOPNativeFptNFCReaderIntroductionController {
  Function? onFinished;

  DOPNativeFptNFCReaderIntroductionController({this.onFinished});
}

class DOPNativeFptNFCReaderIntroductionScreenArg extends PageBaseArg {
  final DOPNativeFptNFCReaderIntroductionController controller;
  final NFCEntryPoint entryPoint;

  DOPNativeFptNFCReaderIntroductionScreenArg({
    required this.controller,
    this.entryPoint = NFCEntryPoint.dopNative,
  });
}

class DOPNativeFptNFCReaderIntroductionScreen extends PageBase {
  final DOPNativeFptNFCReaderIntroductionScreenArg arg;

  final DOPNativeVideoPlayerWidgetController? playerController;
  final DOPNativeFptNFCReaderCubit? nfcReaderCubit;
  final DOPNativeApplicationStateCubit? applicationStateCubit;

  const DOPNativeFptNFCReaderIntroductionScreen({
    required this.arg,
    super.key,
    this.playerController,
    this.nfcReaderCubit,
    this.applicationStateCubit,
  });

  static void pushNamed(DOPNativeFptNFCReaderIntroductionScreenArg arg) {
    return navigatorContext?.pushNamed(
      Screen.dopNativeNFCReaderIntroductionScreen.name,
      extra: arg,
    );
  }

  @override
  DOPNativePageStateBase<DOPNativeFptNFCReaderIntroductionScreen> createState() =>
      DOPNativeFptNFCReaderIntroductionScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId =>
      DOPNativeEventTrackingScreenId.dopNativeNfcIntroductionScreen;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeNFCReaderIntroductionScreen.routeName);
}

@visibleForTesting
class DOPNativeFptNFCReaderIntroductionScreenState
    extends DOPNativePageStateBase<DOPNativeFptNFCReaderIntroductionScreen> with AppSettingUtil {
  @visibleForTesting
  late final DOPNativeVideoPlayerWidgetController playerController =
      widget.playerController ?? DOPNativeVideoPlayerWidgetController();

  @visibleForTesting
  late final DOPNativeFptNFCReaderCubit nfcReaderCubit = widget.nfcReaderCubit ??
      DOPNativeFptNFCReaderCubit(
        dopNativeEkycUIOnlyRepo: getIt.get<DopNativeEkycUIOnlyRepo>(),
        dopNativeRepo: getIt.get<DOPNativeRepo>(),
        appState: getIt.get<AppState>(),
        fptSdkBridge: getIt.get<FptSdkBridge>(),
      );

  @override
  DOPNativeApplicationStateCubit get dopNativeApplicationStateCubit =>
      widget.applicationStateCubit ?? super.dopNativeApplicationStateCubit;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      nfcReaderCubit.initial(widget.arg.entryPoint);
    });
    super.initState();
  }

  @override
  void didPushNext() {
    playerController.pause?.call();
    super.didPushNext();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    const SizedBox spacing18 = SizedBox(height: 18);
    final bool isDopWebEntryPoint = widget.arg.entryPoint == NFCEntryPoint.dopWebView;
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: DOPNativeAppBar(
        title: widget.arg.entryPoint.appBarTitle,
        enableCloseButton: isDopWebEntryPoint,
        enableLeading: isDopWebEntryPoint,
        leading: isDopWebEntryPoint ? EvoAppBarLeadingButton(onPressed: handleBackDopWeb) : null,
        onExitDOP: handleExitDopWeb,
      ),
      body: SafeArea(
        child: BlocProvider<DOPNativeFptNFCReaderCubit>(
          create: (BuildContext context) => nfcReaderCubit,
          child: BlocListener<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
            listener: (_, DOPNativeFptNFCReaderState state) {
              handleStateChanged(state);
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  DOPNativeTitleWidget(
                    onLogEventAliceChatButton: () {
                      onLogEventAliceChatButton();
                    },
                  ),
                  buildIntroductionContent(),
                  spacing18,
                  buildCTAButton(),
                  buildOpenVideoIntroductionButton(),
                  spacing18,
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @visibleForTesting
  Widget buildCTAButton() {
    return BlocBuilder<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      builder: (_, DOPNativeFptNFCReaderState state) {
        return CommonButton(
          onPressed: state is DOPNativeFptNFCReaderLoadingState
              ? null
              : () {
                  logEventClickCTAButton();
                  prepareBeforeStartNFCReader();
                },
          style: dopNativeButtonStyles.primary(ButtonSize.medium),
          child: const Text(DOPNativeStrings.dopNativeFptNFCStart),
        );
      },
    );
  }

  @visibleForTesting
  Widget buildOpenVideoIntroductionButton() {
    return BlocBuilder<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      buildWhen: (_, DOPNativeFptNFCReaderState current) {
        return current is DOPNativeFptNFCReaderStepChangedState;
      },
      builder: (BuildContext context, DOPNativeFptNFCReaderState state) {
        DopNativeFptNfcReaderIntroductionStep step =
            DopNativeFptNfcReaderIntroductionStep.animation;
        if (state is DOPNativeFptNFCReaderStepChangedState) {
          step = state.step;
        }
        return switch (step) {
          DopNativeFptNfcReaderIntroductionStep.animation => CommonButton(
              onPressed: () {
                logEventClickWatchIntroductionVideoButton();
                nfcReaderCubit.changeStep(DopNativeFptNfcReaderIntroductionStep.video);
                playerController.play?.call();
              },
              style: dopNativeButtonStyles.tertiary(ButtonSize.medium),
              child: const Text(DOPNativeStrings.dopNativeFptNFCInstructionGuideButtonText),
            ),
          // hide button when showing video intro
          DopNativeFptNfcReaderIntroductionStep.video => const SizedBox.shrink(),
        };
      },
    );
  }

  @visibleForTesting
  Widget buildIntroductionContent() {
    return BlocBuilder<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      buildWhen: (
        DOPNativeFptNFCReaderState previous,
        DOPNativeFptNFCReaderState current,
      ) {
        return current is DOPNativeFptNFCReaderStepChangedState ||
            current is DOPNativeFptNFCReaderInitialState;
      },
      builder: (BuildContext context, DOPNativeFptNFCReaderState state) {
        DopNativeFptNfcReaderIntroductionStep step =
            DopNativeFptNfcReaderIntroductionStep.animation;
        if (state is DOPNativeFptNFCReaderStepChangedState) {
          step = state.step;
        }
        return Expanded(
          child: IndexedStack(
            index: step.index,
            children: <Widget>[
              const DOPNativeFptNfcReaderAnimationIntroduction(),
              DOPNativeFptNfcReaderVideoIntroduction(controller: playerController),
            ],
          ),
        );
      },
    );
  }

  @visibleForTesting
  void handleStateChanged(DOPNativeFptNFCReaderState state) {
    if (state is DOPNativeFptNFCReaderLoadingState) {
      showDOPLoading();
      return;
    }

    hideDOPLoading();

    if (state is DOPNativeFptNFCReaderInitialDataInvalidState) {
      // navigate to default error screen
      handleDopEvoApiError(state.errorUIModel);
      return;
    }

    if (state is DOPNativeFptNFCReaderInitialRetrieveFormDataFailed) {
      handleDopEvoApiError(state.errorUIModel);
      return;
    }

    if (state is DOPNativeFptNFCReaderInitialSuccess) {
      logScreenLoadedEvent();
      return;
    }

    if (state is DOPNativeFptNFCReaderUnsupportedState) {
      gotoUnsupportedNFCScreen();
      return;
    }

    if (state is DOPNativeFptNFCReaderDisabledState) {
      showEnableNfcPopup();
      return;
    }

    if (state is DOPNativeFptNFCReaderReadNFCErrorState) {
      handleReadNFCErrorState(state);
      return;
    }

    if (state is DOPNativeFptNFCReaderSubmitNFCDataFailedState) {
      handleSubmitNfcError(state.error.statusCode);
      return;
    }

    if (state is DOPNativeFptNFCReaderSubmitNFCDataSuccessState) {
      onFinish();
      return;
    }
  }

  @visibleForTesting
  void onFinish() {
    final NFCEntryPoint entryPoint = widget.arg.entryPoint;
    switch (entryPoint) {
      case NFCEntryPoint.dopWebView:
        widget.arg.controller.onFinished?.call();
        navigatorContext?.pop();
        return;
      case NFCEntryPoint.dopNative:
        dopNativeApplicationStateCubit.getApplicationState();
        return;
    }
  }

  @visibleForTesting
  void prepareBeforeStartNFCReader() {
    // make sure that the video is paused before starting the NFC reader
    playerController.pause?.call();
    // process to start the NFC reader
    nfcReaderCubit.checkNfcSupportAndStartReadIfAvailable();
  }

  // Ref: https://trustingsocial1.atlassian.net/browse/EMA-4175
  @visibleForTesting
  void handleReadNFCErrorState(DOPNativeFptNFCReaderReadNFCErrorState errorState) {
    final NFCError nfcError = errorState.nfcError;
    final String? errorMessage = errorState.errorMessage;

    switch (nfcError) {
      case NFCError.osNotSupportNFC || NFCError.deviceNotSupportNFC || NFCError.cannotOpenNFC:
        gotoUnsupportedNFCScreen();
        break;
      case NFCError.timeout:
        showErrorNfcTimeoutPopup(nfcError: nfcError, errorMessage: errorMessage);
        break;
      case NFCError.wrongInfo:
        if (evoFlutterWrapper.isAndroid()) {
          showErrorNfcWrongInfoPopup(nfcError: nfcError, errorMessage: errorMessage);
        }
        break;
      case NFCError.notInterrupt2s || NFCError.connectionError || NFCError.unknownError:
        showErrorNfcUnknownPopup(nfcError: nfcError, errorMessage: errorMessage);
        break;
      case NFCError.userCanceled:
        // do nothing
        break;
      // sdk customize error code -> show unknown error pop-up
      case NFCError.nullResult || NFCError.sdkException:
        showErrorNfcUnknownPopup(nfcError: nfcError, errorMessage: errorMessage);
        break;
    }
  }

  @visibleForTesting
  void handleSubmitNfcError(int? statusCode) {
    final NFCEntryPoint entryPoint = widget.arg.entryPoint;
    switch (statusCode) {
      case CommonHttpClient.INVALID_TOKEN:
        if (entryPoint == NFCEntryPoint.dopWebView) {
          showNFCVerificationInvalidTokenPopup(statusCode);
        } else {
          handleDopEvoApiError(ErrorUIModel(statusCode: statusCode));
        }
        break;
      case CommonHttpClient.SOCKET_ERRORS:
      case CommonHttpClient.NO_INTERNET:
        if (entryPoint == NFCEntryPoint.dopWebView) {
          showNFCVerificationNetworkPopup(statusCode);
        } else {
          handleDopEvoApiError(ErrorUIModel(statusCode: statusCode));
        }
        break;
      default:
        showRetryNFCVerificationPopup(statusCode);
        break;
    }
  }

  @visibleForTesting
  void showRetryNFCVerificationPopup(int? httpStatusCode) {
    dopUtilFunction.showDialogConfirm(
      dialogId: DOPNativeDialogId.dopRetryNFCVerificationDialog,
      title: DOPNativeStrings.retryNFCVerificationPopupTitle,
      content: DOPNativeStrings.retryNFCVerificationPopupContent,
      textPositive: DOPNativeStrings.dopNativeRetry,
      onClickPositive: () {
        onClickPopupPositiveRetryButton();
      },
      loggingEventOnShowMetaData: <String, dynamic>{
        DOPNativeEventMetadataKey.errorCode: httpStatusCode,
      },
    );
  }

  @visibleForTesting
  void showNFCVerificationInvalidTokenPopup(int? httpStatusCode) {
    dopUtilFunction.showDialogConfirm(
      dialogId: DOPNativeDialogId.dopNFCVerificationInvalidTokenDialog,
      title: DOPNativeStrings.dopNativeInvalidTokenTitle,
      content: DOPNativeStrings.nfcVerificationInvalidTokenPopupContent,
      textPositive: DOPNativeStrings.nfcInvalidTokenRetry,
      onClickPositive: () {
        navigatorContext?.pop();
        onFinish();
      },
      loggingEventOnShowMetaData: <String, dynamic>{
        DOPNativeEventMetadataKey.errorCode: httpStatusCode,
      },
    );
  }

  @visibleForTesting
  void showNFCVerificationNetworkPopup(int? httpStatusCode) {
    dopUtilFunction.showDialogConfirm(
      dialogId: DOPNativeDialogId.dopNFCVerificationNetworkErrorDialog,
      title: DOPNativeStrings.dopNativeInternetErrorTitle,
      content: DOPNativeStrings.nfcInternetErrorDesc,
      textPositive: DOPNativeStrings.dopNativeRetry,
      onClickPositive: () {
        onClickPopupPositiveRetryButton();
      },
      loggingEventOnShowMetaData: <String, dynamic>{
        DOPNativeEventMetadataKey.errorCode: httpStatusCode,
      },
    );
  }

  @visibleForTesting
  void showEnableNfcPopup() {
    dopUtilFunction.showDialogConfirm(
      dialogId: DOPNativeDialogId.dopNFCVerificationEnableNfcDialog,
      title: DOPNativeStrings.dopNativeFptNfcEnableNfcTitle,
      content: DOPNativeStrings.dopNativeFptNfcEnableNfcContent,
      textPositive: DOPNativeStrings.dopNativeFptNfcEnableNfcButton,
      onClickPositive: () {
        navigatorContext?.pop();
        openAndroidNfcSetting();
      },
      textNegative: DOPNativeStrings.dopNativeIgnore,
      onClickNegative: () {
        navigatorContext?.pop();
      },
    );
  }

  @visibleForTesting
  void showErrorNfcTimeoutPopup({
    String? errorMessage,
    NFCError? nfcError,
  }) {
    dopUtilFunction.showDialogConfirm(
      dialogId: DOPNativeDialogId.dopNFCVerificationErrorTimeoutDialog,
      title: DOPNativeStrings.dopNativeFptNfcErrorTimeoutTitle,
      content: DOPNativeStrings.dopNativeFptNfcErrorTimeoutContent,
      textPositive: DOPNativeStrings.dopNativeFptNfcErrorTimeoutButton,
      isDismissible: false,
      onClickPositive: () {
        onClickPopupPositiveRetryButton();
      },
      textNegative: DOPNativeStrings.dopNativeClose,
      onClickNegative: () {
        navigatorContext?.pop();
      },
      positiveDelayInSeconds: DOPNativeConstants.nfcErrorPopupPositiveButtonDelayInSeconds,
      loggingEventOnShowMetaData: <String, dynamic>{
        DOPNativeEventMetadataKey.errorCode: nfcError?.errorCode,
        DOPNativeEventMetadataKey.verdict: errorMessage,
      },
    );
  }

  @visibleForTesting
  void showErrorNfcWrongInfoPopup({
    String? errorMessage,
    NFCError? nfcError,
  }) {
    dopUtilFunction.showDialogConfirm(
      dialogId: DOPNativeDialogId.dopNFCVerificationErrorWrongInfoDialog,
      title: DOPNativeStrings.dopNativeFptNfcErrorWrongInfoTitle,
      content: DOPNativeStrings.dopNativeFptNfcErrorWrongInfoContent,
      textPositive: DOPNativeStrings.dopNativeFptNfcErrorWrongInfoButton,
      isDismissible: false,
      onClickPositive: () {
        onClickPopupPositiveRetryButton();
      },
      textNegative: DOPNativeStrings.dopNativeClose,
      onClickNegative: () {
        navigatorContext?.pop();
      },
      positiveDelayInSeconds: DOPNativeConstants.nfcErrorPopupPositiveButtonDelayInSeconds,
      loggingEventOnShowMetaData: <String, dynamic>{
        DOPNativeEventMetadataKey.errorCode: nfcError?.errorCode,
        DOPNativeEventMetadataKey.verdict: errorMessage,
      },
    );
  }

  @visibleForTesting
  void showErrorNfcUnknownPopup({
    String? errorMessage,
    NFCError? nfcError,
  }) {
    dopUtilFunction.showDialogConfirm(
      dialogId: DOPNativeDialogId.dopNFCVerificationErrorUnknownDialog,
      title: DOPNativeStrings.dopNativeFptNfcErrorUnknownTitle,
      content: DOPNativeStrings.dopNativeFptNfcErrorUnknownContent,
      textPositive: DOPNativeStrings.dopNativeFptNfcErrorUnknownButton,
      isDismissible: false,
      onClickPositive: () {
        onClickPopupPositiveRetryButton();
      },
      textNegative: DOPNativeStrings.dopNativeClose,
      onClickNegative: () {
        navigatorContext?.pop();
      },
      positiveDelayInSeconds: DOPNativeConstants.nfcErrorPopupPositiveButtonDelayInSeconds,
      loggingEventOnShowMetaData: <String, dynamic>{
        DOPNativeEventMetadataKey.errorCode: nfcError?.errorCode,
        DOPNativeEventMetadataKey.verdict: errorMessage,
      },
    );
  }

  @visibleForTesting
  void gotoUnsupportedNFCScreen() {
    DOPNativeNFCDeviceUnsupportedScreen.pushReplacementNamed(
      DOPNativeNFCDeviceUnsupportedScreenArg(entryPoint: widget.arg.entryPoint),
    );
  }

  @visibleForTesting
  void onClickPopupPositiveRetryButton() {
    navigatorContext?.pop();
    prepareBeforeStartNFCReader();
  }

  @visibleForTesting
  void onLogEventAliceChatButton() {
    evoEventTrackingUtils.sendEvoUserEvent(
      eventActionId: DOPNativeFptNfcReaderIntroductionScreenActionEvent.clickHelpButton,
      metaData: <String, dynamic>{
        DOPNativeEventMetadataKey.fromSubScreen: nfcReaderCubit.currentStep.eventTrackingName,
      },
    );
  }

  @visibleForTesting
  void logEventClickCTAButton() {
    evoEventTrackingUtils.sendEvoUserEvent(
      eventActionId: DOPNativeFptNfcReaderIntroductionScreenActionEvent.clickScanNFCButton,
      metaData: <String, dynamic>{
        DOPNativeEventMetadataKey.fromSubScreen: nfcReaderCubit.currentStep.eventTrackingName,
      },
    );
  }

  @visibleForTesting
  void logEventClickWatchIntroductionVideoButton() {
    evoEventTrackingUtils.sendEvoUserEvent(
      eventActionId:
          DOPNativeFptNfcReaderIntroductionScreenActionEvent.clickWatchIntroductionVideoButton,
    );
  }

  @visibleForTesting
  void handleExitDopWeb() {
    // handle exit for DOP Web: back to main screen
    // - ref: https://trustingsocial1.atlassian.net/browse/EMA-3091
    navigatorContext?.popUntilNamed(Screen.mainScreen.name);
    // should clear the saved nfcSharedModel in AppState
    nfcReaderCubit.clearNFCSharedModel();
  }

  @visibleForTesting
  void handleBackDopWeb() {
    // handle exit for DOP Web: back to previous screen (web-view)
    // - ref: https://trustingsocial1.atlassian.net/browse/EMA-3091
    navigatorContext?.pop();
    // should clear the saved nfcSharedModel in AppState
    nfcReaderCubit.clearNFCSharedModel();
  }
}
