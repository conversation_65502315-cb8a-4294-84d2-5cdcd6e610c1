import 'package:flutter/material.dart';

import '../../../../../../../util/evo_flutter_wrapper.dart';
import '../../../../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../../../../../widget/animation/lottie_animation_widget.dart';
import '../../../../../dop_native_constants.dart';
import '../../../../../resources/dop_animations.dart';
import '../../../../../resources/dop_native_images.dart';
import '../../../../../resources/dop_native_resources.dart';
import '../../../../../resources/dop_native_ui_strings.dart';
import '../../../../../widgets/notes_container/dop_native_notes_container.dart';
import '../../../../../widgets/notes_container/dop_native_notes_container_item.dart';

class DOPNativeFptNfcReaderAnimationIntroduction extends StatelessWidget {
  const DOPNativeFptNfcReaderAnimationIntroduction({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          const SizedBox(height: 12),
          Text(
            DOPNativeStrings.dopNativeFptNFCInstructionTitle,
            style: dopNativeTextStyles.h500(
              color: dopNativeColors.textActive,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            DOPNativeStrings.dopNativeFptNFCInstructionDesc,
            style: dopNativeTextStyles.bodyLarge(
              dopNativeColors.textPassive,
            ),
          ),
          const SizedBox(height: 20),
          Center(
            child: LottieAnimationWidget.size(
              evoFlutterWrapper.isAndroid()
                  ? DOPAnimation.dopNfcAndroidIntroductionAnimation
                  : DOPAnimation.dopNfcIosIntroductionAnimation,
              height: EvoUiUtils().calculateVerticalSpace(
                context: context,
                heightPercentage: DOPNativeConstants.fptNfcIntroductionAnimationHeightPercentage,
              ),
            ),
          ),
          const SizedBox(height: 20),
          DOPNativeNotesContainer(
            items: <DOPNativeNotesContainerItem>[
              DOPNativeNotesContainerItem(
                imgAsset: DOPNativeImages.imgNFCGuide1,
                description: DOPNativeStrings.dopNativeFptNFCInstructionGuide1,
              ),
              DOPNativeNotesContainerItem(
                imgAsset: DOPNativeImages.imgNFCGuide2,
                description: DOPNativeStrings.dopNativeFptNFCInstructionGuide2,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
