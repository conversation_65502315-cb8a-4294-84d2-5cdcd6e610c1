import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';

import '../../../../../../../resources/resources.dart';
import '../../../../../../../util/evo_flutter_wrapper.dart';
import '../../../../../../logging/evo_event_tracking_utils/evo_event_tracking_utils_impl.dart';
import '../../../../../dop_native_constants.dart';
import '../../../../../resources/dop_native_images.dart';
import '../../../../../resources/dop_native_resources.dart';
import '../../../../../resources/dop_native_ui_strings.dart';
import '../../../../../resources/dop_native_video_url.dart';
import '../../../../../widgets/dop_native_note_widget.dart';
import '../../../../../widgets/video_player/dop_native_video_player_widget.dart';
import '../../../../logging/screen_action_define/dop_native_fpt_nfc_reader_introduction_screen_action_event.dart';
import '../cubit/dop_native_fpt_nfc_reader_cubit.dart';
import '../models/dop_native_fpt_nfc_reader_introduction_step.dart';

class DOPNativeFptNfcReaderVideoIntroduction extends StatelessWidget {
  final DOPNativeVideoPlayerWidgetController controller;

  const DOPNativeFptNfcReaderVideoIntroduction({
    required this.controller,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final DOPNativeFptNFCReaderCubit cubit = context.read<DOPNativeFptNFCReaderCubit>();
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Container(
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () {
                _logEventClickBackButton();
                controller.pause?.call();
                cubit.changeStep(DopNativeFptNfcReaderIntroductionStep.animation);
              },
              child: Padding(
                padding: const EdgeInsets.only(
                  bottom: 12,
                  top: 12,
                  right: 12,
                ),
                child: evoImageProvider.asset(
                  DOPNativeImages.icArrowLeft,
                  width: 24,
                  height: 24,
                ),
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroTitle,
            style: dopNativeTextStyles.h500(
              color: dopNativeColors.textActive,
            ),
          ),
          const SizedBox(height: 20),
          AspectRatio(
            aspectRatio: DOPNativeConstants.fptNfcVideoIntroductionRatio,
            child: DopNativeVideoPlayerWidget(
              url: getDOPNativeNfcVideoIntroductionUrlByFlavor(),
              controller: controller,
              onTapPauseVideo: () {
                _logEventClickPauseVideo();
              },
              onTapPlayVideo: () {
                _logEventClickPlayVideo();
              },
            ),
          ),
          const SizedBox(height: 20),
          DopNativeNoteWidget(
            title: DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescTitle,
            notes: evoFlutterWrapper.isAndroid()
                ? <String>[
                    DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescAndroidContent1,
                    DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescAndroidContent2,
                    DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescAndroidContent3,
                    DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescAndroidContent4,
                    DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescAndroidContent5,
                  ]
                : <String>[
                    DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescIOSContent1,
                    DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescIOSContent2,
                    DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescIOSContent3,
                    DOPNativeStrings.dopNativeFptNFCInstructionSecondIntroDescIOSContent4,
                  ],
          ),
        ],
      ),
    );
  }

  void _logEventClickBackButton() {
    evoEventTrackingUtils.sendEvoUserEvent(
      eventActionId: DOPNativeFptNfcReaderIntroductionScreenActionEvent.clickBackButton,
    );
  }

  void _logEventClickPauseVideo() {
    evoEventTrackingUtils.sendEvoUserEvent(
      eventActionId: DOPNativeFptNfcReaderIntroductionScreenActionEvent.clickPauseVideoButton,
    );
  }

  void _logEventClickPlayVideo() {
    evoEventTrackingUtils.sendEvoUserEvent(
      eventActionId: DOPNativeFptNfcReaderIntroductionScreenActionEvent.clickPlayVideoButton,
    );
  }
}
