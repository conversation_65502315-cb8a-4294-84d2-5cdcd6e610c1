import 'package:barcode_widget/barcode_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../../../base/evo_page_state_base.dart';
import '../../../../../../prepare_for_app_initiation.dart';
import '../../../../../../resources/resources.dart';
import '../../../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../../../logging/evo_event_tracking_utils/evo_event_tracking_utils_impl.dart';
import '../../../../base/dop_native_page_state_base.dart';
import '../../../../resources/dop_native_resources.dart';
import '../../../../resources/dop_native_ui_strings.dart';
import '../../../../util/dop_functions.dart';
import '../../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../../widgets/dop_native_note_widget.dart';
import '../../../logging/dop_native_event_tracking_screen_id.dart';
import '../../../logging/screen_action_define/dop_native_nfc_device_unsupported_screen_action_event.dart';
import '../constants/dop_native_nfc_entry_point.dart';
import 'cubit/dop_native_nfc_device_unsupported_cubit.dart';

class DOPNativeNFCDeviceUnsupportedScreenArg extends PageBaseArg {
  final NFCEntryPoint entryPoint;

  DOPNativeNFCDeviceUnsupportedScreenArg({
    this.entryPoint = NFCEntryPoint.dopNative,
  });
}

class DOPNativeNFCDeviceUnsupportedScreen extends PageBase {
  final DOPNativeNFCDeviceUnsupportedScreenArg arg;

  const DOPNativeNFCDeviceUnsupportedScreen({required this.arg, super.key});

  @override
  EvoPageStateBase<DOPNativeNFCDeviceUnsupportedScreen> createState() =>
      _DOPNativeNFCDeviceUnsupportedScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId =>
      DOPNativeEventTrackingScreenId.dopNativeNfcNotSupportedScreen;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeNFCDeviceUnsupportedScreen.name);

  static void pushNamed(DOPNativeNFCDeviceUnsupportedScreenArg arg) {
    return navigatorContext?.pushNamed(
      Screen.dopNativeNFCDeviceUnsupportedScreen.name,
      extra: arg,
    );
  }

  static void pushReplacementNamed(DOPNativeNFCDeviceUnsupportedScreenArg arg) {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeNFCDeviceUnsupportedScreen.name,
      extra: arg,
    );
  }
}

class _DOPNativeNFCDeviceUnsupportedScreenState
    extends DOPNativePageStateBase<DOPNativeNFCDeviceUnsupportedScreen> {
  final DOPNativeNFCDeviceUnsupportedCubit _cubit = DOPNativeNFCDeviceUnsupportedCubit(
    appState: getIt.get<AppState>(),
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _cubit.generateOneLink(widget.arg.entryPoint);
    });
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      appBar: DOPNativeAppBar(title: widget.arg.entryPoint.appBarTitle),
      backgroundColor: dopNativeColors.screenBackground,
      body: BlocProvider<DOPNativeNFCDeviceUnsupportedCubit>(
        create: (_) => _cubit,
        child: BlocListener<DOPNativeNFCDeviceUnsupportedCubit, DOPNativeNFCDeviceUnsupportedState>(
          listener: (_, DOPNativeNFCDeviceUnsupportedState state) {
            _handleNFCUnsupportedState(state);
          },
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: <Widget>[
                          Text(
                            DOPNativeStrings.nfcDeviceUnsupportedTitle,
                            style: dopNativeTextStyles.h500(),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            DOPNativeStrings.nfcDeviceUnsupportedDesc,
                            style: dopNativeTextStyles.bodyLarge(
                                dopNativeColors.dopNativeDOPDeviceUnsupportedTextSecondary),
                            textAlign: TextAlign.center,
                          ),
                          _buildQrCodeFrame(context),
                          const DopNativeNoteWidget(
                            title: DOPNativeStrings.nfcDeviceUnsupportedRequireDevice,
                            notes: <String>[
                              DOPNativeStrings.nfcDeviceUnsupportedRequireIOS,
                              DOPNativeStrings.nfcDeviceUnsupportedRequireAndroid,
                            ],
                          ),
                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
                ),
                _buildCTA(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleNFCUnsupportedState(DOPNativeNFCDeviceUnsupportedState state) {
    if (state is DOPNativeNFCDeviceUnsupportedLoading) {
      showDOPLoading();
      return;
    }

    hideDOPLoading();

    if (state is DOPNativeNFCDeviceUnsupportedOneLinkGenerated) {
      logScreenLoadedEvent();
    }
  }

  Widget _buildCTA() {
    return BlocBuilder<DOPNativeNFCDeviceUnsupportedCubit, DOPNativeNFCDeviceUnsupportedState>(
      builder: (_, DOPNativeNFCDeviceUnsupportedState state) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: CommonButton(
            onPressed: () {
              _logEventClickCTAButton();
              final String oneLink = _getOneLinkGenerated(state);
              _onShare(oneLink);
            },
            isWrapContent: false,
            style: dopNativeButtonStyles.primary(ButtonSize.medium),
            child: const Text(DOPNativeStrings.nfcDeviceUnsupportedShareToOtherDevice),
          ),
        );
      },
    );
  }

  void _onShare(String oneLink) {
    final String message = DOPNativeStrings.nfcDeviceUnsupportedSharedMessage
        .replaceVariableByValue(<String>[oneLink]);

    dopUtilFunction.sharingData(message);
  }

  Widget _buildQrCodeFrame(BuildContext context) {
    final double responsiveQRCodeFrameWidth = EvoUiUtils().calculateHorizontalSpace(
      widthPercentage: 0.6,
      context: context,
    );

    return BlocBuilder<DOPNativeNFCDeviceUnsupportedCubit, DOPNativeNFCDeviceUnsupportedState>(
      builder: (_, DOPNativeNFCDeviceUnsupportedState state) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 20),
          child: BarcodeWidget(
            barcode: Barcode.qrCode(
              errorCorrectLevel: BarcodeQRCorrectionLevel.medium,
            ),
            width: responsiveQRCodeFrameWidth,
            height: responsiveQRCodeFrameWidth,
            data: _getOneLinkGenerated(state),
          ),
        );
      },
    );
  }

  String _getOneLinkGenerated(DOPNativeNFCDeviceUnsupportedState state) {
    if (state is DOPNativeNFCDeviceUnsupportedOneLinkGenerated) {
      return state.oneLink;
    }

    return '';
  }

  void _logEventClickCTAButton() {
    evoEventTrackingUtils.sendEvoUserEvent(
      eventActionId: DopNativeNfcDeviceUnsupportedScreenActionEvent.clickCTAButton,
    );
  }
}
