part of 'dop_native_nfc_device_unsupported_cubit.dart';

@immutable
abstract class DOPNativeNFCDeviceUnsupportedState extends BlocState {}

class DOPNativeNFCDeviceUnsupportedInitial extends DOPNativeNFCDeviceUnsupportedState {}

class DOPNativeNFCDeviceUnsupportedLoading extends DOPNativeNFCDeviceUnsupportedState {}

class DOPNativeNFCDeviceUnsupportedOneLinkGenerated extends DOPNativeNFCDeviceUnsupportedState {
  final String oneLink;

  DOPNativeNFCDeviceUnsupportedOneLinkGenerated({
    required this.oneLink,
  });
}
