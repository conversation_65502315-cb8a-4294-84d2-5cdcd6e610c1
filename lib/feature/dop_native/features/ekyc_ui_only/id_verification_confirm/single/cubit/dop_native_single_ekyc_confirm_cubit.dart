import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import '../../../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../../../data/request/dop_native/dop_native_ekyc_confirm_request.dart';
import '../../../../../../../data/response/dop_native/dop_native_application_form_data_entity.dart';
import '../../../../../../../data/response/dop_native/dop_native_metadata_item_entity.dart';
import '../../../../../../../data/response/dop_native/dop_native_ocr_base_data_entity.dart';
import '../../../../../../../data/response/dop_native/dop_native_ocr_data_entity.dart';
import '../../../../../../../prepare_for_app_initiation.dart';
import '../../../../../util/metadata/dop_native_metadata_utils.dart';
import '../../../../additional_form/mock/mock_dop_native_application_form_data_use_case.dart';
import '../../../mock/mock_dop_native_ekyc_use_case.dart';
import '../../mock/mock_dop_native_get_ocr_use_case.dart';
import '../../models/dop_native_ocr_data_model.dart';

part 'dop_native_single_ekyc_confirm_state.dart';

class DOPNativeSingleEKYCConfirmCubit extends CommonCubit<DOPNativeSingleEKYCConfirmState> {
  final DOPNativeRepo dopNativeRepo;
  final DopNativeEkycUIOnlyRepo dopNativeEkycUIOnlyRepo;
  final AppState appState;
  final DOPNativeMetadataUtils metadataUtils;

  DOPNativeSingleEKYCConfirmCubit({
    required this.dopNativeRepo,
    required this.dopNativeEkycUIOnlyRepo,
    required this.appState,
    required this.metadataUtils,
  }) : super(DOPNativeSingleEKYCConfirmInitial());

  Future<void> getOCRData() async {
    emit(DOPNativeSingleEKYCConfirmLoading());

    final DOPNativeOCRBaseDataEntity entity = await dopNativeEkycUIOnlyRepo.getOCRData(
      mockConfig: MockConfig(
        enable: false,
        fileName:
            getMockDOPNativeGetOcrFileNameByCase(MockTestDOPNativeGetOcrUseCase.getOCRDataSuccess),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      final DOPNativeOCRDataModel ocrDataModel = DOPNativeOCRDataModel.convert(
        contactInfo: entity.ocr?.contactInfo,
        personalInfo: entity.ocr?.personalInfo,
      );

      String residenceAddressName = '';
      String? idIssuePlaceName = '';
      String? oldIDCardFromAppForm = '';

      await Future.wait<void>(<Future<dynamic>>[
        getResidenceAddressNameFromOcrData(entity.ocr).then((String value) {
          residenceAddressName = value;
        }),
        getIdIssuePlaceName(entity.ocr).then((String value) {
          idIssuePlaceName = value;
        }),
        getOldIDCardFromFormData().then((String? val) {
          oldIDCardFromAppForm = val;
        })
      ]);

      final DOPNativeOCRDataModel ocrData = ocrDataModel.copyWith(
        residenceAddressName: residenceAddressName,
        idIssuePlaceName: idIssuePlaceName,
        allowEditOldIDCard: checkIfOldIDCardIsMissMatch(
          oldIDCardFromOCR: entity.ocr?.personalInfo?.oldIDCard,
          oldIDCardFromAppForm: oldIDCardFromAppForm,
        ),
      );

      saveOCRDataToAppState(ocrData);
      emit(DOPNativeGetOCRDataSucceed(ocrData: ocrData));
    } else {
      emit(DOPNativeGetOCRDataFailed(error: ErrorUIModel.fromEntity(entity)));
    }
  }

  @visibleForTesting
  Future<String> getIdIssuePlaceName(DOPNativeOCRDataEntity? ocrData) async {
    final String? idIssuePlaceCode = ocrData?.personalInfo?.idIssuePlaceId;

    final DOPNativeMetadataItemEntity? entity = await metadataUtils.getIdIssuePlaceName(
      idIssuePlaceCode: idIssuePlaceCode,
    );

    return entity?.name ?? '';
  }

  @visibleForTesting
  Future<String> getResidenceAddressNameFromOcrData(DOPNativeOCRDataEntity? ocrData) async {
    return metadataUtils.getResidenceAddressName(
      provinceCode: ocrData?.contactInfo?.familyBookAddressProvinceId,
      districtCode: ocrData?.contactInfo?.familyBookAddressDistId,
      wardCode: ocrData?.contactInfo?.familyBookAddressWardId,
    );
  }

  @visibleForTesting
  void saveOCRDataToAppState(DOPNativeOCRDataModel? ocrDataModel) {
    final AppState appState = getIt.get<AppState>();
    appState.dopNativeState.ocrData = ocrDataModel;
  }

  void saveInAppState({
    DOPNativeMetadataItemEntity? province,
    DOPNativeMetadataItemEntity? district,
    DOPNativeMetadataItemEntity? ward,
    String? residenceAddressName,
  }) {
    final AppState appState = getIt.get<AppState>();
    final DOPNativeOCRDataModel? ocr = appState.dopNativeState.ocrData?.copyWith(
      familyBookAddressDistIdValue: district?.code,
      familyBookAddressProvinceIdValue: province?.code,
      familyBookAddressWardIdValue: ward?.code,
      residenceAddressName: residenceAddressName,
    );

    appState.dopNativeState.ocrData = ocr;
  }

  @visibleForTesting
  Future<void> submitEkycConfirm(DOPNativeOCRDataModel ocrData) async {
    emit(DOPNativeSingleEKYCConfirmLoading());

    final DOPNativeEkycConfirmRequest ekycConfirmRequest =
        DOPNativeEkycConfirmRequest.fromOcrDataModelForSingleOCR(ocrData);

    final BaseEntity entity = await dopNativeEkycUIOnlyRepo.submitEkycConfirm(
      ekycConfirmRequest: ekycConfirmRequest,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeEkycFileNameByCase(
          MockTestDOPNativeEkycUseCase.submitEkycConfirmSuccess,
        ),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(DOPNativeSubmitOCRSucceed());
      return;
    }
    emit(DOPNativeSubmitOCRFailure(error: ErrorUIModel.fromEntity(entity)));
  }

  void onSubmit({required String? familyAddressValue, required String? oldIDCard}) {
    final DOPNativeOCRDataModel? ocrData = appState.dopNativeState.ocrData?.copyWith(
      familyAddressValue: familyAddressValue,
      oldIDCard: oldIDCard,
    );

    appState.dopNativeState.ocrData = ocrData;

    if (ocrData == null) {
      return;
    }

    submitEkycConfirm(ocrData);
  }

  @visibleForTesting
  Future<String?> getOldIDCardFromFormData() async {
    final DOPNativeApplicationFormDataEntity entity = await dopNativeRepo.getApplicationFormData(
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeAdditionFormData(
            MockDOPNativeAdditionFormData.getApplicationFormDataSuccess),
      ),
    );

    return entity.formData?.personalInfo?.oldIDCard;
  }

  @visibleForTesting
  bool checkIfOldIDCardIsMissMatch({
    required String? oldIDCardFromOCR,
    required String? oldIDCardFromAppForm,
  }) {
    if (oldIDCardFromOCR != oldIDCardFromAppForm) {
      return true;
    }

    if (oldIDCardFromOCR == null || oldIDCardFromOCR.isEmpty) {
      return true;
    }

    return false;
  }
}
