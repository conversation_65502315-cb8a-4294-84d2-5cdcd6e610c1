import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import '../../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../../prepare_for_app_initiation.dart';
import '../../../../../../resources/resources.dart';
import '../../../../base/dop_native_page_state_base.dart';
import '../../../../resources/dop_native_images.dart';
import '../../../../resources/dop_native_resources.dart';
import '../../../../resources/dop_native_ui_strings.dart';
import '../../../../util/metadata/dop_native_metadata_utils_impl.dart';
import '../../../../util/validation/cubit/dop_native_validation_utils_cubit.dart';
import '../../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../../widgets/dop_native_form_header_widget.dart';
import '../../../../widgets/text_field/dop_native_text_field_widget.dart';
import '../cubit/dop_native_ekyc_confirm_cta_cubit.dart';
import '../cubit/dop_native_ekyc_confirm_cta_state.dart';
import '../dialogs/address_dialog/dop_native_address_dialog.dart';
import '../models/dop_native_ocr_data_model.dart';
import '../widgets/dop_native_id_card_information_widget.dart';
import 'cubit/dop_native_single_ekyc_confirm_cubit.dart';

class DOPNativeSingleEKYCConfirmScreen extends PageBase {
  const DOPNativeSingleEKYCConfirmScreen({
    super.key,
    this.mPosIDConfirmCubit,
    this.validationUtilsCubit,
    this.ctaCubit,
  });

  final DOPNativeSingleEKYCConfirmCubit? mPosIDConfirmCubit;
  final DOPNativeValidationUtilsCubit? validationUtilsCubit;
  final DOPNativeEkycConfirmCTACubit? ctaCubit;

  @override
  DOPNativePageStateBase<DOPNativeSingleEKYCConfirmScreen> createState() =>
      DOPNativeSingleEKYCConfirmScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(
        name: Screen.dopNativeSingleEKYCConfirmScreen.routeName,
      );

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeSingleEKYCConfirmScreen.name,
    );
  }
}

@visibleForTesting
class DOPNativeSingleEKYCConfirmScreenState
    extends DOPNativePageStateBase<DOPNativeSingleEKYCConfirmScreen> {
  @visibleForTesting
  final TextEditingController familyAddressTextController = TextEditingController();
  @visibleForTesting
  final TextEditingController permanentResidenceTextController = TextEditingController();

  late final DOPNativeSingleEKYCConfirmCubit ekycConfirmCubit;
  late final DOPNativeValidationUtilsCubit validationUtilsCubit;
  late final DOPNativeEkycConfirmCTACubit ctaCubit;
  String? oldIdCardValue;

  @override
  void initState() {
    super.initState();
    initCubit();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ekycConfirmCubit.getOCRData();
    });
  }

  @visibleForTesting
  void initCubit() {
    ekycConfirmCubit = widget.mPosIDConfirmCubit ??
        DOPNativeSingleEKYCConfirmCubit(
          dopNativeRepo: getIt.get<DOPNativeRepo>(),
          dopNativeEkycUIOnlyRepo: getIt.get<DopNativeEkycUIOnlyRepo>(),
          appState: getIt.get<AppState>(),
          metadataUtils: DOPNativeMetadataUtilsImpl(dopNativeRepo: getIt.get<DOPNativeRepo>()),
        );
    validationUtilsCubit = widget.validationUtilsCubit ?? DOPNativeValidationUtilsCubit();
    ctaCubit = widget.ctaCubit ?? DOPNativeEkycConfirmCTACubit();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildVisibilityDetectorPage(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: MultiBlocProvider(
        providers: <BlocProvider<dynamic>>[
          BlocProvider<DOPNativeSingleEKYCConfirmCubit>(
            create: (_) => ekycConfirmCubit,
          ),
          BlocProvider<DOPNativeValidationUtilsCubit>(
            create: (_) => validationUtilsCubit,
          ),
          BlocProvider<DOPNativeEkycConfirmCTACubit>(
            create: (_) => ctaCubit,
          )
        ],
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: buildContent(),
          ),
        ),
      ),
    );
  }

  @visibleForTesting
  Widget buildContent() {
    return MultiBlocListener(
      listeners: <BlocListener<dynamic, dynamic>>[
        BlocListener<DOPNativeSingleEKYCConfirmCubit, DOPNativeSingleEKYCConfirmState>(
          listener: (BuildContext context, DOPNativeSingleEKYCConfirmState state) {
            handleListener(state);
          },
        ),
        BlocListener<DOPNativeValidationUtilsCubit, DOPNativeValidationUtilsState>(
          listener: (BuildContext context, DOPNativeValidationUtilsState state) {
            handleValidationListener(state);
          },
        ),
      ],
      child: BlocBuilder<DOPNativeSingleEKYCConfirmCubit, DOPNativeSingleEKYCConfirmState>(
        buildWhen:
            (DOPNativeSingleEKYCConfirmState previous, DOPNativeSingleEKYCConfirmState current) {
          return current is DOPNativeGetOCRDataSucceed || current is DOPNativeGetOCRDataFailed;
        },
        builder: (_, DOPNativeSingleEKYCConfirmState state) {
          DOPNativeOCRDataModel? ocrDataModel;
          if (state is DOPNativeGetOCRDataSucceed) {
            ocrDataModel = state.ocrData;
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const DOPNativeFormHeaderWidget(
                currentStep: 1,
                titleStep: DOPNativeStrings.dopNativeIdVerificationConfirmIndicatorTitle,
              ),
              const SizedBox(height: 20),
              buildTitleScreenWidget(),
              const SizedBox(height: 32),
              DOPNativeIdCardInformationWidget(
                information: ocrDataModel,
                onEditOldIdCard: (String value) {
                  oldIdCardValue = value;
                },
              ),
              const SizedBox(height: 32),
              buildInputPermanentResidence(ocrDataModel?.residenceAddressName),
              buildCTA(),
            ],
          );
        },
      ),
    );
  }

  @visibleForTesting
  Widget buildTitleScreenWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          DOPNativeStrings.dopNativeIdVerificationConfirmScreenTitle,
          style: dopNativeTextStyles.h500(),
        ),
        const SizedBox(height: 8),
        Text(
          DOPNativeStrings.dopNativeIdVerificationConfirmScreenDesc,
          style: dopNativeTextStyles.bodyLarge(dopNativeColors.textPassive),
        ),
      ],
    );
  }

  @visibleForTesting
  Widget buildInputPermanentResidence(String? permanentResidence) {
    final bool ignoreInputPermanentResidence = hasResidenceAddress(permanentResidence);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          ignoreInputPermanentResidence
              ? '${DOPNativeStrings.dopNativeIdVerificationPermanentResidenceInputTextTitle} (${DOPNativeStrings.dopNativeIdVerificationPermanentResidenceInputTextTitleAddition})'
              : DOPNativeStrings.dopNativeIdVerificationPermanentResidenceInputTextTitle,
          style: dopNativeTextStyles.h200(),
        ),
        const SizedBox(height: 16),
        buildInputResidenceAddress(ignoreInputPermanentResidence),
        buildStreetPermanentAddress(),
        const SizedBox(height: 20),
      ],
    );
  }

  @visibleForTesting
  Widget buildInputResidenceAddress(bool ignoreInputPermanentResidence) {
    if (ignoreInputPermanentResidence) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: GestureDetector(
        onTap: () {
          openAddressDialog();
        },
        child: BlocBuilder<DOPNativeValidationUtilsCubit, DOPNativeValidationUtilsState>(
          buildWhen: (_, DOPNativeValidationUtilsState current) {
            return current is DOPNativeResidenceAddressValid ||
                current is DOPNativeResidenceAddressFailed;
          },
          builder: (BuildContext context, DOPNativeValidationUtilsState state) {
            String? errorMsg;
            if (state is DOPNativeResidenceAddressFailed) {
              errorMsg = state.errorMsg;
            }

            return DOPNativeTextField(
              backgroundColor: dopNativeColors.background,
              controller: permanentResidenceTextController,
              isEnabled: false,
              hintText: DOPNativeStrings.dopNativeIdVerificationPermanentResidenceInputTextHint,
              suffixIcon: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: evoImageProvider.asset(
                  DOPNativeImages.icArrowDown,
                  width: 20,
                  height: 20,
                ),
              ),
              errorText: errorMsg,
            );
          },
        ),
      ),
    );
  }

  @visibleForTesting
  Widget buildStreetPermanentAddress() {
    return BlocBuilder<DOPNativeValidationUtilsCubit, DOPNativeValidationUtilsState>(
      buildWhen: (_, DOPNativeValidationUtilsState current) {
        return current is DOPNativeAddressValid || current is DOPNativeAddressFailed;
      },
      builder: (BuildContext context, DOPNativeValidationUtilsState state) {
        String? errorMsg;
        if (state is DOPNativeAddressFailed) {
          errorMsg = state.errorMsg;
        }

        return DOPNativeTextField(
          backgroundColor: dopNativeColors.background,
          controller: familyAddressTextController,
          hintText: DOPNativeStrings.dopNativeIdVerificationStreetInputTextHint,
          onChanged: (String value) {
            validationUtilsCubit.validateDOPNativeAddress(value);
          },
          errorText: errorMsg,
        );
      },
    );
  }

  @visibleForTesting
  Widget buildCTA() {
    return BlocBuilder<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      builder: (_, DOPNativeEkycConfirmCTAState state) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: CommonButton(
            isWrapContent: false,
            onPressed: state.enable ? onButtonPressed : null,
            style: dopNativeButtonStyles.primary(ButtonSize.medium),
            child: const Text(DOPNativeStrings.dopNativeNext),
          ),
        );
      },
    );
  }

  @visibleForTesting
  void onButtonPressed() {
    final bool canSaveInfo = checkBeforeSave();
    if (!canSaveInfo) {
      return;
    }

    ekycConfirmCubit.onSubmit(
      familyAddressValue: familyAddressTextController.text,
      oldIDCard: oldIdCardValue,
    );
  }

  @visibleForTesting
  bool checkBeforeSave() {
    final bool isStreetValid =
        validationUtilsCubit.validateDOPNativeAddress(familyAddressTextController.text);
    if (!isStreetValid) {
      return false;
    }

    final String? residenceAddressName = appState.dopNativeState.ocrData?.residenceAddressName;
    final bool isValidResidenceAddress =
        validationUtilsCubit.validateResidenceAddress(residenceAddressName);
    if (!isValidResidenceAddress) {
      return false;
    }

    return true;
  }

  @visibleForTesting
  Future<void> openAddressDialog() async {
    /// This dialog has call API and logic from [DOPNativeIDVerificationConfirmCubit]
    /// In the feature we need separate this logic to it's own cubit
    await DOPNativeAddressDialog.show(
      addressData: ekycConfirmCubit.saveInAppState,
    );
    final String? residenceAddress = appState.dopNativeState.ocrData?.residenceAddressName;
    validationUtilsCubit.validateResidenceAddress(residenceAddress);
    permanentResidenceTextController.text = residenceAddress ?? '';
  }

  @visibleForTesting
  void handleListener(DOPNativeSingleEKYCConfirmState state) {
    if (state is DOPNativeSingleEKYCConfirmLoading) {
      showDOPLoading();
      return;
    }

    hideDOPLoading();

    if (state is DOPNativeGetOCRDataSucceed) {
      if (hasResidenceAddress(state.ocrData?.residenceAddressName)) {
        ctaCubit.updateResidenceAddress(FieldState.noneCheck);
      }
      return;
    }

    if (state is DOPNativeGetOCRDataFailed) {
      handleEvoApiError(state.error);
      return;
    }

    if (state is DOPNativeSubmitOCRFailure) {
      handleEvoApiError(state.error);
      return;
    }

    if (state is DOPNativeSubmitOCRSucceed) {
      dopNativeApplicationStateCubit.getApplicationState();
      return;
    }
  }

  @visibleForTesting
  void handleValidationListener(DOPNativeValidationUtilsState state) {
    /// Residence address
    if (state is DOPNativeResidenceAddressValid) {
      ctaCubit.updateResidenceAddress(FieldState.valid);
      return;
    }

    if (state is DOPNativeResidenceAddressFailed) {
      ctaCubit.updateResidenceAddress(FieldState.invalid);
      return;
    }

    /// Street address
    if (state is DOPNativeAddressValid) {
      ctaCubit.updateStreetAddress(FieldState.valid);
      return;
    }

    if (state is DOPNativeAddressFailed) {
      ctaCubit.updateStreetAddress(FieldState.invalid);
      return;
    }
  }

  @visibleForTesting
  bool hasResidenceAddress(String? permanentResidence) {
    return permanentResidence?.isNotEmpty == true;
  }
}
