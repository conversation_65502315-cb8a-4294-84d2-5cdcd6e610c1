import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../../../resources/global.dart';
import '../../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../base/cubit/dop_native_application_state.dart';
import '../../../base/dop_native_page_state_base.dart';
import '../../../dop_native_constants.dart';
import '../../../resources/dop_native_images.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../../util/dop_native_navigation_utils.dart';
import '../../../util/dop_native_submit_status_polling/dop_native_submit_status_polling_impl.dart';
import '../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../widgets/dop_native_status_widget.dart';
import '../dop_native_ekyc_config.dart';
import '../ui_model/ekyc_error_ui_model.dart';
import '../utils/dop_native_ekyc_api_response_handler/dop_native_ekyc_api_response_handler.dart';
import 'cubit/dop_native_id_card_front_side_verification_cubit.dart';

abstract class DOPNativeIdCardFrontSideVerificationScreenArg extends PageBaseArg {
  final VoidCallback onSuccess;
  final EKYCErrorCallback onFailed;
  final String? cardType;

  DOPNativeIdCardFrontSideVerificationScreenArg({
    required this.onSuccess,
    required this.onFailed,
    this.cardType,
  });
}

class DOPNativeIdCardQRCodeVerificationUploadRequiredScreenArg
    extends DOPNativeIdCardFrontSideVerificationScreenArg {
  final String cardFrontSideBase64Image;

  DOPNativeIdCardQRCodeVerificationUploadRequiredScreenArg({
    required this.cardFrontSideBase64Image,
    required super.onSuccess,
    required super.onFailed,
    super.cardType,
  });
}

class DOPNativeIdCardQRCodeVerificationUploadSkippedScreenArg
    extends DOPNativeIdCardFrontSideVerificationScreenArg {
  DOPNativeIdCardQRCodeVerificationUploadSkippedScreenArg({
    required super.onSuccess,
    required super.onFailed,
    super.cardType,
  });
}

class DOPNativeIdCardFrontSideVerificationScreen extends PageBase {
  const DOPNativeIdCardFrontSideVerificationScreen({
    required this.arg,
    super.key,
  });

  final DOPNativeIdCardFrontSideVerificationScreenArg arg;

  @override
  DOPNativePageStateBase<DOPNativeIdCardFrontSideVerificationScreen> createState() =>
      _DOPNativeIdCardFrontSideVerificationState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeIdCardFrontSideVerificationScreen.routeName);

  /// [frontSideBase64Image]: base64 image of front side of ID card. Must be available if capture front side of ID card
  /// [cardTypeId]: ID card type. Must be available if capture front side of ID card
  /// [onSuccess]: callback when ID card verification is successful
  /// [onFailed]: callback when ID card verification is failed
  static void pushNamed({
    required DOPNativeIdCardFrontSideVerificationScreenArg arg,
  }) {
    return navigatorContext?.pushNamed(
      Screen.dopNativeIdCardFrontSideVerificationScreen.name,
      extra: arg,
    );
  }

  /// [frontSideBase64Image]: base64 image of front side of ID card. Must be available if capture front side of ID card
  /// [cardTypeId]: ID card type. Must be available if capture front side of ID card
  /// [onSuccess]: callback when ID card verification is successful
  /// [onFailed]: callback when ID card verification is failed
  static void pushReplacementNamed({
    required DOPNativeIdCardFrontSideVerificationScreenArg arg,
  }) {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeIdCardFrontSideVerificationScreen.name,
      extra: arg,
    );
  }
}

class _DOPNativeIdCardFrontSideVerificationState
    extends DOPNativePageStateBase<DOPNativeIdCardFrontSideVerificationScreen> {
  final DOPNativeIdCardFrontSideVerificationCubit _idCardCubit =
      DOPNativeIdCardFrontSideVerificationCubit(
    dopNativeEkycUiOnlyRepo: getIt.get<DopNativeEkycUIOnlyRepo>(),
    dopNativeSubmitStatusPolling: DOPNativeSubmitStatusPollingImpl(
      intervalDuration: const Duration(
        milliseconds: DOPNativeEKYCConfig.pollingIntervalTimeInMs,
      ),
    ),
    commonApisResponseHandler: getIt.get<EkycCommonApiResponsesHandler>(),
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final DOPNativeIdCardFrontSideVerificationScreenArg arg = widget.arg;
      String? cardFrontSideBase64Image;
      String? cardType;

      if (arg is DOPNativeIdCardQRCodeVerificationUploadRequiredScreenArg) {
        cardFrontSideBase64Image = arg.cardFrontSideBase64Image;
        cardType = arg.cardType;
      }

      if (arg is DOPNativeIdCardQRCodeVerificationUploadSkippedScreenArg) {
        cardType = arg.cardType;
      }

      _idCardCubit.startProcess(
        cardFrontSideBase64Image: cardFrontSideBase64Image,
        cardType: cardType,
      );
    });
  }

  @override
  void dispose() {
    _idCardCubit.cancelPollingSubmitStatus();
    super.dispose();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: SafeArea(
        child: BlocProvider<DOPNativeIdCardFrontSideVerificationCubit>(
          create: (BuildContext context) => _idCardCubit,
          child: BlocListener<DOPNativeIdCardFrontSideVerificationCubit,
              DOPNativeIdCardFrontSideVerificationState>(
            listener: (BuildContext context, DOPNativeIdCardFrontSideVerificationState state) {
              _onDOPNativeEKYCProcessStateChanged(state);
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: DOPNativeStatusWidget(
                icon: DOPNativeImages.imgEkycProcessing,
                description: DOPNativeStrings.dopNativeEkycProcessingTitle,
                title: DOPNativeStrings.dopNativeEkycProcessingDesc,
                iconHeight: EvoUiUtils().calculateVerticalSpace(
                    context: context,
                    heightPercentage: DOPNativeConstants.statusIconHeightPercentage),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onDOPNativeEKYCProcessStateChanged(DOPNativeIdCardFrontSideVerificationState state) {
    if (state is IdCardVerificationSuccess) {
      /// after polling success get application state
      /// refer: https://trustingsocial1.atlassian.net/browse/EMA-2695
      dopNativeApplicationStateCubit.getApplicationState();
      return;
    }

    if (state is IdCardVerificationFailed) {
      widget.arg.onFailed(state.error, null);
      return;
    }
  }

  @override
  void handleDOPNativeApplicationStateChanged(DOPNativeApplicationState state) {
    if (state is DOPNativeApplicationStateLoading) {
      return;
    }

    if (state is DOPNativeApplicationStateLoaded) {
      final bool isEKYCLockError = state.entity.currentStep == DOPNativeNavigationStep.locked.value;
      if (isEKYCLockError) {
        widget.arg.onFailed(const EkycErrorUIModel(code: EkycErrorCode.locked), null);
        return;
      }

      widget.arg.onSuccess();
      return;
    }

    super.handleDOPNativeApplicationStateChanged(state);
  }
}
