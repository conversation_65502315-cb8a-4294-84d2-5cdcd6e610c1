// ignore_for_file: constant_identifier_names
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

enum EkycErrorCode {
  // common errors
  network('network_error'),
  invalidToken('invalid_token'),
  commonError('common_error'),
  limitExceed('limit_exceed'),

  // sanity errors
  cardTypeNotSupported('card_type_not_supported'),
  otherEkycError('other_ekyc_error'),

  // capturing image exceeded limit. so ekyc feature is locked
  locked('locked'),

  // selfie failed matching
  selfieFailedMatching('selfie_failed_matching'),

  // <PERSON><PERSON> cannot scan qr code on front id card
  qrCodeRequired('qr_code_required');

  const EkycErrorCode(this.value);

  final String value;
}

class EkycErrorUIModel {
  final EkycErrorCode code;
  final String? message;

  const EkycErrorUIModel({
    required this.code,
    this.message,
  });

  factory EkycErrorUIModel.fromHttpCode({
    int? statusCode,
    String? message,
  }) {
    return EkycErrorUIModel(
      code: mapEkycErrorCodeByHttpCode(statusCode),
      message: message,
    );
  }

  @visibleForTesting
  static EkycErrorCode mapEkycErrorCodeByHttpCode(int? statusCode) {
    switch (statusCode) {
      case CommonHttpClient.SOCKET_ERRORS:
      case CommonHttpClient.NO_INTERNET:
        return EkycErrorCode.network;
      case CommonHttpClient.INVALID_TOKEN:
        return EkycErrorCode.invalidToken;
      case CommonHttpClient.LIMIT_EXCEEDED:
        return EkycErrorCode.limitExceed;
      default:
        return EkycErrorCode.commonError;
    }
  }

  ErrorUIModel toErrorUIModel() => ErrorUIModel(statusCode: mapEKYCErrorCodeToHttpCode(code));

  @visibleForTesting
  static int mapEKYCErrorCodeToHttpCode(EkycErrorCode? statusCode) {
    if (statusCode == EkycErrorCode.network) {
      return CommonHttpClient.NO_INTERNET;
    } else if (statusCode == EkycErrorCode.invalidToken) {
      return CommonHttpClient.INVALID_TOKEN;
    } else if (statusCode == EkycErrorCode.limitExceed) {
      return CommonHttpClient.LIMIT_EXCEEDED;
    }
    return CommonHttpClient.UNKNOWN_ERRORS;
  }
}
