enum MockDOPNativeGetSubmitStatusUseCase {
  success('dop_native_get_submit_status_success.json'),
  failure('dop_native_get_submit_status_failure.json'),
  invalidParameters('dop_native_get_submit_invalid_parameters.json'),
  forbiddenParameters('dop_native_get_submit_forbidden_parameters.json'),
  missingParameters('dop_native_get_submit_missing_parameters.json'),
  successIDCardAuth('dop_native_get_submit_status_success_id_card_auth.json');

  final String value;

  const MockDOPNativeGetSubmitStatusUseCase(this.value);
}

String getMockDOPNativeGetSubmitStatusFileNameByCase(MockDOPNativeGetSubmitStatusUseCase mockCase) {
  return mockCase.value;
}
