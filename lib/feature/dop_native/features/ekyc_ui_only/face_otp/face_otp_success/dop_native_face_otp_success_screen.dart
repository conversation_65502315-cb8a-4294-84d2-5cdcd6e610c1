import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../../../resources/global.dart';
import '../../../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../../base/dop_native_page_state_base.dart';
import '../../../../dop_native_constants.dart';
import '../../../../resources/dop_native_images.dart';
import '../../../../resources/dop_native_resources.dart';
import '../../../../resources/dop_native_ui_strings.dart';
import '../../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../../widgets/dop_native_status_widget.dart';
import '../../../collect_location/handler/dop_native_collect_location_handler.dart';

class DOPNativeFaceOtpSuccessScreen extends PageBase {
  const DOPNativeFaceOtpSuccessScreen({super.key});

  static void pushNamed() {
    return navigatorContext?.pushNamed(
      Screen.dopNativeFaceOtpSuccessScreen.name,
    );
  }

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeFaceOtpSuccessScreen.name,
    );
  }

  @override
  DOPNativePageStateBase<DOPNativeFaceOtpSuccessScreen> createState() =>
      _DOPNativeFaceOtpSuccessScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeFaceOtpSuccessScreen.routeName);
}

class _DOPNativeFaceOtpSuccessScreenState
    extends DOPNativePageStateBase<DOPNativeFaceOtpSuccessScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: SafeArea(
        child: SizedBox.expand(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: DOPNativeStatusWidget(
              icon: DOPNativeImages.imgFaceOTPSuccess,
              title: DOPNativeStrings.faceOtpSuccessTitle,
              iconHeight: EvoUiUtils().calculateVerticalSpace(
                context: context,
                heightPercentage: DOPNativeConstants.statusIconHeightPercentage,
              ),
              ctaWidget: _buildCTA(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCTA() {
    return CommonButton(
      isWrapContent: false,
      onPressed: () {
        DOPNativeCollectLocationHandler().checkConditionsAndProcessCollectLocation(
          onFinish: () => dopNativeApplicationStateCubit.getApplicationState(),
        );
      },
      style: dopNativeButtonStyles.primary(ButtonSize.medium),
      child: const Text(DOPNativeStrings.dopNativeNext),
    );
  }
}
