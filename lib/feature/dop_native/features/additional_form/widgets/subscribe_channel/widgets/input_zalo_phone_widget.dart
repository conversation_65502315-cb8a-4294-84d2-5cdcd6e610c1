import 'package:flutter/cupertino.dart';

import '../../../../../resources/dop_native_resources.dart';
import '../../../../../resources/dop_native_ui_strings.dart';
import '../../../../../util/dop_functions.dart';
import '../../../../../util/validation/dop_native_validation_case_model.dart';
import '../../../../../widgets/text_field/dop_native_text_field_widget.dart';

class InputZaloPhoneWidget extends StatefulWidget {
  const InputZaloPhoneWidget({
    required this.onPhoneChanged,
    super.key,
  });

  final void Function({
    required String phone,
    required bool isValid,
  }) onPhoneChanged;

  @override
  State<StatefulWidget> createState() => _InputZaloPhoneWidgetState();
}

class _InputZaloPhoneWidgetState extends State<InputZaloPhoneWidget> {
  final TextEditingController _phoneController = TextEditingController();
  String? _phoneErrorMsg;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: DOPNativeTextField(
        controller: _phoneController,
        keyboardType: TextInputType.phone,
        hintText: DOPNativeStrings.dopNativeInputPhoneHint,
        errorText: _phoneErrorMsg,
        backgroundColor: dopNativeColors.textFieldBg,
        onSubmitted: (String value) {
          _hideKeyboard();
        },
        onChanged: (String value) {
          _validatePhoneNumber(value);
        },
      ),
    );
  }

  void _hideKeyboard() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  void _validatePhoneNumber(String phoneNumber) {
    final PhoneCase phoneCase = dopUtilFunction.getPhoneCase(phoneNumber);

    if (phoneCase == PhoneCase.empty) {
      setState(() {
        _phoneErrorMsg = DOPNativeStrings.dopNativeInputZaloPhoneNumberEmptyFailed;
      });
    } else if (phoneCase == PhoneCase.invalid) {
      setState(() {
        _phoneErrorMsg = DOPNativeStrings.dopNativeInputZaloPhoneNumberFormatFailed;
      });
    } else {
      setState(() {
        _phoneErrorMsg = null;
      });
    }

    widget.onPhoneChanged(
      phone: phoneNumber,
      isValid: phoneCase == PhoneCase.valid,
    );
  }
}
