import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_common_package/common_package/common_package.dart';

import '../../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../../prepare_for_app_initiation.dart';
import '../../../../resources/dop_native_resources.dart';
import '../../../../resources/dop_native_ui_strings.dart';
import '../../../../util/metadata/dop_native_metadata_utils_impl.dart';
import '../../models/additional_form_data_model.dart';
import '../../widgets/dop_native_additional_form_controller.dart';
import 'cubit/dop_native_subscribe_channel_cubit.dart';
import 'widgets/dop_native_list_subscribe_channel_widget.dart';
import 'widgets/dop_native_zalo_account_widget.dart';

class DOPNativeSubscribeChannelWidget extends StatefulWidget {
  const DOPNativeSubscribeChannelWidget({
    required this.model,
    required this.controller,
    super.key,
  });

  final DOPNativeAdditionalFormDataModel model;
  final DOPNativeAdditionalFormController controller;

  @override
  State<StatefulWidget> createState() => _DOPNativeSubscribeChannelWidgetState();
}

class _DOPNativeSubscribeChannelWidgetState extends State<DOPNativeSubscribeChannelWidget> {
  final DOPNativeSubscribeChannelCubit _subscribeChannelCubit = DOPNativeSubscribeChannelCubit(
    dopNativeRepo: getIt.get<DOPNativeRepo>(),
    appState: getIt.get<AppState>(),
    metadataUtils: DOPNativeMetadataUtilsImpl(dopNativeRepo: getIt.get<DOPNativeRepo>()),
  );

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      widget.controller.onSubmitted = _submit;
      _subscribeChannelCubit.getSubscribeChannels();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<DOPNativeSubscribeChannelCubit>(
      create: (_) => _subscribeChannelCubit,
      child: BlocConsumer<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
        listener: _listenSubscribeChannel,
        builder: (BuildContext context, DOPNativeSubscribeChannelState state) {
          if (state is FetchSubscribeChannelLoading || state is FetchSubscribeChannelError) {
            return const Offstage();
          }
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              ..._buildTitle(),
              _buildSubscribeChannel(),
              _buildZaloAccount(),
            ],
          );
        },
      ),
    );
  }

  void _submit() {
    FocusManager.instance.primaryFocus?.unfocus();
    _subscribeChannelCubit.submitSubscribeChannels(widget.model);
  }

  Future<void> _listenSubscribeChannel(
      BuildContext context, DOPNativeSubscribeChannelState state) async {
    if (state is FetchSubscribeChannelLoading || state is SubmitSubscribeChannelLoading) {
      widget.controller.onLoading(true);
      return;
    }

    if (state is FetchSubscribeChannelSuccess) {
      widget.controller.onLoading(false);
      return;
    }

    if (state is FetchSubscribeChannelError) {
      widget.controller.onError(state.error);
      return;
    }

    if (state is ZaloChannelSelectedState || state is ZaloChannelUnSelectedState) {
      _subscribeChannelCubit.shouldEnableButton();
      return;
    }

    if (state is SubmitSubscribeChannelSuccess) {
      final DOPNativeAdditionalFormDataModel model = widget.model.copyWith();
      widget.controller.onSuccess(model);
      return;
    }

    if (state is SubmitSubscribeChannelError) {
      widget.controller.onError(state.error);
      return;
    }

    if (state is SubmitButtonEnable) {
      widget.controller.onValid(true);
      return;
    }

    if (state is SubmitButtonDisable) {
      widget.controller.onValid(false);
      return;
    }
  }

  List<Widget> _buildTitle() {
    return <Widget>[
      Text(
        DOPNativeStrings.dopNativeRegisterInfo,
        style: dopNativeTextStyles.h500(),
      ),
      const SizedBox(height: 32),
    ];
  }

  Widget _buildSubscribeChannel() {
    return BlocBuilder<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
      buildWhen: (_, DOPNativeSubscribeChannelState state) {
        return state is FetchSubscribeChannelSuccess;
      },
      builder: (BuildContext context, DOPNativeSubscribeChannelState state) {
        if (state is FetchSubscribeChannelSuccess) {
          return DOPNativeListSubscribeChannelWidget(
            onValueChanged: _subscribeChannelCubit.onChannelSelected,
            subscribeChannels: state.subscribeChannels,
          );
        }
        return const Offstage();
      },
    );
  }

  Widget _buildZaloAccount() {
    return BlocBuilder<DOPNativeSubscribeChannelCubit, DOPNativeSubscribeChannelState>(
      buildWhen: (_, DOPNativeSubscribeChannelState state) {
        return state is ZaloChannelSelectedState || state is ZaloChannelUnSelectedState;
      },
      builder: (BuildContext context, DOPNativeSubscribeChannelState state) {
        if (state is ZaloChannelSelectedState) {
          return Column(
            children: <Widget>[
              const SizedBox(height: 32),
              DOPNativeZaloAccountWidget(
                onPhoneValueChanged: _subscribeChannelCubit.onPhoneValueChanged,
                onSelectZaloTypeChanged: _subscribeChannelCubit.onSelectZaloTypeChanged,
              ),
            ],
          );
        }
        return const Offstage();
      },
    );
  }
}
