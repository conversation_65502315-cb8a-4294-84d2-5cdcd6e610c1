enum MockDOPNativeAdditionFormData {
  getApplicationFormDataSuccess('dop_native_get_application_form_data_success.json'),
  submitSubscribeChannelSuccess('dop_native_submit_subscribe_channel_success.json'),
  addressAdditionalInfoSuccess('dop_native_submit_address_additional_info_success.json'),
  getSubscribeChannelSuccess('dop_native_get_subscribe_channel_success.json'),
  submitEmergencyContactSuccess('dop_native_submit_emergency_contact_success.json'),
  submitSecretQuestionSuccess('dop_native_submit_secret_question_success.json'),
  submitAcquisitionRewardSuccess('dop_native_submit_acquisition_reward_success.json');

  final String value;

  const MockDOPNativeAdditionFormData(this.value);
}

String getMockDOPNativeAdditionFormData(MockDOPNativeAdditionFormData mockCase) {
  return mockCase.value;
}
