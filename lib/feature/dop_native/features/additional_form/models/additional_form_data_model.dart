import '../../../../../data/response/dop_native/dop_native_application_form_data_entity.dart';
import 'additional_form_step_model.dart';

class DOPNativeAdditionalFormDataModel {
  final DOPNativeApplicationFormDataEntity? formDataEntity;
  final DOPNativeAdditionalFormStepModel formStepModel;

  DOPNativeAdditionalFormDataModel({
    required this.formDataEntity,
    required this.formStepModel,
  });

  DOPNativeAdditionalFormDataModel copyWith({
    DOPNativeApplicationFormDataEntity? formDataEntityValue,
    DOPNativeAdditionalFormStepModel? formStepValue,
  }) {
    return DOPNativeAdditionalFormDataModel(
      formDataEntity: formDataEntityValue ?? formDataEntity,
      formStepModel: formStepValue ?? formStepModel,
    );
  }
}
