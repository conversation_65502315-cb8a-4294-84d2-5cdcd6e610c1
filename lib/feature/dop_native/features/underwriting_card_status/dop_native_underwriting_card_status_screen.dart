import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../../../base/evo_page_state_base.dart';
import '../../../../../../resources/resources.dart';
import '../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../base/dop_native_page_state_base.dart';
import '../../resources/dop_native_resources.dart';
import '../../resources/dop_native_ui_strings.dart';
import '../../util/card_status/cubit/dop_native_card_status_cubit.dart';
import '../../util/card_status/cubit/dop_native_card_status_state.dart';
import '../../util/dop_functions.dart';
import '../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../widgets/dop_native_card_acquisition_related_info_widget.dart';
import '../../widgets/dop_native_status_card_icon_widget.dart';
import '../underwriting_sub_flow/dop_native_card_cic_blocked_screen.dart';

class DOPNativeUnderwritingCardStatusScreen extends PageBase {
  const DOPNativeUnderwritingCardStatusScreen({super.key});

  @override
  EvoPageStateBase<DOPNativeUnderwritingCardStatusScreen> createState() =>
      _DOPNativeUnderwritingCardStatusScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeUnderwritingCardStatusScreen.name);

  static void pushNamed() {
    return navigatorContext?.pushNamed(Screen.dopNativeUnderwritingCardStatusScreen.name);
  }

  static void pushReplacementNamed() {
    return navigatorContext
        ?.pushReplacementNamed(Screen.dopNativeUnderwritingCardStatusScreen.name);
  }
}

class _DOPNativeUnderwritingCardStatusScreenState
    extends DOPNativePageStateBase<DOPNativeUnderwritingCardStatusScreen> {
  final DOPNativeCardStatusCubit _cubit = DOPNativeCardStatusCubit(
    dopNativeRepo: getIt.get<DOPNativeRepo>(),
    appState: getIt.get<AppState>(),
    cardStatusUseCase: CardStatusUseCase.underwritingCardStatus,
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _cubit.getCardStatus();
    });
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      appBar: DOPNativeAppBar(
        onExitDOP: dopNativeCompleteOnboardingCubit.onExitDOPFlow,
      ),
      backgroundColor: dopNativeColors.screenBackground,
      body: BlocProvider<DOPNativeCardStatusCubit>(
        create: (_) => _cubit,
        child: BlocListener<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
          listener: (BuildContext context, DOPNativeCardStatusState state) {
            _handleDOPNativeCardStatusStateChanged(state);
          },
          child: BlocBuilder<DOPNativeCardStatusCubit, DOPNativeCardStatusState>(
            builder: (_, DOPNativeCardStatusState state) {
              if (state is UnderwritingCardStatusCardActivated) {
                return SafeArea(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        children: <Widget>[
                          DOPNativeStatusCardIconWidget(
                            title: DOPNativeStrings.dopNativeUnderwritingCardStatusTitle,
                            ctaWidget: buildCTA(),
                            description: DOPNativeStrings.dopNativeUnderwritingCardStatusDesc,
                          ),
                          const SizedBox(height: 20),
                          const DOPNativeCardAcquisitionRelatedInfoWidget(
                            enableViewEContractCTA: false,
                          ),
                          SizedBox(height: dopUtilFunction.getPaddingBottom(context)),
                        ],
                      ),
                    ),
                  ),
                );
              }
              return const SizedBox();
            },
          ),
        ),
      ),
    );
  }

  Widget buildCTA() {
    return CommonButton(
      onPressed: () => dopNativeCompleteOnboardingCubit.onExitDOPFlow(),
      isWrapContent: false,
      style: dopNativeButtonStyles.primary(ButtonSize.medium),
      child: const Text(DOPNativeStrings.dopNativeReceiveVoucher),
    );
  }

  void _handleDOPNativeCardStatusStateChanged(DOPNativeCardStatusState state) {
    if (state is GetCardStatusLoading) {
      showDOPLoading();
      return;
    }

    hideDOPLoading();

    if (state is GetCardStatusBlocked) {
      DOPNativeCardCICBlockedScreen.pushReplacementNamed();
      return;
    }

    if (state is GetCardStatusFailure) {
      handleEvoApiError(state.error);
      return;
    }
  }
}
