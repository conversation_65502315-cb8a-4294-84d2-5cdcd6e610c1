enum MockDOPNativeVerifyOTPCase {
  success('dop_native_verify_otp_success.json'),
  incorrectOTP('dop_native_verify_otp_incorrect_otp.json'),
  expiredConsent('dop_native_verify_otp_expired_consent.json'),
  limitExceeded('dop_native_verify_otp_limit_exceeded.json');

  final String value;

  const MockDOPNativeVerifyOTPCase(this.value);
}

String getMockDOPNativeVerifyOTPFileNameByCase(MockDOPNativeVerifyOTPCase mockCase) {
  return mockCase.value;
}
