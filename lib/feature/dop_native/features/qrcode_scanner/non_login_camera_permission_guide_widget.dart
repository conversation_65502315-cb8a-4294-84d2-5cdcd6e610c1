import 'package:app_settings/app_settings.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../resources/resources.dart';

class NonLoginCameraPermissionGuideWidget extends StatelessWidget {
  final SizedBox space12 = const SizedBox(height: 12);
  final SizedBox space20 = const SizedBox(height: 20);
  final SizedBox space10 = const SizedBox(height: 10);

  const NonLoginCameraPermissionGuideWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: evoColors.storyViewFooterBackgroundColor,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(20),
        ),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: evoColors.storyViewFooterShadowColor,
            blurRadius: 32,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: SafeArea(
        top: false,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            space20,
            Text(
              EvoStrings.dopNativeCameraPermissionDenyTitle,
              style: evoTextStyles.h400(),
            ),
            space10,
            Text(
              EvoStrings.dopNativeCameraPermissionDenyDescription,
              style: evoTextStyles.bodyMedium(evoColors.textPassive),
            ),
            space20,
            Row(
              children: <Widget>[
                _buildIgnoreCTA(),
                const SizedBox(width: 8),
                _buildSettingCTA(),
              ],
            ),
            space12,
          ],
        ),
      ),
    );
  }

  Widget _buildIgnoreCTA() {
    return Expanded(
      child: CommonButton(
        onPressed: () {
          navigatorContext?.pop();
        },
        style: evoButtonStyles.tertiary(ButtonSize.xLarge),
        child: const Text(EvoStrings.ignoreTitle),
      ),
    );
  }

  Widget _buildSettingCTA() {
    return Expanded(
      child: CommonButton(
        onPressed: () {
          AppSettings.openAppSettings();
        },
        style: evoButtonStyles.primary(ButtonSize.xLarge),
        child: const Text(EvoStrings.settingTitle),
      ),
    );
  }
}
