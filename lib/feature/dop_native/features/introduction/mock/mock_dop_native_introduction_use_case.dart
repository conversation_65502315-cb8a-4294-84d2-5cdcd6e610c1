enum MockTestDOPNativeIntroductionUseCase {
  registrationCampaign('dop_native_registration_campaign.json'),
  registerSuccess('dop_native_register_success.json'),
  registerSuccessVerdictIsEmpty('dop_native_register_success_verdict_is_empty.json'),
  registerUnqualified('dop_native_register_unqualified.json'),
  registerDuplicate('dop_native_register_duplicate.json'),
  registerWithLimitExceeded('dop_native_register_limit_exceeded.json'),
  registerFail('dop_native_register_fail.json'),
  registerExistingRecord('dop_native_register_existing_record.json'),
  registerExpiredPToken('dop_native_register_expired_ptoken.json'),
  registerInvalidPToken('dop_native_register_invalid_ptoken.json'),
  registerDuplicateReject('dop_native_register_duplicate_reject.json');

  final String value;

  const MockTestDOPNativeIntroductionUseCase(this.value);
}

String getMockDOPNativeIntroductionFileNameByCase(MockTestDOPNativeIntroductionUseCase mockCase) {
  return mockCase.value;
}
