import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../data/response/dop_native/dop_native_bootstrap_auth_settings_entity.dart';
import '../../../../../data/response/dop_native/dop_native_bootstrap_entity.dart';
import '../../../../../data/response/dop_native/dop_native_data_entity.dart';
import '../../../../../data/response/dop_native/dop_native_registration_campaign_entity.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../../../util/extension.dart';
import '../../../../../util/url_launcher_uri_wrapper.dart';
import '../../../../logging/evo_logging_event.dart';
import '../../../dialogs/input_phone_number/mock/mock_dop_native_bootstrap_auth_setting_use_case.dart';
import '../../../util/dop_functions.dart';
import '../mock/mock_dop_native_introduction_use_case.dart';

part 'dop_native_introduction_state.dart';

class DOPNativeIntroductionCubit extends CommonCubit<DOPNativeIntroductionState>
    with LogErrorMixin {
  final DOPNativeRepo dopNativeRepo;
  final AppState appState;

  DOPNativeIntroductionCubit({
    required this.dopNativeRepo,
    required this.appState,
  }) : super(DOPNativeIntroductionInitial());

  Future<void> initWithUniqueToken(String uniqueToken) async {
    appState.dopNativeState.uniqueToken = uniqueToken;
    getBootstrap(uniqueToken);

    registrationCampaign();
  }

  Future<void> initToRegistrationCampaign({String? campaignCode}) async {
    emit(DOPNativeIntroductionLoading());

    final DOPNativeRegistrationCampaignEntity entity =
        await registrationCampaign(campaignCode: campaignCode);

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(RegistrationCampaignSuccess(entity));
    } else {
      emit(RegistrationCampaignFail(error: ErrorUIModel.fromEntity(entity)));
    }
  }

  @visibleForTesting
  Future<DOPNativeRegistrationCampaignEntity> registrationCampaign({String? campaignCode}) async {
    final String leadSource = appState.deepLinkSharedData.leadSource;

    final DOPNativeRegistrationCampaignEntity entity = await dopNativeRepo.registrationCampaign(
      campaignCode: campaignCode,
      leadSource: leadSource,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeIntroductionFileNameByCase(
            MockTestDOPNativeIntroductionUseCase.registrationCampaign),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      saveInfoCampaignToAppState(
        campaignCode: entity.campaignCode,
        source: entity.source,
      );
    }
    return entity;
  }

  void continueDOEJourney(DOPNativeDataEntity entity) {
    saveInfoToAppState(entity);
    dopUtilFunction.setDOPNativeAccessTokenHeader(entity.accessToken);
  }

  @visibleForTesting
  void saveInfoToAppState(DOPNativeDataEntity entity) {
    appState.dopNativeState.dopNativeAccessToken = entity.accessToken;
    appState.dopNativeState.phoneNumber = entity.phoneNumber;
    appState.dopNativeState.uniqueToken = entity.uniqueToken;
    appState.dopNativeState.dopApplicationState = entity.dopApplicationState;

    saveInfoCampaignToAppState(
      campaignCode: entity.campaignCode,
      source: entity.source,
    );
  }

  @visibleForTesting
  void saveInfoCampaignToAppState({
    required String? campaignCode,
    required String? source,
  }) {
    appState.dopNativeState.campaignCode = campaignCode;
    appState.dopNativeState.source = source;
  }

  Future<void> requestToDialPhoneNumber(String phoneNumber) async {
    emit(DOPNativeIntroductionLoading());
    final Uri uri = phoneNumber.uriForDialNumber();

    try {
      final bool canLaunchResult = await urlLauncherWrapper.canLaunchUrl(uri);
      if (!canLaunchResult) {
        emit(DialPhoneNumberCanNotLaunch());
        return;
      }

      final bool launchResult = await urlLauncherWrapper.launchUrl(uri);
      if (launchResult) {
        emit(DialPhoneNumberLaunchSucceed());
      } else {
        emit(DialPhoneNumberLaunchFailed());
      }
    } on PlatformException catch (e) {
      emit(DialPhoneNumberLaunchError());
      logException(
        eventType: EvoEventType.launchUrl,
        methodName: 'requestToDialPhoneNumber',
        exception: e,
      );
    }
  }

  Future<void> getBootstrap(
    String? uniqueToken, {
    VoidCallback? logEvent,
    VoidCallback? hideInputPhoneDialogLoading,
  }) async {
    emit(DOPNativeIntroductionLoading());

    final DOPNativeBootstrapEntity entityBootstrapInfo = await getBootstrapInfo(uniqueToken);
    if (entityBootstrapInfo.statusCode != CommonHttpClient.SUCCESS) {
      emit(DOPNativeGetBootstrapFailed(
        error: ErrorUIModel.fromEntity(entityBootstrapInfo),
        hideInputPhoneDialogLoading: hideInputPhoneDialogLoading,
      ));
      return;
    }

    final DOPNativeBootstrapAuthSettingsEntity entityBootstrapAuthSettings =
        await getBootstrapAuthSettings(uniqueToken);

    if (entityBootstrapAuthSettings.statusCode == CommonHttpClient.SUCCESS) {
      logEvent?.call();

      /// Saving bootstrap auth settings to app state
      appState.dopNativeState.bootstrapAuthSettings = entityBootstrapAuthSettings;

      /// for id_card_auth, BE will return temporary access token (with less permission and expired time)
      /// we need to save it to app state and then going to next step
      final DOPNativeAuthType? authType =
          entityBootstrapAuthSettings.authType?.toDOPNativeAuthType();
      if (authType == DOPNativeAuthType.idCard) {
        appState.dopNativeState.dopNativeAccessToken = entityBootstrapAuthSettings.accessToken;
        dopUtilFunction.setDOPNativeAccessTokenHeader(entityBootstrapAuthSettings.accessToken);
      }

      emit(DOPNativeBootstrapAuthSettingsLoaded(
          entityBootstrapAuthSettings, hideInputPhoneDialogLoading));
      return;
    }

    emit(DOPNativeGetBootstrapFailed(
      error: ErrorUIModel.fromEntity(entityBootstrapAuthSettings),
      hideInputPhoneDialogLoading: hideInputPhoneDialogLoading,
    ));
  }

  @visibleForTesting
  Future<DOPNativeBootstrapEntity> getBootstrapInfo(String? uniqueToken) async {
    final DOPNativeBootstrapEntity entity = await dopNativeRepo.getBootstrapInfo(
      token: uniqueToken,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeBootstrapAuthSetting(
            MockDOPNativeBootstrapAuthSetting.getBootstrapAfterInputtingPhoneNumber),
      ),
    );

    return entity;
  }

  @visibleForTesting
  Future<DOPNativeBootstrapAuthSettingsEntity> getBootstrapAuthSettings(String? uniqueToken) async {
    final DOPNativeBootstrapAuthSettingsEntity entity =
        await dopNativeRepo.getBootstrapAuthSettings(
      token: uniqueToken,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeBootstrapAuthSetting(
            MockDOPNativeBootstrapAuthSetting.getBootstrapAuthTypeOTP),
      ),
    );

    return entity;
  }
}
