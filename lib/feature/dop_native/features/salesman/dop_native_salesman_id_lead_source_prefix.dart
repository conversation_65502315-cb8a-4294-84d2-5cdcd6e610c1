import '../../../../data/response/dop_native/dop_native_application_state_entity.dart';
import '../../../../prepare_for_app_initiation.dart';

enum DOPNativeSalesmanIDLeadSourcePrefix {
  viettelStore('VST');

  final String value;

  const DOPNativeSalesmanIDLeadSourcePrefix(this.value);
}

String getPrefixByLeadSource(String? leadSource) {
  switch (leadSource) {
    case LeadSource.viettelStore:
      return DOPNativeSalesmanIDLeadSourcePrefix.viettelStore.value;
    default:
      return '';
  }
}

String getSalesmanIdWithPrefix(String id) {
  final AppState appState = getIt.get<AppState>();
  final String? leadSource = appState.dopNativeState.dopApplicationState?.flowConfig?.leadSource;

  return '${getPrefixByLeadSource(leadSource)}$id';
}
