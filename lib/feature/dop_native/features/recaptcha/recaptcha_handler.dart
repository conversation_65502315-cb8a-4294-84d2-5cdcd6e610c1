import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:recaptcha_enterprise_flutter/recaptcha_action.dart';
import 'package:recaptcha_enterprise_flutter/recaptcha_enterprise.dart';

import '../../../../flavors/flavors_type.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../logging/evo_logging_event.dart';

enum RecaptchaClientState { notInitialized, success, failure }

class RecaptchaHandler {
  /// Non-Production (Staging & UAT) Site Keys
  @visibleForTesting
  static const String androidNonProductionSiteKey = '6LcsxO8pAAAAAB8Dfjq71IziOxV4bCrc5itXz1Xd';
  @visibleForTesting
  static const String iosNonProductionSiteKey = '6LfZQ_ApAAAAAKc8dkiY8i8wURdVTr_W5aM8w2B9';

  /// Production Site Keys.
  /// Refer: Ticket https://trustingsocial1.atlassian.net/browse/EMA-2395
  @visibleForTesting
  static const String androidProductionSiteKey = '6Ld1xfQpAAAAAOUrKyeZKQPHUiEDrdC7HP2hB6Hk';
  @visibleForTesting
  static const String iosProductionSiteKey = '6LeumPQpAAAAAJrY2cEJtf_fUINn-tp_ZjSLwCtH';

  LoggingRepo get loggingRepo => getIt.get<LoggingRepo>();

  /// Apple App Store - Evo App ID
  @visibleForTesting
  String get getSiteKey {
    final String flavorName = FlavorConfig.instance.flavor;
    if (flavorName == FlavorType.prod.name) {
      return getIt.get<DevicePlatform>().isAndroid()
          ? androidProductionSiteKey
          : iosProductionSiteKey;
    } else {
      return getIt.get<DevicePlatform>().isAndroid()
          ? androidNonProductionSiteKey
          : iosNonProductionSiteKey;
    }
  }

  @visibleForTesting
  RecaptchaClientState clientState = RecaptchaClientState.notInitialized;

  Future<RecaptchaClientState> initClient() async {
    if (clientState == RecaptchaClientState.success) {
      return clientState;
    }

    bool result = false;

    try {
      result = await RecaptchaEnterprise.initClient(getSiteKey, timeout: 10000);
      clientState = result ? RecaptchaClientState.success : RecaptchaClientState.failure;
    } on PlatformException catch (err) {
      clientState = RecaptchaClientState.failure;
      loggingRepo.logErrorEvent(
        errorType: EvoEventType.dopRecaptcha.name,
        args: <String, dynamic>{'step': 'init', 'code': err.code, 'message': err.message},
      );

      commonLog('Caught platform exception on init: $err Code: ${err.code} Message ${err.message}');
    } on Exception catch (err) {
      clientState = RecaptchaClientState.failure;
      loggingRepo.logErrorEvent(
        errorType: EvoEventType.dopRecaptcha.name,
        args: <String, dynamic>{'step': 'init', 'message': err.toString()},
      );
      commonLog('Caught exception on init: $err');
    }

    return clientState;
  }

  Future<String?> execute(
      {required RecaptchaAction recaptchaAction, double timeout = 10000}) async {
    String? token;
    try {
      token = await RecaptchaEnterprise.execute(recaptchaAction, timeout: timeout);
    } on PlatformException catch (err) {
      commonLog(
          'Caught platform exception on execute: $err Code: ${err.code} Message ${err.message}');
      loggingRepo.logErrorEvent(
        errorType: EvoEventType.dopRecaptcha.name,
        args: <String, dynamic>{'step': 'execute', 'code': err.code, 'message': err.message},
      );
    } on Exception catch (err) {
      commonLog('Caught exception on execute: $err');
      loggingRepo.logErrorEvent(
        errorType: EvoEventType.dopRecaptcha.name,
        args: <String, dynamic>{'step': 'execute', 'message': err.toString()},
      );
    }
    return token;
  }
}
