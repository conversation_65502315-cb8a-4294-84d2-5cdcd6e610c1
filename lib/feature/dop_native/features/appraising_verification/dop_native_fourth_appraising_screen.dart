import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../base/cubit/dop_native_application_state_cubit.dart';
import '../../base/dop_native_page_state_base.dart';
import '../../dop_native_constants.dart';
import '../../resources/dop_native_images.dart';
import '../../resources/dop_native_resources.dart';
import '../../resources/dop_native_ui_strings.dart';
import '../../util/dop_native_submit_status_polling/dop_native_submit_status_polling_impl.dart';
import '../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../widgets/dop_native_status_widget.dart';
import 'cubit/dop_native_appraising_verification_cubit.dart';
import 'cubit/dop_native_appraising_verification_state.dart';

class DOPNativeFourthApprisingScreen extends PageBase {
  final DOPNativeAppraisingVerificationCubit? appraisingCubit;
  final DOPNativeApplicationStateCubit? applicationStateCubit;

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeFourthAppraisingScreen.name,
    );
  }

  const DOPNativeFourthApprisingScreen({
    super.key,
    this.appraisingCubit,
    this.applicationStateCubit,
  });

  @override
  State<DOPNativeFourthApprisingScreen> createState() => DOPNativeFourthApprisingScreenState();

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeFourthAppraisingScreen.routeName);

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;
}

@visibleForTesting
class DOPNativeFourthApprisingScreenState
    extends DOPNativePageStateBase<DOPNativeFourthApprisingScreen> {
  // polling ech 5 seconds, refer: https://trustingsocial1.atlassian.net/wiki/spaces/Mobile/pages/3907354812/EMA-5942+Dive-in+Mobile+Optimize+MWG+offline+flow
  static const int pollingIntervalInMilliseconds = 5000;

  late final DOPNativeAppraisingVerificationCubit appraisingCubit = widget.appraisingCubit ??
      DOPNativeAppraisingVerificationCubit(
        dopNativeRepo: getIt.get<DOPNativeRepo>(),
        dopNativeSubmitStatusPolling: DOPNativeSubmitStatusPollingImpl(
          intervalDuration: const Duration(
            milliseconds: pollingIntervalInMilliseconds,
          ),
        ),
      );

  @override
  DOPNativeApplicationStateCubit get dopNativeApplicationStateCubit =>
      widget.applicationStateCubit ?? super.dopNativeApplicationStateCubit;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      appraisingCubit.getAppraisingStatus();
    });
  }

  @override
  void dispose() {
    appraisingCubit.cancelPollingAppraisingStatus();
    super.dispose();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: BlocProvider<DOPNativeAppraisingVerificationCubit>(
        create: (_) => appraisingCubit,
        child: BlocListener<DOPNativeAppraisingVerificationCubit,
                DOPNativeAppraisingVerificationState>(
            listenWhen: (
              DOPNativeAppraisingVerificationState previous,
              DOPNativeAppraisingVerificationState current,
            ) {
              return current is AppraisingVerificationSuccessState ||
                  current is AppraisingVerificationFailedState;
            },
            listener: (BuildContext context, DOPNativeAppraisingVerificationState state) {
              handleDOPNativeAppraisingVerificationStateChanged(state);
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: DOPNativeStatusWidget(
                icon: DOPNativeImages.icSandClock,
                title: DOPNativeStrings.dopNativeFourthAppraisingTitle,
                description: DOPNativeStrings.dopNativeFourthAppraisingDesc,
                iconHeight: EvoUiUtils().calculateVerticalSpace(
                  context: context,
                  heightPercentage: DOPNativeConstants.statusIconHeightPercentage,
                ),
              ),
            )),
      ),
    );
  }

  @visibleForTesting
  void handleDOPNativeAppraisingVerificationStateChanged(
    DOPNativeAppraisingVerificationState state,
  ) {
    if (state is AppraisingVerificationSuccessState) {
      dopNativeApplicationStateCubit.getApplicationState();
      return;
    }

    if (state is AppraisingVerificationFailedState) {
      handleDopEvoApiError(state.error);
      return;
    }
  }
}
