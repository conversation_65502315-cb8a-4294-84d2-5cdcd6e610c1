import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../data/response/user_app_browsing_info_entity.dart';

abstract class EvoLocalStorageHelper implements CommonLocalStorageHelper {
  Future<Locale> setLocale(String languageCode);

  Future<Locale> getLocale();

  Future<void> changeLanguage(BuildContext context, String selectedLanguageCode);

  Future<void> setLatestVersionIgnore(String? latestVersion);

  Future<String?> getLatestVersionIgnore();

  Future<void> setDeviceId(String? deviceId);

  Future<String?> getDeviceId();

  Future<void> setUserPhoneNumber(String? userPhoneNumber);

  Future<String?> getUserPhoneNumber();

  Future<void> setRefreshToken(String? refreshToken);

  Future<String?> getRefreshToken();

  Future<String?> getBiometricToken();

  Future<void> setBiometricToken(String? biometricToken);

  Future<bool> isEnableBiometricAuthenticator();

  Future<void> setBiometricAuthenticator(bool enable);

  Future<void> clearAllUserData();

  Future<String?> getTimeShowBiometric();

  Future<void> saveTimeShowBiometric(String timeShow);

  Future<void> setAccessToken(String? accessToken);

  Future<String?> getAccessToken();

  Future<void> setDeviceToken(String? deviceToken);

  Future<String?> getDeviceToken();

  ///true: MUST show private policy screen
  Future<void> setDecreeConsentStatus(bool status);

  Future<bool> getDecreeConsentStatus();

  Future<void> setNewDevice(bool status);

  Future<bool> isNewDevice();

  Future<void> setCreditLimitMasked(bool status);

  Future<bool?> isCreditLimitMasked();

  Future<void> setChatwootIdForNonEvoUser(String chatwootId);

  Future<String?> getChatwootIdForNonEvoUser();

  Future<void> setEnablePosLimitWarning(bool isEnable);

  Future<bool?> getEnablePosLimitWarning();

  Future<bool?> getConsentAgreed();

  Future<void> setConsentAgreed(bool isAgreed);

  Future<UserAppBrowsingInfoEntity?> getUserAppBrowsingInfoEntity();

  Future<void> setUserAppBrowsingInfoEntity(UserAppBrowsingInfoEntity? entity);

  Future<bool?> getValueToCheckReviewPopupShown();

  Future<void> setValueToCheckReviewPopupShown(bool isShown);

  /// Delete all keys except the [EvoSecureStorageHelperImpl.deviceId] key
  /// Because we need this key to check whether the user deleted the application and reinstalled it or not
  /// to delete all data in Keychain on iOS
  /// So, we do not use [await delete(key: deviceId)]
  Future<void> deleteAllSecureStorageData();

  Future<void> setShowedRequestNotifyPermission(bool status);

  Future<bool> hasShowRequestNotifyPermission();

  /// Refer ticket: https://trustingsocial1.atlassian.net/browse/EMA-5294
  /// Save time as milliseconds to storage
  Future<void> setLastTimeRequest3DSCardActivation(int? time);

  // Refer ticket: https://trustingsocial1.atlassian.net/browse/EMA-5294
  Future<int?> getLastTimeRequest3DSCardActivation();
}
