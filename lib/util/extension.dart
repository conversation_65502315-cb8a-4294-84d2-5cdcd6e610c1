import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/path.dart' as path_lib;
import 'package:flutter_common_package/util/utils.dart';

import '../feature/payment/payment_shared_data.dart';
import '../feature/payment/widget/amount_input/ui_config/currency_display_type.dart';
import '../model/promotion_expired_date.dart';
import '../model/running_out_time_info.dart';
import '../prepare_for_app_initiation.dart';
import '../resources/global.dart';
import '../resources/ui_strings.dart';
import 'functions.dart';
import 'promotion/promotion_constants.dart';

extension DateTimeExt on DateTime {
  /// The campaign which start in the next x days (x is [configDisplayCampaignInDays])
  /// Ex: A campaign was scheduled to run on 06/02, with [configDisplayCampaignInDays] = 5 days
  /// on 01/02 the app will shown campaign with status “Bắt đầu vào 06/02”
  bool isCampaignComing({
    DateTime? today,
    int configDisplayCampaignInDays = PromotionConstants.configDisplayCampaignInDays,
  }) {
    final DateTime td = today ?? DateTime.now();
    if (isBefore(td) || this == today) {
      return false;
    }

    if (isAfter(td) && subtract(Duration(days: configDisplayCampaignInDays)).isAfter(td)) {
      return false;
    }

    return true;
  }

  PromotionExpiredDate? toPromotionExpiredDate({
    DateTime? compareDate,
    int configRunningOutTimeInHours = PromotionConstants.configRunningOutTimeInHours,
    int configHotTimeInHours = PromotionConstants.configHotTimeInHours,
  }) {
    final DateTime td = compareDate ?? DateTime.now();
    final int differenceHours = getDifferenceHours(td);

    // Other case: Display "HSD: dd/MM - dd/MM"
    if (differenceHours > configRunningOutTimeInHours) {
      return const PromotionExpiredDate(status: ExpiredDateStatus.beforeRunningOut);
    }

    if (isBefore(td)) {
      return const PromotionExpiredDate(status: ExpiredDateStatus.expired);
    }

    final RunningOutTimeInfo? info = getRunningOutExpiredTime(today: td);
    if (info == null) {
      return const PromotionExpiredDate(status: ExpiredDateStatus.expired);
    }

    final bool isHotVoucher =
        info.day != null && info.day == 0 && info.hour != null && info.hour! < configHotTimeInHours;
    if (isHotVoucher) {
      return PromotionExpiredDate(status: ExpiredDateStatus.hotTime, runningOutTimeInfo: info);
    }

    return PromotionExpiredDate(status: ExpiredDateStatus.runningOut, runningOutTimeInfo: info);
  }

  RunningOutTimeInfo? getRunningOutExpiredTime({DateTime? today}) {
    final DateTime td = today ?? DateTime.now();

    // Don't calculate when the campaign is expired
    if (isBefore(td)) {
      return null;
    }

    /// Example: today is 2023, Feb 03, 14:00:02
    /// expired date is 2023, Feb 03, 15:00:01
    final int differenceHours = getDifferenceHours(td); // differenceHours = 1
    final int differenceMinutes = getDifferenceMinutes(td); // differenceMinutes = 60
    final int differenceSeconds = getDifferenceSeconds(td); //differenceSeconds = 3601

    final int days = (differenceHours / 24).truncate(); // days = 0
    final int hours = (differenceHours - (days * 24)).truncate(); // hours = 1
    final int minutes = (differenceMinutes - (differenceHours * 60)).truncate(); // minutes = 0
    final int seconds = (differenceSeconds - (differenceMinutes * 60)).truncate(); // seconds = 1

    return RunningOutTimeInfo(day: days, hour: hours, minute: minutes, second: seconds);
  }

  int getDifferenceHours(DateTime dt) {
    return difference(dt).inHours;
  }

  int getDifferenceMinutes(DateTime dt) {
    return difference(dt).inMinutes;
  }

  int getDifferenceSeconds(DateTime dt) {
    return difference(dt).inSeconds;
  }

  String toStringFormatDate(String format) {
    return toStringFormat(format);
  }

  //today: Hôm nay HH:MM
  //yesterday: Hôm qua HH:MM
  //other: DD/MM HH:MM format 24h
  String toNotificationDateTimeFormat({DateTime? today, String? dateFormat}) {
    final DateTime td = today ?? DateTime.now();
    final int differenceDays = getDifferenceDays(td);

    if (differenceDays == 0) {
      return '${EvoStrings.todayText} - ${toStringFormat('H:mm')}';
    } else if (differenceDays == -1) {
      return '${EvoStrings.yesterdayText} - ${toStringFormat('H:mm')}';
    } else {
      return toStringFormat('${dateFormat ?? 'dd/MM'} - H:mm');
    }
  }

  // format: H:mm dd/MM/yyyy
  // E.g: 15:30 14/02/2025
  String toTransactionDateTimeFormat({DateTime? today, String? dateFormat}) {
    return toStringFormat('H:mm  ${dateFormat ?? 'dd/MM/yyyy'}');
  }

  /// Check a time is before now in x minutes
  /// E.g:
  /// [nowTime]: '2024-11-14 10:47:13.847327'
  /// [previousTime]: '2024-11-14 10:45:13.847327'
  /// [minutes]: 5
  /// result: true
  bool isBeforeInXMinutes({
    required DateTime previousTime,
    required int minutes,
  }) {
    if (!previousTime.isBefore(this)) {
      return false;
    }

    final DateTime xMinutesAgo = subtract(Duration(minutes: minutes));
    return xMinutesAgo == previousTime || xMinutesAgo.isBefore(previousTime);
  }
}

extension FormatPhoneNumber on String? {
  String getFormatVietnamesePhoneNumber() {
    if (this == null || this!.isEmpty || (this!.contains('+') && this!.lastIndexOf('+') != 0)) {
      return '';
    }
    String tempPhoneNumber = this!.contains('+') ? this!.replaceAll('+', '') : this!;
    if (CommonValidator().validatePhone(tempPhoneNumber)) {
      tempPhoneNumber = tempPhoneNumber.replaceFirst('84', '0');
      return tempPhoneNumber.applyStringFormat(
          prefixGroup: 4, stringFormatType: StringFormatType.phone);
    } else {
      return '';
    }
  }
}

extension FilesExt on FileSystemEntity {
  bool get isHidden => path_lib.basename(path).startsWith('.');

  String get fileName => path_lib.basename(path);
}

extension RunningOutTimeInfoExt on RunningOutTimeInfo {
  String getFormatRunningOutExpiredDateTitle() {
    if (day == null && hour == null && minute == null) {
      return '';
    }

    final bool hasDayData = day != null && day != 0;
    final bool hasHourData = hour != null && hour != 0;
    final bool hasMinuteData = minute != null && minute != 0;

    if (hasDayData && hasHourData) {
      /// Còn x ngày, x giờ
      return '${EvoStrings.promotionRunningOut} $day ${EvoStrings.promotionDay}, $hour ${EvoStrings.promotionHour}';
    }

    if (hasDayData) {
      /// Còn x ngày
      return '${EvoStrings.promotionRunningOut} $day ${EvoStrings.promotionDay}';
    }

    if (hasHourData && hasMinuteData) {
      /// Còn x giờ, x phút
      return '${EvoStrings.promotionRunningOut} $hour ${EvoStrings.promotionHour}, $minute ${EvoStrings.promotionMinute}';
    }

    if (hasHourData) {
      /// Còn x giờ
      return '${EvoStrings.promotionRunningOut} $hour ${EvoStrings.promotionHour}';
    }

    if (hasMinuteData) {
      /// Còn x phút
      return '${EvoStrings.promotionRunningOut} $minute ${EvoStrings.promotionMinute}';
    }

    /// If the remaining time is less than 1 minute, just display 'Còn 1 phút'
    return '${EvoStrings.promotionRunningOut} 1 ${EvoStrings.promotionMinute}';
  }
}

extension GlobalKeyExtension on GlobalKey {
  /// Refer to: https://stackoverflow.com/a/71568630
  Offset? get globalOffset {
    final RenderObject? renderObject = currentContext?.findRenderObject();
    if (renderObject is RenderBox) {
      final Offset offset = renderObject.localToGlobal(Offset.zero);
      return offset;
    } else {
      return null;
    }
  }
}

extension CurrencyDisplayTypeEx on CurrencyDisplayType {
  int getAmountMaxLength() {
    final PaymentConfigModel paymentConfig = getIt<AppState>().paymentSharedData.paymentConfig;
    final int maxLengthAmountWithVietnameseCurrency = evoUtilFunction
        .evoFormatCurrency(
          paymentConfig.maxOrderAmount,
          currencySymbol: vietNamCurrencySymbol,
        )
        .length;
    final int maxLengthAmount =
        evoUtilFunction.evoFormatCurrency(paymentConfig.maxOrderAmount).length;

    switch (this) {
      case CurrencyDisplayType.text:
        return maxLengthAmountWithVietnameseCurrency;

      case CurrencyDisplayType.icon:
      default:
        return maxLengthAmount;
    }
  }

  String getCurrencySuffix() {
    switch (this) {
      case CurrencyDisplayType.text:
        return vietNamCurrencySymbol;

      case CurrencyDisplayType.icon:
      default:
        return '';
    }
  }
}

extension StringEx on String {
  String getLastCharacters(int count) {
    if (length <= count) {
      return this;
    }

    final int lastIndex = length;
    return substring(lastIndex - count, lastIndex);
  }

  /// Use for sending email: https://stackoverflow.com/a/72812644/10262450
  Uri uriForSendMail({String? subject, String? body}) {
    return Uri(
      scheme: 'mailto',
      path: this,
      query: evoUtilFunction.encodeQueryParameters(
        <String, String>{'subject': subject ?? '', 'body': body ?? ''},
      ),
    );
  }

  Uri uriForDialNumber() {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: this,
    );

    return launchUri;
  }
}

extension DiacriticsAwareString on String {
  @visibleForTesting
  static const String diacritics =
      'ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚÝàáâãèéêìíòóôõùúýĂăĐđĨĩŨũƠơƯưẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặẸẹẺẻẼẽẾếỀềỂểỄễỆệỈỉỊịỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợỤụỦủỨứỪừỬửỮữỰựỲỳỴỵỶỷỸỹ';
  @visibleForTesting
  static const String nonDiacritics =
      'AAAAEEEIIOOOOUUYaaaaeeeiioooouuyAaDdIiUuOoUuAaAaAaAaAaAaAaAaAaAaAaAaEeEeEeEeEeEeEeEeIiIiOoOoOoOoOoOoOoOoOoOoOoOoUuUuUuUuUuUuUuYyYyYyYy';

  String get removeDiacritics {
    final Map<String, String> diacriticsMap = <String, String>{};
    for (int i = 0; i < diacritics.length; i++) {
      diacriticsMap[diacritics[i]] = nonDiacritics[i];
    }
    String s = '';
    for (final String char in split('')) {
      if (diacriticsMap.containsKey(char)) {
        s += diacriticsMap[char]!;
      } else {
        s += char;
      }
    }
    return s;
  }
}

extension MapConversion on Map<String, dynamic>? {
  Map<String, String>? toStringMap() {
    final Map<String, dynamic>? map = this;

    if (map == null) {
      return null;
    }

    final Map<String, String> result = <String, String>{};
    map.forEach((String key, dynamic value) {
      if (value != null) {
        result[key] = value.toString();
      }
    });

    return result;
  }

  Map<String, Object> toNonNullable() {
    if (this == null) {
      return <String, Object>{};
    }
    return this!.map(
      (String key, Object? value) => MapEntry<String, Object>(key, value ?? ''),
    );
  }
}

extension IterableExtension<T> on Iterable<T> {
  Iterable<T> intersperse(T element) {
    return _intersperse(element, this);
  }

  Iterable<T> _intersperse(T element, Iterable<T> iterable) sync* {
    final Iterator<T> iterator = iterable.iterator;
    if (iterator.moveNext()) {
      yield iterator.current;
      while (iterator.moveNext()) {
        yield element;
        yield iterator.current;
      }
    }
  }
}
