import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';

import '../../prepare_for_app_initiation.dart';

WebLinkUtils get webLinkUtils => getIt.get<WebLinkUtils>();

class WebLinkUtils {
  String? getUniqueTokenFromDOPWebUrl(String? url) {
    if (url == null || isDOPLink(url) == false) {
      return null;
    }
    final Uri? uri = Uri.tryParse(url);
    if (uri == null || uri.pathSegments.isEmpty) {
      return null;
    }
    return uri.pathSegments.first;
  }

  bool isDOPLink(String? url) {
    if (url == null) {
      return false;
    }

    final List<String> dopSchemeURLs = getDopSchemeURLs();

    final String? result = dopSchemeURLs.firstWhereOrNull((String item) => url.contains(item));
    return result != null;
  }

  /// Refer: https://trustingsocial.slack.com/archives/C0446DCNV70/p1702008326142369
  @visibleForTesting
  List<String> getDopSchemeURLs() {
    return <String>[
      /// Staging
      'https://staging-tpbank.avay.vn/',

      /// UAT
      'https://release-tpbank.avay.vn/',

      /// PROD
      'https://evocard.tpb.vn/',

      /// Change URL for DOP WebView
      /// Refer: https://trustingsocial.slack.com/archives/C04GEJCR6TY/p1704255047176019
      'https://release-tpbank.tsengineering.io',
      'https://staging-tpbank.tsengineering.io',
    ];
  }
}
