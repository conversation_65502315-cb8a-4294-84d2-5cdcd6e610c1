import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../data/response/maintenance_info_entity.dart';
import '../../feature/maintenance/maintenance_handler.dart';

class MaintenanceInterceptor extends Interceptor {
  final MaintenanceHandler _maintenanceHandler;

  MaintenanceInterceptor(
    this._maintenanceHandler,
  );

  @override
  void onResponse(Response<dynamic> response, ResponseInterceptorHandler handler) {
    try {
      if (response.statusCode == CommonHttpClient.SUCCESS &&
          response.data is Map<String, dynamic>) {
        final Map<String, dynamic> data = response.data as Map<String, dynamic>;
        final String? verdict = data['verdict'] as String?;

        if (verdict == MaintenanceInfoEntity.verdictMaintenance) {
          final dynamic maintainData = data['data'];

          if (maintainData is Map<String, dynamic>) {
            final MaintenanceInfoEntity maintenanceInfo =
                MaintenanceInfoEntity.fromJson(maintainData);

            _maintenanceHandler.emitMaintenance(maintenanceInfo);

            return;
          } else {
            commonLog('Invalid maintenance data format');
          }
        }
      }
    } on Exception catch (e, stackTrace) {
      commonLog('Error parsing maintenance info: $e\n$stackTrace');
    }

    super.onResponse(response, handler);
  }
}
