import 'package:jwt_decoder/jwt_decoder.dart';

import 'jwt_helper.dart';

class EvoJwtHelperImpl extends JwtHelper {
  @override
  Map<String, dynamic>? decode(String token) {
    return JwtDecoder.tryDecode(token);
  }

  @override
  bool isExpired(String token) {
    return JwtDecoder.isExpired(token);
  }

  @override
  bool isCanUse(String? token, {int bufferTimeExpiredInSec = 60}) {
    return token != null && JwtDecoder.getRemainingTime(token).inSeconds >= bufferTimeExpiredInSec;
  }
}
