// ignore_for_file: avoid_catches_without_on_clauses

import 'dart:convert';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../feature/logging/evo_logging_event.dart';
import '../response/remote_config_biometric_entity.dart';
import '../response/remote_config_common_entity.dart';
import '../response/remote_config_feature_toggle_entity.dart';
import 'ts_remote_config.dart';

class FirebaseRemoteConfigImpl with LogErrorMixin implements RemoteConfigRepo {
  final FirebaseRemoteConfig firebaseRemoteConfig;

  static const String featureToggleKey = 'feature_toggle';
  static const String biometricConfig = 'biometric_config';
  static const String commonConfig = 'common_config';

  FirebaseRemoteConfigImpl(this.firebaseRemoteConfig);

  @override
  Future<void> initConfig() async {
    try {
      await firebaseRemoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(seconds: 10),
          minimumFetchInterval: const Duration(hours: 12),
        ),
      );
      RemoteConfigValue(null, ValueSource.valueStatic);
    } catch (e) {
      logException(
        eventType: EvoEventType.firebaseRemoteConfig,
        methodName: 'initConfig',
        exception: e,
      );
      commonLog(e);
    }
  }

  @override
  Future<void> fetch() async {
    try {
      await firebaseRemoteConfig.fetchAndActivate();
    } catch (e) {
      logException(
        eventType: EvoEventType.firebaseRemoteConfig,
        methodName: 'fetch',
        exception: e,
      );
      commonLog(e);
    }
  }

  @override
  Future<RemoteConfigBiometricEntity> getBiometricConfigs() async {
    try {
      final String remoteConfigJsonStr = firebaseRemoteConfig.getString(biometricConfig);

      commonLog('RemoteConfigFeature: $biometricConfig: $remoteConfigJsonStr');

      final Map<String, dynamic> decodedJson =
          json.decode(remoteConfigJsonStr) as Map<String, dynamic>;
      return RemoteConfigBiometricEntity.fromJson(decodedJson);
    } catch (e) {
      commonLog(e);
      logException(
        eventType: EvoEventType.firebaseRemoteConfig,
        methodName: 'getBiometricConfigs',
        exception: e,
      );
      return RemoteConfigBiometricEntity();
    }
  }

  @override
  Future<RemoteConfigFeatureToggleEntity> getFeatureToggle() async {
    try {
      final String remoteConfigJsonStr = firebaseRemoteConfig.getString(featureToggleKey);

      commonLog('RemoteConfigFeature: $featureToggleKey: $remoteConfigJsonStr');

      final Map<String, dynamic> decodedJson =
          json.decode(remoteConfigJsonStr) as Map<String, dynamic>;
      return RemoteConfigFeatureToggleEntity.fromJson(decodedJson);
    } catch (e) {
      commonLog(e);
      logException(
        eventType: EvoEventType.firebaseRemoteConfig,
        methodName: 'getFeatureToggle',
        exception: e,
      );
      return RemoteConfigFeatureToggleEntity();
    }
  }

  @override
  Future<RemoteConfigCommonEntity> getCommonConfig() async {
    try {
      final String remoteConfigJsonStr = firebaseRemoteConfig.getString(commonConfig);

      commonLog('RemoteConfigFeature: $commonConfig: $remoteConfigJsonStr');

      final Map<String, dynamic> decodedJson =
          json.decode(remoteConfigJsonStr) as Map<String, dynamic>;
      return RemoteConfigCommonEntity.fromJson(decodedJson);
    } catch (e) {
      commonLog(e);
      logException(
        eventType: EvoEventType.firebaseRemoteConfig,
        methodName: 'getCommonConfig',
        exception: e,
      );
      return RemoteConfigCommonEntity();
    }
  }
}
