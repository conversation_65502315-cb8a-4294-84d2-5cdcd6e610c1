import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import '../response/private_policy_entity.dart';

abstract class DecreeConsentRepo {
  Future<PrivacyPolicyEntity> checkDecreeConsent({MockConfig? mockConfig});

  Future<BaseEntity> createDecreeConsent(int? decreeVersionId, {MockConfig? mockConfig});

  Future<PrivacyPolicyEntity> latestDecreeConsent({MockConfig? mockConfig});
}
