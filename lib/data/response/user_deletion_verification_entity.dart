import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class UserDeletionVerificationEntity extends BaseEntity {
  final String? challengeType;
  final List<ReasonEntity>? reasons;
  final String? sessionToken;

  static const String verdictSuccess = 'success';
  static const String verdictPinLimitExceed = 'limit_exceeded';

  /// status code [CommonHttpClient.BAD_REQUEST]
  static const String verdictInvalidCredential = 'invalid_credential';
  static const String verdictOneLastTry = 'one_last_try';

  ///user has been locked to delete account
  static const String verdictLockedResource = 'locked_resource';

  /// Delete account's session token is expired, return status code [CommonHttpClient.INVALID_TOKEN]
  static const String verdictExpiredDeleteAccountSession = 'expired_delete_account_session';

  UserDeletionVerificationEntity({
    this.challengeType,
    this.reasons,
    this.sessionToken,
  });

  UserDeletionVerificationEntity.unserializable()
      : challengeType = null,
        reasons = null,
        sessionToken = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  UserDeletionVerificationEntity.fromBaseResponse(BaseResponse super.response)
      : challengeType = response.data?['challenge_type'] as String?,
        reasons = (response.data?['reasons'] as List<dynamic>?)
            ?.map((dynamic e) => ReasonEntity.fromJson(e as Map<String, dynamic>))
            .toList(),
        sessionToken = response.data?['session_token'] as String?,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'challenge_type': challengeType,
      'reasons': reasons?.map((ReasonEntity x) => x.toJson()),
      'session_token': sessionToken,
    });
    return json;
  }
}

class ReasonEntity {
  final String? detail;
  final int? id;

  ReasonEntity({
    this.detail,
    this.id,
  });

  factory ReasonEntity.fromJson(Map<String, dynamic> json) => ReasonEntity(
        detail: json['detail'] as String?,
        id: json['id'] as int?,
      );

  Map<String, dynamic> toJson() => <String, dynamic>{
        'detail': detail,
        'id': id,
      };
}
