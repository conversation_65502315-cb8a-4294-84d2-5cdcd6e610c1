class LinkedCardEntity {
  final String? cardImage;
  final PaymentPermission? paymentPermission;

  LinkedCardEntity({
    this.cardImage,
    this.paymentPermission,
  });

  LinkedCardEntity.fromJson(Map<String, dynamic> json)
      : cardImage = json['card_image'] as String?,
        paymentPermission = (json['payment_permission'] as Map<String, dynamic>?) != null
            ? PaymentPermission.fromJson(json['payment_permission'] as Map<String, dynamic>)
            : null;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'card_image': cardImage,
        'payment_permission': paymentPermission?.toJson(),
      };
}

class PaymentPermission {
  final String? maxPerTrans;
  final String? minPerTrans;

  PaymentPermission({
    this.maxPerTrans,
    this.minPerTrans,
  });

  PaymentPermission.fromJson(Map<String, dynamic> json)
      : maxPerTrans = json['max_per_trans'] as String?,
        minPerTrans = json['min_per_trans'] as String?;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'max_per_trans': maxPerTrans,
        'min_per_trans': minPerTrans,
      };
}
