import 'package:flutter_common_package/util/utils.dart';

import 'action_entity.dart';
import 'emi_offer_entity.dart';
import 'merchant_info_entity.dart';
import 'order_info_entity.dart';
import 'payment_info_entity.dart';
import 'promotion_info_entity.dart';
import 'store_info_entity.dart';
import 'user_payment_information_entity.dart';

class OrderSessionEntity {
  final String? id;
  final String? transactionId;
  final String? channel;
  final String? createdAt;
  final String? productCode;
  final List<String?>? promotionCodes;
  final String? status;
  final String? updatedAt;
  final StoreInfoEntity? storeInfo;
  final ActionEntity? nextAction;
  final OrderInfoEntity? orderInfo;
  final PaymentInfoEntity? paymentInfo;
  final PromotionInfoEntity? promotionInfo;
  final UserPaymentInformationEntity? userInfo;
  final int? fee;
  final int? orderAmount;
  final int? promotionAmount;
  final int? userChargeAmount;
  final MerchantInfoEntity? merchantInfo;
  final EmiOfferEntity? emiOffer;

  OrderSessionEntity({
    this.id,
    this.transactionId,
    this.fee,
    this.orderAmount,
    this.promotionAmount,
    this.userChargeAmount,
    this.channel,
    this.createdAt,
    this.productCode,
    this.promotionCodes,
    this.status,
    this.updatedAt,
    this.storeInfo,
    this.promotionInfo,
    this.nextAction,
    this.orderInfo,
    this.paymentInfo,
    this.userInfo,
    this.merchantInfo,
    this.emiOffer,
  });

  DateTime? get createdAtDateTime => commonUtilFunction.toDateTime(createdAt);

  factory OrderSessionEntity.fromJson(Map<String, dynamic> json) => OrderSessionEntity(
        id: json['id'] as String?,
        transactionId: json['transaction_id'] as String?,
        channel: json['channel'] as String?,
        fee: json['fee'] as int?,
        orderAmount: json['order_amount'] as int?,
        promotionAmount: json['promotion_amount'] as int?,
        userChargeAmount: json['user_charge_amount'] as int?,
        createdAt: json['created_at'] as String?,
        productCode: json['product_code'] as String?,
        promotionCodes:
            (json['promotion_codes'] as List<dynamic>?)?.map((dynamic e) => e.toString()).toList(),
        status: json['status'] as String?,
        updatedAt: json['updated_at'] as String?,
        storeInfo: (json['store_info'] as Map<String, dynamic>?) != null
            ? StoreInfoEntity.fromJson(json['store_info'] as Map<String, dynamic>)
            : null,
        promotionInfo: (json['promotion_info'] as Map<String, dynamic>?) != null
            ? PromotionInfoEntity.fromJson(
                json['promotion_info'] as Map<String, dynamic>,
              )
            : null,
        nextAction: (json['next_action'] as Map<String, dynamic>?) != null
            ? ActionEntity.fromJson(
                json['next_action'] as Map<String, dynamic>,
              )
            : null,
        orderInfo: (json['order_info'] as Map<String, dynamic>?) != null
            ? OrderInfoEntity.fromJson(json['order_info'] as Map<String, dynamic>)
            : null,
        paymentInfo: (json['payment_info'] as Map<String, dynamic>?) != null
            ? PaymentInfoEntity.fromJson(json['payment_info'] as Map<String, dynamic>)
            : null,
        userInfo: (json['user_info'] as Map<String, dynamic>?) != null
            ? UserPaymentInformationEntity.fromJson(
                json['user_info'] as Map<String, dynamic>,
              )
            : null,
        merchantInfo: (json['merchant_info'] as Map<String, dynamic>?) != null
            ? MerchantInfoEntity.fromJson(json['merchant_info'] as Map<String, dynamic>)
            : null,
        emiOffer: (json['emi_offer'] as Map<String, dynamic>?) != null
            ? EmiOfferEntity.fromJson(json['emi_offer'] as Map<String, dynamic>)
            : null,
      );

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'transaction_id': transactionId,
        'fee': fee,
        'order_amount': orderAmount,
        'promotion_amount': promotionAmount,
        'user_charge_amount': userChargeAmount,
        'channel': channel,
        'created_at': createdAt,
        'product_code': productCode,
        'promotion_codes': promotionCodes,
        'status': status,
        'updated_at': updatedAt,
        'store_info': storeInfo?.toJson(),
        'promotion_info': promotionInfo?.toJson(),
        'next_action': nextAction?.toJson(),
        'order_info': orderInfo?.toJson(),
        'payment_info': paymentInfo?.toJson(),
        'user_info': userInfo?.toJson(),
        'merchant_info': merchantInfo?.toJson(),
        'emi_offer': emiOffer?.toJson(),
      };
}
