/// this remote config is used to get base url for api request
/// this is a solution for the case that QA need to change base url for mocking api
/// currently only applied on Staging environment
class RemoteConfigCommonBaseUrlEntity {
  String? description;
  String? value;

  RemoteConfigCommonBaseUrlEntity({
    this.description,
    this.value,
  });

  factory RemoteConfigCommonBaseUrlEntity.fromJson(Map<String, dynamic> json) =>
      RemoteConfigCommonBaseUrlEntity(
        description: json['description'] as String?,
        value: json['value'] as String?,
      );

  Map<String, dynamic> toJson() => <String, dynamic>{
        'description': description,
        'value': value,
      };
}
