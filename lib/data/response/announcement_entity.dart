import 'package:flutter_common_package/util/functions.dart';

import 'action_entity.dart';

class AnnouncementEntity {
  static const String statusRead = 'read';
  static const String statusUnread = 'unread';

  final ActionEntity? action;
  final String? image;
  final int id;
  final String? description;
  final String? title;
  final String? createdAt;
  final String? sentAt;
  final String? status;

  AnnouncementEntity(
      {required this.id,
      this.action,
      this.image,
      this.description,
      this.title,
      this.createdAt,
      this.sentAt,
      this.status});

  DateTime? get sentAtDateTime => commonUtilFunction.toDateTime(sentAt);

  AnnouncementEntity.fromJson(Map<String, dynamic> json)
      : action = (json['action'] as Map<dynamic, dynamic>?) != null
            ? ActionEntity.fromJson(json['action'] as Map<dynamic, dynamic>)
            : null,
        image = json['image'] as String?,
        id = json['id'] as int,
        description = json['description'] as String?,
        title = json['title'] as String?,
        createdAt = json['created_at'] as String?,
        sentAt = json['sent_at'] as String?,
        status = json['status'] as String?;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'action': action?.toJson(),
        'image': image,
        'id': id,
        'description': description,
        'title': title,
        'created_at': createdAt,
        'sent_at': sentAt,
        'status': status,
      };

  AnnouncementEntity copyWith({
    ActionEntity? action,
    String? createdAt,
    String? sentAt,
    int? id,
    String? settings,
    String? status,
    String? image,
    String? description,
    String? title,
  }) =>
      AnnouncementEntity(
        action: action ?? this.action,
        createdAt: createdAt ?? this.createdAt,
        sentAt: sentAt ?? this.sentAt,
        id: id ?? this.id,
        status: status ?? this.status,
        image: image ?? this.image,
        description: description ?? this.description,
        title: title ?? this.title,
      );
}

extension AnnouncementExt on AnnouncementEntity {
  bool hasAction() {
    return action != null;
  }

  bool isUnread() {
    return status == AnnouncementEntity.statusUnread;
  }
}
