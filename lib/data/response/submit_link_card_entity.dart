import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'action_entity.dart';

class SubmitLinkCardEntity extends BaseEntity {
  static const String verdictSuccess = 'success';

  /// Ref: https://trustingsocial1.atlassian.net/browse/EMA-920
  static const String verdictDuplicatedLinkRequest = 'duplicated_link_request';

  /// SUBMIT LINK STATUS
  /// Ref: https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/3296461984/Manual+Card+Link+-+Response+code
  static const String verdictPermissionDenied = 'permission_denied';
  static const String verdictLinkCardRecordNotFound = 'record_not_found';
  static const String verdictLinkCardFailureAll = 'failure';

  final int? intervalInquiryMs;
  final String? linkCardRequestId;
  final String? linkCardStatus;
  final int? nextRetryIfExitDurationInMinute;
  final ActionEntity? action;

  SubmitLinkCardEntity({
    this.intervalInquiryMs,
    this.linkCardRequestId,
    this.linkCardStatus,
    this.nextRetryIfExitDurationInMinute,
    this.action,
  });

  SubmitLinkCardEntity.unserializable({
    this.intervalInquiryMs,
    this.linkCardRequestId,
    this.linkCardStatus,
    this.nextRetryIfExitDurationInMinute,
    this.action,
  }) : super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  SubmitLinkCardEntity.fromBaseResponse(BaseResponse super.response)
      : intervalInquiryMs = response.data?['interval_inquiry_ms'] as int?,
        linkCardRequestId = response.data?['link_card_request_id'] as String?,
        linkCardStatus = response.data?['link_card_status'] as String?,
        nextRetryIfExitDurationInMinute =
            response.data?['next_retry_if_exit_duration_in_minute'] as int?,
        action = (response.data?['action'] as Map<String, dynamic>?) != null
            ? ActionEntity.fromJson(response.data?['action'] as Map<String, dynamic>)
            : null,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'interval_inquiry_ms': intervalInquiryMs,
      'link_card_request_id': linkCardRequestId,
      'link_card_status': linkCardStatus,
      'next_retry_if_exit_duration_in_minute': nextRetryIfExitDurationInMinute,
      'action': action?.toJson(),
    });
    return json;
  }
}
