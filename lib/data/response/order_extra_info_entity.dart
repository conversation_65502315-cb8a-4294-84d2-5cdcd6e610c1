// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

class OrderExtraInfoEntity {
  //this flag indicate that this is the first transaction or not (mean there is no success transaction before)
  final bool? enableRatingPrompt;

  //this flag indicate should we auto apply voucher for this order
  final bool? autoApplyVoucher;

  OrderExtraInfoEntity({
    this.enableRatingPrompt,
    this.autoApplyVoucher,
  });

  factory OrderExtraInfoEntity.fromJson(Map<String, dynamic> json) => OrderExtraInfoEntity(
        enableRatingPrompt: json['enable_rating_prompt'] as bool?,
        autoApplyVoucher: json['auto_apply_voucher'] as bool?,
      );

  Map<String, dynamic> toJson() => <String, dynamic>{
        'enable_rating_prompt': enableRatingPrompt,
        'auto_apply_voucher': autoApplyVoucher,
      };
}
