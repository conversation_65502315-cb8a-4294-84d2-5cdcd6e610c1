import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class DOPVerifyOTPEntity extends BaseEntity {
  /// Status code [CommonHttpClient.SUCCESS]
  static const String verdictIncorrectOTP = 'incorrect_otp';

  /// Status code 403
  static const String verdictOTPExpired = 'expired_consent';

  /// Status code [CommonHttpClient.LIMIT_EXCEEDED]
  static const String verdictManyRequest = 'limit_exceeded';

  final String? accessToken;

  DOPVerifyOTPEntity({
    this.accessToken,
  });

  DOPVerifyOTPEntity.unserializable()
      : accessToken = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  DOPVerifyOTPEntity.fromBaseResponse(BaseResponse super.response)
      : accessToken = response.data?['access_token'],
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'access_token': accessToken,
    });
    return json;
  }
}
