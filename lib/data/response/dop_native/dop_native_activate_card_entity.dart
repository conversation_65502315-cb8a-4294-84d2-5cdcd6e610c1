import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'dop_native_card_status_entity.dart';

class DOPNativeActivateCardEntity extends BaseEntity {
  final DOPNativeCardActivationStatus? activationStatus;
  final String? redirectUrl;

  DOPNativeActivateCardEntity({
    this.activationStatus,
    this.redirectUrl,
  });

  DOPNativeActivateCardEntity.unserializable()
      : activationStatus = null,
        redirectUrl = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  DOPNativeActivateCardEntity.fromBaseResponse(BaseResponse super.baseResponse)
      : activationStatus = DOPNativeCardActivationStatus.fromValue(
            baseResponse.data?['activation_status'] as String?),
        redirectUrl = baseResponse.data?['redirect_url'] as String?,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'activation_status': activationStatus?.value,
      'redirect_url': redirectUrl,
    });
    return json;
  }
}
