import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'ekyc_info_entity.dart';

class ResetPinEntity extends BaseEntity {
  /// if incorrect otp too many times, return status code [CommonHttpClient.LIMIT_EXCEEDED]
  /// limit call API (3 times within 24h)
  // limit call API with challenge type verify_national_id (5 times / reset PIN request)
  // limit call API with challenge type verify_otp (3 times / reset PIN request)
  // limit call API with challenge type resend_otp (3 times / verify otp request / reset PIN request)
  static const String verdictLimitExceeded = 'limit_exceeded';

  ///user with phone_number not found, return status code [CommonHttpClient.NOT_FOUND]
  static const String verdictUserNotExisted = 'record_not_found';

  ///request type and type in session_token are not the same, return status code [CommonHttpClient.BAD_REQUEST]
  static const String verdictInvalidState = 'invalid_state';

  ///national id, otp, phone number, pin not valid format or rule, return status code [CommonHttpClient.BAD_REQUEST]
  static const String verdictInvalidParameters = 'invalid_parameters';

  static const String verdictInvalidCredential = 'invalid_credential';

  static const String verdictOneLastTry = 'one_last_try';

  static const String verdictExpiredData = 'expired_data';

  static const String verdictInvalidPin = 'invalid_pin';

  ///user has been locked to reset pin
  static const String verdictLockedResource = 'locked_resource';

  ///invalid session token, return status code [CommonHttpClient.INVALID_TOKEN]
  static const String verdictInvalidResetPinSession = 'invalid_reset_pin_session';

  ///session token is expired, return status code [CommonHttpClient.INVALID_TOKEN]
  static const String verdictExpiredResetPinSession = 'expired_reset_pin_session';

  ///internal server error, return status code [CommonHttpClient.INTERNAL_SERVER_ERROR]
  static const String verdictFailure = 'failure';

  ///reset pin success, return status code [CommonHttpClient.SUCCESS]
  static const String verdictSuccess = 'success';

  final String? challengeType;
  final int? otpResendSecs;
  final int? otpValiditySecs;
  final int? retryRemainingCount;
  final String? sessionToken;
  final EKYCSessionEntity? ekycCredential;

  ResetPinEntity({
    this.challengeType,
    this.otpResendSecs,
    this.otpValiditySecs,
    this.retryRemainingCount,
    this.sessionToken,
    this.ekycCredential,
  });

  ResetPinEntity.unSerializable()
      : challengeType = null,
        otpResendSecs = null,
        otpValiditySecs = null,
        retryRemainingCount = null,
        sessionToken = null,
        ekycCredential = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  ResetPinEntity.fromBaseResponse(BaseResponse super.baseResponse)
      : challengeType = baseResponse.data?['challenge_type'] as String?,
        sessionToken = baseResponse.data?['session_token'] as String?,
        otpResendSecs = baseResponse.data?['otp_resend_secs'] as int?,
        otpValiditySecs = baseResponse.data?['otp_validity_secs'] as int?,
        retryRemainingCount = baseResponse.data?['retry_remaining_count'] as int?,
        ekycCredential = (baseResponse.data?['ekyc_credential'] as Map<String, dynamic>?) != null
            ? EKYCSessionEntity.fromBaseResponseForFaceOtp(
                baseResponse, baseResponse.data?['ekyc_credential'] as Map<String, dynamic>)
            : null,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'challenge_type': challengeType,
      'session_token': sessionToken,
      'otp_resend_secs': otpResendSecs,
      'otp_validity_secs': otpValiditySecs,
      'retry_remaining_count': retryRemainingCount,
      'ekyc_credential': ekycCredential?.toJson(),
    });
    return json;
  }
}
