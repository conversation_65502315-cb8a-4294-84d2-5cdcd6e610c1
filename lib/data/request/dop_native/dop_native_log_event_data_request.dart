class DOPNativeLogEventDataRequest {
  final String? eventType;
  final String? token;
  final DOPNativeLogEventPayload? data;

  DOPNativeLogEventDataRequest({
    this.eventType,
    this.token,
    this.data,
  });

  Map<String, dynamic>? toJson() {
    final Map<String, dynamic> json = <String, dynamic>{
      'event_type': eventType,
      'token': token,
      'data': data?.toJson(),
    };
    json.removeWhere((_, dynamic value) {
      return value == null;
    });
    if (json.isEmpty) {
      return null;
    }

    return json;
  }
}

class DOPNativeLogEventPayload {
  final String? type;
  final String? leadSource;
  final String? phoneNumber;
  final String? existingUniqueToken;

  DOPNativeLogEventPayload({
    this.type,
    this.leadSource,
    this.phoneNumber,
    this.existingUniqueToken,
  });

  Map<String, dynamic>? toJson() {
    return <String, dynamic>{
      'type': type,
      'lead_source': leadSource,
      'phone_number': phoneNumber,
      'existing_unique_token': existingUniqueToken,
    };
  }
}
