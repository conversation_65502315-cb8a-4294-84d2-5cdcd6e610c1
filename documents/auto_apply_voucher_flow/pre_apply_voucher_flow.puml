@startuml
'https://plantuml.com/sequence-diagram

autonumber
title EvoApp - Pre-apply Voucher Flow

Actor User as user
participant EvoApp as app

user -> app: Tap on **Voucher Item** at MyVoucherView
app --> user: Redirect to **Voucher Detail Screen** with **EvoActionModel**
note right
    Currently, voucher detail will be displayed in InApp WebView
end note

==Voucher Detail Screen==
alt #Azure EvoActionModel != null
    user -> app: Press CTA button to **Apply a Voucher**
    alt #LightGreen EvoActionModel.type == promotion_scan_to_pay
        app --> app: Save **Selected Voucher** to **PaymentSharedData** in AppState
        app --> user: Move to **ScanQRFlow**
    end alt
end alt

==ScanQRFlow==
user -> app: scan QR code
alt #Azure ScanQRFlow success
    app --> user: Move to **UpdateConfirmPaymentFlow**
end alt

@enduml