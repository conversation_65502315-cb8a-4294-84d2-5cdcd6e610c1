@startuml

autonumber

title Manual Link Card - Submit Link Card Flow

actor User
participant <PERSON><PERSON><PERSON><PERSON> as app
participant E<PERSON><PERSON><PERSON><PERSON> as gateway
participant TPB as tpb

== LinkCardThreeDPollingScreen ==

opt user clicks button (X) OR device back button
    app --> app: pause polling
    activate app
    app --> User: show warning dialog
    deactivate app
    User -> app: do action on warning dialog
    alt user clicks button "Ở lại"
        app --> User: hide warning dialog
        activate app
        app --> app: continue polling
        deactivate app
    else user clicks button "Thoát"
        app --> app: stop polling
        activate app
        app --> User: hide warning dialog & back to entry point
        deactivate app
    end
end opt

par Delay 120s to limit Polling
    app --> app: start timer
    activate app
    ...Delay **120s limit time**...
    app --> app: stop polling
    app --> User: redirect to **ManualLinkCardResultScreen** case timeout
    deactivate app
else Polling submit link card
app -> gateway: POST user/card/submit-link-card
note over gateway
    submit-link-card request:
        - data: link_card_request_id
        - header: {"X-SESSION": session_token}
    get from response of check linking status
end note
activate gateway
gateway --> app: return submit-link-card response
deactivate gateway
alt #Salmon invalid token response
    app --> app: stop polling
    activate app
    app --> User: display request login dialog
    deactivate app
else #Salmon fail response
    app --> app: check verdict
    alt #Salmon error verdicts that user can't link card
        note over app
            error verdicts that user can't link card include:
                - permission_denied
                - record_not_found
                - failure
        end note
        app --> app: stop polling
        activate app
        app --> User: redirect to **ManualLinkCardResultScreen** with error verdicts
        deactivate app
    else other error verdicts
        ...Delay **interval_inquiry_ms** OR default **5000 ms**...
        note over app
            The **interval_inquiry_ms** is gotten from API submit-link-card response,
            used for delaying to next polling API
            Default value is **5000 ms**
        end note
        app --> app: Go to **step 11**
        activate app
        deactivate app
    end
else #LightGreen success response
    activate app
    note over app
        success response:
            - interval_inquiry_ms
            - link_card_request_id
            - link_card_status
            - next_retry_if_exit_duration_in_minute
            - action
    end note
    app --> app: check verdict
    alt verdict == duplicated_link_request
        app --> app: stop polling
        activate app
        app --> User: show DOPCardStatusScreen case duplicated_link_request
        deactivate app
    else verdict == success
        app --> app: check action.type
        alt #LightGreen action.type == INQUIRY_STATUS
            app --> app: update interval_inquiry_ms
            activate app
            note over app
                update **interval_inquiry_ms** if it is not NULL.
                The **interval_inquiry_ms** is used for delaying to next polling API
                Default value is **5000 ms**
            end note
            app --> app: update next_retry_if_exit_duration_in_minute
            note over app
                update next_retry_if_exit_duration_in_minute,
                the next time user can do manual link card again
                **Default value is 15 minutes**
                Used to display message in warning dialog (**step 5**)
            end note
            ...Delay **interval_inquiry_ms** OR default **5000 ms**...
            app --> app: Go to **step 11**
            deactivate app
        else action.type == OPEN_APP_SCREEN_FAILED || OPEN_APP_SCREEN_SUCCESS
            app --> app: stop polling
            activate app
            app --> User: redirect to **ManualLinkCardResultScreen** case fail || success
            deactivate app
        else action.type == OPEN_3D_SECURE
            app --> app: stop polling
            activate app
            app --> User: redirect to **LinkCardThreeDSecureScreen**
            deactivate app
        end
    end
end
end

== LinkCardThreeDSecureScreen ==
note over app
    LinkCardThreeDSecureScreen displays a TPB WebView, allows user input OTP.
    Url get from response: action.arg.url
end note
User -> tpb: enter OTP
activate tpb
tpb --> app: redirect to webview to **evo://card-linking-processing**
deactivate tpb
activate app
app --> User: redirect to **LinkCardSubmissionStatusPollingScreen**
deactivate app
note over User
    Go to Inquiry Status Flow
end note

@enduml