@startuml

autonumber

title Manual Link Card - Check Linking Status Flow

actor User
participant E<PERSON><PERSON><PERSON> as app
participant EvoGateway as gateway

User -> app: enter manual link card flow at entry point
note over app
    Entry point screen is a screen that user can enter manual link card flow.
    It can be:
        - QrCodeScannerScreen
        - NormalConfirmPaymentScreen
        - UpdateConfirmPaymentScreen
        - ProfileScreen
end note

app -> gateway: GET user/card/check-linking-status
note over gateway
   Refer: [[https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/Cards/handleCheckCardLinkingStatusV1 API Docs]]
end note
activate gateway

alt #Salmon fail
    gateway --> app: return check-linking-status fail
    activate app
    alt verdict == fail && status_code == 500
        app --> User: redirect DOPCardStatusScreen case fail
    else other fail verdicts
        app --> User: show error toast
        deactivate app
    end
    deactivate app
else #LightGreen success
    gateway --> app: return check-linking-status success
    note over app
        check-linking-status response:
            - challenge_type
            - session_token
            - action
            - link_card_request_id
    end note
    deactivate gateway
    activate app
    app --> app: check verdict
    deactivate app
    alt verdict == unfulfilled_card
        app --> User: redirect DOPCardStatusScreen case unfulfilled_card
    else verdict == success
        app --> app: check challenge_type

        alt #Salmon challenge_type == null OR challenge_type unsupported
            app --> app: throw exception and do NOTHING
            note over app
                This is a development issue, **NOTICE TO FIX IT**.
            end note
        else #LightGreen challenge_type == face_otp
            app --> User: redirect user to FaceOTPFlow
            User -> app: do FaceOTP
            activate app

            alt #Salmon face otp fail
                app --> User:  back to entry point screen
            else #LightGreen face otp success
                app --> User: redirect LinkCardThreeDPollingScreen
                note over User
                    Go to Submit Link Card Flow
                end note
                deactivate app
            end
        else #LightGreen challenge_type == none
            app --> User: redirect LinkCardThreeDPollingScreen
            note over User
                Go to Submit Link Card Flow
            end note
        end
    end
end

@enduml