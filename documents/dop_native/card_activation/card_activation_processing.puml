@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"

actor "**User**" as User
participant "**EvoApp**" as App
participant "**EvoGateWay**" as BE
title DOP Native - Card Activation Processing

note right User
- User has been activated card with 3D Secure
- User go to DOP flow with
*state is **card_status.information**
*ui_version: v9.4.1.0 || v9.4.2.0 || v9.4.3.0
end note

App --> User: Redirect to **DopNativeCardStatusInformationScreen**

opt User press CTA (X) on app bar
    ref over User, App
        **Close DOP flow** diagram
    end ref
end opt

alt User goto DopNativeCardStatusInformationScreen from 3D secure
    App -> BE: GET /dop/api/application/state
    BE --> App: API Response
    note left
    response: {
      current_step: ...
    }

    Refer: **[[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3544678490/ES+Combine+API+get+application+state+and+get+bootstrap#API-spec API docs]]**
    end note
    alt current_step is card_status.information
        App --> User: Waiting 15 seconds
        App --> User: goto step [9] **Get Card Status**
    else current_step is other value
       App --> User: Redirect to next state
        note right
           Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3544744596/Appendix+Essential+source application state navigation mapping]]**
        end note
    else #LightPink http error
        App --> User: Goto **Error screen**
    end alt
else User come from Drop-off
    App-->User: goto step [9] **Get Card Status**
end alt

==Get Card Status==
App -> BE: GET dop/api/card/status
BE --> App: API Response
note left
   response: {
     activation_status: String,
     can_activate_card: bool,
     pos_limit: {
        pos_limit_activation_status: String,
     }
   }

   Refer: [[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/**********/CO-9833+TPBank+EVO+DOE+Card+activation+-+Update+new+activation+APIs Card status API Specs]]
             [[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/**********/Underwriting+sub+flow+Card+activation Activation status API Specs]]
end note

alt status_code == 200
    alt activation_status in [temporary_blocked, permanent_blocked, lock_card]
        App --> User: redirect to **Block card screen**
    else #Lavender activation_status is activated
        alt pos_limit_activation_status IN [success, empty]
            App --> User: redirect to **Card Activate Success screen**
            note right
               Refer: **card_activate_success.puml**
            end note
        else #white pos_limit_activation_status is pending
            App --> User: redirect to **Card Activated POS Retry screen**
            note right
               Refer: **card_activate_success_pos_retry.puml**
            end note
        else pos_limit_activation_status IN [timeout, failure]
            App --> User: redirect to **Card Activated POS Failed screen**
            note right
               Refer: **card_activate_success_pos_failed.puml**
            end note
        else #white
            App --> User: redirect to **Failure screen**
        end alt

    else  activation_status is not activated
        alt #White activation_status is null or can_activate_card is null
            App --> User: redirect to **Failure screen**
        else #PowderBLue activation_status is not null && can_activate_card is true
            App --> User: redirect to **Card Activate Retry screen**
            note right
                Refer: **card_activate_retry.puml**
            end note
        else activation_status is not null && can_activate_card is false
            App --> User: redirect to **Card Activate Fail screen**
            note right
                Refer: **card_activate_fail.puml**
            end note
        end alt
    end alt
else #LightPink http error
    App --> User: Goto **Error screen**
end alt

@enduml