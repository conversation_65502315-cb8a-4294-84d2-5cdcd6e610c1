@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**TvSDK**" as tvSDK
participant "**EvoGateWay**" as be

title DOP Native - Face OTP for Drop-off flow

note right user
User is required to verify their face using FaceOTP when
    * User is drop-off current flow and back to DOE flow
    * Access Token of DOE is expired
**Note:** The user has completed selfie step in DOE flow
end note

user -> app: Press **Mở camera** button
alt #Salmon The user did not grant camera permission
    app --> user: Required to grant camera permission
else #White The user granted camera permission
    app -> tvSDK: Init SDK with **eKYC UIOnly** Mode
    note right tvSDK
        **param:** jsonConfigurationByServer = '{}'
    end note
    alt #Salmon TvSDK initialization failed
        app --> user: redirect to **common error screen**
    else #White TvSDK is initialized successfully
       app --> user: Start FaceOTP with **Passive** Mode
       user -> app: Capture selfie
       app -> tvSDK: Send requests
       tvSDK --> app: Return response
       note left app
            Ref: **[[https://ekyc.trustingsocial.com/sdks/Flutter-SDK#223-get-qr-image:~:text=to%20upload%20image-,3.%20Selfie%20capturing,-3.1.%20Configuration API docs]]**
       end note
       alt #Salmon User cannot capture selfie
           app --> user: Redirect to **common error screen**
       else #White User can capture selfie
           app -> be: Upload FaceID API **ekyc/upload_face_id**
           be --> app: Return response
           note left app
               Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#POST-Upload-face-id API docs]]**
           end note
           alt #Salmon statusCode != 200 && statusCode != 400
               app --> user: Show error screen
           else #cyan statusCode == 400
               app --> user: Redirect to **face otp retry screen**
           else #LightGreen statusCode == 200
               app -> be: Verify FaceID API **ekyc/verify_face_id**
               be --> app: Return response
               note left app
                  Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#POST-Verify-face-id API docs]]**
               end note
               alt #Salmon statusCode != 200 && statusCode != 400
                    app --> user: Show error screen
               else #cyan statusCode == 400 || (statusCode == 200 && verdict == "face_unmatched")
                    app --> user: Redirect to **face otp retry screen**
               else #Lime statusCode == 200
                    app --> app: Set **accessToken** to header
                    app --> user: Redirect to **face otp success screen**
           end alt
       end alt
    end
    end alt
end alt

@enduml