@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

title DOP Native - Back ID Card verification

== Upload Back ID Card image==
app -> be: POST **dop/id_back/upload**
note over be
    request:
        - image: MultipartFile
            - Back ID Card base 64 image bytes: List<int>
            - file name: String
   Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3531964765/S3+KH+ch+p+hai+m+t+CCCD#9.-Upload-back-id-card API docs]]**
end note
be --> app: return
note over app
    response:
        - job_id
end note
alt #LightYellow upload success
    app -> app: go to **Polling card status** steps with **job_id** & **step check back id card**
else #LightPink upload failed
    app --> user: back to **DOPNativeIDCaptureIntroductionScreen**
    app --> user: return Back ID Card verification failed
    ref over user, app
        **DOP Native - ID Card capturing** diagram
    end ref
end alt

== Polling card status==
loop get card status
    app -> be: GET **dop/api/ekyc/status**
    note over be
        request:
            - job_id
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#GET-Ekyc-status API docs]]**
    end note
    be --> app: return
    note over app
        response:
            - status
            - verdict
    end note
    alt verdict == success
        alt status == success
            app -> app: stop loop
            alt step check back id card
                app -> app: go to **Process ID Card** steps
            else step check process id card
                app -> app: go to **Check application state** steps
            end alt
        else status == pending || status == in_progress
            ...Delay 1s...
            app -> app: continue loop
        else #LightPink status == failure
            app -> app: stop loop
            app --> user: back to **DOPNativeIDCaptureIntroductionScreen**
            app --> user: return Back ID Card verification failed
            ref over user, app
                **DOP Native - ID Card capturing** diagram
            end ref
        end alt
    else #LightPink verdict != success
        app --> user: back to **DOPNativeIDCaptureIntroductionScreen**
        app --> user: return Back ID Card verification failed
        ref over user, app
            **DOP Native - ID Card capturing** diagram
        end ref
    end alt
end loop

== Process ID Card==
app -> be: POST **dop/api/ekyc/process_id_card**
note over app
    response:
        - job_id
   Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3531964765/S3+KH+ch+p+hai+m+t+CCCD#12.-Process-id-card API docs]]**
end note
alt verdict == success
    app -> app: go to **Polling card status** steps with **job_id** & **step check process id card**
else #LightPink verdict != success
    app --> user: back to **DOPNativeIDCaptureIntroductionScreen**
    app --> user: return Back ID Card verification failed
    ref over user, app
        **DOP Native - ID Card capturing** diagram
    end ref
end alt

== Check application state==
app -> be: GET **dop/application/state**
note over be
    request:
        - token
        - flow_selected_at
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Application-State-API API docs]]**
end note
be --> app: return
note over app
    response:
        - status_code
        - current_step
end note
alt status_code == 200
    alt #LightPink current_step == locked
        app --> user: back to **DOPNativeIDCaptureIntroductionScreen**
        app --> user: return Back ID Card verification failed
        ref over user, app
            **DOP Native - ID Card capturing** diagram
        end ref
    else #LightYellow current_step != locked
        app --> user: back to **DOPNativeIDCaptureIntroductionScreen**
        app --> user: return Back ID Card verification success
        ref over user, app
            **DOP Native - ID Card capturing** diagram
        end ref
    end alt
else #LightPink status_code != 200
    app --> user: redirect to **common error screen**
end alt

@enduml