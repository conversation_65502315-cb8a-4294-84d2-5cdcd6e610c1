@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

title DOP Native - Input phone dialog

user -> app: request to open EVO card
note right
- Press CTA 'Mở thẻ ngay' as DOP introduction screen
- Press CTA 'Mở thẻ ngay' as DOP sub introduction screen
end note
app --> user: Display input phone dialog

app -> app: clear dop-authorization on http client header

opt user open Term and condition
app --> user: open Term and Condition pdf
end opt

user -> app: input phone number
alt phone number is invalid
    app --> user: Show inline error
end alt

user -> app: Press CTA '->' to submit phone number
alt phone number is invalid
    app --> user: Show common error
else campaignCode || source is empty
    app --> user: Show common error
    note right
        The **campaignCode** and **source** are obtained beforehand and
        saved in the **AppState**. They are required for the register API
    end note
else phone number is valid && campaignCode && source is not empty
    app -> be: POST dop/api/registration/register
    note right
    //payload//: {
        allowed_switch_flow: true,
    }

    Refer: [[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/**********/CO-9705+TPBank+EVO+Web+DOE+Dive+In+Implement+the+solution+to+allow+user+to+switch+between+different+UI+flows#MOBILE---NATIVE-APP CO-9705]]
    end note

    be --> app: API response

    alt status_code == 200
        alt verdict == null || empty || unqualified
            app --> user: Show common error
        else verdict == duplicate
            app --> user: Show duplicate error screen
        else #LightYellow verdict == duplicate_reject
            app --> user: Show DOPNativeFailureScreen
            note right
                Refer: [[https://trustingsocial1.atlassian.net/browse/EMA-5937 EMA-5937]]
            end note
        else #Azure verdict == success
            app --> user: Continue flow at **DOP Introduction screen - Call bootstrap API**
            note right
                Refer: **DOP Native Introduction** diagram
            end note
        else verdict == existed_record
            note over app
            //result//: {
                  existing_app:{
                      unique_token: ...,
                      is_priority: ...,
                      platform: ...,
                      lead_source: ...,
                  },
                  mc_name: ...,
            }

            Refer: [[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/**********/CO-9705+TPBank+EVO+Web+DOE+Dive+In+Implement+the+solution+to+allow+user+to+switch+between+different+UI+flows#MOBILE---NATIVE-APP CO-9705]]
            end note

            alt is_priority == true
                app --> user: Show UI priority for existing app
            else is_priority == false
                app --> user: Show UI priority for new app
            end alt


               opt #Azure User press CTA **Đăng ký mới**
                   user -> app: Press CTA **Đăng ký mới**
                   app -> be: POST /api/registration/register

                   note right
                   //payload//: {
                       allowed_switch_flow: false,
                   }

                   Refer: [[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/**********/CO-9705+TPBank+EVO+Web+DOE+Dive+In+Implement+the+solution+to+allow+user+to+switch+between+different+UI+flows#MOBILE---NATIVE-APP CO-9705]]
                   end note

                   be --> app: API Response

                   alt  #white status_code != 200
                       app --> user: Redirect to **Error screen**
                   else status_code == 200
                       app --> user: Continue flow at **DOP Introduction screen - Call bootstrap API** with callback POST /event/track API
                       note right
                         Refer: **DOP Native Introduction** diagram
                       end note
                   end alt
               end opt

               opt #Azure User press CTA **Hoàn thành hồ sơ đăng ký cũ**
                   user -> app: Press CTA **Hoàn thành hồ sơ đăng ký cũ**
                   alt #white existing_app.platform == web
                       app --> user: Open DOP on Web view with token

                       note right
                          Refer web link: [[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/**********/CO-9705+TPBank+EVO+Web+DOE+Dive+In+Implement+the+solution+to+allow+user+to+switch+between+different+UI+flows#MOBILE---NATIVE-APP CO-9705]]
                       end note

                       alt open web success
                        app -> app: goto **Call event/track API** step
                       end alt



                   else existing_app.platform = native
                      app --> user: Continue flow at **DOP Introduction screen - Call bootstrap API** with callback POST /event/track API
                      note right
                        Refer: **DOP Native Introduction** diagram
                      end note
                   end alt
               end opt
        end alt
    else #LightYellow status_code == 429 && verdict == limit_exceeded
        app --> user: Show limit exceed error
    else #LightCyan status_code == 409 && verdict == expired_ptoken
        app --> user: Show PToken Error screen
        note right
            Refer: [[https://trustingsocial1.atlassian.net/browse/EMA-4997 EMA-4997]]
        end note
    else #LightCyan status_code == 400 && verdict == invalid_ptoken
        app --> user: Show PToken Error screen
        note right
            Refer: [[https://trustingsocial1.atlassian.net/browse/EMA-4997 EMA-4997]]
        end note
    else #White other error status_code
        app -> user: Show common error
    end alt
end alt

== Call event/track API ==

    app -> be: POST /event/track

    note right
    //payload:// {
       type: register.lead_source_selected,
       token: phone_number,
       data: {
           lead_source:  <lead_source>, ////  Archived from beginning DOE //, Refer: [[ https://trustingsocial1.atlassian.net/browse/EMA-3781 EMA-3781]]
           type: onboarding_app
       }
    }

    Refer: [[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3546251466/DOP+-+API+Specs API Specs]]

    end note

@enduml