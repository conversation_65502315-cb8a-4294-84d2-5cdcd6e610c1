@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"

actor User as user
participant EvoApp as app

title DOP Native - OnBoarding Offline Channel | Entry Point by DeepLink \n using QR code Scanner on EVO App

note over of user
   using QR Code Scanner on EVO App (at non-user Home screen)
end note
app --> user: show Non-user Home screen
user -> app: click **Scanner** icon
app --> user: show **NonLoginQrCodeScannerScreen**
app --> app: check Camera permission

== Checking Camera Permission ==

alt #LightCyan Permission is denied permanently
    app --> user: show warning popup
    alt #Pink Click CTA **Bỏ Qua**
        app --> user: close popup & move to **Non-user Home screen**
    else #White click CTA **Settings**
        app --> user: close popup & goto System Setting app to adjust permission
    end

else #White need to ask user permission
    app --> user: system dialog is showed & ask user permission
    alt #Salmon Deny
        user -> app: click **Deny** CTA
        note over of app
            back to step **#5**
        end note
    else #LightGreen Grant
        user -> app: click **Grant** CTA
        note over of app
            goto **QR code Scanning** steps
        end note
    end
else #LightGreen Permission is granted
   note over of app
        goto **QR code Scanning** steps
   end note
end

== QR code Scanning ==
app --> user: Camera View is ready for scanning
user -> app: scan QR code
app --> app: getting QR code **rawData**


alt #Salmon **rawData** is NULL or EMPTY
    app --> user: show warning popup
    user -> app: click **Thử Lại** CTA
    note over of app
        back to step **#11**
    end note
end

app --> app: check rawData contains url start with format **https://goevo.vn**
note left
    Ref: **[[https://trustingsocial1.atlassian.net/browse/EMA-3845 EMA-3845]]**
    * Note: Support non or multiple **sub-domain**
end note

alt #cyan rawData is Go-EVO-URL
    app --> user: Redirect to **CommonWebView** screen with URL
else #white

app --> app: extract **EvoDeepLink** from **rawData**

alt #LightPink **EvoDeepLink** is NULL or EMPTY
    app --> user: show **warning popup** like step **#14**
end

app --> app: generate **DeepLinkModel** from **EvoDeepLink**
note over app
    DeeplinkModel
        * screenName
        * params
end note

app --> app: execute **DeepLinkModel**
note over app
    response: **isProcessed**
end note

alt #LightPink **isProcessed** is FALSE
    app --> user: show **warning popup** like step **#14**
else #LightGreen **isProcessed** is TRUE
    alt #white screenName is **dop_native_introduction_screen**
        ref over app, user
            goto **DOP Native Flow**
        end ref
    else #LightYellow other screenName
        ref over app, user
            reference **DeepLinkHandler**
        end ref
    end alt
end
end alt

@enduml