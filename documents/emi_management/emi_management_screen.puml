@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
title EMI Management Screen

Actor User as user
participant EvoApp as app
participant EvoGateway as be

user -> app: open EMI Management Screen
note over user
    Users go to **EMI Management Screen**
    from:
        - Profile Screen
end note
==EMI Management Screen==

user -> app: Entry EMI Management Screen
app -> be: GET emi/records
note right app
    Param:
        - page_id: 1
        - per_page: 10
end note
be --> app: Response
note left be
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3732537795/FS+EMI+EMI+Management#List-User-EMI-Records-API API Response]]**
end note
alt #Salmon statusCode != 200
    app --> user: Show **EmiManagementEmptyWidget**
    app --> user: Show error
else #lightgreen statusCode == 200
    app --> app: Set emi records
    alt #white emi records is empty
        app --> user: Show **EmiManagementEmptyWidget**
    else #cyan emi records is not empty
        app --> user: Show **EmiManagementListWidget**
    end alt
end alt

opt #LightCyan Users refresh the screen
    user -> app: pull to refresh
    app -> be: GET emi/records
    note right app
        Param:
            - page_id: 1
            - per_page: 10
    end note
    be --> app: Response
    note left be
        Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3732537795/FS+EMI+EMI+Management#List-User-EMI-Records-API API Response]]**
    end note
    app --> app: Reset current page
    app --> app: Reset emi records
end opt

opt #LightCyan Users load more
    user -> app: scroll to load more
    alt #Salmon Don't have item to load more
        app --> user: Don't show new items
    else #lightgreen Have item to load more
        app -> be: GET emi/records
        note right app
            Param:
                - page_id: previous page_id + 1
                - per_page: 10
        end note
        be --> app: Response
        note left be
            Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3732537795/FS+EMI+EMI+Management#List-User-EMI-Records-API API Response]]**
        end note
        app --> app: Add new emi records to list
        app --> app: Set current page ++ for loading more
    end alt
end opt

opt #LightCyan Users back the screen
    user -> app: back the screen
    app --> user: back to previous screen
end opt

opt #LightCyan User clicks an item
    user -> app:User clicks an item
    app --> user: Navigate EMI Management Detail Screen
end opt
@enduml