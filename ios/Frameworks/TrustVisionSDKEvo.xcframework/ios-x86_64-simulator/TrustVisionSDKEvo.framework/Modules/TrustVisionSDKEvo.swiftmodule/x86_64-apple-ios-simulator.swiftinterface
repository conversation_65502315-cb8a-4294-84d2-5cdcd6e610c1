// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 6.0.2 effective-5.10 (swiftlang-6.0.2.1.2 clang-1600.0.26.4)
// swift-module-flags: -target x86_64-apple-ios12.0-simulator -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -O -enable-bare-slash-regex -module-name TrustVisionSDKEvo
// swift-module-flags-ignorable: -no-verify-emitted-module-interface
import CocoaLumberjack
import CoreGraphics
import CoreText
import Foundation
import QuartzCore
import Swift
import TrustVisionCoreSDK
@_exported import TrustVisionSDKEvo
import UIKit
import _Concurrency
import _StringProcessing
import _SwiftConcurrencyShims
infix operator +| : DefaultPrecedence
infix operator +- : DefaultPrecedence
@_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers public class TrustVisionSdk : TrustVisionCoreSDK.BaseTrustVisionSdk {
  @objc public static let shared: any TrustVisionCoreSDK.TrustVisionSdkFullProtocol & TrustVisionCoreSDK.TrustVisionSdkUiOnlyProtocol
  override public func injectClientConfig() -> any TrustVisionCoreSDK.TVClientSpecificationConfigProtocol
  @objc deinit
}
@_hasMissingDesignatedInitializers public class TVClientViewControllerBuilder : TrustVisionCoreSDK.TVViewControllerBuilderProtocol {
  public func idDetectionVcType() -> any TrustVisionCoreSDK.IDDetectionViewProtocol.Type
  public func idDetectionVmType() -> any TrustVisionCoreSDK.IDDetectionViewModelProtocol.Type
  public func livenessDetectionVcType() -> any TrustVisionCoreSDK.TVLivenessDetectionViewProtocol.Type
  public func livenessDetectionVmType() -> any TrustVisionCoreSDK.TVLivenessDetectionViewModelProtocol.Type
  public func createLivenessDetectionResult() -> UIKit.UIViewController
  public func flashLivenessDetectionVcType() -> any TrustVisionCoreSDK.TVFlashLivenessDetectionViewProtocol.Type
  public func flashLivenessDetectionVmType() -> any TrustVisionCoreSDK.TVFlashLivenessDetectionViewModelProtocol.Type
  public func qrDetectionVcType() -> any TrustVisionCoreSDK.TVQRDetectionViewProtocol.Type
  public func qrDetectionVmType() -> any TrustVisionCoreSDK.TVQRDetectionViewModelProtocol.Type
  public func createConfirmationButton(type: TrustVisionCoreSDK.TVConfirmationButtonType, image: UIKit.UIImage, didPressCallback: (() -> Swift.Void)?) -> UIKit.UIView
  public func createLivenessStepView() -> any TrustVisionCoreSDK.PureLayoutTVLivenessStepViewProtocol
  @objc deinit
}
@_inheritsConvenienceInitializers @_Concurrency.MainActor @preconcurrency public class TVClientProgressView : TrustVisionCoreSDK.TVProgressBaseView {
  @_Concurrency.MainActor @preconcurrency override open func setupViews()
  @_Concurrency.MainActor @preconcurrency override open func setupLayouts()
  @_Concurrency.MainActor @preconcurrency override open func updateMessage(text: Swift.String?)
  @_Concurrency.MainActor @preconcurrency override open func setImageLoading(loadingImg: UIKit.UIImage?)
  @_Concurrency.MainActor @preconcurrency override open func startLoading()
  @_Concurrency.MainActor @preconcurrency override open func stopLoading()
  @_Concurrency.MainActor @preconcurrency @objc override dynamic public init(frame: CoreFoundation.CGRect)
  @_Concurrency.MainActor @preconcurrency @objc required dynamic public init?(coder: Foundation.NSCoder)
  @objc deinit
}
extension TrustVisionCoreSDK.TVSdkConstants.TSLocalizedString {
  public static var idDetectorGuidelineFrontSide: Swift.String {
    get
  }
  public static var idDetectorDescriptionGuidelineFrontSide: Swift.String {
    get
  }
  public static var idDetectorGuidelineBackSide: Swift.String {
    get
  }
  public static var idDetectorDescriptionGuidelineBackSide: Swift.String {
    get
  }
  public static var idDetectorGuidelineQRFrontSide: Swift.String {
    get
  }
  public static var idDetectorDescriptionGuidelineQRFrontSide: Swift.String {
    get
  }
  public static var idDetectorCaptureDescriptionFront: Swift.String {
    get
  }
  public static var idDetectorCaptureDescriptionBack: Swift.String {
    get
  }
}
