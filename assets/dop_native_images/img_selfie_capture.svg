<svg width="335" height="108" viewBox="0 0 335 108" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_25553_34401)">
<rect x="219" y="6" width="94" height="104" rx="8" transform="rotate(90 219 6)" fill="url(#paint0_linear_25553_34401)"/>
</g>
<path d="M104 95.4864V99.8942C104 102.103 105.791 103.894 108 103.894H111.196" stroke="#D82D8D" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M111.196 3.00053L108 3.00053C105.791 3.00053 104 4.79139 104 7.00053L104 11.4084" stroke="#D82D8D" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M231.138 11.4079L231.138 7.00009C231.138 4.79095 229.347 3.00009 227.138 3.00009L223.941 3.00009" stroke="#D82D8D" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M223.941 105.296L227.138 105.296C229.347 105.296 231.138 103.505 231.138 101.296L231.138 96.8878" stroke="#D82D8D" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<g filter="url(#filter1_b_25553_34401)">
<mask id="path-6-outside-1_25553_34401" maskUnits="userSpaceOnUse" x="133.067" y="9" width="69" height="84" fill="black">
<rect fill="white" x="133.067" y="9" width="69" height="84"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M145.722 31.583C145.722 43.5643 155.526 53.1659 167.761 53.1659C179.991 53.1659 189.8 43.5643 189.8 31.583C189.8 19.6016 179.991 10 167.761 10C155.526 10 145.722 19.6016 145.722 31.583ZM201.067 77.4496C201.067 66.3585 185.724 63.5835 167.761 63.5835C149.699 63.5835 134.454 66.454 134.454 77.5538C134.454 88.6449 149.797 91.4199 167.761 91.4199C185.822 91.4199 201.067 88.5494 201.067 77.4496Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M145.722 31.583C145.722 43.5643 155.526 53.1659 167.761 53.1659C179.991 53.1659 189.8 43.5643 189.8 31.583C189.8 19.6016 179.991 10 167.761 10C155.526 10 145.722 19.6016 145.722 31.583ZM201.067 77.4496C201.067 66.3585 185.724 63.5835 167.761 63.5835C149.699 63.5835 134.454 66.454 134.454 77.5538C134.454 88.6449 149.797 91.4199 167.761 91.4199C185.822 91.4199 201.067 88.5494 201.067 77.4496Z" fill="url(#paint1_linear_25553_34401)"/>
<path d="M167.761 54.1659C154.994 54.1659 144.722 44.1362 144.722 31.583H146.722C146.722 42.9924 156.059 52.1659 167.761 52.1659V54.1659ZM190.8 31.583C190.8 44.1363 180.523 54.1659 167.761 54.1659V52.1659C179.459 52.1659 188.8 42.9923 188.8 31.583H190.8ZM167.761 9C180.523 9 190.8 19.0296 190.8 31.583H188.8C188.8 20.1736 179.459 11 167.761 11V9ZM144.722 31.583C144.722 19.0297 154.994 9 167.761 9V11C156.059 11 146.722 20.1735 146.722 31.583H144.722ZM167.761 62.5835C176.764 62.5835 185.229 63.2751 191.474 65.4112C194.602 66.4812 197.246 67.9375 199.116 69.9173C201.009 71.9209 202.067 74.4189 202.067 77.4496H200.067C200.067 74.9348 199.208 72.9267 197.662 71.2907C196.094 69.6307 193.78 68.3137 190.827 67.3036C184.909 65.2794 176.721 64.5835 167.761 64.5835V62.5835ZM133.454 77.5538C133.454 74.5233 134.505 72.019 136.388 70.0052C138.249 68.0148 140.884 66.5447 144.007 65.4615C150.241 63.2992 158.707 62.5835 167.761 62.5835V64.5835C158.753 64.5835 150.566 65.3031 144.662 67.351C141.716 68.3729 139.41 69.7017 137.849 71.3713C136.309 73.0175 135.454 75.0345 135.454 77.5538H133.454ZM167.761 92.4199C158.757 92.4199 150.292 91.7283 144.047 89.5922C140.919 88.5222 138.275 87.0659 136.405 85.0862C134.512 83.0826 133.454 80.5845 133.454 77.5538H135.454C135.454 80.0687 136.313 82.0767 137.859 83.7128C139.427 85.3727 141.742 86.6897 144.695 87.6999C150.612 89.724 158.801 90.4199 167.761 90.4199V92.4199ZM202.067 77.4496C202.067 80.4802 201.017 82.9844 199.133 84.9982C197.272 86.9887 194.638 88.4587 191.515 89.542C185.281 91.7043 176.815 92.4199 167.761 92.4199V90.4199C176.768 90.4199 184.955 89.7003 190.859 87.6524C193.805 86.6305 196.111 85.3017 197.673 83.6321C199.212 81.9859 200.067 79.9689 200.067 77.4496H202.067Z" fill="url(#paint2_linear_25553_34401)" mask="url(#path-6-outside-1_25553_34401)"/>
</g>
<defs>
<filter id="filter0_b_25553_34401" x="101" y="-8" width="132" height="122" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_25553_34401"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_25553_34401" result="shape"/>
</filter>
<filter id="filter1_b_25553_34401" x="118.454" y="-6" width="98.6133" height="113.42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_25553_34401"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_25553_34401" result="shape"/>
</filter>
<linearGradient id="paint0_linear_25553_34401" x1="309.475" y1="10.7273" x2="199.744" y2="48.8839" gradientUnits="userSpaceOnUse">
<stop offset="0.197917" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_25553_34401" x1="167.761" y1="10" x2="167.761" y2="91.4199" gradientUnits="userSpaceOnUse">
<stop offset="0.203125" stop-color="#8D56E8"/>
<stop offset="1" stop-color="#E47AFF"/>
</linearGradient>
<linearGradient id="paint2_linear_25553_34401" x1="190.464" y1="19.4855" x2="137.338" y2="73.3173" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.25"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
