<svg width="335" height="131" viewBox="0 0 335 131" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_25553_34942)">
<rect x="116" y="10.7398" width="113" height="108" rx="8" fill="url(#paint0_linear_25553_34942)"/>
</g>
<path d="M111.84 112.177V118.193C111.84 120.403 113.631 122.193 115.84 122.193H121.856" stroke="#D82D8D" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M121.855 7.28467L115.839 7.28467C113.63 7.28467 111.839 9.07553 111.839 11.2847L111.839 17.3008" stroke="#D82D8D" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M232 17.0161L232 11C232 8.79087 230.209 7.00001 228 7.00001L221.984 7.00001" stroke="#D82D8D" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M221.984 121.909L228.001 121.909C230.21 121.909 232.001 120.118 232.001 117.909L232.001 111.893" stroke="#D82D8D" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<g filter="url(#filter1_bd_25553_34942)">
<g filter="url(#filter2_b_25553_34942)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M154.795 44.0793C154.795 53.6486 162.626 61.3172 172.397 61.3172C182.165 61.3172 189.999 53.6486 189.999 44.0793C189.999 34.51 182.165 26.8413 172.397 26.8413C162.626 26.8413 154.795 34.51 154.795 44.0793ZM199 80.8372C199 71.9789 186.746 69.7626 172.398 69.7626C157.973 69.7626 145.797 72.0552 145.797 80.9204C145.797 89.7787 158.051 91.995 172.398 91.995C186.824 91.995 199 89.7024 199 80.8372Z" fill="url(#paint1_linear_25553_34942)"/>
</g>
</g>
<g filter="url(#filter3_b_25553_34942)">
<g filter="url(#filter4_b_25553_34942)">
<mask id="path-7-outside-1_25553_34942" maskUnits="userSpaceOnUse" x="102.164" y="46.4372" width="75.7664" height="76.1027" fill="black">
<rect fill="white" x="102.164" y="46.4372" width="75.7664" height="76.1027"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M167.921 72.5704C164.272 58.8089 150.305 50.6332 136.727 54.3094C130.206 56.0748 124.648 60.3931 121.277 66.3143C117.905 72.2356 116.995 79.2747 118.748 85.8832C122.397 99.6447 136.364 107.82 149.943 104.144C163.521 100.468 171.571 86.332 167.921 72.5704ZM124.563 105.846L118.812 113.755L118.685 113.789C117.602 115.692 118.247 118.126 120.127 119.226C122.006 120.326 124.408 119.676 125.491 117.774L129.716 109.146C130.137 108.41 130.251 107.535 130.033 106.713C129.815 105.891 129.283 105.19 128.554 104.765C127.154 103.962 125.378 104.443 124.563 105.846Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M167.921 72.5704C164.272 58.8089 150.305 50.6332 136.727 54.3094C130.206 56.0748 124.648 60.3931 121.277 66.3143C117.905 72.2356 116.995 79.2747 118.748 85.8832C122.397 99.6447 136.364 107.82 149.943 104.144C163.521 100.468 171.571 86.332 167.921 72.5704ZM124.563 105.846L118.812 113.755L118.685 113.789C117.602 115.692 118.247 118.126 120.127 119.226C122.006 120.326 124.408 119.676 125.491 117.774L129.716 109.146C130.137 108.41 130.251 107.535 130.033 106.713C129.815 105.891 129.283 105.19 128.554 104.765C127.154 103.962 125.378 104.443 124.563 105.846Z" fill="url(#paint2_linear_25553_34942)"/>
<path d="M136.727 54.3094L136.984 55.2758L136.727 54.3094ZM167.921 72.5704L166.956 72.8311L167.921 72.5704ZM121.277 66.3143L122.144 66.808L121.277 66.3143ZM118.748 85.8832L117.783 86.1438L118.748 85.8832ZM149.943 104.144L150.2 105.111L149.943 104.144ZM118.812 113.755L119.618 114.343C119.484 114.528 119.29 114.662 119.069 114.721L118.812 113.755ZM124.563 105.846L125.425 106.347C125.408 106.377 125.389 106.406 125.369 106.434L124.563 105.846ZM118.685 113.789L117.818 113.296C117.95 113.063 118.17 112.893 118.428 112.823L118.685 113.789ZM120.127 119.226L119.62 120.091L120.127 119.226ZM125.491 117.774L126.388 118.212C126.379 118.231 126.369 118.249 126.358 118.268L125.491 117.774ZM129.716 109.146L128.82 108.708C128.829 108.688 128.839 108.669 128.85 108.651L129.716 109.146ZM130.033 106.713L129.067 106.973L129.067 106.973L130.033 106.713ZM128.554 104.765L129.053 103.896L129.059 103.9L128.554 104.765ZM136.47 53.343C150.593 49.5194 165.099 58.0252 168.887 72.3098L166.956 72.8311C163.445 59.5927 150.018 51.7469 136.984 55.2758L136.47 53.343ZM120.41 65.8207C123.913 59.6684 129.689 55.1787 136.47 53.343L136.984 55.2758C130.723 56.9709 125.384 61.1178 122.144 66.808L120.41 65.8207ZM117.783 86.1438C115.963 79.2827 116.907 71.9724 120.41 65.8207L122.144 66.808C118.903 72.4988 118.028 79.2667 119.713 85.6226L117.783 86.1438ZM150.2 105.111C136.077 108.934 121.571 100.429 117.783 86.1438L119.713 85.6226C123.224 98.861 136.651 106.707 149.686 103.178L150.2 105.111ZM168.887 72.3098C172.675 86.594 164.322 101.287 150.2 105.111L149.686 103.178C162.721 99.6488 170.467 86.07 166.956 72.8311L168.887 72.3098ZM118.005 113.167L123.756 105.259L125.369 106.434L119.618 114.343L118.005 113.167ZM118.428 112.823L118.555 112.789L119.069 114.721L118.942 114.756L118.428 112.823ZM119.62 120.091C117.269 118.714 116.463 115.674 117.818 113.296L119.552 114.283C118.74 115.709 119.226 117.537 120.633 118.361L119.62 120.091ZM126.358 118.268C124.999 120.654 121.98 121.473 119.62 120.091L120.633 118.361C122.032 119.18 123.817 118.698 124.624 117.28L126.358 118.268ZM130.612 109.584L126.388 118.212L124.595 117.336L128.82 108.708L130.612 109.584ZM130.998 106.452C131.283 107.527 131.135 108.675 130.582 109.641L128.85 108.651C129.139 108.146 129.218 107.542 129.067 106.973L130.998 106.452ZM129.059 103.9C130.017 104.458 130.713 105.377 130.998 106.452L129.067 106.973C128.916 106.405 128.549 105.922 128.05 105.631L129.059 103.9ZM123.7 105.345C124.792 103.465 127.176 102.819 129.053 103.896L128.056 105.635C127.132 105.105 125.964 105.42 125.425 106.347L123.7 105.345Z" fill="url(#paint3_linear_25553_34942)" mask="url(#path-7-outside-1_25553_34942)"/>
</g>
</g>
<defs>
<filter id="filter0_b_25553_34942" x="102" y="-3.26022" width="141" height="136" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_25553_34942"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_25553_34942" result="shape"/>
</filter>
<filter id="filter1_bd_25553_34942" x="130.797" y="11.8413" width="83.2031" height="95.1537" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_25553_34942"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5" dy="5"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.879167 0 0 0 0 0.879167 0 0 0 0 0.879167 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_25553_34942" result="effect2_dropShadow_25553_34942"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_25553_34942" result="shape"/>
</filter>
<filter id="filter2_b_25553_34942" x="130.797" y="11.8413" width="83.2031" height="95.1537" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_25553_34942"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_25553_34942" result="shape"/>
</filter>
<filter id="filter3_b_25553_34942" x="92.877" y="28.4276" width="100.922" height="116.335" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_25553_34942"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_25553_34942" result="shape"/>
</filter>
<filter id="filter4_b_25553_34942" x="92.877" y="28.4276" width="100.922" height="116.335" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_25553_34942"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_25553_34942" result="shape"/>
</filter>
<linearGradient id="paint0_linear_25553_34942" x1="222.569" y1="6.27061" x2="171.243" y2="97.3679" gradientUnits="userSpaceOnUse">
<stop stop-color="#F8ECFF"/>
<stop offset="1" stop-color="white" stop-opacity="0.47"/>
</linearGradient>
<linearGradient id="paint1_linear_25553_34942" x1="172.398" y1="26.8413" x2="172.398" y2="91.995" gradientUnits="userSpaceOnUse">
<stop offset="0.203125" stop-color="#8D56E8"/>
<stop offset="1" stop-color="#E47AFF"/>
</linearGradient>
<linearGradient id="paint2_linear_25553_34942" x1="132.675" y1="55.4048" x2="148.202" y2="112.842" gradientUnits="userSpaceOnUse">
<stop stop-color="#677EF1"/>
<stop offset="1" stop-color="#FF8BF3"/>
</linearGradient>
<linearGradient id="paint3_linear_25553_34942" x1="110.86" y1="72.432" x2="165.311" y2="59.2681" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.46"/>
</linearGradient>
</defs>
</svg>
