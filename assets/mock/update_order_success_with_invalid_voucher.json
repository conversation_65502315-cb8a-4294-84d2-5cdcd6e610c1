{"message": "", "time": "2022-10-26T02:35:54Z", "verdict": "success", "user_message": "", "data": {"session": {"channel": "string", "created_at": "string", "fee": 0, "id": "string", "merchant_info": {"address": "string", "banner": "string", "id": "string", "large_banner": "string", "name": "string", "status": "string", "thumbnail": "string"}, "next_action": {"args": {"link": "string", "screen_name": "string"}, "type": "string"}, "order_amount": 0, "order_info": {"created_date": "string", "description": "string", "id": "string", "order_amount": 0, "order_number": "string"}, "payment_info": {"payment_method_id": "string", "payment_methods": [{"card_info": {"brand": "string", "issuer": "string", "last4": "string", "product_id": "string", "type": "string"}, "id": "string", "psp_code": "string", "source_name": "string", "source_of_fund": {"name": "string", "product_code": "string", "status": "string"}, "status": "string", "type": "string"}]}, "product_code": "string", "promotion_amount": 0, "promotion_info": {"discount_amount": 0, "invalid_vouchers": [{"rejected_reason": "string", "rejected_verdict": "promotion_invalid", "user_message": "update_order_success_with_invalid_voucher", "voucher_id": 0}], "voucher_ids": []}, "status": "string", "store_info": {"address": "string", "banner": "string", "id": "string", "large_banner": "string", "merchant_id": "string", "merchant_name": "string", "name": "string", "status": "string", "thumbnail": "string"}, "transaction_id": "string", "updated_at": "string", "user_charge_amount": 0, "user_info": {"email": "string", "id": 0, "name": "string", "phone": "string"}}, "emi_package": {"conversion_fee": 999999, "monthly_installment_amount": 1111111, "offer": {"conversion_fee_rate": 2.22, "id": "fake_emi_offer_id", "information": ["string-1", "string-2", "string-3"], "interest_rate": 0, "is_recommended": false, "tenor": 3}, "outright_purchase_diff": 99999}}}