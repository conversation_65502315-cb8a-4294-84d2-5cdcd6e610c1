# ts_bio_detect_changed

Use to detect when biometrics entity in device (Android | iOS) is changed (adding/remove one)

## Getting Started

Prerequisites: there is at least one biometric entity which is enrolled 

**For IOS**:
can detect when new biometrics entity is added or removed.
refer: https://carvercode.com/how-to-detect-a-change-in-biometrics-on-ios/

**For Android**:
can detect when new biometrics entity is added or removed all entities.
***Note***:
* we just detect changed with [Biometrics - strong type](https://developer.android.com/training/articles/keystore).
* Some Android devices can't detect when removed all biometrics entities  

refer:
https://medium.com/@ghodasarabhaumik/android-fingerprint-enrolment-detection-detect-fingerprint-added-removed-68f8189766f9

**IMPLEMENTATION**:
#1: invoke `initialize()` after App allow end-user using Biometrics Authentication feature
#2: invoke `isBiometricChanged()` to detect if biometrics entities are changed 


